{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["<PERSON><PERSON>(mvn test:*)", "Bash(rm \"C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\docs\\development_roadmap.md\")", "Bash(rm:*)", "Bash(mvn compile -q)", "<PERSON><PERSON>(timeout 15 mvn spring-boot:run)"], "deny": [], "_permissions_description": "管理 Claude Code 可以执行的命令和网络请求。", "_deny_section_description": "以下是禁止 Claude Code 自主执行的命令列表，这些命令需要手动确认。默认情况下，不在 'allow' 列表中的命令都需要确认。但明确列出在这里可以更清晰地表达“风险”命令。"}, "_file_description": "此文件配置 Claude Code 的本地权限。"}