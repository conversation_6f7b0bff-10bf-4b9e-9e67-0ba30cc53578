# IDE 配置文件 (IntelliJ IDEA)
.idea/
*.iml
*.iws
*.ipr
.project
.classpath
.settings/
 
# VS Code 配置文件
.vscode/
 
# Maven/Gradle 构建输出
target/
build/
.gradle/
*.log
*.jar
*.war
*.zip
*.tar.gz
*.log
!**/src/main/resources/application.properties  # 如果你的 Spring Boot 配置在 resources 下，且是提交的，保留此行
!**/src/main/resources/*.yml                 # 同上
!**/src/main/resources/*.yaml                # 同上
 
# Node.js 相关
frontend/node_modules/
frontend/dist/
frontend/npm-debug.log*
frontend/yarn-debug.log*
frontend/.cache/
frontend/public/dist/

# 排除的前端目录
frontend/newFront/
frontend/nginx/
 
# 开发相关（根据需要添加）
.DS_Store # macOS
Thumbs.db # Windows
*.swp     # Vim 临时文件
*.swo     # Vim 临时文件
 
# 其他可能忽略的文件
*.bak
*.tmp

# 本地配置文件（不提交到仓库）
**/application-local.yaml
**/application-local.yml
**/application-local.properties

# 上传文件目录
uploads/
*.tmp

# 环境变量文件
.env
.env.local
.env.development
.env.production