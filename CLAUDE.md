# CLAUDE.md

This file provides guidance to Claude Code (claude.ai/code) when working with code in this repository.

## 重要规则 / Important Rules

**必须使用中文回答** - Claude Code必须始终使用中文与用户交流，包括所有解释、建议和技术指导。只有在引用具体的代码、命令或英文技术文档时才可以使用英文。

**Must answer in Chinese** - Claude Code must always communicate with users in Chinese, including all explanations, suggestions, and technical guidance. English should only be used when quoting specific code, commands, or English technical documentation.

## 项目概述 / Project Overview

这是一个剧本杀(Script Murder)平台 - 为剧本杀游戏爱好者提供的全栈Web应用。该项目从原有的黑马点评(HMDP)平台重构而来，专门为剧本杀游戏爱好者打造。

**核心架构 / Key Architecture:**
- **后端 / Backend**: Spring Boot 2.7.18 + MySQL 8.0 + Redis 6.0 + MyBatis-Plus
- **前端 / Frontend**: Vue 3.4 + TypeScript + Vite + Element Plus + Pinia
- **包结构 / Package Structure**: `com.scriptmurder.*` (所有Java类统一使用)
- **数据库 / Database**: 所有表都使用 `tb_` 前缀

## 开发命令 / Development Commands

### 后端 (Java Spring Boot)
```bash
cd backend

# 运行应用 / Run the application
mvn spring-boot:run

# 运行测试 / Run tests
mvn test

# 运行特定测试类 / Run specific test class
mvn test -Dtest=UserSettingsServiceTest

# 运行单个测试方法 / Run specific test method
mvn test -Dtest=UserSettingsServiceTest#testSpecificMethod

# 构建JAR / Build JAR
mvn clean package

# 跳过测试构建 / Build without tests
mvn clean package -DskipTests

# 主类: com.scriptmurder.ScriptMurderApplication
# 服务器运行在: http://localhost:8081
# API文档: http://localhost:8081/doc.html
```

### 前端 (Vue 3)
```bash
cd frontend

# 安装依赖 / Install dependencies
npm install

# 开发服务器 / Development server
npm run dev

# 生产构建 / Build for production
npm run build

# 类型检查 / Type checking
npm run type-check

# 代码检查 / Lint code
npm run lint

# 格式化代码 / Format code
npm run format

# 前端运行在: http://localhost:3000 (vite.config.ts中配置)
```

### 数据库设置 / Database Setup
```bash
# MySQL 数据库: hmdp
# 导入结构: sql/hmdp.sql
# 连接信息: localhost:3306, 用户: root, 密码: 123456
# Redis: localhost:6379, 密码: 123321

# 导入数据库 / Import database
mysql -u root -p123456 hmdp < sql/hmdp.sql

# 数据库迁移脚本 / Database migration scripts
mysql -u root -p123456 hmdp < sql/upgrade_user_settings.sql
```

### 项目脚本 / Project Scripts
```powershell
# Windows PowerShell 脚本位于 scripts/ 目录
# 重命名包结构 / Rename packages
.\scripts\rename-packages.ps1

# 更新作者信息 / Update author info
.\scripts\update-author.ps1

# Git 设置 / Git setup
.\scripts\git-setup.ps1

# 清理敏感信息 / Clean secrets
.\scripts\clean-secrets-safe.ps1
```

## 代码架构和模式 / Code Architecture & Patterns

### 后端架构 (Spring Boot)
- **分层架构 / Layered Architecture**: Controller → Service → Mapper (MyBatis-Plus)
- **包结构 / Package Structure**: 所有类都遵循 `com.scriptmurder.*` 模式
- **认证方式 / Authentication**: 基于JWT + Redis存储token
- **API文档 / API Documentation**: Knife4j (Swagger) 在 `/doc.html`
- **数据库访问 / Database Access**: MyBatis-Plus 自动CRUD操作
- **缓存策略 / Caching**: Redis + Redisson分布式锁
- **文件上传 / File Upload**: 支持本地存储和阿里云OSS

**核心模式 / Key Patterns:**
- 全局异常处理通过 `WebExceptionAdvice`
- 统一API响应格式通过 `Result` 类
- 基于拦截器的认证 (`LoginInterceptor`, `RefreshTokenInterceptor`)
- `com.scriptmurder.utils` 中的工具类 (UserHolder, RedisConstants 等)

### 前端架构 (Vue 3)
- **组合式API / Composition API**: 主要开发模式使用 `<script setup>`
- **状态管理 / State Management**: Pinia 模块化store
- **路由管理 / Routing**: Vue Router 4 基于布局的组织
- **API层 / API Layer**: Axios 集中式请求/响应拦截器
- **组件架构 / Component Architecture**: 布局组件 + 页面组件 + 可复用组件
- **开发服务器代理 / Dev Server Proxy**: `/api` 路由代理到 backend:8081

**核心模式 / Key Patterns:**
- 可复用逻辑的组合式函数 (`useAuth.ts`, `useSettings.js` 等)
- `src/types/` 中的TypeScript接口确保类型安全
- `src/styles/` 中的SCSS设计系统变量
- Element Plus UI组件库
- 路径别名 `@` 指向 `src` 目录

### 数据库设计 / Database Design
- **用户系统 / User System**: `tb_user`, `tb_user_info`, `tb_user_settings`
- **认证审计 / Authentication**: `tb_login_history` 登录历史审计
- **社交功能 / Social Features**: `tb_follow`, `tb_user_favorite`
- **剧本系统 / Script System**: `tb_script`, `tb_script_character`, `tb_script_review`

## 关键配置和集成 / Key Configuration & Integrations

### 认证系统 / Authentication System
- **登录方式**: 手机验证码 + 邮箱密码双重登录支持
- **Token机制**: JWT + Redis存储，自动刷新机制
- **权限控制**: 双拦截器架构 (`LoginInterceptor` + `RefreshTokenInterceptor`)
- **用户状态**: `UserHolder.getUser()` 获取当前用户信息

### 高级功能集成 / Advanced Integrations
- **搜索引擎**: Elasticsearch 8.x 集成，支持复杂查询
- **消息队列**: RabbitMQ 异步任务处理
- **分布式锁**: Redisson 保证数据一致性
- **文件存储**: 本地存储 + 阿里云OSS双模式
- **监控健康检查**: Spring Boot Actuator + 自定义健康指标

### 开发环境配置 / Development Environment
- **端口配置**: 后端8081，前端3000，配置在 `vite.config.ts`
- **CORS设置**: 后端已配置跨域支持
- **代理设置**: Vite开发服务器代理 `/api` 到后端
- **热重载**: 前后端都支持开发时热重载

## 测试策略 / Testing Strategy

### 后端测试 / Backend Testing
```bash
# 核心测试类
mvn test -Dtest=UserSettingsServiceTest     # 用户设置服务测试
mvn test -Dtest=RedisConnectionTest         # Redis连接测试
mvn test -Dtest=RedissonTest               # 分布式锁测试

# 数据加载测试
mvn test -Dtest=LoadShopData               # 批量数据导入测试
mvn test -Dtest=GetAllUserId               # 用户ID获取测试
```

### 关键测试覆盖 / Key Test Coverage
- **用户认证流程**: 登录、注册、Token刷新
- **数据库连接**: MySQL + Redis连接性测试
- **分布式锁**: Redisson锁机制测试
- **服务层逻辑**: 业务逻辑单元测试

## 部署和环境 / Deployment & Environment

### 环境配置 / Environment Configuration
- **开发环境**: `application-local.yaml` (默认)
- **测试环境**: `application-test.yaml`
- **生产环境**: `application-prod.yaml`

### 关键配置项 / Critical Configuration
- **数据库**: MySQL 8.0 + Redis 6.0
- **文件存储模式**: `script-murder.blog.upload.type` (local/oss)
- **JWT密钥**: 生产环境需要强密钥配置
- **OSS配置**: 阿里云对象存储集成

## 项目状态 / Project Status

| 模块 Module | 后端状态 Backend | 前端状态 Frontend | 备注 Notes |
|-------------|------------------|-------------------|------------|
| 用户认证 Authentication | ✅ 完成 | ✅ 完成 | JWT + 手机验证 |
| 用户管理 User Management | ✅ 完成 | ✅ 完成 | 个人资料、设置、收藏 |
| 剧本管理 Script Management | 🚧 开发中 | 📋 计划中 | 基础CRUD已完成 |
| 房间系统 Room/Lobby | 📋 计划中 | 📋 计划中 | 实时游戏房间 |
| 搜索功能 Search | 🚧 开发中 | 📋 计划中 | Elasticsearch集成 |

## 开发规范 / Development Guidelines

### 代码规范 / Code Standards
- **Java**: 遵循阿里巴巴Java开发手册
- **作者标签**: 所有类使用 `<AUTHOR>
- **包命名**: 统一使用 `com.scriptmurder.*`
- **数据库**: 所有表使用 `tb_` 前缀
- **API规范**: RESTful风格，使用 `/api` 前缀

### 新功能开发流程 / New Feature Development
1. **后端API**: 创建DTO → Service接口和实现 → Controller → Knife4j文档
2. **前端页面**: Vue组件 → 路由配置 → API调用 → 状态管理
3. **数据库**: 实体类更新 → Mapper接口 → SQL迁移脚本
4. **测试验证**: 单元测试 → 集成测试 → 功能验证

### 安全和性能 / Security & Performance
- **敏感信息**: 使用环境变量，不提交到代码库
- **缓存策略**: Redis多层缓存提升性能
- **SQL优化**: MyBatis-Plus自动分页和条件构造
- **前端优化**: Vite构建优化、代码分割、懒加载