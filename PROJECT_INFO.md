# 项目信息

## 基本信息
- **项目名称**: Script Murder Platform (剧本杀平台)
- **项目类型**: 全栈Web应用
- **开发者**: an
- **创建时间**: 2025年1月
- **当前版本**: v1.0.0

## 技术栈

### 后端
- **语言**: Java 17
- **框架**: Spring Boot 2.7.18
- **数据库**: MySQL 8.0
- **缓存**: Redis 6.0
- **ORM**: MyBatis-Plus 3.5.3
- **认证**: JWT
- **文档**: Knife4j 4.4.0
- **工具库**: Hutool 5.8.22
- **分布式锁**: Redisson 3.23.4

### 前端
- **框架**: Vue 3.4
- **语言**: TypeScript
- **构建工具**: Vite
- **UI库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4

### 数据库设计
- **用户系统**: tb_user, tb_user_info
- **剧本管理**: tb_script, tb_script_character, tb_script_review, tb_script_rule
- **房间系统**: tb_lobby, tb_lobby_player
- **社区功能**: tb_blog, tb_blog_comments, tb_follow
- **其他功能**: tb_shop, tb_voucher, tb_sign等

## 项目结构
```
script-murder-platform/
├── backend/                 # Spring Boot 后端
│   ├── src/main/java/com/scriptmurder/
│   │   ├── controller/      # 控制器层
│   │   ├── service/         # 服务层
│   │   ├── entity/          # 实体类
│   │   ├── mapper/          # 数据访问层
│   │   ├── dto/             # 数据传输对象
│   │   ├── config/          # 配置类
│   │   ├── utils/           # 工具类
│   │   └── enums/           # 枚举类
│   ├── src/main/resources/  # 配置文件
│   └── pom.xml              # Maven配置
├── frontend/                # Vue 3 前端
│   ├── src/                 # 前端源码
│   ├── public/              # 静态资源
│   └── package.json         # npm配置
├── sql/                     # 数据库脚本
├── docs/                    # 项目文档
├── scripts/                 # 脚本文件
└── README.md                # 项目说明
```

## 核心功能
1. **用户认证系统** - 手机验证码登录、JWT认证
2. **剧本管理** - 剧本浏览、搜索、评价
3. **房间系统** - 创建房间、加入房间、角色分配
4. **社区功能** - 用户动态、关注、评论
5. **用户中心** - 个人信息、游戏记录

## 开发规范
- **代码风格**: 遵循阿里巴巴Java开发手册
- **注释规范**: 所有类和方法都有完整的JavaDoc注释
- **作者标识**: 统一使用 `<AUTHOR>
- **包命名**: 统一使用 `com.scriptmurder.*`

## 部署说明
- **开发环境**: 本地开发，支持热重载
- **生产环境**: 支持Docker容器化部署
- **数据库**: MySQL 8.0，支持主从复制
- **缓存**: Redis集群部署
- **文件存储**: 支持本地存储和阿里云OSS

## 安全特性
- JWT Token认证
- 参数校验和SQL注入防护
- XSS攻击防护
- 接口限流
- 敏感信息加密存储
