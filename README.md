# 剧本杀应用 (Script Murder Platform)

[![Build Status](https://img.shields.io/badge/build-passing-brightgreen)](https://github.com/your-username/script-murder-platform)
[![Java](https://img.shields.io/badge/Java-17-orange)](https://openjdk.java.net/projects/jdk/17/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-2.7.18-green)](https://spring.io/projects/spring-boot)
[![Vue](https://img.shields.io/badge/Vue-3.4-green)](https://vuejs.org/)
[![License](https://img.shields.io/badge/license-MIT-blue)](LICENSE)
[![Version](https://img.shields.io/badge/version-1.0.0-orange)](CHANGELOG.md)

> 一个现代化的剧本杀社交平台，为剧本杀爱好者提供剧本发现、房间管理、社区交流的一站式服务。

## ✨ 特性

- 🎭 **丰富剧本库** - 支持剧本分类、搜索、评价
- 🏠 **智能房间管理** - 便捷的房间创建、加入、角色分配
- 👥 **社区互动** - 用户动态、关注、评论系统
- 📱 **响应式设计** - 完美适配桌面端和移动端
- 🔐 **安全认证** - JWT Token + 手机验证码登录
- ⚡ **高性能** - Redis缓存 + 数据库优化
- 🐳 **容器化部署** - Docker + Docker Compose 一键部署

## 🚀 快速开始

### 🎯 开发状态

| 模块 | 状态 | 完成度 | 说明 |
|------|------|--------|------|
| 🔐 用户认证 | ✅ 完成 | 100% | 手机验证码登录、JWT认证、权限控制 |
| 🏗️ 基础架构 | ✅ 完成 | 100% | 前后端分离、API规范、错误处理 |
| 📚 剧本管理 | 🚧 开发中 | 30% | 基础CRUD，需完善详情页和评价 |
| 🎭 房间系统 | 📋 规划中 | 0% | 待开发 |
| 👥 社交功能 | 📋 规划中 | 0% | 待开发 |

### 前置要求

- Java 11+ (推荐 JDK 11)
- Node.js 16+ (推荐 18.x)
- MySQL 8.0+
- Redis 6.0+
- Maven 3.6+
- Docker (可选)

### 一键启动

```bash
# 1. 克隆项目
git clone https://github.com/example/HmdpReconstruction.git
cd HmdpReconstruction

# 2. 启动后端服务 (新终端)
cd backend
mvn spring-boot:run

# 3. 启动前端服务 (新终端)
cd frontend
npm install
npm run dev
```

### 访问地址

- 🌐 **前端应用**: http://localhost:3000
- 🔧 **后端API**: http://localhost:8081
- 📖 **API文档**: http://localhost:8081/doc.html


## 📖 文档

完整的项目文档位于 `docs/` 目录：

- 📝 **[项目总览](docs/DOCUMENTATION.md)** - 架构设计、开发指南、API规范
- 📈 **[模块进度](docs/modules/README.md)** - 各模块开发状态和计划
- 🚀 **[后端指南](docs/backend/README.md)** - Spring Boot 后端开发文档
- 🌐 **[前端指南](docs/frontend/README.md)** - Vue 3 前端开发文档
- 📜 **[版本日志](docs/CHANGELOG.md)** - 版本历史和变更记录
- ✨ **[项目亮点](docs/PROJECT_HIGHLIGHTS.md)** - 技术亮点和成果展示

## 🏗️ 项目结构

```
hmdp-reconstruction/
├── backend/                 # Spring Boot 后端
│   ├── src/main/java/      # Java 源码
│   ├── src/main/resources/ # 配置文件
│   └── pom.xml             # Maven 配置
├── frontend/               # Vue 3 前端
│   ├── src/                # 前端源码
│   ├── public/             # 静态资源
│   └── package.json        # npm 配置
├── sql/                    # 数据库脚本
├── docs/                   # 项目文档
├── docker-compose.dev.yml  # 开发环境编排
└── README.md               # 项目说明
```

## 🛠️ 技术栈

### 后端
- **框架**: Spring Boot 2.7.18
- **Java版本**: Java 17
- **数据库**: MySQL 8.0 + Redis 6.0
- **ORM**: MyBatis-Plus 3.5.3
- **认证**: JWT + Spring Security
- **文档**: Knife4j 4.4.0
- **工具**: Hutool 5.8.22, Redisson 3.23.4

### 前端
- **框架**: Vue 3 + TypeScript
- **构建**: Vite
- **UI**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4

### 基础设施
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **监控**: Prometheus + Grafana
- **CI/CD**: GitHub Actions

## 🧪 测试

```bash
# 后端测试
cd backend
mvn test

# 前端测试
cd frontend
npm run test

# 端到端测试
npm run test:e2e
```

## 📦 部署

### 开发环境
```bash
# 使用Docker Compose
docker-compose -f docker-compose.dev.yml up -d
```

### 生产环境
```bash
# 构建镜像
docker build -t hmdp-backend ./backend
docker build -t hmdp-frontend ./frontend

# 部署
docker-compose -f docker-compose.prod.yml up -d
```

## 🤝 贡献

我们欢迎所有形式的贡献！请查看 [贡献指南](CONTRIBUTING.md) 了解详情。

### 开发流程

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交变更 (`git commit -m 'feat: add amazing feature'`)
4. 推送分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 代码规范

- 遵循 [编码规范](docs/03_guides/coding_standards.md)
- 确保测试通过
- 更新相关文档

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 获取帮助

- 💬 **讨论交流**: [GitHub Discussions](https://github.com/example/hmdp-reconstruction/discussions)
- 🐛 **问题反馈**: [GitHub Issues](https://github.com/example/hmdp-reconstruction/issues)
- 📧 **邮件支持**: <EMAIL>
- 📖 **文档问题**: <EMAIL>

## 🌟 致谢

感谢所有为这个项目做出贡献的开发者和用户！

特别感谢：
- [Vue.js](https://vuejs.org/) 和 [Spring Boot](https://spring.io/projects/spring-boot) 社区
- 所有测试用户和反馈者

## 📊 项目状态

- 🚧 **开发状态**: 活跃开发中
- 📅 **最新版本**: v1.0.0-beta.3 (2025-08-01)
- 🎯 **下个版本**: v1.0.0 (计划 2025-08-25)
- 👥 **贡献者**: 10+
- ⭐ **GitHub Stars**: 100+

---

<div align="center">

**[⬆ 回到顶部](#剧本杀应用-hmdp-reconstruction)**

Made with ❤️ by [开发团队](https://github.com/example/hmdp-reconstruction/graphs/contributors)

</div>
