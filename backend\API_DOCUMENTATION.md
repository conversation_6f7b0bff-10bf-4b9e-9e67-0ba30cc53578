# 迷雾拼本 (Misty Labyrinth) 前后端API对接文档

## 📋 映射策略总览

本文档基于现有的**黑马点评**后端架构，将剧本杀平台的前端需求映射到已有的API接口上，实现**零后端代码修改**的前后端对接。

### 🔄 核心映射关系

| 前端功能 (剧本杀平台) | 后端技术内核 (点评项目) | 映射的API接口 |
|---------------------|----------------------|--------------|
| 🎭 **剧本库** (分页/搜索) | 商户信息查询 | `GET /shop/of/type`, `GET /shop/of/name` |
| 🎯 **剧本详情页** | 单个商户详情 | `GET /shop/{id}` |
| 🚗 **拼车大厅** (分页展示) | 查询秒杀优惠券列表 | `GET /voucher/list/{shopId}` |
| ⚡ **一键上车** (抢名额) | 抢购秒杀券 | `POST /voucher-order/seckill/{id}` |
| 📱 **动态广场** (Feed流) | 关注推送Feed流 | `GET /blog/hot`, `GET /blog/of/follow` |
| 📍 **附近的车队** | 附近商户 (Geo) | `GET /shop/of/type` (带坐标参数) |
| 👤 **用户系统** | 用户登录/注册 | `POST /user/login`, `GET /user/me` |

---

## 基础信息

**Base URL**: `http://localhost:8081`
**响应格式**: 统一JSON格式 (黑马点评原生格式)
**认证方式**: Token认证（请求头：`Authorization: Bearer <token>`）

### 统一响应格式

```json
{
  "success": true,
  "errorMsg": null,
  "data": {
    // 具体数据内容
  },
  "total": 100  // 分页时包含
}
```

**注意**: 实际后端使用的是 `Result` 类，包含 `success`、`errorMsg`、`data` 字段

---

## 1. 用户认证模块 (User Authentication)

### 1.1 发送邮箱验证码
**POST** `/user/code`

**请求参数**:
```json
{
  "email": "<EMAIL>"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": "验证码发送成功",
  "errorMsg": null
}
```

### 1.2 用户注册
**POST** `/user/register`

**请求参数**:
```json
{
  "email": "<EMAIL>",
  "code": "123456",
  "password": "123456",
  "confirmPassword": "123456",
  "nickName": "用户昵称"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": "注册成功",
  "errorMsg": null
}
```

### 1.3 用户登录
**POST** `/user/login`

**请求参数**:
```json
{
  "email": "<EMAIL>",
  "password": "123456"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "nickName": "用户昵称",
      "icon": "https://example.com/avatar.jpg",
      "email": "<EMAIL>"
    }
  },
  "errorMsg": null
}
```

### 1.4 获取用户信息
**GET** `/user/me`

**请求头**:
```
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "nickName": "用户昵称",
    "icon": "https://example.com/avatar.jpg",
    "email": "<EMAIL>",
    "createTime": "2024-07-30T10:00:00"
  },
  "errorMsg": null
}
```

---

## 2. 剧本模块 (Script Management)

### 2.1 获取剧本列表
**GET** `/script/list`

**查询参数**:
- `current`: 页码 (默认: 1)
- `size`: 每页数量 (默认: 10)
- `genre`: 剧本类型 (可选)
- `difficulty`: 难度等级 (可选)
- `playerCount`: 玩家人数 (可选)

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "id": 1,
        "title": "迷雾庄园",
        "coverImage": "https://example.com/cover1.jpg",
        "genre": "推理",
        "difficulty": 4,
        "playerCount": 6,
        "duration": 240,
        "rating": 4.8,
        "reviewCount": 156,
        "description": "一个充满谜团的古老庄园...",
        "tags": ["推理", "悬疑", "古风"],
        "price": 68
      }
    ],
    "total": 50,
    "current": 1,
    "size": 10
  }
}
```

### 2.2 获取剧本详情
**GET** `/script/{id}`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "title": "迷雾庄园",
    "coverImage": "https://example.com/cover1.jpg",
    "images": ["https://example.com/img1.jpg", "https://example.com/img2.jpg"],
    "genre": "推理",
    "difficulty": 4,
    "playerCount": 6,
    "duration": 240,
    "rating": 4.8,
    "reviewCount": 156,
    "description": "详细的剧本介绍...",
    "story": "背景故事...",
    "characters": [
      {
        "name": "管家",
        "description": "庄园的老管家，知晓许多秘密",
        "avatar": "https://example.com/char1.jpg"
      }
    ],
    "rules": "游戏规则说明...",
    "tags": ["推理", "悬疑", "古风"],
    "price": 68,
    "author": "知名编剧",
    "publishDate": "2024-01-15"
  }
}
```

### 2.3 获取热门剧本
**GET** `/script/hot`

**查询参数**:
- `limit`: 返回数量 (默认: 10)

---

## 3. 车队模块 (Lobby Management)

### 3.1 获取车队列表
**GET** `/lobby/list`

**查询参数**:
- `current`: 页码 (默认: 1)
- `size`: 每页数量 (默认: 12)
- `scriptId`: 剧本ID (可选)
- `status`: 状态筛选 (可选: waiting, full, in_progress)

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "id": 1,
        "script": {
          "id": 1,
          "title": "迷雾庄园",
          "coverImage": "https://example.com/cover1.jpg"
        },
        "host": {
          "id": 2,
          "nickname": "房主大大",
          "avatar": "https://example.com/avatar2.jpg"
        },
        "currentPlayers": 4,
        "maxPlayers": 6,
        "status": "waiting",
        "startTime": "2024-07-30T19:00:00",
        "endTime": "2024-07-30T23:00:00",
        "description": "欢迎新手，氛围轻松",
        "requirements": "有基础推理经验",
        "location": "线上",
        "price": 68,
        "createdAt": "2024-07-29T10:00:00"
      }
    ],
    "total": 25,
    "current": 1,
    "size": 12
  }
}
```

### 3.2 创建车队
**POST** `/lobby/create`

**请求参数**:
```json
{
  "scriptId": 1,
  "maxPlayers": 6,
  "startTime": "2024-07-30T19:00:00",
  "description": "欢迎新手，氛围轻松",
  "requirements": "有基础推理经验",
  "location": "线上",
  "price": 68
}
```

### 3.3 加入车队
**POST** `/lobby/{id}/join`

**响应示例**:
```json
{
  "code": 200,
  "message": "成功加入车队",
  "data": {
    "lobbyId": 1,
    "joinedAt": "2024-07-29T14:30:00"
  }
}
```

### 3.4 退出车队
**POST** `/lobby/{id}/leave`

### 3.5 获取车队详情
**GET** `/lobby/{id}`

---

## 4. 动态模块 (Feed/Blog Management)

### 4.1 获取动态列表
**GET** `/blog/hot`

**查询参数**:
- `current`: 页码 (默认: 1)
- `size`: 每页数量 (默认: 10)

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "id": 1,
        "title": "《迷雾庄园》游戏体验分享",
        "content": "昨晚和朋友们一起玩了这个剧本...",
        "images": ["https://example.com/blog1.jpg"],
        "author": {
          "id": 3,
          "nickname": "推理达人",
          "avatar": "https://example.com/avatar3.jpg"
        },
        "script": {
          "id": 1,
          "title": "迷雾庄园"
        },
        "liked": 23,
        "isLike": false,
        "commentCount": 8,
        "createdAt": "2024-07-29T12:00:00"
      }
    ],
    "total": 100,
    "current": 1,
    "size": 10
  }
}
```

### 4.2 发布动态
**POST** `/blog/save`

**请求参数**:
```json
{
  "title": "游戏体验分享",
  "content": "详细的游戏体验...",
  "images": ["https://example.com/upload1.jpg"],
  "scriptId": 1
}
```

### 4.3 点赞动态
**PUT** `/blog/like/{id}`

### 4.4 获取动态详情
**GET** `/blog/{id}`

---

## 5. 评论模块 (Review Management)

### 5.1 获取剧本评论
**GET** `/review/script/{scriptId}`

**查询参数**:
- `current`: 页码 (默认: 1)
- `size`: 每页数量 (默认: 10)

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "summary": {
      "averageRating": 4.8,
      "totalReviews": 156,
      "ratingDistribution": {
        "5": 89,
        "4": 45,
        "3": 15,
        "2": 5,
        "1": 2
      }
    },
    "reviews": [
      {
        "id": 1,
        "user": {
          "id": 4,
          "nickname": "玩家A",
          "avatar": "https://example.com/avatar4.jpg"
        },
        "rating": 5,
        "content": "非常精彩的剧本，推理逻辑很棒！",
        "images": ["https://example.com/review1.jpg"],
        "createdAt": "2024-07-28T20:00:00",
        "liked": 12,
        "isLike": false
      }
    ],
    "total": 156,
    "current": 1,
    "size": 10
  }
}
```

### 5.2 提交评论
**POST** `/review/submit`

**请求参数**:
```json
{
  "scriptId": 1,
  "rating": 5,
  "content": "非常精彩的剧本！",
  "images": ["https://example.com/review1.jpg"]
}
```

---

## 6. 文件上传模块

### 6.1 上传图片
**POST** `/upload/image`

**请求参数**: FormData (multipart/form-data)
- `file`: 图片文件

**响应示例**:
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "url": "https://example.com/uploads/20240729/image.jpg",
    "filename": "image.jpg",
    "size": 1024000
  }
}
```

---

## 7. WebSocket 实时通信

### 7.1 连接地址
`ws://localhost:8081/ws`

### 7.2 消息格式
```json
{
  "type": "LOBBY_UPDATE",
  "data": {
    "lobbyId": 1,
    "currentPlayers": 5,
    "action": "player_joined"
  }
}
```

### 7.3 消息类型
- `LOBBY_UPDATE`: 车队状态更新
- `NEW_LOBBY`: 新车队创建
- `LOBBY_FULL`: 车队满员
- `GAME_START`: 游戏开始

---

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 40001 | 邮箱格式错误 |
| 40002 | 验证码错误或已过期 |
| 40003 | 用户不存在或密码错误 |
| 40004 | 邮箱已被注册 |
| 40005 | 密码长度不能少于6位 |
| 40006 | 两次输入的密码不一致 |
| 40007 | 昵称不能为空 |
| 40008 | 车队已满员 |
| 40009 | 已加入该车队 |
| 40010 | 剧本不存在 |
| 40011 | 权限不足 |
| 40012 | Token无效或已过期 |
| 50001 | 服务器内部错误 |
| 50002 | 邮件发送失败 |

---

## 8. Elasticsearch 管理接口 (Admin APIs)

### 8.1 重建索引
**POST** `/admin/es/rebuild-index`

**权限**: 管理员  
**说明**: 全量重建Elasticsearch索引，适用于初始化或数据修复

**请求头**:
```
Authorization: Bearer <admin-token>
Content-Type: application/json
```

**响应示例**:
```json
{
  "code": 200,
  "message": "索引重建成功",
  "data": "重建完成，同步了1500条数据，耗时45秒"
}
```

### 8.2 增量同步
**POST** `/admin/es/incremental-sync`

**权限**: 管理员  
**说明**: 增量同步最近更新的数据到Elasticsearch

**响应示例**:
```json
{
  "code": 200,
  "message": "增量同步成功",
  "data": "同步了25条数据"
}
```

### 8.3 数据一致性检查
**POST** `/admin/es/data-consistency-check`

**权限**: 管理员  
**说明**: 检查数据库与Elasticsearch的数据一致性

**响应示例**:
```json
{
  "code": 200,
  "message": "数据一致性检查完成",
  "data": {
    "dbCount": 1500,
    "esCount": 1498,
    "difference": 2,
    "consistency": "97.8%",
    "action": "增量同步已自动触发"
  }
}
```

### 8.4 清空同步队列
**POST** `/admin/es/clear-sync-queues`

**权限**: 管理员  
**说明**: 清空Redis中的待同步和失败同步队列

**响应示例**:
```json
{
  "code": 200,
  "message": "同步队列已清空",
  "data": {
    "clearedPending": 5,
    "clearedFailed": 2
  }
}
```

### 8.5 查看同步状态
**GET** `/admin/es/sync-status`

**权限**: 管理员  
**说明**: 查看当前Elasticsearch同步状态

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "lastSyncTime": "2024-01-01 12:00:00",
    "pendingCount": 5,
    "failedCount": 2,
    "indexHealth": "green",
    "syncStrategies": {
      "realtime": {
        "enabled": true,
        "description": "数据变更时立即同步"
      },
      "scheduled": {
        "enabled": true,
        "interval": "每5分钟",
        "nextRun": "2024-01-01 12:05:00"
      },
      "retry": {
        "enabled": true,
        "interval": "每30分钟",
        "nextRun": "2024-01-01 12:30:00"
      },
      "consistencyCheck": {
        "enabled": true,
        "schedule": "每日凌晨2点",
        "lastCheck": "2024-01-01 02:00:00"
      }
    }
  }
}
```

### 8.6 查看索引统计
**GET** `/admin/es/index-stats`

**权限**: 管理员  
**说明**: 查看Elasticsearch索引的详细统计信息

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "indexInfo": {
      "indexName": "script_murder_scripts",
      "documentCount": 1500,
      "indexSize": "25.6MB",
      "shardCount": 1,
      "replicaCount": 0,
      "health": "green",
      "status": "open"
    },
    "performanceStats": {
      "searchStats": {
        "totalSearches": 12580,
        "totalSearchTime": "2.5s",
        "avgSearchTime": "0.2ms",
        "currentSearchRate": "45/min"
      },
      "indexingStats": {
        "totalIndexed": 1500,
        "totalIndexTime": "1.2s",
        "avgIndexTime": "0.8ms",
        "currentIndexRate": "12/min"
      }
    },
    "cacheStats": {
      "queryCacheSize": "5.2MB",
      "queryCacheHitRate": "85%",
      "fieldDataSize": "2.1MB"
    }
  }
}
```

## 9. 高级搜索接口

### 9.1 智能搜索
**GET** `/scripts/search/advanced`

**说明**: 基于Elasticsearch 8.x的高级搜索功能

**查询参数**:
- `keyword`: 搜索关键词 (支持模糊匹配)
- `page`: 页码 (默认: 1)
- `size`: 每页数量 (默认: 10)
- `category`: 剧本类型
- `difficulties[]`: 难度等级数组
- `priceMin`: 最低价格
- `priceMax`: 最高价格
- `playerCountMin`: 最少玩家数
- `playerCountMax`: 最多玩家数
- `sortBy`: 排序字段 (createTime, averageRating, playCount, price)
- `sortOrder`: 排序方向 (asc, desc)

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "id": 1,
        "title": "<em class=\"highlight\">推理</em>剧本",
        "description": "精彩的<em class=\"highlight\">推理</em>故事...",
        "coverImage": "https://example.com/cover1.jpg",
        "category": "推理",
        "playerCountRange": "4-6人",
        "duration": "4-5小时",
        "difficulty": 3,
        "price": 88.00,
        "averageRating": 4.5,
        "reviewCount": 128,
        "playCount": 567,
        "isFavorite": false,
        "tags": ["推理", "悬疑", "烧脑"]
      }
    ],
    "total": 50,
    "current": 1,
    "size": 10,
    "pages": 5,
    "aggregations": {
      "categories": {
        "推理": 25,
        "恐怖": 15,
        "情感": 10,
        "欢乐": 8,
        "古风": 12
      },
      "difficulties": {
        "1": 5,
        "2": 12,
        "3": 18,
        "4": 10,
        "5": 5
      },
      "priceRanges": {
        "0-50": 20,
        "50-100": 25,
        "100+": 5
      },
      "playerCountRanges": {
        "2-4人": 15,
        "4-8人": 30,
        "8+人": 5
      }
    }
  }
}
```

### 9.2 搜索建议
**GET** `/scripts/search/suggestions`

**说明**: 获取搜索建议和自动补全

**查询参数**:
- `keyword`: 输入的关键词前缀

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "suggestions": [
      "推理剧本",
      "推理悬疑",
      "推理解密",
      "推理烧脑",
      "推理社交"
    ],
    "hotKeywords": [
      "推理",
      "恐怖",
      "情感",
      "欢乐",
      "古风"
    ]
  }
}
```

### 9.3 热门搜索
**GET** `/scripts/search/hot-keywords`

**说明**: 获取热门搜索关键词排行

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "keyword": "推理",
      "searchCount": 1250,
      "rank": 1
    },
    {
      "keyword": "恐怖",
      "searchCount": 890,
      "rank": 2
    },
    {
      "keyword": "情感",
      "searchCount": 567,
      "rank": 3
    }
  ]
}
```

### 9.4 个性化推荐
**GET** `/scripts/recommendations`

**说明**: 基于用户行为的个性化剧本推荐

**查询参数**:
- `userId`: 用户ID (可选，默认当前用户)
- `category`: 指定类型推荐 (可选)
- `limit`: 推荐数量 (默认: 10)

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "recommendations": [
      {
        "id": 1,
        "title": "迷雾庄园",
        "coverImage": "https://example.com/cover1.jpg",
        "category": "推理",
        "averageRating": 4.8,
        "recommendScore": 0.95,
        "recommendReason": "基于您喜欢的推理类型"
      }
    ],
    "algorithm": "collaborative_filtering",
    "generatedAt": "2024-01-01T12:00:00"
  }
}
```

---
