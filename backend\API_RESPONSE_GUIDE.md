# API响应格式使用指南

## 概述

本项目采用统一的API响应格式，基于剧本杀应用后端开发文档v1.0的规范设计。所有API接口都应该使用统一的响应格式，以确保前后端交互的一致性。

## 核心类说明

### 1. ApiResponse<T> - 统一响应格式

```java
{
  "code": 200,
  "message": "操作成功", 
  "data": {}
}
```

### 2. PageResponse<T> - 分页响应格式

```java
{
  "total": 100,
  "pages": 10,
  "current": 1,
  "size": 10,
  "records": []
}
```

### 3. ResponseCode - 响应状态码枚举

定义了所有标准的响应状态码，包括HTTP标准码和业务自定义码。

### 4. BusinessException - 业务异常类

用于抛出业务逻辑异常，会被全局异常处理器统一处理。

## 使用方法

### 1. 基本成功响应

```java
@RestController
public class ExampleController {
    
    // 成功响应（无数据）
    @GetMapping("/success")
    public ApiResponse<Void> success() {
        return ResponseUtils.success();
    }
    
    // 成功响应（带数据）
    @GetMapping("/data")
    public ApiResponse<User> getData() {
        User user = userService.getCurrentUser();
        return ResponseUtils.success(user);
    }
    
    // 成功响应（自定义消息）
    @PostMapping("/create")
    public ApiResponse<User> create(@RequestBody User user) {
        User created = userService.create(user);
        return ResponseUtils.success("用户创建成功", created);
    }
}
```

### 2. 分页响应

```java
@GetMapping("/users")
public ApiResponse<PageResponse<User>> getUsers(
        @RequestParam(defaultValue = "1") Long current,
        @RequestParam(defaultValue = "10") Long size) {
    
    List<User> users = userService.getUsers(current, size);
    Long total = userService.getTotalCount();
    
    return ResponseUtils.successPage(users, current, size, total);
}
```

### 3. 错误响应

```java
@GetMapping("/user/{id}")
public ApiResponse<User> getUser(@PathVariable Long id) {
    User user = userService.findById(id);
    if (user == null) {
        return ResponseUtils.userNotFound();
    }
    return ResponseUtils.success(user);
}
```

### 4. 使用业务异常

```java
@PostMapping("/login")
public ApiResponse<LoginResult> login(@RequestBody LoginRequest request) {
    User user = userService.findByPhone(request.getPhone());
    if (user == null) {
        throw BusinessException.userNotFound();
    }
    
    if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
        throw new BusinessException(ResponseCode.INVALID_CREDENTIALS);
    }
    
    LoginResult result = authService.login(user);
    return ResponseUtils.success("登录成功", result);
}
```

### 5. 使用响应码枚举

```java
@PostMapping("/register")
public ApiResponse<User> register(@RequestBody RegisterRequest request) {
    if (userService.existsByPhone(request.getPhone())) {
        return ResponseUtils.error(ResponseCode.USER_ALREADY_EXISTS);
    }
    
    User user = userService.register(request);
    return ResponseUtils.success("注册成功", user);
}
```

## 响应状态码规范

### HTTP标准状态码
- `200` - 成功
- `400` - 参数错误
- `401` - 未授权
- `403` - 禁止访问
- `404` - 资源不存在
- `409` - 冲突
- `422` - 业务逻辑错误
- `500` - 服务器内部错误

### 业务自定义状态码
- `1000-1999` - 用户相关
- `2000-2999` - 认证相关
- `3000-3999` - 剧本相关
- `4000-4999` - 房间相关
- `5000-5999` - 评价相关
- `6000-6999` - 文件相关
- `9000-9999` - 系统相关

## 全局异常处理

项目配置了全局异常处理器 `GlobalExceptionHandler`，会自动捕获以下异常：

1. **BusinessException** - 业务异常
2. **MethodArgumentNotValidException** - 参数校验异常
3. **BindException** - 参数绑定异常
4. **ConstraintViolationException** - 约束校验异常
5. **IllegalArgumentException** - 非法参数异常
6. **NullPointerException** - 空指针异常
7. **RuntimeException** - 运行时异常
8. **Exception** - 其他未知异常

所有异常都会被转换为统一的API响应格式返回给前端。

## 最佳实践

### 1. 控制器层

```java
@RestController
@RequestMapping("/api/scripts")
public class ScriptController {
    
    @Autowired
    private ScriptService scriptService;
    
    @GetMapping
    public ApiResponse<PageResponse<Script>> getScripts(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String category) {
        
        PageResponse<Script> scripts = scriptService.getScripts(current, size, category);
        return ResponseUtils.successPage("获取成功", scripts.getRecords(), 
                current, size, scripts.getTotal());
    }
    
    @GetMapping("/{id}")
    public ApiResponse<Script> getScript(@PathVariable Long id) {
        Script script = scriptService.getById(id);
        if (script == null) {
            throw BusinessException.scriptNotFound();
        }
        return ResponseUtils.success(script);
    }
    
    @PostMapping
    public ApiResponse<Script> createScript(@RequestBody @Valid CreateScriptRequest request) {
        Script script = scriptService.create(request);
        return ResponseUtils.success("创建成功", script);
    }
}
```

### 2. 服务层

```java
@Service
public class ScriptService {
    
    public Script getById(Long id) {
        Script script = scriptMapper.selectById(id);
        if (script == null) {
            throw BusinessException.scriptNotFound();
        }
        return script;
    }
    
    public Script create(CreateScriptRequest request) {
        // 检查剧本是否已存在
        if (existsByTitle(request.getTitle())) {
            throw new BusinessException(ResponseCode.SCRIPT_ALREADY_EXISTS);
        }
        
        Script script = new Script();
        BeanUtils.copyProperties(request, script);
        scriptMapper.insert(script);
        
        return script;
    }
}
```

### 3. 参数校验

```java
public class CreateScriptRequest {
    
    @NotBlank(message = "剧本标题不能为空")
    @Length(max = 255, message = "剧本标题长度不能超过255个字符")
    private String title;
    
    @NotBlank(message = "剧本描述不能为空")
    private String description;
    
    @NotNull(message = "最小玩家数不能为空")
    @Min(value = 1, message = "最小玩家数不能小于1")
    private Integer playerCountMin;
    
    // getter/setter...
}
```

## 测试示例

项目提供了 `ApiResponseExampleController` 控制器，包含了各种响应格式的示例。可以通过以下接口进行测试：

- `GET /api/example/success` - 成功响应示例
- `GET /api/example/success-with-data` - 带数据的成功响应
- `GET /api/example/page` - 分页响应示例
- `GET /api/example/error-bad-request` - 错误响应示例
- `GET /api/example/business-exception` - 业务异常示例
- `GET /api/example/common-responses` - 常用响应示例

## 注意事项

1. **统一使用** - 所有API接口都应该使用统一的响应格式
2. **异常处理** - 优先使用业务异常而不是直接返回错误响应
3. **状态码** - 使用预定义的响应状态码，避免自定义状态码
4. **消息内容** - 错误消息应该清晰明确，便于前端展示给用户
5. **数据结构** - 保持响应数据结构的一致性和可预测性

## 前端对接

前端可以根据响应格式进行统一处理：

```javascript
// axios响应拦截器示例
axios.interceptors.response.use(
  response => {
    const { code, message, data } = response.data;
    
    if (code === 200) {
      return data;
    } else {
      // 处理业务错误
      Message.error(message);
      return Promise.reject(new Error(message));
    }
  },
  error => {
    // 处理HTTP错误
    Message.error('网络错误');
    return Promise.reject(error);
  }
);
```
