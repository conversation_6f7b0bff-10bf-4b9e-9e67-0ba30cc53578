-- 创建测试用户
-- 密码是 "123456" 的BCrypt哈希值
INSERT IGNORE INTO tb_user (
    id,
    phone,
    email,
    password,
    nick_name,
    icon,
    gender,
    birthday,
    city,
    signature,
    level,
    experience,
    create_time,
    update_time
) VALUES (
    1037,
    '13800138000',
    '<EMAIL>',
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIgwkPKBpuE6toP.q6/p8YK5jm', -- 密码: 123456
    'luck',
    'http://localhost:8081/images/default-avatar.png',
    1,
    '1990-01-01',
    '北京市',
    '热爱推理剧本，擅长逻辑分析',
    1,
    0,
    NOW(),
    NOW()
);

-- 创建用户设置记录
INSERT IGNORE INTO tb_user_settings (
    user_id,
    theme,
    language,
    notifications_enabled,
    email_notifications,
    push_notifications,
    sound_enabled,
    auto_save,
    privacy_level,
    show_online_status,
    allow_friend_requests,
    allow_game_invites,
    data_usage_analytics,
    marketing_emails,
    security_alerts,
    login_alerts,
    password_change_alerts,
    account_activity_alerts,
    game_reminders,
    friend_activity,
    system_updates,
    promotional_offers,
    weekly_digest,
    achievement_notifications,
    create_time,
    update_time
) VALUES (
    1037,
    'dark',
    'zh-CN',
    1,
    1,
    1,
    1,
    1,
    'public',
    1,
    1,
    1,
    1,
    0,
    1,
    1,
    1,
    1,
    1,
    1,
    1,
    0,
    1,
    1,
    NOW(),
    NOW()
);

-- 创建一些测试登录历史记录
INSERT IGNORE INTO tb_login_history (
    user_id,
    login_ip,
    login_location,
    device_type,
    browser,
    login_time,
    logout_time,
    status
) VALUES 
(1037, '*************', '北京市', 'Desktop', 'Chrome 120.0', NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 23 HOUR, 1),
(1037, '*************', '北京市', 'Mobile', 'Chrome Mobile 120.0', NOW() - INTERVAL 2 HOUR, NOW() - INTERVAL 1 HOUR, 1),
(1037, '*************', '北京市', 'Desktop', 'Chrome 120.0', NOW() - INTERVAL 30 MINUTE, NULL, 1);
