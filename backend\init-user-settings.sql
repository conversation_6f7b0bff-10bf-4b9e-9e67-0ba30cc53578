-- 为现有用户初始化设置数据
-- 这个脚本可以在数据库中直接执行

-- 为用户ID 1037 创建默认设置（如果不存在）
INSERT IGNORE INTO tb_user_settings (
    user_id, 
    theme, 
    language, 
    timezone, 
    email_notifications, 
    system_notifications, 
    activity_notifications, 
    social_notifications, 
    privacy_level, 
    auto_save, 
    create_time, 
    update_time
) VALUES (
    1037,
    'dark',
    'zh-CN',
    'Asia/Shanghai',
    1,
    1,
    1,
    0,
    1,
    1,
    NOW(),
    NOW()
);

-- 创建一些测试登录历史记录
INSERT IGNORE INTO tb_login_history (
    user_id,
    login_ip,
    login_location,
    device_type,
    browser,
    login_time,
    logout_time,
    status
) VALUES
(1037, '*************', '北京市', 'Desktop', 'Chrome 120.0', NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 23 HOUR, 1),
(1037, '*************', '北京市', 'Mobile', 'Chrome Mobile 120.0', NOW() - INTERVAL 2 HOUR, NOW() - INTERVAL 1 HOUR, 1),
(1037, '*************', '北京市', 'Desktop', 'Chrome 120.0', NOW() - INTERVAL 30 MINUTE, NULL, 1);

-- 查看创建的数据
SELECT 'User Settings:' as info;
SELECT * FROM tb_user_settings WHERE user_id = 1037;

SELECT 'Login History:' as info;
SELECT * FROM tb_login_history WHERE user_id = 1037 ORDER BY login_time DESC;
