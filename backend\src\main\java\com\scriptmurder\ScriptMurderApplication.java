package com.scriptmurder;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

@EnableAspectJAutoProxy(exposeProxy = true)
@MapperScan("com.scriptmurder.mapper")
@SpringBootApplication
public class ScriptMurderApplication {

    public static void main(String[] args) {
        SpringApplication.run(ScriptMurderApplication.class, args);
    }
}
