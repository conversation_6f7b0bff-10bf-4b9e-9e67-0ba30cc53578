package com.scriptmurder.config;

import com.scriptmurder.service.IScriptSearchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 应用启动初始化配置
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ApplicationInitializer implements CommandLineRunner {

    @Autowired
    private IScriptSearchService scriptSearchService;

    @Override
    public void run(String... args) throws Exception {
        log.info("开始初始化应用配置...");

        try {
            // 初始化Elasticsearch索引
            scriptSearchService.initializeIndex();
            log.info("Elasticsearch索引初始化完成");

        } catch (Exception e) {
            log.error("应用初始化失败", e);
        }

        log.info("应用配置初始化完成");
    }
}