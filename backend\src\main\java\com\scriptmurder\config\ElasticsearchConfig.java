package com.scriptmurder.config;

import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.repository.config.EnableElasticsearchRepositories;

/**
 * Elasticsearch 7.x 配置 (兼容Spring Boot 2.7.x)
 *
 * <AUTHOR>
 */
@Configuration
@EnableElasticsearchRepositories(basePackages = "com.scriptmurder.search.repository")
public class ElasticsearchConfig {

    @Value("${spring.elasticsearch.uris:http://localhost:9200}")
    private String elasticsearchUrl;

    @Value("${spring.elasticsearch.username:}")
    private String username;

    @Value("${spring.elasticsearch.password:}")
    private String password;

    @Value("${script-murder.elasticsearch.index-prefix:script_murder}")
    private String indexPrefix;

    @Bean
    public RestHighLevelClient elasticsearchClient() {
        // 解析URL
        String host = elasticsearchUrl.replace("http://", "").replace("https://", "");
        String[] hostParts = host.split(":");
        String hostname = hostParts[0];
        int port = hostParts.length > 1 ? Integer.parseInt(hostParts[1]) : 9200;

        // 创建RestClient Builder (使用RestClientBuilder)
        RestClientBuilder builder = RestClient.builder(new HttpHost(hostname, port, "http"));

        // 配置认证
        if (!username.isEmpty() && !password.isEmpty()) {
            CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(AuthScope.ANY,
                    new UsernamePasswordCredentials(username, password));

            builder.setHttpClientConfigCallback(httpClientBuilder ->
                    httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider));
        }

        return new RestHighLevelClient(builder);
    }

    @Bean
    public ElasticsearchRestTemplate elasticsearchTemplate() {
        return new ElasticsearchRestTemplate(elasticsearchClient());
    }

    @Bean
    public ElasticsearchOperations elasticsearchOperations() {
        return elasticsearchTemplate();
    }

    /**
     * 获取剧本搜索索引名
     */
    public String getScriptIndex() {
        return indexPrefix + "_scripts";
    }
}