package com.scriptmurder.config;

import org.elasticsearch.action.admin.cluster.health.ClusterHealthRequest;
import org.elasticsearch.action.admin.cluster.health.ClusterHealthResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.cluster.health.ClusterHealthStatus;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Component;

/**
 * Elasticsearch 7.x 健康检查指标
 *
 * <AUTHOR>
 */
@Component
public class ElasticsearchHealthIndicator implements HealthIndicator {

    private final RestHighLevelClient elasticsearchClient;

    public ElasticsearchHealthIndicator(RestHighLevelClient elasticsearchClient) {
        this.elasticsearchClient = elasticsearchClient;
    }

    @Override
    public Health health() {
        try {
            // 使用ES 7.x的API检查集群健康状态
            ClusterHealthRequest request = new ClusterHealthRequest();
            request.timeout("5s");
            
            ClusterHealthResponse response = elasticsearchClient.cluster()
                    .health(request, RequestOptions.DEFAULT);
            
            ClusterHealthStatus status = response.getStatus();
            
            if (status == ClusterHealthStatus.GREEN || status == ClusterHealthStatus.YELLOW) {
                return Health.up()
                        .withDetail("elasticsearch", "连接正常")
                        .withDetail("status", status.name())
                        .withDetail("cluster", response.getClusterName())
                        .withDetail("nodes", response.getNumberOfNodes())
                        .withDetail("dataNodes", response.getNumberOfDataNodes())
                        .build();
            } else {
                return Health.down()
                        .withDetail("elasticsearch", "集群状态异常")
                        .withDetail("status", status.name())
                        .withDetail("cluster", response.getClusterName())
                        .build();
            }
        } catch (Exception e) {
            return Health.down()
                    .withDetail("elasticsearch", "连接失败")
                    .withDetail("error", e.getMessage())
                    .build();
        }
    }
}