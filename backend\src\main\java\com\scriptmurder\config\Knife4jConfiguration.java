package com.scriptmurder.config; // 或者你的配置包名，但通常会放在 config 下

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class Knife4jConfiguration {

//    @Bean(value = "dockerBean")
//    public Docket dockerBean() {
//        //指定使用Swagger2规范
//        Docket docket=new Docket(DocumentationType.SWAGGER_2)
//                .apiInfo(apiInfo()) // 调用一个私有方法来构建ApiInfo
//                //分组名称
//                .groupName("用户端")
//                .select()
//                //这里指定Controller扫描包路径
//                // 注意：这里需要替换成你项目实际的Controller包路径
//                // 例如：你想扫描 com.hmdp.controller 包下的所有接口
//                .apis(RequestHandlerSelectors.basePackage("com.scriptmurder.controller"))
//                // 或者如果你想扫描所有被 @RestController 或 @Controller 注解的类
//                // .apis(RequestHandlerSelectors.withClassAnnotation(RestController.class))
//                // .apis(RequestHandlerSelectors.withClassAnnotation(Controller.class))
//                .paths(PathSelectors.any())
//                .build();
//        return docket;
//    }
//
//    private ApiInfo apiInfo() {
//        return new ApiInfoBuilder()
//                //描述字段支持Markdown语法
//                .description("# Knife4j RESTful APIs")
//                .termsOfServiceUrl("https://doc.xiaominfo.com/")
//                .contact(new Contact("An", "", "<EMAIL>")) // 注意 Contact 的构造函数
//                .title("Fog") // 建议改为你的项目名
//                .version("1.0")
//                .build();
//    }
}