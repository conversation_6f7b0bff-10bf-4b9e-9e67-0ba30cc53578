package com.scriptmurder.config;

import com.scriptmurder.utils.LoginInterceptor;
import com.scriptmurder.utils.RefreshTokenInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import java.io.File;

@Configuration
public class MVCConfig implements WebMvcConfigurer {
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Value("${file.upload.path:/uploads/}")
    private String uploadPath;

    /**
     * 配置全局API路径前缀
     * 注意：Spring Boot 2.x 版本使用不同的配置方式
     */
    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        // 对于Spring Boot 2.x，我们需要手动在Controller上添加@RequestMapping("/api")
        // 或者使用application.yml配置：spring.mvc.servlet.path=/api
        // configurer.addPathPrefix("/api", c -> c.isAnnotationPresent(org.springframework.web.bind.annotation.RestController.class));
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 登录拦截器 - 拦截需要登录的接口
        registry.addInterceptor(new LoginInterceptor())
                .excludePathPatterns(
                        // 用户认证相关接口
                        "/api/user/code",           // 发送手机验证码
                        "/api/user/email/code",     // 发送邮箱验证码
                        "/api/user/register",       // 用户注册
                        "/api/user/login",          // 用户登录

                        // 公开访问的接口
                        "/api/blog/hot",            // 热门博客
                        "/api/shop/**",             // 商铺信息
                        "/api/shop/type/**",        // 商铺类型
                        "/api/voucher/**",          // 优惠券信息
                        "/api/voucher/seckill/**",  // 秒杀优惠券

                        // 文件上传下载
                        "/upload/**",           // 文件上传
                        "/uploads/**",          // 上传文件访问
                        "/images/**",           // 图片访问

                        // API文档相关
                        "/doc.html",            // Knife4j 文档主页
                        "/webjars/**",          // Swagger UI 静态资源
                        "/swagger-resources/**", // Swagger 资源
                        "/v2/api-docs",         // Swagger API文档
                        "/swagger-ui.html",     // Swagger UI
                        "/favicon.ico",         // 网站图标

                        // 健康检查
                        "/actuator/**",         // Spring Boot Actuator

                        // 错误页面
                        "/error"                // 错误页面
                ).order(1);

        // Token刷新拦截器 - 处理API请求，刷新token有效期
        registry.addInterceptor(new RefreshTokenInterceptor(stringRedisTemplate))
                .addPathPatterns("/api/**")
                .order(0);
    }

    /**
     * 配置静态资源处理
     * 静态资源不使用/api前缀，直接访问
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 确保上传目录存在
        File uploadDir = new File(uploadPath);
        if (!uploadDir.exists()) {
            uploadDir.mkdirs();
        }

        // 配置文件上传静态资源映射
        registry.addResourceHandler("/uploads/**")
                .addResourceLocations("file:" + uploadPath)
                .setCachePeriod(3600); // 设置缓存时间1小时

        // 配置图片静态资源映射
        registry.addResourceHandler("/images/**")
                .addResourceLocations("file:" + uploadPath + "images/")
                .setCachePeriod(3600);
    }
}
