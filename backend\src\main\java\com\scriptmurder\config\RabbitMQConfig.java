package com.scriptmurder.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.annotation.EnableRabbit;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RabbitMQ配置
 *
 * <AUTHOR>
 */
@Configuration
@EnableRabbit
@Slf4j
public class RabbitMQConfig {

    // 交换机名称
    public static final String SCRIPT_EXCHANGE = "script.exchange";
    
    // 队列名称
    public static final String SCRIPT_SYNC_QUEUE = "script.sync.queue";
    public static final String SCRIPT_STATS_QUEUE = "script.stats.queue";
    
    // 路由键
    public static final String SCRIPT_SYNC_ROUTING_KEY = "script.sync";
    public static final String SCRIPT_STATS_ROUTING_KEY = "script.stats";
    
    // 死信交换机和队列
    public static final String SCRIPT_DLX_EXCHANGE = "script.dlx.exchange";
    public static final String SCRIPT_DLX_QUEUE = "script.dlx.queue";
    public static final String SCRIPT_DLX_ROUTING_KEY = "script.dlx";

    /**
     * 剧本交换机
     */
    @Bean
    public DirectExchange scriptExchange() {
        return new DirectExchange(SCRIPT_EXCHANGE, true, false);
    }

    /**
     * 剧本同步队列
     */
    @Bean
    public Queue scriptSyncQueue() {
        return QueueBuilder.durable(SCRIPT_SYNC_QUEUE)
                .withArgument("x-dead-letter-exchange", SCRIPT_DLX_EXCHANGE)
                .withArgument("x-dead-letter-routing-key", SCRIPT_DLX_ROUTING_KEY)
                .withArgument("x-message-ttl", 3600000) // 1小时TTL
                .build();
    }

    /**
     * 剧本统计队列
     */
    @Bean
    public Queue scriptStatsQueue() {
        return QueueBuilder.durable(SCRIPT_STATS_QUEUE)
                .withArgument("x-dead-letter-exchange", SCRIPT_DLX_EXCHANGE)
                .withArgument("x-dead-letter-routing-key", SCRIPT_DLX_ROUTING_KEY)
                .build();
    }

    /**
     * 死信交换机
     */
    @Bean
    public DirectExchange scriptDlxExchange() {
        return new DirectExchange(SCRIPT_DLX_EXCHANGE, true, false);
    }

    /**
     * 死信队列
     */
    @Bean
    public Queue scriptDlxQueue() {
        return QueueBuilder.durable(SCRIPT_DLX_QUEUE).build();
    }

    /**
     * 绑定剧本同步队列到交换机
     */
    @Bean
    public Binding scriptSyncBinding() {
        return BindingBuilder.bind(scriptSyncQueue())
                .to(scriptExchange())
                .with(SCRIPT_SYNC_ROUTING_KEY);
    }

    /**
     * 绑定剧本统计队列到交换机
     */
    @Bean
    public Binding scriptStatsBinding() {
        return BindingBuilder.bind(scriptStatsQueue())
                .to(scriptExchange())
                .with(SCRIPT_STATS_ROUTING_KEY);
    }

    /**
     * 绑定死信队列到死信交换机
     */
    @Bean
    public Binding scriptDlxBinding() {
        return BindingBuilder.bind(scriptDlxQueue())
                .to(scriptDlxExchange())
                .with(SCRIPT_DLX_ROUTING_KEY);
    }

    /**
     * RabbitTemplate配置
     */
    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setMessageConverter(new Jackson2JsonMessageConverter());
        
        // 开启发送确认
        template.setConfirmCallback((correlationData, ack, cause) -> {
            if (ack) {
                log.debug("消息发送成功: correlationData({})", correlationData);
            } else {
                log.error("消息发送失败: correlationData({}), cause({})", correlationData, cause);
            }
        });
        
        // 开启返回确认
        template.setReturnsCallback(returned -> {
            log.error("消息返回: message({}), replyCode({}), replyText({}), exchange({}), routingKey({})",
                    returned.getMessage(), returned.getReplyCode(), returned.getReplyText(),
                    returned.getExchange(), returned.getRoutingKey());
        });
        
        return template;
    }
}