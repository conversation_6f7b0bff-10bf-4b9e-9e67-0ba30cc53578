package com.scriptmurder.config;

import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

/**
 * Redis健康检查
 *
 * <AUTHOR>
 */
@Component
public class RedisHealthIndicator implements HealthIndicator {

    private final StringRedisTemplate stringRedisTemplate;

    public RedisHealthIndicator(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }

    @Override
    public Health health() {
        try {
            // 执行简单的ping命令
            String result = stringRedisTemplate.getConnectionFactory()
                .getConnection()
                .ping();
            
            if ("PONG".equals(result)) {
                return Health.up()
                    .withDetail("redis", "连接正常")
                    .withDetail("ping", result)
                    .build();
            } else {
                return Health.down()
                    .withDetail("redis", "连接异常")
                    .withDetail("ping", result)
                    .build();
            }
        } catch (Exception e) {
            return Health.down()
                .withDetail("redis", "连接失败")
                .withDetail("error", e.getMessage())
                .build();
        }
    }
}