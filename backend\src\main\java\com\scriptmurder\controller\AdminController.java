package com.scriptmurder.controller;

import com.scriptmurder.dto.ApiResponse;
import com.scriptmurder.dto.PageResponse;
import com.scriptmurder.dto.ScriptStatusStatsDTO;
import com.scriptmurder.entity.Script;
import com.scriptmurder.service.IScriptService;
import com.scriptmurder.service.IScriptStatusService;
import com.scriptmurder.utils.UserHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/**
 * 管理员后台控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/admin")
@Slf4j
@Validated
public class AdminController {

    @Autowired
    private IScriptService scriptService;

    @Autowired
    private IScriptStatusService scriptStatusService;

    // ==================== 剧本管理 ====================

    @GetMapping("/scripts")
    public ApiResponse<PageResponse<Script>> getAllScripts(
            @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) String keyword) {
        
        // TODO: 实现管理员剧本列表查询
        // 这里需要创建一个专门的管理员剧本查询方法
        log.info("管理员查询剧本列表: page={}, size={}, status={}, keyword={}", page, size, status, keyword);
        
        // 暂时返回空结果，后续实现
        return ApiResponse.success(PageResponse.<Script>builder()
                .records(List.of())
                .total(0L)
                .page(page)
                .size(size)
                .pages(0)
                .build());
    }

    @GetMapping("/scripts/pending-review")
    public ApiResponse<PageResponse<Script>> getPendingReviewScripts(
            @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size) {
        PageResponse<Script> result = scriptStatusService.getPendingReviewScripts(page, size);
        return ApiResponse.success(result);
    }

    @PostMapping("/scripts/{scriptId}/review")
    public ApiResponse<Void> reviewScript(
            @PathVariable Long scriptId,
            @RequestParam boolean approved,
            @RequestParam(required = false) String comment) {
        Long reviewerId = UserHolder.getUser().getId();
        boolean success = scriptStatusService.reviewScript(scriptId, reviewerId, approved, comment);
        
        if (success) {
            return ApiResponse.success();
        } else {
            return ApiResponse.fail("审核失败");
        }
    }

    @PostMapping("/scripts/batch-review")
    public ApiResponse<Map<String, Object>> batchReviewScripts(
            @RequestBody @NotEmpty List<Long> scriptIds,
            @RequestParam boolean approved,
            @RequestParam(required = false) String comment) {
        Long reviewerId = UserHolder.getUser().getId();
        int successCount = scriptStatusService.batchReviewScripts(scriptIds, reviewerId, approved, comment);
        
        Map<String, Object> result = Map.of(
            "total", scriptIds.size(),
            "success", successCount,
            "failed", scriptIds.size() - successCount
        );
        
        return ApiResponse.success(result);
    }

    @PostMapping("/scripts/{scriptId}/toggle-publish")
    public ApiResponse<Void> togglePublishStatus(
            @PathVariable Long scriptId,
            @RequestParam boolean publish,
            @RequestParam(required = false) String reason) {
        Long operatorId = UserHolder.getUser().getId();
        boolean success = scriptStatusService.togglePublishStatus(scriptId, operatorId, publish, reason);
        
        if (success) {
            return ApiResponse.success();
        } else {
            return ApiResponse.fail("状态变更失败");
        }
    }

    @PostMapping("/scripts/batch-toggle-publish")
    public ApiResponse<Map<String, Object>> batchTogglePublishStatus(
            @RequestBody @NotEmpty List<Long> scriptIds,
            @RequestParam boolean publish,
            @RequestParam(required = false) String reason) {
        Long operatorId = UserHolder.getUser().getId();
        int successCount = scriptStatusService.batchTogglePublishStatus(scriptIds, operatorId, publish, reason);
        
        Map<String, Object> result = Map.of(
            "total", scriptIds.size(),
            "success", successCount,
            "failed", scriptIds.size() - successCount
        );
        
        return ApiResponse.success(result);
    }

    // ==================== 统计数据 ====================

    @GetMapping("/dashboard/stats")
    public ApiResponse<Map<String, Object>> getDashboardStats() {
        ScriptStatusStatsDTO statusStats = scriptStatusService.getStatusStats();
        
        Map<String, Object> dashboardStats = Map.of(
            "scriptStats", statusStats,
            "todayStats", Map.of(
                "newScripts", statusStats.getTodayNewScripts(),
                "reviewedScripts", statusStats.getTodayReviewedCount(),
                "pendingReview", statusStats.getPendingReviewCount()
            ),
            "systemStats", Map.of(
                "totalScripts", statusStats.getTotalScripts(),
                "approvalRate", statusStats.getApprovalRate(),
                "avgReviewHours", statusStats.getAvgReviewHours()
            )
        );
        
        return ApiResponse.success(dashboardStats);
    }

    @GetMapping("/stats/script-status")
    public ApiResponse<ScriptStatusStatsDTO> getScriptStatusStats() {
        ScriptStatusStatsDTO result = scriptStatusService.getStatusStats();
        return ApiResponse.success(result);
    }

    @GetMapping("/stats/review-efficiency")
    public ApiResponse<Map<String, Object>> getReviewEfficiencyStats(
            @RequestParam(defaultValue = "30") @Min(1) @Max(365) Integer days) {
        Map<String, Object> result = scriptStatusService.getReviewEfficiencyStats(days);
        return ApiResponse.success(result);
    }

    @GetMapping("/stats/status-changes")
    public ApiResponse<Map<String, Object>> getStatusChangeStats(
            @RequestParam String startDate,
            @RequestParam String endDate) {
        Map<String, Object> stats = scriptStatusService.getStatusChangeStats(startDate, endDate);
        return ApiResponse.success(stats);
    }

    // ==================== 系统管理 ====================

    @PostMapping("/scripts/{scriptId}/auto-review")
    public ApiResponse<Boolean> autoReviewScript(@PathVariable Long scriptId) {
        boolean success = scriptStatusService.autoReviewScript(scriptId);
        return ApiResponse.success(success);
    }

    @GetMapping("/scripts/{scriptId}/validate-completeness")
    public ApiResponse<Map<String, Object>> validateScriptCompleteness(@PathVariable Long scriptId) {
        Map<String, Object> validation = scriptStatusService.validateScriptCompleteness(scriptId);
        return ApiResponse.success(validation);
    }

    @PostMapping("/system/rebuild-search-index")
    public ApiResponse<Map<String, Object>> rebuildSearchIndex() {
        // TODO: 实现重建搜索索引功能
        log.info("管理员请求重建搜索索引");
        
        Map<String, Object> result = Map.of(
            "status", "started",
            "message", "搜索索引重建已开始，请稍后查看进度"
        );
        
        return ApiResponse.success(result);
    }

    @GetMapping("/system/health")
    public ApiResponse<Map<String, Object>> getSystemHealth() {
        // TODO: 实现系统健康检查
        Map<String, Object> health = Map.of(
            "database", "healthy",
            "redis", "healthy",
            "elasticsearch", "healthy",
            "timestamp", System.currentTimeMillis()
        );
        
        return ApiResponse.success(health);
    }

    // ==================== 用户管理 ====================

    @GetMapping("/users")
    public ApiResponse<Map<String, Object>> getUsers(
            @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size,
            @RequestParam(required = false) String keyword) {
        
        // TODO: 实现用户管理功能
        log.info("管理员查询用户列表: page={}, size={}, keyword={}", page, size, keyword);
        
        Map<String, Object> result = Map.of(
            "users", List.of(),
            "total", 0,
            "page", page,
            "size", size
        );
        
        return ApiResponse.success(result);
    }

    @PostMapping("/users/{userId}/toggle-status")
    public ApiResponse<Void> toggleUserStatus(
            @PathVariable Long userId,
            @RequestParam boolean enabled,
            @RequestParam(required = false) String reason) {
        
        // TODO: 实现用户状态切换功能
        log.info("管理员切换用户状态: userId={}, enabled={}, reason={}", userId, enabled, reason);
        
        return ApiResponse.success();
    }

    // ==================== 内容管理 ====================

    @GetMapping("/content/reports")
    public ApiResponse<Map<String, Object>> getContentReports(
            @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size) {
        
        // TODO: 实现内容举报管理
        log.info("管理员查询内容举报: page={}, size={}", page, size);
        
        Map<String, Object> result = Map.of(
            "reports", List.of(),
            "total", 0,
            "page", page,
            "size", size
        );
        
        return ApiResponse.success(result);
    }

    @PostMapping("/content/reports/{reportId}/handle")
    public ApiResponse<Void> handleContentReport(
            @PathVariable Long reportId,
            @RequestParam String action,
            @RequestParam(required = false) String comment) {
        
        // TODO: 实现举报处理功能
        log.info("管理员处理举报: reportId={}, action={}, comment={}", reportId, action, comment);
        
        return ApiResponse.success();
    }
}
