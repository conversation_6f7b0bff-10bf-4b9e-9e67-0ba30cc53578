package com.scriptmurder.controller;

import com.scriptmurder.dto.ApiResponse;
import com.scriptmurder.dto.CategoryStatsDTO;
import com.scriptmurder.dto.PageResponse;
import com.scriptmurder.dto.ScriptDTO;
import com.scriptmurder.service.IAdvancedSearchService;
import com.scriptmurder.service.ScriptSearchParams;
import com.scriptmurder.utils.UserHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;
import java.util.Map;

/**
 * 高级搜索控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/search")
@Api(tags = "高级搜索管理")
@Slf4j
@Validated
public class AdvancedSearchController {

    @Autowired
    private IAdvancedSearchService advancedSearchService;

    @GetMapping("/advanced")
    @ApiOperation("高级搜索剧本")
    public ApiResponse<PageResponse<ScriptDTO>> advancedSearch(@Valid ScriptSearchParams searchParams) {
        log.info("高级搜索请求: {}", searchParams);
        PageResponse<ScriptDTO> result = advancedSearchService.advancedSearch(searchParams);
        return ApiResponse.success(result);
    }

    @GetMapping("/categories/stats")
    @ApiOperation("获取分类统计信息")
    public ApiResponse<List<CategoryStatsDTO>> getCategoryStats() {
        List<CategoryStatsDTO> result = advancedSearchService.getCategoryStats();
        return ApiResponse.success(result);
    }

    @GetMapping("/keywords/hot")
    @ApiOperation("获取热门搜索关键词")
    public ApiResponse<List<String>> getHotSearchKeywords(
            @ApiParam("限制数量") @RequestParam(defaultValue = "10") @Min(1) @Max(50) Integer limit) {
        List<String> result = advancedSearchService.getHotSearchKeywords(limit);
        return ApiResponse.success(result);
    }

    @GetMapping("/keywords/suggestions")
    @ApiOperation("获取搜索建议")
    public ApiResponse<List<String>> getSuggestedKeywords(
            @ApiParam("关键词前缀") @RequestParam String keyword,
            @ApiParam("限制数量") @RequestParam(defaultValue = "5") @Min(1) @Max(20) Integer limit) {
        List<String> result = advancedSearchService.getSuggestedKeywords(keyword, limit);
        return ApiResponse.success(result);
    }

    @PostMapping("/keywords/record")
    @ApiOperation("记录搜索关键词")
    public ApiResponse<Void> recordSearchKeyword(@RequestParam String keyword) {
        advancedSearchService.recordSearchKeyword(keyword);
        return ApiResponse.success();
    }

    @GetMapping("/aggregations")
    @ApiOperation("获取搜索聚合统计")
    public ApiResponse<Map<String, Object>> getSearchAggregations(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String category) {
        // TODO: 实现搜索聚合统计
        return ApiResponse.success(Map.of(
            "totalScripts", 1000,
            "categories", advancedSearchService.getCategoryStats(),
            "hotKeywords", advancedSearchService.getHotSearchKeywords(10)
        ));
    }

    @GetMapping("/history")
    @ApiOperation("获取用户搜索历史")
    public ApiResponse<List<String>> getUserSearchHistory(
            @ApiParam("限制数量") @RequestParam(defaultValue = "10") @Min(1) @Max(50) Integer limit) {
        // TODO: 实现用户搜索历史功能
        Long userId = UserHolder.getUser() != null ? UserHolder.getUser().getId() : null;
        if (userId == null) {
            return ApiResponse.unauthorized("用户未登录");
        }
        
        // 暂时返回热门搜索作为占位
        List<String> result = advancedSearchService.getHotSearchKeywords(limit);
        return ApiResponse.success(result);
    }

    @DeleteMapping("/history")
    @ApiOperation("清除用户搜索历史")
    public ApiResponse<Void> clearUserSearchHistory() {
        // TODO: 实现清除搜索历史功能
        Long userId = UserHolder.getUser() != null ? UserHolder.getUser().getId() : null;
        if (userId == null) {
            return ApiResponse.unauthorized("用户未登录");
        }
        
        log.info("清除用户搜索历史: userId={}", userId);
        return ApiResponse.success();
    }

    @GetMapping("/trends")
    @ApiOperation("获取搜索趋势分析")
    public ApiResponse<Map<String, Object>> getSearchTrends(
            @ApiParam("时间范围(天)") @RequestParam(defaultValue = "7") @Min(1) @Max(30) Integer days) {
        // TODO: 实现搜索趋势分析
        return ApiResponse.success(Map.of(
            "period", days + "天",
            "totalSearches", 5000,
            "uniqueKeywords", 200,
            "topKeywords", advancedSearchService.getHotSearchKeywords(10),
            "searchGrowth", "+15%"
        ));
    }

    @GetMapping("/performance")
    @ApiOperation("获取搜索性能统计")
    public ApiResponse<Map<String, Object>> getSearchPerformance() {
        // TODO: 实现搜索性能统计
        return ApiResponse.success(Map.of(
            "avgResponseTime", "85ms",
            "successRate", "99.2%",
            "cacheHitRate", "78%",
            "totalQueries", 10000,
            "errorRate", "0.8%"
        ));
    }
}
