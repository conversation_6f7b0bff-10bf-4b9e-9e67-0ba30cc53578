package com.scriptmurder.controller;

import com.scriptmurder.dto.ApiResponse;
import com.scriptmurder.dto.CategoryStatsDTO;
import com.scriptmurder.dto.PageResponse;
import com.scriptmurder.dto.ScriptDTO;
import com.scriptmurder.service.IAdvancedSearchService;
import com.scriptmurder.service.ISearchHistoryService;
import com.scriptmurder.service.ScriptSearchParams;
import com.scriptmurder.utils.UserHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;
import java.util.Map;

/**
 * 高级搜索控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/search")
@Slf4j
@Validated
public class AdvancedSearchController {

    @Autowired
    private IAdvancedSearchService advancedSearchService;

    @Autowired
    private ISearchHistoryService searchHistoryService;

    @GetMapping("/advanced")
    public ApiResponse<PageResponse<ScriptDTO>> advancedSearch(@Valid ScriptSearchParams searchParams) {
        log.info("高级搜索请求: {}", searchParams);
        PageResponse<ScriptDTO> result = advancedSearchService.advancedSearch(searchParams);
        return ApiResponse.success(result);
    }

    @GetMapping("/categories/stats")
    public ApiResponse<List<CategoryStatsDTO>> getCategoryStats() {
        List<CategoryStatsDTO> result = advancedSearchService.getCategoryStats();
        return ApiResponse.success(result);
    }

    @GetMapping("/keywords/hot")
    public ApiResponse<List<String>> getHotSearchKeywords(
            @RequestParam(defaultValue = "10") @Min(1) @Max(50) Integer limit) {
        List<String> result = advancedSearchService.getHotSearchKeywords(limit);
        return ApiResponse.success(result);
    }

    @GetMapping("/keywords/suggestions")
    public ApiResponse<List<String>> getSuggestedKeywords(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "5") @Min(1) @Max(20) Integer limit) {
        List<String> result = advancedSearchService.getSuggestedKeywords(keyword, limit);
        return ApiResponse.success(result);
    }

    @PostMapping("/keywords/record")
    public ApiResponse<Void> recordSearchKeyword(@RequestParam String keyword) {
        advancedSearchService.recordSearchKeyword(keyword);
        return ApiResponse.success();
    }

    @GetMapping("/aggregations")
    public ApiResponse<Map<String, Object>> getSearchAggregations(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String category) {
        return ApiResponse.success(Map.of(
            "totalScripts", 1000,
            "categories", advancedSearchService.getCategoryStats(),
            "hotKeywords", advancedSearchService.getHotSearchKeywords(10)
        ));
    }

    @GetMapping("/history")
    public ApiResponse<List<String>> getUserSearchHistory(
            @RequestParam(defaultValue = "10") @Min(1) @Max(50) Integer limit) {
        Long userId = UserHolder.getUser() != null ? UserHolder.getUser().getId() : null;
        if (userId == null) {
            return ApiResponse.unauthorized("用户未登录");
        }

        List<String> result = searchHistoryService.getUserSearchHistory(userId, limit);
        return ApiResponse.success(result);
    }

    @DeleteMapping("/history")
    public ApiResponse<Void> clearUserSearchHistory() {
        Long userId = UserHolder.getUser() != null ? UserHolder.getUser().getId() : null;
        if (userId == null) {
            return ApiResponse.unauthorized("用户未登录");
        }

        boolean success = searchHistoryService.clearUserSearchHistory(userId);
        if (success) {
            return ApiResponse.success();
        } else {
            return ApiResponse.fail("清除搜索历史失败");
        }
    }

    @GetMapping("/trends")
    public ApiResponse<Map<String, Object>> getSearchTrends(
            @RequestParam(defaultValue = "7") @Min(1) @Max(30) Integer days) {
        Map<String, Object> result = searchHistoryService.getSearchTrends(days);
        return ApiResponse.success(result);
    }

    @GetMapping("/performance")
    public ApiResponse<Map<String, Object>> getSearchPerformance() {
        Map<String, Object> result = searchHistoryService.getSearchPerformance();
        return ApiResponse.success(result);
    }

    @GetMapping("/related")
    public ApiResponse<List<String>> getRelatedSearches(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "5") @Min(1) @Max(20) Integer limit) {
        List<String> result = searchHistoryService.getRelatedSearches(keyword, limit);
        return ApiResponse.success(result);
    }

    @GetMapping("/autocomplete")
    public ApiResponse<List<String>> getAutoCompleteSuggestions(
            @RequestParam String prefix,
            @RequestParam(defaultValue = "10") @Min(1) @Max(20) Integer limit) {
        List<String> result = searchHistoryService.getAutoCompleteSuggestions(prefix, limit);
        return ApiResponse.success(result);
    }

    @PostMapping("/click")
    public ApiResponse<Void> recordSearchClick(
            @RequestParam String keyword,
            @RequestParam Long scriptId,
            @RequestParam Integer position) {
        Long userId = UserHolder.getUser() != null ? UserHolder.getUser().getId() : null;
        if (userId == null) {
            return ApiResponse.unauthorized("用户未登录");
        }

        searchHistoryService.recordSearchClick(userId, keyword, scriptId, position);
        return ApiResponse.success();
    }

    @GetMapping("/no-result")
    public ApiResponse<List<String>> getNoResultKeywords(
            @RequestParam(defaultValue = "10") @Min(1) @Max(50) Integer limit) {
        List<String> result = searchHistoryService.getNoResultKeywords(limit);
        return ApiResponse.success(result);
    }
}
