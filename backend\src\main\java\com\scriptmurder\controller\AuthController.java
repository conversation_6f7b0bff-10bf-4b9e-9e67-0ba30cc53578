package com.scriptmurder.controller;


import cn.hutool.core.bean.BeanUtil;
import com.scriptmurder.dto.ApiResponse;
import com.scriptmurder.dto.LoginFormDTO;
import com.scriptmurder.dto.PasswordChangeDTO;
import com.scriptmurder.dto.RegisterFormDTO;
import com.scriptmurder.dto.Result;
import com.scriptmurder.dto.UserDTO;
import com.scriptmurder.dto.UserUpdateDTO;
import com.scriptmurder.entity.User;
import com.scriptmurder.entity.UserInfo;
import com.scriptmurder.service.IUserInfoService;
import com.scriptmurder.service.IUserService;
import com.scriptmurder.service.ILoginHistoryService;
import com.scriptmurder.utils.UserHolder;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;


/**
 * <p>
 * 用户认证控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
@Slf4j
@RestController
@RequestMapping("/api/user")
public class AuthController {

    @Resource
    private IUserService userService;

    @Resource
    private IUserInfoService userInfoService;

    @Resource
    private ILoginHistoryService loginHistoryService;

    /**
     * 发送手机验证码
     */
    @PostMapping("code")
    public ApiResponse<String> sendCode(@RequestParam("phone") String phone, HttpSession session) {
        // 发送短信验证码并保存验证码
        Result result = userService.sendCode(phone, session);
        if (result.getSuccess()) {
            return ApiResponse.success("验证码发送成功");
        } else {
            return ApiResponse.error(result.getErrorMsg());
        }
    }

    /**
     * 发送邮箱验证码
     */
    @PostMapping("email/code")
    public ApiResponse<String> sendEmailCode(@RequestParam("email") String email) {
        Result result = userService.sendEmailCode(email);
        if (result.getSuccess()) {
            return ApiResponse.success((String) result.getData());
        } else {
            return ApiResponse.error(result.getErrorMsg());
        }
    }

    /**
     * 用户注册
     */

    @PostMapping("/register")
    public ApiResponse<String> register( @RequestBody RegisterFormDTO registerForm) {
        Result result = userService.register(registerForm);
        if (result.getSuccess()) {
            return ApiResponse.success((String) result.getData());
        } else {
            return ApiResponse.error(result.getErrorMsg());
        }
    }

    /**
     * 登录功能
     *
     * @param loginForm 登录参数，支持邮箱密码登录或手机验证码登录
     */
    @PostMapping("/login")
    public ApiResponse<String> login(@RequestBody LoginFormDTO loginForm,
                                   HttpSession session, HttpServletRequest request) {
        // 实现登录功能
        Result result = userService.login(loginForm, session);
        if (result.getSuccess()) {
            // 登录成功，记录登录历史
            try {
                UserDTO currentUser = UserHolder.getUser();
                if (currentUser != null) {
                    loginHistoryService.recordLogin(currentUser.getId(), request);
                }
            } catch (Exception e) {
                log.warn("记录登录历史失败", e);
            }
            return ApiResponse.success("登录成功", (String) result.getData());
        } else {
            // 登录失败，记录失败历史
            try {
                // 尝试根据邮箱或手机号获取用户ID（如果需要的话）
                loginHistoryService.recordLoginFailure(null, request, result.getErrorMsg());
            } catch (Exception e) {
                log.warn("记录登录失败历史失败", e);
            }
            return ApiResponse.error(result.getErrorMsg());
        }
    }

    /**
     * 登出功能
     *
     * @param request HTTP请求对象，用于获取token
     * @return 无
     */
    @PostMapping("/logout")
    public ApiResponse<String> logout(HttpServletRequest request) {
        // 记录登出历史
        try {
            UserDTO currentUser = UserHolder.getUser();
            if (currentUser != null) {
                loginHistoryService.recordLogout(currentUser.getId());
            }
        } catch (Exception e) {
            log.warn("记录登出历史失败", e);
        }

        // 从请求头中获取token
        String authorization = request.getHeader("authorization");
        String token = null;
        if (authorization != null && authorization.startsWith("Bearer ")) {
            token = authorization.substring(7);
        }

        Result result = userService.logout(token);
        if (result.getSuccess()) {
            return ApiResponse.success("登出成功");
        } else {
            return ApiResponse.error(result.getErrorMsg());
        }
    }

    @GetMapping("/me")
    public ApiResponse<UserDTO> me() {
        // 获取当前登录的用户并返回
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser == null) {
            return ApiResponse.unauthorized("用户未登录");
        }

        // 获取完整的用户信息
        User user = userService.getById(currentUser.getId());
        if (user == null) {
            return ApiResponse.error("用户信息不存在");
        }

        // 转换为完整的UserDTO
        UserDTO userDTO = BeanUtil.copyProperties(user, UserDTO.class);
        return ApiResponse.success("获取用户信息成功", userDTO);
    }

    @GetMapping("/info/{id}")
    public Result info(@PathVariable("id") Long userId) {
        // 查询详情
        UserInfo info = userInfoService.getById(userId);
        if (info == null) {
            // 没有详情，应该是第一次查看详情
            return Result.ok();
        }
        info.setCreateTime(null);
        info.setUpdateTime(null);
        // 返回
        return Result.ok(info);
    }

    @GetMapping("/{id}")
    public Result queryUserById(@PathVariable("id") Long userId) {
        // 查询详情
        User user = userService.getById(userId);
        if (user == null) {
            return Result.ok();
        }
        UserDTO userDTO = BeanUtil.copyProperties(user, UserDTO.class);
        // 返回
        return Result.ok(userDTO);
    }

    @PostMapping("/sign")
    public Result sign() {
        return userService.sign();
    }

    @GetMapping("/sign/count")
    public Result singCount() {
        return userService.singCount();
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/profile")
    public ApiResponse<Boolean> updateProfile(@RequestBody @Valid UserUpdateDTO updateDTO) {
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser == null) {
            return ApiResponse.unauthorized("用户未登录");
        }

        boolean success = userService.updateUserProfile(currentUser.getId(), updateDTO);
        return success ?
            ApiResponse.success("更新成功", true) :
            ApiResponse.error("更新失败");
    }

    /**
     * 修改密码
     */
    @PutMapping("/password")
    public ApiResponse<Boolean> changePassword(@RequestBody @Valid PasswordChangeDTO passwordDTO) {
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser == null) {
            return ApiResponse.unauthorized("用户未登录");
        }

        boolean success = userService.changePassword(
            currentUser.getId(),
            passwordDTO.getOldPassword(),
            passwordDTO.getNewPassword()
        );
        return success ?
            ApiResponse.success("密码修改成功", true) :
            ApiResponse.error("原密码错误");
    }
}
