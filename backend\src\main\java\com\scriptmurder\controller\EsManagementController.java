package com.scriptmurder.controller;

import com.scriptmurder.dto.ApiResponse;
import com.scriptmurder.service.impl.ScriptBulkImportService;
import com.scriptmurder.service.impl.ScriptSyncStrategyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * ES管理控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/es")
@Slf4j
public class EsManagementController {

    @Autowired
    private ScriptBulkImportService bulkImportService;

    @Autowired
    private ScriptSyncStrategyService syncStrategyService;

    @PostMapping("/rebuild-index")
    public ApiResponse<String> rebuildIndex() {
        try {
            bulkImportService.rebuildAllIndex();
            return ApiResponse.ok("索引重建成功");
        } catch (Exception e) {
            log.error("重建索引失败", e);
            return ApiResponse.fail("重建索引失败: " + e.getMessage());
        }
    }

    @PostMapping("/incremental-sync")
    public ApiResponse<String> incrementalSync() {
        try {
            bulkImportService.incrementalSync();
            return ApiResponse.ok("增量同步成功");
        } catch (Exception e) {
            log.error("增量同步失败", e);
            return ApiResponse.fail("增量同步失败: " + e.getMessage());
        }
    }

    @PostMapping("/data-consistency-check")
    public ApiResponse<String> dataConsistencyCheck() {
        try {
            syncStrategyService.dataConsistencyCheck();
            return ApiResponse.ok("数据一致性检查完成");
        } catch (Exception e) {
            log.error("数据一致性检查失败", e);
            return ApiResponse.fail("检查失败: " + e.getMessage());
        }
    }

    @PostMapping("/clear-sync-queues")
    public ApiResponse<String> clearSyncQueues() {
        try {
            syncStrategyService.clearSyncQueues();
            return ApiResponse.ok("同步队列已清空");
        } catch (Exception e) {
            log.error("清空同步队列失败", e);
            return ApiResponse.fail("清空失败: " + e.getMessage());
        }
    }

    @GetMapping("/sync-status")
    public ApiResponse<Map<String, Object>> getSyncStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            
            // 这里可以添加更多状态信息
            status.put("lastSyncTime", "2024-01-01 12:00:00");
            status.put("pendingCount", 0);
            status.put("failedCount", 0);
            status.put("indexHealth", "green");
            
            return ApiResponse.ok(status);
        } catch (Exception e) {
            log.error("获取同步状态失败", e);
            return ApiResponse.fail("获取状态失败: " + e.getMessage());
        }
    }

    @GetMapping("/index-stats")
    public ApiResponse<Map<String, Object>> getIndexStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 这里可以调用ES API获取实际统计信息
            stats.put("documentCount", 0);
            stats.put("indexSize", "0MB");
            stats.put("shardCount", 1);
            stats.put("replicaCount", 0);
            
            return ApiResponse.ok(stats);
        } catch (Exception e) {
            log.error("获取索引统计失败", e);
            return ApiResponse.fail("获取统计失败: " + e.getMessage());
        }
    }
}