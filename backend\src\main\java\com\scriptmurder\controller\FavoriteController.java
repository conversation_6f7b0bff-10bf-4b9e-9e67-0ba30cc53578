package com.scriptmurder.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.scriptmurder.dto.ApiResponse;
import com.scriptmurder.dto.FavoriteAddDTO;
import com.scriptmurder.dto.FavoriteDTO;
import com.scriptmurder.dto.UserDTO;
import com.scriptmurder.service.IUserFavoriteService;
import com.scriptmurder.utils.UserHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 用户收藏控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-31
 */
@Slf4j
@RestController
@RequestMapping("/api/user/favorites")
public class FavoriteController {

    @Resource
    private IUserFavoriteService favoriteService;

    /**
     * 获取我的收藏
     */
    @GetMapping
    public ApiResponse<Page<FavoriteDTO>> getMyFavorites(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String type) {

        UserDTO currentUser = UserHolder.getUser();
        if (currentUser == null) {
            return ApiResponse.unauthorized("用户未登录");
        }
        
        Page<FavoriteDTO> result = favoriteService.getUserFavorites(
            currentUser.getId(), page, size, type
        );
        return ApiResponse.success("获取收藏列表成功", result);
    }

    /**
     * 添加收藏
     */
    @PostMapping
    public ApiResponse<Boolean> addFavorite(@RequestBody @Valid FavoriteAddDTO addDTO) {
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser == null) {
            return ApiResponse.unauthorized("用户未登录");
        }
        
        boolean success = favoriteService.addFavorite(
            currentUser.getId(),
            addDTO.getTargetId(),
            addDTO.getTargetType()
        );
        return success ?
            ApiResponse.success("收藏成功", true) :
            ApiResponse.error("收藏失败，可能已经收藏过了");
    }

    /**
     * 取消收藏
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Boolean> removeFavorite(@PathVariable Long id) {
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser == null) {
            return ApiResponse.unauthorized("用户未登录");
        }
        
        boolean success = favoriteService.removeFavorite(currentUser.getId(), id);
        return success ?
            ApiResponse.success("取消收藏成功", true) :
            ApiResponse.error("取消收藏失败");
    }

    /**
     * 检查是否已收藏
     */
    @GetMapping("/check")
    public ApiResponse<Boolean> checkFavorite(
            @RequestParam Long targetId,
            @RequestParam String targetType) {
        
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser == null) {
            return ApiResponse.unauthorized("用户未登录");
        }
        
        boolean isFavorited = favoriteService.isFavorited(
            currentUser.getId(), targetId, targetType
        );
        return ApiResponse.success("检查完成", isFavorited);
    }
}
