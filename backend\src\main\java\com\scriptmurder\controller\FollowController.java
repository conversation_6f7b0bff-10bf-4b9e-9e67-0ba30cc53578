package com.scriptmurder.controller;

import com.scriptmurder.dto.Result;
import com.scriptmurder.service.IFollowService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 关注功能控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-31
 */
@RestController
@RequestMapping("/api/user/follow")
public class FollowController {

    @Resource
    private IFollowService followService;

    @PutMapping("/{id}/{isFollow}")
    public Result follow(
            @PathVariable("id") Long id,
            @PathVariable("isFollow") Boolean isFollow) {
        return followService.follow(id, isFollow);
    }

    @GetMapping("/check/{id}")
    public Result isFollow(
            @PathVariable("id") Long followUserId) {
        return followService.isFollow(followUserId);
    }

    @GetMapping("/common/{id}")
    public Result commonFollow(
            @PathVariable Long id) {
        return followService.commonFollow(id);
    }
}
