package com.scriptmurder.controller;

import com.scriptmurder.dto.*;
import com.scriptmurder.service.IScriptSearchService;
import com.scriptmurder.service.IScriptService;
import com.scriptmurder.service.IUserFavoriteService;
import com.scriptmurder.utils.UserHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 剧本管理控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/scripts")
@Slf4j
@Validated
public class ScriptController {

    @Autowired
    private IScriptService scriptService;

    @Autowired
    private IScriptSearchService scriptSearchService;

    @Autowired
    private IUserFavoriteService favoriteService;

    @GetMapping("/list")
    public ApiResponse<PageResponse<ScriptDTO>> getScriptList(@Valid ScriptSearchDTO searchDTO) {
        PageResponse<ScriptDTO> result = scriptService.getScriptList(searchDTO);
        return ApiResponse.success(result);
    }

    @GetMapping("/search")
    public ApiResponse<PageResponse<ScriptDTO>> searchScripts(@Valid ScriptSearchDTO searchDTO) {
        PageResponse<ScriptDTO> result = scriptService.searchScripts(searchDTO);
        return ApiResponse.success(result);
    }

    @GetMapping("/{id}")
    public ApiResponse<ScriptDetailDTO> getScriptDetail(@PathVariable Long id) {
        ScriptDetailDTO result = scriptService.getScriptDetail(id);

        // 如果用户已登录，检查收藏状态
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser != null) {
            boolean isFavorited = favoriteService.isFavorited(currentUser.getId(), id, "script");
            result.setIsFavorite(isFavorited);
        }

        return ApiResponse.success(result);
    }

    @GetMapping("/popular")
    public ApiResponse<List<ScriptDTO>> getPopularScripts(
            @RequestParam(defaultValue = "10") Integer limit) {
        List<ScriptDTO> result = scriptService.getPopularScripts(limit);
        return ApiResponse.success(result);
    }

    @GetMapping("/recommendations")
    public ApiResponse<List<ScriptDTO>> getRecommendedScripts(
            @RequestParam(defaultValue = "10") Integer limit) {
        Long userId = UserHolder.getUser().getId();
        List<ScriptDTO> result = scriptService.getRecommendedScripts(userId, limit);
        return ApiResponse.success(result);
    }

    @GetMapping("/categories")
    public ApiResponse<List<CategoryStatsDTO>> getCategoryStats() {
        List<CategoryStatsDTO> result = scriptService.getCategoryStats();
        return ApiResponse.success(result);
    }

    @GetMapping("/search/suggestions")
    public ApiResponse<List<String>> getSearchSuggestions(@RequestParam String keyword) {
        List<String> result = scriptSearchService.getSearchSuggestions(keyword);
        return ApiResponse.success(result);
    }

    @GetMapping("/search/hot")
    public ApiResponse<List<String>> getHotSearchKeywords() {
        List<String> result = scriptSearchService.getHotSearchKeywords();
        return ApiResponse.success(result);
    }

    @PostMapping("/search/index/rebuild")
    public ApiResponse<Void> rebuildSearchIndex() {
        scriptSearchService.rebuildIndex();
        return ApiResponse.success();
    }

    @PostMapping("/favorites/toggle")
    public ApiResponse<Boolean> toggleFavorite(@RequestParam Long scriptId) {
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser == null) {
            return ApiResponse.unauthorized("用户未登录");
        }

        boolean isFavorited = favoriteService.isFavorited(currentUser.getId(), scriptId, "script");

        if (isFavorited) {
            // 取消收藏 - 需要先找到收藏记录的ID
            // 这里简化处理，实际应该通过targetId和targetType查找
            boolean success = favoriteService.removeFavoriteByTarget(currentUser.getId(), scriptId, "script");
            return success ?
                ApiResponse.success("取消收藏成功", false) :
                ApiResponse.error("取消收藏失败");
        } else {
            // 添加收藏
            boolean success = favoriteService.addFavorite(currentUser.getId(), scriptId, "script");
            return success ?
                ApiResponse.success("收藏成功", true) :
                ApiResponse.error("收藏失败");
        }
    }
}