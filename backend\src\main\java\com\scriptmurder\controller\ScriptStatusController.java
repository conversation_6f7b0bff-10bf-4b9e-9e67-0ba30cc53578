package com.scriptmurder.controller;

import com.scriptmurder.dto.ApiResponse;
import com.scriptmurder.dto.PageResponse;
import com.scriptmurder.dto.ScriptStatusHistoryDTO;
import com.scriptmurder.dto.ScriptStatusStatsDTO;
import com.scriptmurder.entity.Script;
import com.scriptmurder.service.IScriptStatusService;
import com.scriptmurder.utils.UserHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 剧本状态管理控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/scripts/status")
@Slf4j
@Validated
public class ScriptStatusController {

    @Autowired
    private IScriptStatusService scriptStatusService;

    @PostMapping("/{scriptId}/submit-review")
    public ApiResponse<Void> submitForReview(@PathVariable Long scriptId) {
        Long userId = UserHolder.getUser().getId();
        boolean success = scriptStatusService.submitForReview(scriptId, userId);
        
        if (success) {
            return ApiResponse.success();
        } else {
            return ApiResponse.fail("提交审核失败");
        }
    }

    @PostMapping("/{scriptId}/review")
    public ApiResponse<Void> reviewScript(
            @PathVariable Long scriptId,
            @RequestParam boolean approved,
            @RequestParam(required = false) String comment) {
        Long reviewerId = UserHolder.getUser().getId();
        boolean success = scriptStatusService.reviewScript(scriptId, reviewerId, approved, comment);
        
        if (success) {
            return ApiResponse.success();
        } else {
            return ApiResponse.fail("审核失败");
        }
    }

    @PostMapping("/{scriptId}/toggle-publish")
    public ApiResponse<Void> togglePublishStatus(
            @PathVariable Long scriptId,
            @RequestParam boolean publish,
            @RequestParam(required = false) String reason) {
        Long operatorId = UserHolder.getUser().getId();
        boolean success = scriptStatusService.togglePublishStatus(scriptId, operatorId, publish, reason);
        
        if (success) {
            return ApiResponse.success();
        } else {
            return ApiResponse.fail("状态变更失败");
        }
    }

    @PostMapping("/batch-review")
    public ApiResponse<Map<String, Object>> batchReviewScripts(
            @RequestBody @NotEmpty List<Long> scriptIds,
            @RequestParam boolean approved,
            @RequestParam(required = false) String comment) {
        Long reviewerId = UserHolder.getUser().getId();
        int successCount = scriptStatusService.batchReviewScripts(scriptIds, reviewerId, approved, comment);
        
        Map<String, Object> result = Map.of(
            "total", scriptIds.size(),
            "success", successCount,
            "failed", scriptIds.size() - successCount
        );
        
        return ApiResponse.success(result);
    }

    @PostMapping("/batch-toggle-publish")
    public ApiResponse<Map<String, Object>> batchTogglePublishStatus(
            @RequestBody @NotEmpty List<Long> scriptIds,
            @RequestParam boolean publish,
            @RequestParam(required = false) String reason) {
        Long operatorId = UserHolder.getUser().getId();
        int successCount = scriptStatusService.batchTogglePublishStatus(scriptIds, operatorId, publish, reason);
        
        Map<String, Object> result = Map.of(
            "total", scriptIds.size(),
            "success", successCount,
            "failed", scriptIds.size() - successCount
        );
        
        return ApiResponse.success(result);
    }

    @GetMapping("/pending-review")
    public ApiResponse<PageResponse<Script>> getPendingReviewScripts(
            @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size) {
        PageResponse<Script> result = scriptStatusService.getPendingReviewScripts(page, size);
        return ApiResponse.success(result);
    }

    @GetMapping("/{scriptId}/history")
    public ApiResponse<PageResponse<ScriptStatusHistoryDTO>> getStatusHistory(
            @PathVariable Long scriptId,
            @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size) {
        PageResponse<ScriptStatusHistoryDTO> result = scriptStatusService.getStatusHistory(scriptId, page, size);
        return ApiResponse.success(result);
    }

    @GetMapping("/stats")
    public ApiResponse<ScriptStatusStatsDTO> getStatusStats() {
        ScriptStatusStatsDTO result = scriptStatusService.getStatusStats();
        return ApiResponse.success(result);
    }

    @GetMapping("/review-efficiency")
    public ApiResponse<Map<String, Object>> getReviewEfficiencyStats(
            @RequestParam(defaultValue = "30") @Min(1) @Max(365) Integer days) {
        Map<String, Object> result = scriptStatusService.getReviewEfficiencyStats(days);
        return ApiResponse.success(result);
    }

    @GetMapping("/{scriptId}/can-transition")
    public ApiResponse<Boolean> canTransitionToStatus(
            @PathVariable Long scriptId,
            @RequestParam @NotNull Integer targetStatus) {
        boolean canTransition = scriptStatusService.canTransitionToStatus(scriptId, targetStatus);
        return ApiResponse.success(canTransition);
    }

    @GetMapping("/{scriptId}/available-actions")
    public ApiResponse<List<String>> getAvailableActions(@PathVariable Long scriptId) {
        Long userId = UserHolder.getUser().getId();
        List<String> actions = scriptStatusService.getAvailableActions(scriptId, userId);
        return ApiResponse.success(actions);
    }

    @PostMapping("/{scriptId}/auto-review")
    public ApiResponse<Boolean> autoReviewScript(@PathVariable Long scriptId) {
        boolean success = scriptStatusService.autoReviewScript(scriptId);
        return ApiResponse.success(success);
    }

    @GetMapping("/user-distribution")
    public ApiResponse<Map<String, Integer>> getUserScriptStatusDistribution() {
        Long userId = UserHolder.getUser().getId();
        Map<String, Integer> distribution = scriptStatusService.getUserScriptStatusDistribution(userId);
        return ApiResponse.success(distribution);
    }

    @GetMapping("/{scriptId}/validate-completeness")
    public ApiResponse<Map<String, Object>> validateScriptCompleteness(@PathVariable Long scriptId) {
        Map<String, Object> validation = scriptStatusService.validateScriptCompleteness(scriptId);
        return ApiResponse.success(validation);
    }

    @GetMapping("/change-stats")
    public ApiResponse<Map<String, Object>> getStatusChangeStats(
            @RequestParam String startDate,
            @RequestParam String endDate) {
        Map<String, Object> stats = scriptStatusService.getStatusChangeStats(startDate, endDate);
        return ApiResponse.success(stats);
    }

    @PostMapping("/{scriptId}/send-notification")
    public ApiResponse<Void> sendStatusChangeNotification(
            @PathVariable Long scriptId,
            @RequestParam Integer fromStatus,
            @RequestParam Integer toStatus,
            @RequestParam(required = false) String comment) {
        Long operatorId = UserHolder.getUser().getId();
        scriptStatusService.sendStatusChangeNotification(scriptId, fromStatus, toStatus, operatorId, comment);
        return ApiResponse.success();
    }
}
