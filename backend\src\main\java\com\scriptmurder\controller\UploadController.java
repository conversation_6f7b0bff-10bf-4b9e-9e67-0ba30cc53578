package com.scriptmurder.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.scriptmurder.dto.ApiResponse;
import com.scriptmurder.dto.Result;
import com.scriptmurder.dto.UserDTO;
import com.scriptmurder.service.IUserService;
import com.scriptmurder.utils.BlogUploadService;
import com.scriptmurder.utils.SystemConstants;
import com.scriptmurder.utils.UserHolder;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Slf4j
@RestController
@RequestMapping("/api/upload")
public class UploadController {

    /**
     * 博客图片上传服务类
     * 使用@Autowired注解自动注入，如果BlogUploadService Bean不存在则为null
     */
    @Autowired(required = false)
    private BlogUploadService blogUploadService;

    @Resource
    private IUserService userService;

    /**
     * 博客图片上传接口
     * 支持两种上传方式：
     * 1. 如果配置了BlogUploadUtil，使用新的上传工具（推荐）
     * 2. 否则使用原有的本地上传方式（兼容性保证）
     *
     * @param image 上传的图片文件
     * @return 返回上传结果，包含图片访问路径
     */
    @PostMapping
    // 上传博客图片
    public Result uploadImage(@RequestParam("file") MultipartFile image) {
        try {
            // 优先使用新的博客上传服务
            if (blogUploadService != null) {
                log.info("使用BlogUploadService进行文件上传");
                String imageUrl = blogUploadService.uploadBlogImage(image);
                return Result.ok(imageUrl);
            } else {
                // 兼容原有的上传方式
                log.warn("BlogUploadService未配置，使用原有上传方式");
                return uploadImageLegacy(image);
            }
        } catch (Exception e) {
            log.error("博客图片上传失败: {}", e.getMessage(), e);
            return Result.fail("图片上传失败: " + e.getMessage());
        }
    }

    /**
     * 原有的图片上传方式（兼容性保证）
     *
     * @param image 上传的图片文件
     * @return 返回上传结果
     */
    private Result uploadImageLegacy(MultipartFile image) {
        try {
            // 获取原始文件名称
            String originalFilename = image.getOriginalFilename();
            // 生成新文件名
            String fileName = createNewFileName(originalFilename);
            // 保存文件
            image.transferTo(new File(SystemConstants.IMAGE_UPLOAD_DIR, fileName));
            // 返回结果
            log.debug("文件上传成功，{}", fileName);
            return Result.ok(fileName);
        } catch (IOException e) {
            throw new RuntimeException("文件上传失败", e);
        }
    }

    /**
     * 博客图片删除接口
     * 支持两种删除方式：
     * 1. 如果配置了BlogUploadUtil，使用新的删除工具（推荐）
     * 2. 否则使用原有的本地删除方式（兼容性保证）
     *
     * @param filename 要删除的文件名（相对路径）
     * @return 返回删除结果
     */
    @GetMapping("/blog/delete")
    public Result deleteBlogImg(@RequestParam("name") String filename) {
        try {
            // 优先使用新的博客上传服务进行删除
            if (blogUploadService != null) {
                log.info("使用BlogUploadService删除文件: {}", filename);
                boolean deleted = blogUploadService.deleteBlogImage(filename);
                if (deleted) {
                    return Result.ok("文件删除成功");
                } else {
                    return Result.fail("文件删除失败");
                }
            } else {
                // 兼容原有的删除方式
                log.warn("BlogUploadService未配置，使用原有删除方式");
                return deleteBlogImgLegacy(filename);
            }
        } catch (Exception e) {
            log.error("博客图片删除失败: {}", e.getMessage(), e);
            return Result.fail("图片删除失败: " + e.getMessage());
        }
    }

    /**
     * 原有的图片删除方式（兼容性保证）
     *
     * @param filename 要删除的文件名
     * @return 返回删除结果
     */
    private Result deleteBlogImgLegacy(String filename) {
        File file = new File(SystemConstants.IMAGE_UPLOAD_DIR, filename);
        if (file.isDirectory()) {
            return Result.fail("错误的文件名称");
        }
        FileUtil.del(file);
        return Result.ok();
    }

    private String createNewFileName(String originalFilename) {
        // 获取后缀
        String suffix = StrUtil.subAfter(originalFilename, ".", true);
        // 生成目录
        String name = UUID.randomUUID().toString();
        int hash = name.hashCode();
        int d1 = hash & 0xF;
        int d2 = (hash >> 4) & 0xF;
        // 判断目录是否存在
        File dir = new File(SystemConstants.IMAGE_UPLOAD_DIR, StrUtil.format("/blogs/{}/{}", d1, d2));
        if (!dir.exists()) {
            dir.mkdirs();
        }
        // 生成文件名
        return StrUtil.format("/blogs/{}/{}/{}.{}", d1, d2, name, suffix);
    }

    /**
     * 头像上传接口
     * 
     * @param file 上传的头像文件
     * @return 返回上传结果，包含头像访问路径
     */
    @PostMapping("/avatar")
    public ApiResponse<Map<String, String>> uploadAvatar(@RequestParam("file") MultipartFile file) {
        try {
            UserDTO currentUser = UserHolder.getUser();
            if (currentUser == null) {
                return ApiResponse.unauthorized("用户未登录");
            }

            // 验证文件类型
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || (!originalFilename.toLowerCase().endsWith(".jpg") 
                    && !originalFilename.toLowerCase().endsWith(".jpeg") 
                    && !originalFilename.toLowerCase().endsWith(".png"))) {
                return ApiResponse.error("头像只支持 JPG、JPEG、PNG 格式");
            }

            // 验证文件大小 (2MB)
            if (file.getSize() > 2 * 1024 * 1024) {
                return ApiResponse.error("头像文件大小不能超过 2MB");
            }

            String avatarUrl;
            if (blogUploadService != null) {
                // 使用云存储服务上传头像
                log.info("使用BlogUploadService上传头像，用户ID: {}", currentUser.getId());
                avatarUrl = blogUploadService.uploadBlogImage(file);
            } else {
                // 使用本地存储上传头像
                log.info("使用本地存储上传头像，用户ID: {}", currentUser.getId());
                String fileName = createAvatarFileName(currentUser.getId(), originalFilename);
                file.transferTo(new File(SystemConstants.IMAGE_UPLOAD_DIR, fileName));
                avatarUrl = fileName;
            }

            // 更新用户头像
            boolean updated = userService.updateUserAvatar(currentUser.getId(), avatarUrl);
            if (!updated) {
                return ApiResponse.error("头像上传成功，但更新用户信息失败");
            }

            Map<String, String> result = new HashMap<>();
            result.put("url", avatarUrl);
            
            log.info("头像上传成功，用户ID: {}, 头像URL: {}", currentUser.getId(), avatarUrl);
            return ApiResponse.success("头像上传成功", result);
            
        } catch (IOException e) {
            log.error("头像上传失败，IO异常", e);
            return ApiResponse.error("头像上传失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("头像上传失败，未知异常", e);
            return ApiResponse.error("头像上传失败: " + e.getMessage());
        }
    }

    /**
     * 生成头像文件名
     * 
     * @param userId 用户ID
     * @param originalFilename 原始文件名
     * @return 新的文件名
     */
    private String createAvatarFileName(Long userId, String originalFilename) {
        String suffix = StrUtil.subAfter(originalFilename, ".", true);
        long timestamp = System.currentTimeMillis();
        
        // 确保avatar目录存在
        File avatarDir = new File(SystemConstants.IMAGE_UPLOAD_DIR, "/avatar");
        if (!avatarDir.exists()) {
            avatarDir.mkdirs();
        }
        
        return StrUtil.format("/avatar/{}_{}.{}", userId, timestamp, suffix);
    }
}
