package com.scriptmurder.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.scriptmurder.dto.ApiResponse;
import com.scriptmurder.dto.LoginHistoryDTO;
import com.scriptmurder.dto.UserDTO;
import com.scriptmurder.dto.UserSettingsDTO;
import com.scriptmurder.service.ILoginHistoryService;
import com.scriptmurder.service.IUserSettingsService;
import com.scriptmurder.utils.UserHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 用户设置管理控制器
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Slf4j
@RestController
@RequestMapping("/api/user")
public class UserProfileController {

    @Resource
    private IUserSettingsService userSettingsService;

    @Resource
    private ILoginHistoryService loginHistoryService;

    // ==================== 用户设置相关接口 ====================

    @GetMapping("/settings")
    public ApiResponse<UserSettingsDTO> getUserSettings() {
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser == null) {
            return ApiResponse.unauthorized("用户未登录");
        }

        UserSettingsDTO settings = userSettingsService.getUserSettings(currentUser.getId());
        return ApiResponse.success("获取设置成功", settings);
    }

    @PutMapping("/settings")
    public ApiResponse<Boolean> updateUserSettings(
            @Valid @RequestBody UserSettingsDTO settingsDTO) {
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser == null) {
            return ApiResponse.unauthorized("用户未登录");
        }

        boolean success = userSettingsService.updateUserSettings(currentUser.getId(), settingsDTO);
        if (success) {
            return ApiResponse.success("设置更新成功", true);
        } else {
            return ApiResponse.error("设置更新失败");
        }
    }

    @PostMapping("/settings/reset")
    public ApiResponse<Boolean> resetToDefaults() {
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser == null) {
            return ApiResponse.unauthorized("用户未登录");
        }

        boolean success = userSettingsService.resetToDefaults(currentUser.getId());
        if (success) {
            return ApiResponse.success("已重置为默认设置", true);
        } else {
            return ApiResponse.error("重置失败");
        }
    }

    @PostMapping("/settings/toggle-theme")
    public ApiResponse<String> toggleTheme() {
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser == null) {
            return ApiResponse.unauthorized("用户未登录");
        }

        String newTheme = userSettingsService.toggleTheme(currentUser.getId());
        return ApiResponse.success("主题切换成功", newTheme);
    }

    @PostMapping("/settings/toggle-notification")
    public ApiResponse<Boolean> toggleNotification(
            @RequestParam String notificationType) {
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser == null) {
            return ApiResponse.unauthorized("用户未登录");
        }

        boolean newStatus = userSettingsService.toggleNotification(currentUser.getId(), notificationType);
        return ApiResponse.success("通知设置更新成功", newStatus);
    }

    @PutMapping("/settings/field")
    public ApiResponse<Boolean> updateSingleSetting(
            @RequestParam String fieldName,
            @RequestParam String fieldValue) {
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser == null) {
            return ApiResponse.unauthorized("用户未登录");
        }

        boolean success = userSettingsService.updateSingleSetting(currentUser.getId(), fieldName, fieldValue);
        if (success) {
            return ApiResponse.success("设置更新成功", true);
        } else {
            return ApiResponse.error("设置更新失败");
        }
    }

    @GetMapping("/settings/summary")
    public ApiResponse<Map<String, Object>> getUserSettingsSummary() {
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser == null) {
            return ApiResponse.unauthorized("用户未登录");
        }

        Map<String, Object> summary = userSettingsService.getUserSettingsSummary(currentUser.getId());
        return ApiResponse.success("获取设置摘要成功", summary);
    }

    @GetMapping("/settings/export")
    public ApiResponse<String> exportUserSettings() {
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser == null) {
            return ApiResponse.unauthorized("用户未登录");
        }

        String settingsJson = userSettingsService.exportUserSettings(currentUser.getId());
        if (settingsJson != null) {
            return ApiResponse.success("导出设置成功", settingsJson);
        } else {
            return ApiResponse.error("导出设置失败");
        }
    }

    @PostMapping("/settings/import")
    public ApiResponse<Boolean> importUserSettings(
            @RequestBody String settingsJson) {
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser == null) {
            return ApiResponse.unauthorized("用户未登录");
        }

        boolean success = userSettingsService.importUserSettings(currentUser.getId(), settingsJson);
        if (success) {
            return ApiResponse.success("导入设置成功", true);
        } else {
            return ApiResponse.error("导入设置失败");
        }
    }

    // ==================== 登录历史相关接口 ====================

    @GetMapping("/login-history")
    public ApiResponse<Page<LoginHistoryDTO>> getLoginHistory(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser == null) {
            return ApiResponse.unauthorized("用户未登录");
        }

        Page<LoginHistoryDTO> historyPage = loginHistoryService.getLoginHistory(
                currentUser.getId(), page, size);
        return ApiResponse.success("获取登录历史成功", historyPage);
    }

    @GetMapping("/login-history/recent")
    public ApiResponse<List<LoginHistoryDTO>> getRecentLogins(
            @RequestParam(defaultValue = "5") Integer limit) {
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser == null) {
            return ApiResponse.unauthorized("用户未登录");
        }

        List<LoginHistoryDTO> recentLogins = loginHistoryService.getRecentLogins(
                currentUser.getId(), limit);
        return ApiResponse.success("获取最近登录记录成功", recentLogins);
    }

    @GetMapping("/login-history/online")
    public ApiResponse<List<LoginHistoryDTO>> getOnlineSessions() {
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser == null) {
            return ApiResponse.unauthorized("用户未登录");
        }

        List<LoginHistoryDTO> onlineSessions = loginHistoryService.getOnlineSessions(currentUser.getId());
        return ApiResponse.success("获取在线会话成功", onlineSessions);
    }

    @DeleteMapping("/login-history")
    public ApiResponse<Boolean> clearLoginHistory() {
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser == null) {
            return ApiResponse.unauthorized("用户未登录");
        }

        boolean success = loginHistoryService.clearLoginHistory(currentUser.getId());
        if (success) {
            return ApiResponse.success("登录历史已清除", true);
        } else {
            return ApiResponse.error("清除失败");
        }
    }

    @GetMapping("/login-history/statistics")
    public ApiResponse<Map<String, Object>> getLoginStatistics() {
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser == null) {
            return ApiResponse.unauthorized("用户未登录");
        }

        Map<String, Object> statistics = loginHistoryService.getUserLoginStatistics(currentUser.getId());
        return ApiResponse.success("获取登录统计成功", statistics);
    }

    @GetMapping("/login-history/suspicious")
    public ApiResponse<List<LoginHistoryDTO>> detectSuspiciousLogins() {
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser == null) {
            return ApiResponse.unauthorized("用户未登录");
        }

        List<LoginHistoryDTO> suspiciousLogins = loginHistoryService.detectSuspiciousLogins(currentUser.getId());
        return ApiResponse.success("检测可疑登录成功", suspiciousLogins);
    }

    @GetMapping("/login-history/security-report")
    public ApiResponse<Map<String, Object>> generateSecurityReport(
            @RequestParam(defaultValue = "30") Integer days) {
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser == null) {
            return ApiResponse.unauthorized("用户未登录");
        }

        Map<String, Object> report = loginHistoryService.generateSecurityReport(currentUser.getId(), days);
        return ApiResponse.success("生成安全报告成功", report);
    }

    @PostMapping("/login-history/logout-all")
    public ApiResponse<Integer> logoutAllSessions() {
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser == null) {
            return ApiResponse.unauthorized("用户未登录");
        }

        int count = loginHistoryService.logoutAllUserSessions(currentUser.getId());
        return ApiResponse.success("已登出所有会话", count);
    }

    @PostMapping("/logout-record")
    public ApiResponse<Boolean> recordLogout(HttpServletRequest request) {
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser == null) {
            return ApiResponse.unauthorized("用户未登录");
        }

        boolean success = loginHistoryService.recordLogout(currentUser.getId());
        return ApiResponse.success("登出记录成功", success);
    }
}
