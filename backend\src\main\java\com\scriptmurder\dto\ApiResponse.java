package com.scriptmurder.dto;

import com.scriptmurder.enums.ResponseCode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 统一API响应格式
 * 根据剧本杀应用后端开发文档v1.0定义
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiResponse<T> {
    
    /**
     * 响应状态码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    // ==================== 成功响应 ====================
    
    /**
     * 成功响应（无数据）
     */
    public static <T> ApiResponse<T> success() {
        return new ApiResponse<>(200, "操作成功", null);
    }
    
    /**
     * 成功响应（带数据）
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(200, "操作成功", data);
    }
    
    /**
     * 成功响应（自定义消息）
     */
    public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<>(200, message, data);
    }

    /**
     * 使用响应码枚举创建响应
     */
    public static <T> ApiResponse<T> of(ResponseCode responseCode) {
        return new ApiResponse<>(responseCode.getCode(), responseCode.getMessage(), null);
    }

    /**
     * 使用响应码枚举创建响应（带数据）
     */
    public static <T> ApiResponse<T> of(ResponseCode responseCode, T data) {
        return new ApiResponse<>(responseCode.getCode(), responseCode.getMessage(), data);
    }

    /**
     * 使用响应码枚举创建响应（自定义消息）
     */
    public static <T> ApiResponse<T> of(ResponseCode responseCode, String message, T data) {
        return new ApiResponse<>(responseCode.getCode(), message, data);
    }
    
    // ==================== 错误响应 ====================
    
    /**
     * 错误响应（默认400错误）
     */
    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>(400, message, null);
    }
    
    /**
     * 错误响应（自定义错误码）
     */
    public static <T> ApiResponse<T> error(Integer code, String message) {
        return new ApiResponse<>(code, message, null);
    }
    
    // ==================== 常用状态码响应 ====================
    
    /**
     * 参数错误 400
     */
    public static <T> ApiResponse<T> badRequest(String message) {
        return new ApiResponse<>(400, message, null);
    }
    
    /**
     * 未授权 401
     */
    public static <T> ApiResponse<T> unauthorized(String message) {
        return new ApiResponse<>(401, message != null ? message : "未授权", null);
    }
    
    /**
     * 禁止访问 403
     */
    public static <T> ApiResponse<T> forbidden(String message) {
        return new ApiResponse<>(403, message != null ? message : "禁止访问", null);
    }
    
    /**
     * 资源不存在 404
     */
    public static <T> ApiResponse<T> notFound(String message) {
        return new ApiResponse<>(404, message != null ? message : "资源不存在", null);
    }
    
    /**
     * 冲突 409
     */
    public static <T> ApiResponse<T> conflict(String message) {
        return new ApiResponse<>(409, message, null);
    }
    
    /**
     * 业务逻辑错误 422
     */
    public static <T> ApiResponse<T> businessError(String message) {
        return new ApiResponse<>(422, message, null);
    }
    
    /**
     * 服务器内部错误 500
     */
    public static <T> ApiResponse<T> serverError(String message) {
        return new ApiResponse<>(500, message != null ? message : "服务器内部错误", null);
    }
    
    // ==================== 判断方法 ====================
    
    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return this.code != null && this.code == 200;
    }
    
    /**
     * 判断是否失败
     */
    public boolean isError() {
        return !isSuccess();
    }

    // ==================== 兼容性别名方法 ====================
    
    /**
     * 成功响应别名（兼容ok方法）
     */
    public static <T> ApiResponse<T> ok() {
        return success();
    }
    
    /**
     * 成功响应别名（兼容ok方法，带消息）
     */
    public static <T> ApiResponse<T> ok(String message) {
        return new ApiResponse<>(200, message, null);
    }
    
    /**
     * 成功响应别名（兼容ok方法，带数据）
     */
    public static <T> ApiResponse<T> ok(T data) {
        return success(data);
    }
    
    /**
     * 成功响应别名（兼容ok方法，带消息和数据）
     */
    public static <T> ApiResponse<T> ok(String message, T data) {
        return success(message, data);
    }
    
    /**
     * 失败响应别名（兼容fail方法）
     */
    public static <T> ApiResponse<T> fail(String message) {
        return error(message);
    }
    
    /**
     * 失败响应别名（兼容fail方法，带错误码）
     */
    public static <T> ApiResponse<T> fail(Integer code, String message) {
        return error(code, message);
    }
}
