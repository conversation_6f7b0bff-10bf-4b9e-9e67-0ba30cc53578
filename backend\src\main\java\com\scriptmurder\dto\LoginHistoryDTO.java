package com.scriptmurder.dto;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * 登录历史DTO
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Data
public class LoginHistoryDTO {

    private Long id;

    private String loginIp;

    private String loginLocation;

    private String deviceType;

    private String browser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime loginTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime logoutTime;

    private Integer sessionDuration;

    private Integer status;

    private String failureReason;

    // 扩展字段，不存储在数据库中
    private String statusDesc;

    private String deviceTypeDesc;

    private String formattedSessionDuration;

    private String simpleBrowserName;

    private String loginTimeDesc;

    private Boolean isOnline;

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        if (statusDesc != null) {
            return statusDesc;
        }
        return status != null && status == 1 ? "成功" : "失败";
    }

    /**
     * 获取设备类型描述
     */
    public String getDeviceTypeDesc() {
        if (deviceTypeDesc != null) {
            return deviceTypeDesc;
        }
        
        if (deviceType == null) {
            return "未知";
        }
        
        switch (deviceType) {
            case "Mobile":
                return "手机";
            case "Tablet":
                return "平板";
            case "Desktop":
                return "桌面";
            default:
                return "未知";
        }
    }

    /**
     * 获取格式化的会话时长
     */
    public String getFormattedSessionDuration() {
        if (formattedSessionDuration != null) {
            return formattedSessionDuration;
        }
        
        if (sessionDuration == null || sessionDuration <= 0) {
            return "未知";
        }
        
        int hours = sessionDuration / 3600;
        int minutes = (sessionDuration % 3600) / 60;
        int seconds = sessionDuration % 60;
        
        if (hours > 0) {
            return String.format("%d小时%d分钟", hours, minutes);
        } else if (minutes > 0) {
            return String.format("%d分钟%d秒", minutes, seconds);
        } else {
            return String.format("%d秒", seconds);
        }
    }

    /**
     * 获取简化的浏览器名称
     */
    public String getSimpleBrowserName() {
        if (simpleBrowserName != null) {
            return simpleBrowserName;
        }
        
        if (browser == null) {
            return "未知";
        }
        
        String[] parts = browser.split(" ");
        return parts.length > 0 ? parts[0] : browser;
    }

    /**
     * 检查是否为成功登录
     */
    public boolean isSuccessful() {
        return status != null && status == 1;
    }

    /**
     * 检查是否为失败登录
     */
    public boolean isFailed() {
        return status != null && status == 0;
    }

    /**
     * 检查是否还在线
     */
    public Boolean getIsOnline() {
        if (isOnline != null) {
            return isOnline;
        }
        return isSuccessful() && logoutTime == null;
    }

    /**
     * 检查是否为移动设备
     */
    public boolean isMobileDevice() {
        return "Mobile".equals(deviceType);
    }

    /**
     * 检查是否为桌面设备
     */
    public boolean isDesktopDevice() {
        return "Desktop".equals(deviceType);
    }

    /**
     * 获取设备图标
     */
    public String getDeviceIcon() {
        if (deviceType == null) {
            return "❓";
        }
        
        switch (deviceType) {
            case "Mobile":
                return "📱";
            case "Tablet":
                return "📱";
            case "Desktop":
                return "💻";
            default:
                return "❓";
        }
    }

    /**
     * 获取状态图标
     */
    public String getStatusIcon() {
        return isSuccessful() ? "✅" : "❌";
    }

    /**
     * 获取在线状态图标
     */
    public String getOnlineStatusIcon() {
        return Boolean.TRUE.equals(getIsOnline()) ? "🟢" : "⚫";
    }
}
