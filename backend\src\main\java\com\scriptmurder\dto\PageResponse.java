package com.scriptmurder.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 分页响应数据格式
 * 根据剧本杀应用后端开发文档v1.0定义
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageResponse<T> {
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 总页数
     */
    private Long pages;
    
    /**
     * 当前页码
     */
    private Long current;
    
    /**
     * 每页大小
     */
    private Long size;
    
    /**
     * 数据列表
     */
    private List<T> records;
    
    /**
     * 构造分页响应
     */
    public static <T> PageResponse<T> of(List<T> records, Long current, Long size, Long total) {
        PageResponse<T> pageResponse = new PageResponse<>();
        pageResponse.setRecords(records);
        pageResponse.setCurrent(current);
        pageResponse.setSize(size);
        pageResponse.setTotal(total);
        
        // 计算总页数
        if (size != null && size > 0) {
            pageResponse.setPages((total + size - 1) / size);
        } else {
            pageResponse.setPages(0L);
        }
        
        return pageResponse;
    }
    
    /**
     * 构造空分页响应
     */
    public static <T> PageResponse<T> empty(Long current, Long size) {
        return of(List.of(), current, size, 0L);
    }
    
    /**
     * 判断是否有数据
     */
    public boolean hasData() {
        return records != null && !records.isEmpty();
    }
    
    /**
     * 判断是否为空
     */
    public boolean isEmpty() {
        return !hasData();
    }
    
    /**
     * 获取数据条数
     */
    public int getRecordCount() {
        return records != null ? records.size() : 0;
    }
}
