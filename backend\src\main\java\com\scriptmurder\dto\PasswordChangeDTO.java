package com.scriptmurder.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 密码修改DTO
 *
 * <AUTHOR>
 * @since 2025-01-31
 */
@Data
public class PasswordChangeDTO {

    @NotBlank(message = "原密码不能为空")
    private String oldPassword;

    @NotBlank(message = "新密码不能为空")
    @Length(min = 6, max = 20, message = "密码长度必须在6-20位之间")
    private String newPassword;
}
