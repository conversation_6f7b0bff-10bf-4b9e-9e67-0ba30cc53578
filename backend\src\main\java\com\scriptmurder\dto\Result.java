package com.scriptmurder.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 统一API响应结果类
 * 兼容原有的Result格式，同时支持新的ApiResponse格式
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Result<T> {
    
    /**
     * 响应状态码（新格式）
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 是否成功（旧格式兼容）
     */
    private Boolean success;
    
    /**
     * 错误信息（旧格式兼容）
     */
    private String errorMsg;
    
    // ==================== 旧格式兼容方法 ====================
    
    /**
     * 成功响应（旧格式）
     */
    public static <T> Result<T> ok() {
        Result<T> result = new Result<>();
        result.success = true;
        result.code = 200;
        result.message = "操作成功";
        return result;
    }
    
    /**
     * 成功响应（旧格式，带数据）
     */
    public static <T> Result<T> ok(T data) {
        Result<T> result = new Result<>();
        result.success = true;
        result.code = 200;
        result.message = "操作成功";
        result.data = data;
        return result;
    }
    
    /**
     * 成功响应（旧格式，自定义消息）
     */
    public static <T> Result<T> ok(String message, T data) {
        Result<T> result = new Result<>();
        result.success = true;
        result.code = 200;
        result.message = message;
        result.data = data;
        return result;
    }
    
    /**
     * 失败响应（旧格式）
     */
    public static <T> Result<T> fail(String errorMsg) {
        Result<T> result = new Result<>();
        result.success = false;
        result.code = 400;
        result.message = errorMsg;
        result.errorMsg = errorMsg;
        return result;
    }
    
    /**
     * 失败响应（旧格式，自定义错误码）
     */
    public static <T> Result<T> fail(Integer code, String errorMsg) {
        Result<T> result = new Result<>();
        result.success = false;
        result.code = code;
        result.message = errorMsg;
        result.errorMsg = errorMsg;
        return result;
    }
    
    // ==================== 新格式静态方法 ====================
    
    /**
     * 成功响应（新格式）
     */
    public static <T> Result<T> success() {
        Result<T> result = new Result<>();
        result.code = 200;
        result.message = "操作成功";
        result.success = true;
        return result;
    }
    
    /**
     * 成功响应（新格式，带数据）
     */
    public static <T> Result<T> success(T data) {
        Result<T> result = new Result<>();
        result.code = 200;
        result.message = "操作成功";
        result.data = data;
        result.success = true;
        return result;
    }
    
    /**
     * 成功响应（新格式，自定义消息）
     */
    public static <T> Result<T> success(String message, T data) {
        Result<T> result = new Result<>();
        result.code = 200;
        result.message = message;
        result.data = data;
        result.success = true;
        return result;
    }
    
    /**
     * 错误响应（新格式，默认400）
     */
    public static <T> Result<T> error(String message) {
        Result<T> result = new Result<>();
        result.code = 400;
        result.message = message;
        result.success = false;
        result.errorMsg = message;
        return result;
    }
    
    /**
     * 错误响应（新格式，自定义错误码）
     */
    public static <T> Result<T> error(Integer code, String message) {
        Result<T> result = new Result<>();
        result.code = code;
        result.message = message;
        result.success = false;
        result.errorMsg = message;
        return result;
    }
    
    // ==================== 常用状态码响应 ====================
    
    /**
     * 参数错误 400
     */
    public static <T> Result<T> badRequest(String message) {
        return error(400, message);
    }
    
    /**
     * 未授权 401
     */
    public static <T> Result<T> unauthorized(String message) {
        return error(401, message != null ? message : "未授权");
    }
    
    /**
     * 禁止访问 403
     */
    public static <T> Result<T> forbidden(String message) {
        return error(403, message != null ? message : "禁止访问");
    }
    
    /**
     * 资源不存在 404
     */
    public static <T> Result<T> notFound(String message) {
        return error(404, message != null ? message : "资源不存在");
    }
    
    /**
     * 冲突 409
     */
    public static <T> Result<T> conflict(String message) {
        return error(409, message);
    }
    
    /**
     * 业务逻辑错误 422
     */
    public static <T> Result<T> businessError(String message) {
        return error(422, message);
    }
    
    /**
     * 服务器内部错误 500
     */
    public static <T> Result<T> serverError(String message) {
        return error(500, message != null ? message : "服务器内部错误");
    }
    
    // ==================== 判断方法 ====================
    
    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        // 优先判断新格式的code
        if (this.code != null) {
            return this.code == 200;
        }
        // 兼容旧格式的success字段
        return this.success != null && this.success;
    }
    
    /**
     * 判断是否失败
     */
    public boolean isError() {
        return !isSuccess();
    }
    
    /**
     * 获取成功标识（旧格式兼容）
     */
    public Boolean getSuccess() {
        if (this.success != null) {
            return this.success;
        }
        // 根据code判断
        return this.code != null && this.code == 200;
    }
    
    /**
     * 获取错误信息（旧格式兼容）
     */
    public String getErrorMsg() {
        if (this.errorMsg != null) {
            return this.errorMsg;
        }
        // 如果没有errorMsg，返回message
        return this.message;
    }
}