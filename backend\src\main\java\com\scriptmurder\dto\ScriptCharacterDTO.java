package com.scriptmurder.dto;


import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 剧本角色DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScriptCharacterDTO {

    private Long id;

    private String name;

    private String title;

    private String gender;

    private String age;

    private String occupation;

    private String description;

    private String avatar;

    private String traits;

    private Integer difficulty;

    private Integer secretsCount;

    private Integer sortOrder;
}