package com.scriptmurder.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 剧本信息DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScriptDTO {

    private Long id;

    private String title;

    private String description;

    private String coverImage;

    private String category;

    private String playerCountRange;

    private Integer playerCountMin;

    private Integer playerCountMax;

    private String duration;

    private Integer difficulty;

    private String ageRange;

    private List<String> tagList;

    private List<String> highlights;

    private List<String> warnings;

    private BigDecimal price;

    private BigDecimal averageRating;

    private Integer reviewCount;

    private Integer playCount;

    private Boolean isFavorite;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}