package com.scriptmurder.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 剧本详情DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScriptDetailDTO {

    private Long id;

    private String title;

    private String description;

    private String coverImage;

    private List<String> images;

    private String category;

    private Integer playerCountMin;

    private Integer playerCountMax;

    private String duration;

    private Integer difficulty;

    private String ageRange;

    private List<String> tagList;

    private List<String> highlights;

    private List<String> warnings;

    private BigDecimal price;

    private BigDecimal averageRating;

    private Integer reviewCount;

    private Integer playCount;

    private Boolean isFavorite;

    private List<ScriptCharacterDTO> characters;

    private List<ScriptRuleDTO> rules;

    private List<ScriptReviewDTO> recentReviews;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}