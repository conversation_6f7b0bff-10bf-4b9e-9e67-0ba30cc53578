package com.scriptmurder.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 剧本评价DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScriptReviewDTO {

    private Long id;

    private Long userId;

    private String userNickname;

    private String userAvatar;

    private BigDecimal rating;

    private String title;

    private String content;

    private List<String> images;

    private Integer likedCount;

    private Integer helpfulCount;

    private Boolean isAnonymous;

    private List<String> tags;

    private Boolean isLiked;


    private Boolean isHelpful;


    private LocalDateTime createTime;
}