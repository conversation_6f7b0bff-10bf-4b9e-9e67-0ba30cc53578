package com.scriptmurder.dto;

import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 剧本搜索请求DTO
 *
 * <AUTHOR>
 */
@Data
public class ScriptSearchDTO {

    private String keyword;

    private String category;

    @Min(value = 1, message = "最少玩家数不能小于1")
    private Integer playerCountMin;

    @Max(value = 20, message = "最多玩家数不能大于20")
    private Integer playerCountMax;

    private List<Integer> difficulties;

    @DecimalMin(value = "0.0", message = "价格不能小于0")
    private BigDecimal priceMin;

    @DecimalMax(value = "9999.99", message = "价格不能大于9999.99")
    private BigDecimal priceMax;

    private List<String> tags;

    private String sortBy = "createTime";

    @Pattern(regexp = "^(asc|desc)$", message = "排序方向只能是asc或desc")
    private String sortOrder = "desc";

    @Min(value = 1, message = "页码不能小于1")
    private Integer page = 1;

    @Min(value = 1, message = "每页大小不能小于1")
    @Max(value = 100, message = "每页大小不能大于100")
    private Integer size = 20;
}