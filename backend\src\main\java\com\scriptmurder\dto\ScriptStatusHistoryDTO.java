package com.scriptmurder.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 剧本状态变更历史DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScriptStatusHistoryDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 剧本ID
     */
    private Long scriptId;

    /**
     * 剧本标题
     */
    private String scriptTitle;

    /**
     * 原状态码
     */
    private Integer fromStatus;

    /**
     * 原状态名称
     */
    private String fromStatusName;

    /**
     * 新状态码
     */
    private Integer toStatus;

    /**
     * 新状态名称
     */
    private String toStatusName;

    /**
     * 操作员ID
     */
    private Long operatorId;

    /**
     * 操作员昵称
     */
    private String operatorName;

    /**
     * 操作员类型
     */
    private String operatorType;

    /**
     * 操作员类型名称
     */
    private String operatorTypeName;

    /**
     * 操作原因
     */
    private String reason;

    /**
     * 操作备注
     */
    private String comment;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 操作IP地址
     */
    private String operatorIp;

    /**
     * 操作设备信息
     */
    private String deviceInfo;

    /**
     * 审核耗时（分钟）
     */
    private Integer reviewDuration;

    /**
     * 审核耗时描述
     */
    private String reviewDurationDesc;

    /**
     * 扩展信息
     */
    private String extraInfo;
}
