package com.scriptmurder.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 剧本状态统计DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScriptStatusStatsDTO {

    /**
     * 总剧本数
     */
    private Long totalScripts;

    /**
     * 已上架数量
     */
    private Long publishedCount;

    /**
     * 已下架数量
     */
    private Long unpublishedCount;

    /**
     * 审核中数量
     */
    private Long reviewingCount;

    /**
     * 审核拒绝数量
     */
    private Long rejectedCount;

    /**
     * 草稿数量
     */
    private Long draftCount;

    /**
     * 状态分布百分比
     */
    private Map<String, Double> statusPercentage;

    /**
     * 状态分布详情
     */
    private List<StatusDistributionItem> statusDistribution;

    /**
     * 今日新增剧本数
     */
    private Long todayNewScripts;

    /**
     * 今日审核数量
     */
    private Long todayReviewedCount;

    /**
     * 待审核剧本数
     */
    private Long pendingReviewCount;

    /**
     * 平均审核时长（小时）
     */
    private Double avgReviewHours;

    /**
     * 审核通过率
     */
    private Double approvalRate;

    /**
     * 最近7天状态变更趋势
     */
    private List<StatusTrendItem> recentTrends;

    /**
     * 状态分布项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StatusDistributionItem {
        private Integer statusCode;
        private String statusName;
        private Long count;
        private Double percentage;
        private String color;
    }

    /**
     * 状态趋势项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StatusTrendItem {
        private String date;
        private Long submittedCount;
        private Long reviewedCount;
        private Long approvedCount;
        private Long rejectedCount;
        private Double approvalRate;
    }
}
