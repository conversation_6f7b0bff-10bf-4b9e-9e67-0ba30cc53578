package com.scriptmurder.dto;

import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;

/**
 * 用户设置DTO
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Data
public class UserSettingsDTO {

    @Pattern(regexp = "^(light|dark)$", message = "主题只能是 light 或 dark")
    private String theme;

    @Pattern(regexp = "^[a-z]{2}-[A-Z]{2}$", message = "语言格式不正确，应为 xx-XX 格式")
    private String language;

    private String timezone;

    private Boolean emailNotifications;

    private Boolean systemNotifications;

    private Boolean activityNotifications;

    private Boolean socialNotifications;

    @Min(value = 1, message = "隐私级别最小值为1")
    @Max(value = 3, message = "隐私级别最大值为3")
    private Integer privacyLevel;

    private Boolean autoSave;

    /**
     * 获取隐私级别描述
     */
    public String getPrivacyLevelDesc() {
        if (privacyLevel == null) {
            return "未设置";
        }
        switch (privacyLevel) {
            case 1:
                return "公开";
            case 2:
                return "好友";
            case 3:
                return "私密";
            default:
                return "未知";
        }
    }

    /**
     * 获取主题描述
     */
    public String getThemeDesc() {
        if (theme == null) {
            return "未设置";
        }
        return "dark".equals(theme) ? "深色" : "浅色";
    }

    /**
     * 检查是否启用了任何通知
     */
    public boolean hasAnyNotificationEnabled() {
        return Boolean.TRUE.equals(emailNotifications) || 
               Boolean.TRUE.equals(systemNotifications) || 
               Boolean.TRUE.equals(activityNotifications) || 
               Boolean.TRUE.equals(socialNotifications);
    }

    /**
     * 检查是否所有通知都已启用
     */
    public boolean areAllNotificationsEnabled() {
        return Boolean.TRUE.equals(emailNotifications) && 
               Boolean.TRUE.equals(systemNotifications) && 
               Boolean.TRUE.equals(activityNotifications) && 
               Boolean.TRUE.equals(socialNotifications);
    }

    /**
     * 获取通知设置摘要
     */
    public String getNotificationSummary() {
        int enabledCount = 0;
        if (Boolean.TRUE.equals(emailNotifications)) enabledCount++;
        if (Boolean.TRUE.equals(systemNotifications)) enabledCount++;
        if (Boolean.TRUE.equals(activityNotifications)) enabledCount++;
        if (Boolean.TRUE.equals(socialNotifications)) enabledCount++;
        
        return String.format("已启用 %d/4 项通知", enabledCount);
    }

    /**
     * 验证设置的有效性
     */
    public boolean isValid() {
        // 验证主题
        if (theme != null && !theme.matches("^(dark|light)$")) {
            return false;
        }
        
        // 验证隐私级别
        if (privacyLevel != null && (privacyLevel < 1 || privacyLevel > 3)) {
            return false;
        }
        
        // 验证语言格式
        if (language != null && !language.matches("^[a-z]{2}-[A-Z]{2}$")) {
            return false;
        }
        
        return true;
    }

    /**
     * 创建默认设置
     */
    public static UserSettingsDTO createDefault() {
        UserSettingsDTO dto = new UserSettingsDTO();
        dto.setTheme("dark");
        dto.setLanguage("zh-CN");
        dto.setTimezone("Asia/Shanghai");
        dto.setEmailNotifications(true);
        dto.setSystemNotifications(true);
        dto.setActivityNotifications(true);
        dto.setSocialNotifications(false);
        dto.setPrivacyLevel(1);
        dto.setAutoSave(true);
        return dto;
    }

    /**
     * 复制非空字段到目标对象
     */
    public void copyNonNullFieldsTo(UserSettingsDTO target) {
        if (this.theme != null) target.setTheme(this.theme);
        if (this.language != null) target.setLanguage(this.language);
        if (this.timezone != null) target.setTimezone(this.timezone);
        if (this.emailNotifications != null) target.setEmailNotifications(this.emailNotifications);
        if (this.systemNotifications != null) target.setSystemNotifications(this.systemNotifications);
        if (this.activityNotifications != null) target.setActivityNotifications(this.activityNotifications);
        if (this.socialNotifications != null) target.setSocialNotifications(this.socialNotifications);
        if (this.privacyLevel != null) target.setPrivacyLevel(this.privacyLevel);
        if (this.autoSave != null) target.setAutoSave(this.autoSave);
    }
}
