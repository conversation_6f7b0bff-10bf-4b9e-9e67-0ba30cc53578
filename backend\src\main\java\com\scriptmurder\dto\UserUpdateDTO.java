package com.scriptmurder.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.time.LocalDate;

/**
 * 用户信息更新DTO
 *
 * <AUTHOR>
 * @since 2025-01-31
 */
@Data
public class UserUpdateDTO {

    @NotBlank(message = "昵称不能为空")
    @Length(max = 32, message = "昵称长度不能超过32个字符")
    private String nickName;

    @Email(message = "邮箱格式不正确")
    private String email;

    @Min(value = 0, message = "性别值不正确")
    @Max(value = 2, message = "性别值不正确")
    private Integer gender;

    private LocalDate birthday;

    @Length(max = 50, message = "城市名称长度不能超过50个字符")
    private String city;

    @Length(max = 200, message = "个性签名长度不能超过200个字符")
    private String signature;
}
