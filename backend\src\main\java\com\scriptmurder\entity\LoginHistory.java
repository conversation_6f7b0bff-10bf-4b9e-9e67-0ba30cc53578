package com.scriptmurder.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.Duration;

/**
 * 登录历史实体类
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_login_history")
public class LoginHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 登录IP
     */
    private String loginIp;

    /**
     * 登录地点
     */
    private String loginLocation;

    /**
     * 设备类型 Mobile/Tablet/Desktop
     */
    private String deviceType;

    /**
     * 浏览器信息
     */
    private String browser;



    /**
     * 登录时间
     */
    private LocalDateTime loginTime;

    /**
     * 登出时间
     */
    private LocalDateTime logoutTime;

    /**
     * 会话时长(秒)
     */
    private Integer sessionDuration;

    /**
     * 状态 1-成功 0-失败
     */
    private Integer status = 1;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        return status == 1 ? "成功" : "失败";
    }

    /**
     * 获取设备类型描述
     */
    public String getDeviceTypeDesc() {
        switch (deviceType) {
            case "Mobile":
                return "手机";
            case "Tablet":
                return "平板";
            case "Desktop":
                return "桌面";
            default:
                return "未知";
        }
    }

    /**
     * 检查是否为成功登录
     */
    public boolean isSuccessful() {
        return status != null && status == 1;
    }

    /**
     * 检查是否为失败登录
     */
    public boolean isFailed() {
        return status != null && status == 0;
    }

    /**
     * 检查是否还在线（未登出）
     */
    public boolean isOnline() {
        return isSuccessful() && logoutTime == null;
    }

    /**
     * 计算会话时长（如果已登出）
     */
    public void calculateSessionDuration() {
        if (loginTime != null && logoutTime != null) {
            Duration duration = Duration.between(loginTime, logoutTime);
            this.sessionDuration = (int) duration.getSeconds();
        }
    }

    /**
     * 获取格式化的会话时长
     */
    public String getFormattedSessionDuration() {
        if (sessionDuration == null || sessionDuration <= 0) {
            return "未知";
        }
        
        int hours = sessionDuration / 3600;
        int minutes = (sessionDuration % 3600) / 60;
        int seconds = sessionDuration % 60;
        
        if (hours > 0) {
            return String.format("%d小时%d分钟", hours, minutes);
        } else if (minutes > 0) {
            return String.format("%d分钟%d秒", minutes, seconds);
        } else {
            return String.format("%d秒", seconds);
        }
    }

    /**
     * 获取简化的浏览器名称
     */
    public String getSimpleBrowserName() {
        if (browser == null) {
            return "未知";
        }
        
        String[] parts = browser.split(" ");
        return parts.length > 0 ? parts[0] : browser;
    }

    /**
     * 检查是否为移动设备
     */
    public boolean isMobileDevice() {
        return "Mobile".equals(deviceType);
    }

    /**
     * 检查是否为桌面设备
     */
    public boolean isDesktopDevice() {
        return "Desktop".equals(deviceType);
    }

    /**
     * 获取登录时间距现在的描述
     */
    public String getLoginTimeDesc() {
        if (loginTime == null) {
            return "未知";
        }
        
        Duration duration = Duration.between(loginTime, LocalDateTime.now());
        long days = duration.toDays();
        long hours = duration.toHours();
        long minutes = duration.toMinutes();
        
        if (days > 0) {
            return days + "天前";
        } else if (hours > 0) {
            return hours + "小时前";
        } else if (minutes > 0) {
            return minutes + "分钟前";
        } else {
            return "刚刚";
        }
    }

    /**
     * 设置登录失败信息
     */
    public void setLoginFailure(String reason) {
        this.status = 0;
        this.failureReason = reason;
        this.logoutTime = null;
        this.sessionDuration = null;
    }

    /**
     * 设置登录成功信息
     */
    public void setLoginSuccess() {
        this.status = 1;
        this.failureReason = null;
    }

    /**
     * 设置登出信息
     */
    public void setLogout() {
        this.logoutTime = LocalDateTime.now();
        calculateSessionDuration();
    }
}
