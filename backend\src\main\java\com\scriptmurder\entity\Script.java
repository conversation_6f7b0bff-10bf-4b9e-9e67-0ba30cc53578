package com.scriptmurder.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 剧本实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_script")
public class Script implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String title;

    private String description;

    private String coverImage;

    private String images;

    private String category;

    private Integer playerCountMin;

    private Integer playerCountMax;

    private String duration;

    private Integer difficulty;

    private String ageRange;

    private String tags;

    private String highlights;

    private String warnings;

    private BigDecimal price;

    private Integer status;

    private BigDecimal averageRating;

    private Integer reviewCount;

    private Integer playCount;

    /**
     * 创建者ID
     */
    private Long creatorId;

    /**
     * 审核员ID
     */
    private Long reviewerId;

    /**
     * 审核时间
     */
    private LocalDateTime reviewTime;

    /**
     * 审核意见
     */
    private String reviewComment;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}