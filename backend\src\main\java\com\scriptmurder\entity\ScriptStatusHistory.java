package com.scriptmurder.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 剧本状态变更历史实体
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_script_status_history")
public class ScriptStatusHistory {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 剧本ID
     */
    private Long scriptId;

    /**
     * 原状态
     */
    private Integer fromStatus;

    /**
     * 新状态
     */
    private Integer toStatus;

    /**
     * 操作员ID
     */
    private Long operatorId;

    /**
     * 操作员类型 (USER/ADMIN/SYSTEM)
     */
    private String operatorType;

    /**
     * 操作原因
     */
    private String reason;

    /**
     * 操作备注
     */
    private String comment;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 操作IP地址
     */
    private String operatorIp;

    /**
     * 操作设备信息
     */
    private String deviceInfo;

    /**
     * 审核耗时（分钟）
     */
    private Integer reviewDuration;

    /**
     * 扩展信息（JSON格式）
     */
    private String extraInfo;
}
