package com.scriptmurder.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户设置实体类
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_user_settings")
public class UserSettings implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 主题偏好 dark/light
     */
    private String theme = "dark";

    /**
     * 语言设置
     */
    private String language = "zh-CN";

    /**
     * 时区设置
     */
    private String timezone = "Asia/Shanghai";

    /**
     * 邮件通知开关
     */
    private Boolean emailNotifications = true;

    /**
     * 系统通知开关
     */
    private Boolean systemNotifications = true;

    /**
     * 活动通知开关
     */
    private Boolean activityNotifications = true;

    /**
     * 社交通知开关
     */
    private Boolean socialNotifications = false;

    /**
     * 隐私级别 1-公开 2-好友 3-私密
     */
    private Integer privacyLevel = 1;

    /**
     * 自动保存开关
     */
    private Boolean autoSave = true;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 获取隐私级别描述
     */
    public String getPrivacyLevelDesc() {
        switch (privacyLevel) {
            case 1:
                return "公开";
            case 2:
                return "好友";
            case 3:
                return "私密";
            default:
                return "未知";
        }
    }

    /**
     * 获取主题描述
     */
    public String getThemeDesc() {
        return "dark".equals(theme) ? "深色" : "浅色";
    }

    /**
     * 检查是否启用了任何通知
     */
    public boolean hasAnyNotificationEnabled() {
        return emailNotifications || systemNotifications || 
               activityNotifications || socialNotifications;
    }

    /**
     * 检查是否所有通知都已启用
     */
    public boolean areAllNotificationsEnabled() {
        return emailNotifications && systemNotifications && 
               activityNotifications && socialNotifications;
    }

    /**
     * 重置为默认设置
     */
    public void resetToDefaults() {
        this.theme = "dark";
        this.language = "zh-CN";
        this.timezone = "Asia/Shanghai";
        this.emailNotifications = true;
        this.systemNotifications = true;
        this.activityNotifications = true;
        this.socialNotifications = false;
        this.privacyLevel = 1;
        this.autoSave = true;
    }

    /**
     * 验证设置的有效性
     */
    public boolean isValid() {
        // 验证主题
        if (theme == null || (!theme.equals("dark") && !theme.equals("light"))) {
            return false;
        }
        
        // 验证隐私级别
        if (privacyLevel == null || privacyLevel < 1 || privacyLevel > 3) {
            return false;
        }
        
        // 验证语言格式
        if (language == null || !language.matches("^[a-z]{2}-[A-Z]{2}$")) {
            return false;
        }
        
        return true;
    }
}
