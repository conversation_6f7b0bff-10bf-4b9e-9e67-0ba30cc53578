package com.scriptmurder.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应状态码枚举
 * 根据剧本杀应用后端开发文档v1.0定义
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Getter
@AllArgsConstructor
public enum ResponseCode {
    
    // ==================== 成功状态码 ====================
    SUCCESS(200, "操作成功"),
    
    // ==================== 客户端错误 4xx ====================
    BAD_REQUEST(400, "参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "冲突"),
    BUSINESS_ERROR(422, "业务逻辑错误"),
    
    // ==================== 服务器错误 5xx ====================
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    BAD_GATEWAY(502, "网关错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    GATEWAY_TIMEOUT(504, "网关超时"),
    
    // ==================== 业务相关错误码 ====================
    
    // 用户相关 1000-1999
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_ALREADY_EXISTS(1002, "用户已存在"),
    USER_DISABLED(1003, "用户已被禁用"),
    INVALID_CREDENTIALS(1004, "用户名或密码错误"),
    PASSWORD_TOO_WEAK(1005, "密码强度不够"),
    PHONE_ALREADY_EXISTS(1006, "手机号已存在"),
    INVALID_VERIFICATION_CODE(1007, "验证码错误或已过期"),
    
    // 认证相关 2000-2999
    TOKEN_EXPIRED(2001, "Token已过期"),
    TOKEN_INVALID(2002, "Token无效"),
    TOKEN_MISSING(2003, "Token缺失"),
    PERMISSION_DENIED(2004, "权限不足"),
    LOGIN_REQUIRED(2005, "请先登录"),
    
    // 剧本相关 3000-3999
    SCRIPT_NOT_FOUND(3001, "剧本不存在"),
    SCRIPT_UNAVAILABLE(3002, "剧本不可用"),
    SCRIPT_ALREADY_EXISTS(3003, "剧本已存在"),
    INVALID_SCRIPT_DATA(3004, "剧本数据无效"),
    
    // 房间相关 4000-4999
    LOBBY_NOT_FOUND(4001, "房间不存在"),
    LOBBY_FULL(4002, "房间已满员"),
    LOBBY_ALREADY_STARTED(4003, "房间已开始"),
    LOBBY_ALREADY_ENDED(4004, "房间已结束"),
    LOBBY_CANCELLED(4005, "房间已取消"),
    ALREADY_IN_LOBBY(4006, "已在房间中"),
    NOT_IN_LOBBY(4007, "不在房间中"),
    NOT_LOBBY_HOST(4008, "不是房主"),
    INVALID_LOBBY_DATA(4009, "房间数据无效"),
    
    // 评价相关 5000-5999
    REVIEW_NOT_FOUND(5001, "评价不存在"),
    REVIEW_ALREADY_EXISTS(5002, "已评价过该剧本"),
    INVALID_RATING(5003, "评分无效"),
    REVIEW_PERMISSION_DENIED(5004, "无权限评价"),
    
    // 文件相关 6000-6999
    FILE_UPLOAD_FAILED(6001, "文件上传失败"),
    FILE_TOO_LARGE(6002, "文件过大"),
    INVALID_FILE_TYPE(6003, "文件类型不支持"),
    FILE_NOT_FOUND(6004, "文件不存在"),
    
    // 系统相关 9000-9999
    SYSTEM_BUSY(9001, "系统繁忙，请稍后重试"),
    MAINTENANCE(9002, "系统维护中"),
    RATE_LIMIT_EXCEEDED(9003, "请求过于频繁"),
    DATABASE_ERROR(9004, "数据库错误"),
    CACHE_ERROR(9005, "缓存错误");
    
    /**
     * 状态码
     */
    private final Integer code;
    
    /**
     * 状态消息
     */
    private final String message;
    
    /**
     * 根据状态码获取枚举
     */
    public static ResponseCode getByCode(Integer code) {
        for (ResponseCode responseCode : values()) {
            if (responseCode.getCode().equals(code)) {
                return responseCode;
            }
        }
        return null;
    }
    
    /**
     * 判断是否为成功状态码
     */
    public boolean isSuccess() {
        return this.code == 200;
    }
    
    /**
     * 判断是否为客户端错误
     */
    public boolean isClientError() {
        return this.code >= 400 && this.code < 500;
    }
    
    /**
     * 判断是否为服务器错误
     */
    public boolean isServerError() {
        return this.code >= 500 && this.code < 600;
    }
    
    /**
     * 判断是否为业务错误
     */
    public boolean isBusinessError() {
        return this.code >= 1000 && this.code < 10000;
    }
}
