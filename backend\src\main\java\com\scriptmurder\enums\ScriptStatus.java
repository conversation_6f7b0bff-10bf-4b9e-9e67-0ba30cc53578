package com.scriptmurder.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 剧本状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ScriptStatus {

    /**
     * 已上架
     */
    PUBLISHED(1, "已上架", "剧本已通过审核，可以被用户浏览和使用"),

    /**
     * 已下架
     */
    UNPUBLISHED(2, "已下架", "剧本被下架，用户无法访问"),

    /**
     * 审核中
     */
    REVIEWING(3, "审核中", "剧本提交审核，等待管理员审核"),

    /**
     * 审核拒绝
     */
    REJECTED(4, "审核拒绝", "剧本审核未通过，需要修改后重新提交"),

    /**
     * 草稿
     */
    DRAFT(5, "草稿", "剧本尚未完成，保存为草稿状态");

    private final Integer code;
    private final String name;
    private final String description;

    /**
     * 根据状态码获取枚举
     */
    public static ScriptStatus fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ScriptStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的剧本状态码: " + code);
    }

    /**
     * 检查是否为已发布状态
     */
    public boolean isPublished() {
        return this == PUBLISHED;
    }

    /**
     * 检查是否为可编辑状态
     */
    public boolean isEditable() {
        return this == DRAFT || this == REJECTED;
    }

    /**
     * 检查是否为终态
     */
    public boolean isFinalState() {
        return this == PUBLISHED || this == UNPUBLISHED;
    }
}
