package com.scriptmurder.exception;

import com.scriptmurder.enums.ResponseCode;
import lombok.Getter;

/**
 * 业务异常类
 * 用于处理业务逻辑中的异常情况
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Getter
public class BusinessException extends RuntimeException {
    
    /**
     * 错误码
     */
    private final Integer code;
    
    /**
     * 错误消息
     */
    private final String message;
    
    /**
     * 构造函数 - 使用响应码枚举
     */
    public BusinessException(ResponseCode responseCode) {
        super(responseCode.getMessage());
        this.code = responseCode.getCode();
        this.message = responseCode.getMessage();
    }
    
    /**
     * 构造函数 - 使用响应码枚举和自定义消息
     */
    public BusinessException(ResponseCode responseCode, String customMessage) {
        super(customMessage);
        this.code = responseCode.getCode();
        this.message = customMessage;
    }
    
    /**
     * 构造函数 - 自定义错误码和消息
     */
    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
    
    /**
     * 构造函数 - 默认业务错误
     */
    public BusinessException(String message) {
        super(message);
        this.code = ResponseCode.BUSINESS_ERROR.getCode();
        this.message = message;
    }
    
    /**
     * 构造函数 - 带原因的异常
     */
    public BusinessException(ResponseCode responseCode, Throwable cause) {
        super(responseCode.getMessage(), cause);
        this.code = responseCode.getCode();
        this.message = responseCode.getMessage();
    }
    
    /**
     * 构造函数 - 带原因的异常和自定义消息
     */
    public BusinessException(ResponseCode responseCode, String customMessage, Throwable cause) {
        super(customMessage, cause);
        this.code = responseCode.getCode();
        this.message = customMessage;
    }
    
    // ==================== 静态工厂方法 ====================
    
    /**
     * 创建用户不存在异常
     */
    public static BusinessException userNotFound() {
        return new BusinessException(ResponseCode.USER_NOT_FOUND);
    }
    
    /**
     * 创建用户已存在异常
     */
    public static BusinessException userAlreadyExists() {
        return new BusinessException(ResponseCode.USER_ALREADY_EXISTS);
    }
    
    /**
     * 创建剧本不存在异常
     */
    public static BusinessException scriptNotFound() {
        return new BusinessException(ResponseCode.SCRIPT_NOT_FOUND);
    }
    
    /**
     * 创建房间不存在异常
     */
    public static BusinessException lobbyNotFound() {
        return new BusinessException(ResponseCode.LOBBY_NOT_FOUND);
    }
    
    /**
     * 创建房间已满异常
     */
    public static BusinessException lobbyFull() {
        return new BusinessException(ResponseCode.LOBBY_FULL);
    }
    
    /**
     * 创建权限不足异常
     */
    public static BusinessException permissionDenied() {
        return new BusinessException(ResponseCode.PERMISSION_DENIED);
    }
    
    /**
     * 创建Token无效异常
     */
    public static BusinessException tokenInvalid() {
        return new BusinessException(ResponseCode.TOKEN_INVALID);
    }
    
    /**
     * 创建Token过期异常
     */
    public static BusinessException tokenExpired() {
        return new BusinessException(ResponseCode.TOKEN_EXPIRED);
    }
}
