package com.scriptmurder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.scriptmurder.entity.LoginHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 登录历史Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Mapper
public interface LoginHistoryMapper extends BaseMapper<LoginHistory> {

    /**
     * 分页查询用户登录历史
     */
    @Select({
        "SELECT * FROM tb_login_history ",
        "WHERE user_id = #{userId} ",
        "ORDER BY login_time DESC"
    })
    Page<LoginHistory> selectPageByUserId(Page<LoginHistory> page, @Param("userId") Long userId);

    /**
     * 查询用户最近的登录记录
     */
    @Select({
        "SELECT * FROM tb_login_history ",
        "WHERE user_id = #{userId} AND status = 1 ",
        "ORDER BY login_time DESC LIMIT #{limit}"
    })
    List<LoginHistory> selectRecentLoginsByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 查询用户当前在线的会话
     */
    @Select({
        "SELECT * FROM tb_login_history ",
        "WHERE user_id = #{userId} AND status = 1 AND logout_time IS NULL ",
        "ORDER BY login_time DESC"
    })
    List<LoginHistory> selectOnlineSessionsByUserId(@Param("userId") Long userId);

    /**
     * 更新登出时间和会话时长
     */
    @Update({
        "UPDATE tb_login_history SET ",
        "logout_time = #{logoutTime}, ",
        "session_duration = TIMESTAMPDIFF(SECOND, login_time, #{logoutTime}) ",
        "WHERE id = #{id}"
    })
    int updateLogoutInfo(@Param("id") Long id, @Param("logoutTime") LocalDateTime logoutTime);

    /**
     * 批量更新用户的在线会话为登出状态
     */
    @Update({
        "UPDATE tb_login_history SET ",
        "logout_time = NOW(), ",
        "session_duration = TIMESTAMPDIFF(SECOND, login_time, NOW()) ",
        "WHERE user_id = #{userId} AND status = 1 AND logout_time IS NULL"
    })
    int batchLogoutUserSessions(@Param("userId") Long userId);

    /**
     * 统计用户登录次数
     */
    @Select({
        "SELECT COUNT(*) FROM tb_login_history ",
        "WHERE user_id = #{userId} AND status = 1"
    })
    int countSuccessfulLoginsByUserId(@Param("userId") Long userId);

    /**
     * 统计用户登录失败次数
     */
    @Select({
        "SELECT COUNT(*) FROM tb_login_history ",
        "WHERE user_id = #{userId} AND status = 0"
    })
    int countFailedLoginsByUserId(@Param("userId") Long userId);

    /**
     * 查询指定时间范围内的登录统计
     */
    @Select({
        "SELECT ",
        "  DATE(login_time) as login_date,",
        "  COUNT(*) as total_logins,",
        "  COUNT(DISTINCT user_id) as unique_users,",
        "  SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as successful_logins,",
        "  SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as failed_logins",
        "FROM tb_login_history ",
        "WHERE login_time BETWEEN #{startTime} AND #{endTime} ",
        "GROUP BY DATE(login_time) ",
        "ORDER BY login_date DESC"
    })
    List<Map<String, Object>> selectLoginStatisticsByDateRange(
        @Param("startTime") LocalDateTime startTime, 
        @Param("endTime") LocalDateTime endTime
    );

    /**
     * 统计设备类型使用情况
     */
    @Select({
        "SELECT ",
        "  device_type,",
        "  COUNT(*) as login_count,",
        "  COUNT(DISTINCT user_id) as unique_users",
        "FROM tb_login_history ",
        "WHERE status = 1 AND device_type IS NOT NULL ",
        "GROUP BY device_type ",
        "ORDER BY login_count DESC"
    })
    List<Map<String, Object>> selectDeviceTypeStatistics();

    /**
     * 统计浏览器使用情况
     */
    @Select({
        "SELECT ",
        "  SUBSTRING_INDEX(browser, ' ', 1) as browser_name,",
        "  COUNT(*) as login_count,",
        "  COUNT(DISTINCT user_id) as unique_users",
        "FROM tb_login_history ",
        "WHERE status = 1 AND browser IS NOT NULL ",
        "GROUP BY SUBSTRING_INDEX(browser, ' ', 1) ",
        "ORDER BY login_count DESC ",
        "LIMIT #{limit}"
    })
    List<Map<String, Object>> selectBrowserStatistics(@Param("limit") Integer limit);

    /**
     * 查询用户平均会话时长
     */
    @Select({
        "SELECT ",
        "  user_id,",
        "  COUNT(*) as session_count,",
        "  AVG(session_duration) as avg_session_duration,",
        "  SUM(session_duration) as total_session_duration",
        "FROM tb_login_history ",
        "WHERE user_id = #{userId} AND status = 1 AND session_duration IS NOT NULL ",
        "GROUP BY user_id"
    })
    Map<String, Object> selectUserSessionStatistics(@Param("userId") Long userId);

    /**
     * 查询异常登录记录（可疑IP或设备）
     */
    @Select({
        "SELECT * FROM tb_login_history ",
        "WHERE user_id = #{userId} AND (",
        "  login_ip NOT IN (",
        "    SELECT DISTINCT login_ip FROM tb_login_history ",
        "    WHERE user_id = #{userId} AND status = 1 ",
        "    AND login_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)",
        "  ) OR device_type NOT IN (",
        "    SELECT DISTINCT device_type FROM tb_login_history ",
        "    WHERE user_id = #{userId} AND status = 1 ",
        "    AND login_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)",
        "  )",
        ") AND login_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) ",
        "ORDER BY login_time DESC"
    })
    List<LoginHistory> selectSuspiciousLogins(@Param("userId") Long userId);

    /**
     * 清理指定天数之前的登录历史
     */
    @Update({
        "DELETE FROM tb_login_history ",
        "WHERE login_time < DATE_SUB(NOW(), INTERVAL #{days} DAY)"
    })
    int cleanupOldRecords(@Param("days") Integer days);

    /**
     * 查询最活跃的用户（按登录次数）
     */
    @Select({
        "SELECT ",
        "  lh.user_id,",
        "  u.nick_name,",
        "  COUNT(*) as login_count,",
        "  MAX(lh.login_time) as last_login_time",
        "FROM tb_login_history lh ",
        "LEFT JOIN tb_user u ON lh.user_id = u.id ",
        "WHERE lh.status = 1 AND lh.login_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY) ",
        "GROUP BY lh.user_id, u.nick_name ",
        "ORDER BY login_count DESC ",
        "LIMIT #{limit}"
    })
    List<Map<String, Object>> selectMostActiveUsers(@Param("days") Integer days, @Param("limit") Integer limit);
}
