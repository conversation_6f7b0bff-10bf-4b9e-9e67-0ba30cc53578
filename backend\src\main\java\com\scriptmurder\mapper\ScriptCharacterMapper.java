package com.scriptmurder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.scriptmurder.entity.ScriptCharacter;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 剧本角色Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface ScriptCharacterMapper extends BaseMapper<ScriptCharacter> {

    /**
     * 根据剧本ID获取角色列表
     */
    @Select("SELECT * FROM tb_script_character WHERE script_id = #{scriptId} ORDER BY sort_order ASC, difficulty ASC")
    List<ScriptCharacter> selectByScriptId(@Param("scriptId") Long scriptId);
}