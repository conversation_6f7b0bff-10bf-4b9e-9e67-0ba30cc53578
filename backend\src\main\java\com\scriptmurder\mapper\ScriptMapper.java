package com.scriptmurder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.scriptmurder.entity.Script;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 剧本Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface ScriptMapper extends BaseMapper<Script> {

    /**
     * 获取热门剧本
     */
    @Select("SELECT * FROM tb_script " +
            "WHERE status = 1 " +
            "ORDER BY play_count DESC, average_rating DESC " +
            "LIMIT #{limit}")
    List<Script> selectHotScripts(@Param("limit") Integer limit);

    /**
     * 获取推荐剧本（基于用户偏好）
     */
    @Select("SELECT s.* FROM tb_script s " +
            "LEFT JOIN tb_user_favorite uf ON s.id = uf.item_id AND uf.item_type = 'script' AND uf.user_id = #{userId} " +
            "WHERE s.status = 1 AND uf.id IS NULL " +
            "ORDER BY s.average_rating DESC, s.play_count DESC " +
            "LIMIT #{limit}")
    List<Script> selectRecommendedScripts(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 根据类型获取剧本数量
     */
    @Select("SELECT COUNT(*) FROM tb_script WHERE category = #{category} AND status = 1")
    Integer countByCategory(@Param("category") String category);

    /**
     * 更新剧本统计信息
     */
    void updateScriptStats(@Param("scriptId") Long scriptId);

    /**
     * 获取在指定时间之后更新的剧本
     */
    @Select("SELECT * FROM tb_script WHERE update_time > #{updateTime}")
    List<Script> selectUpdatedAfter(@Param("updateTime") LocalDateTime updateTime);
}