package com.scriptmurder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.scriptmurder.entity.ScriptReview;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 剧本评价Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface ScriptReviewMapper extends BaseMapper<ScriptReview> {

    /**
     * 获取剧本的最新评价
     */
    @Select("SELECT * FROM tb_script_review " +
            "WHERE script_id = #{scriptId} AND status = 1 " +
            "ORDER BY create_time DESC " +
            "LIMIT #{limit}")
    List<ScriptReview> selectRecentReviews(@Param("scriptId") Long scriptId, @Param("limit") Integer limit);

    /**
     * 获取用户对剧本的评价
     */
    @Select("SELECT * FROM tb_script_review " +
            "WHERE script_id = #{scriptId} AND user_id = #{userId}")
    ScriptReview selectByScriptIdAndUserId(@Param("scriptId") Long scriptId, @Param("userId") Long userId);
}