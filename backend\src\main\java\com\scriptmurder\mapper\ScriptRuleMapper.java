package com.scriptmurder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.scriptmurder.entity.ScriptRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 剧本规则Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface ScriptRuleMapper extends BaseMapper<ScriptRule> {

    /**
     * 根据剧本ID获取规则列表
     */
    @Select("SELECT * FROM tb_script_rule WHERE script_id = #{scriptId} ORDER BY sort_order ASC")
    List<ScriptRule> selectByScriptId(@Param("scriptId") Long scriptId);
}