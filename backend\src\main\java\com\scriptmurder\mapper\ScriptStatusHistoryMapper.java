package com.scriptmurder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.scriptmurder.dto.ScriptStatusHistoryDTO;
import com.scriptmurder.entity.ScriptStatusHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 剧本状态历史Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface ScriptStatusHistoryMapper extends BaseMapper<ScriptStatusHistory> {

    /**
     * 分页查询剧本状态变更历史
     */
    @Select("SELECT h.*, s.title as script_title, u.nick_name as operator_name " +
            "FROM tb_script_status_history h " +
            "LEFT JOIN tb_script s ON h.script_id = s.id " +
            "LEFT JOIN tb_user u ON h.operator_id = u.id " +
            "WHERE h.script_id = #{scriptId} " +
            "ORDER BY h.create_time DESC")
    IPage<ScriptStatusHistoryDTO> selectHistoryByScriptId(Page<ScriptStatusHistoryDTO> page, 
                                                         @Param("scriptId") Long scriptId);

    /**
     * 查询状态统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as total_count, " +
            "SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as published_count, " +
            "SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as unpublished_count, " +
            "SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as reviewing_count, " +
            "SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) as rejected_count, " +
            "SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) as draft_count " +
            "FROM tb_script")
    Map<String, Object> selectStatusStats();

    /**
     * 查询今日新增剧本数
     */
    @Select("SELECT COUNT(*) FROM tb_script WHERE DATE(create_time) = CURDATE()")
    Long selectTodayNewScripts();

    /**
     * 查询今日审核数量
     */
    @Select("SELECT COUNT(*) FROM tb_script_status_history " +
            "WHERE DATE(create_time) = CURDATE() " +
            "AND to_status IN (1, 4)")
    Long selectTodayReviewedCount();

    /**
     * 查询平均审核时长
     */
    @Select("SELECT AVG(TIMESTAMPDIFF(HOUR, create_time, review_time)) " +
            "FROM tb_script " +
            "WHERE review_time IS NOT NULL " +
            "AND create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)")
    Double selectAvgReviewHours();

    /**
     * 查询审核通过率
     */
    @Select("SELECT " +
            "ROUND(" +
            "  SUM(CASE WHEN to_status = 1 THEN 1 ELSE 0 END) * 100.0 / " +
            "  SUM(CASE WHEN to_status IN (1, 4) THEN 1 ELSE 0 END), 2" +
            ") as approval_rate " +
            "FROM tb_script_status_history " +
            "WHERE to_status IN (1, 4) " +
            "AND create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)")
    Double selectApprovalRate();

    /**
     * 查询最近7天状态变更趋势
     */
    @Select("SELECT " +
            "DATE(create_time) as date, " +
            "SUM(CASE WHEN to_status = 3 THEN 1 ELSE 0 END) as submitted_count, " +
            "SUM(CASE WHEN to_status IN (1, 4) THEN 1 ELSE 0 END) as reviewed_count, " +
            "SUM(CASE WHEN to_status = 1 THEN 1 ELSE 0 END) as approved_count, " +
            "SUM(CASE WHEN to_status = 4 THEN 1 ELSE 0 END) as rejected_count " +
            "FROM tb_script_status_history " +
            "WHERE create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) " +
            "GROUP BY DATE(create_time) " +
            "ORDER BY date DESC")
    List<Map<String, Object>> selectRecentTrends();

    /**
     * 查询审核效率统计
     */
    @Select("SELECT " +
            "DATE(create_time) as date, " +
            "COUNT(*) as submitted_count, " +
            "COUNT(CASE WHEN to_status IN (1, 4) THEN 1 END) as reviewed_count, " +
            "ROUND(AVG(CASE " +
            "  WHEN review_duration IS NOT NULL THEN review_duration " +
            "END), 2) as avg_review_minutes " +
            "FROM tb_script_status_history h " +
            "LEFT JOIN tb_script s ON h.script_id = s.id " +
            "WHERE h.create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY) " +
            "GROUP BY DATE(h.create_time) " +
            "ORDER BY date DESC")
    List<Map<String, Object>> selectReviewEfficiencyStats(@Param("days") Integer days);

    /**
     * 查询用户剧本状态分布
     */
    @Select("SELECT " +
            "status, " +
            "COUNT(*) as count " +
            "FROM tb_script " +
            "WHERE creator_id = #{userId} " +
            "GROUP BY status")
    List<Map<String, Object>> selectUserScriptStatusDistribution(@Param("userId") Long userId);

    /**
     * 查询状态变更统计
     */
    @Select("SELECT " +
            "DATE(create_time) as date, " +
            "from_status, " +
            "to_status, " +
            "COUNT(*) as count " +
            "FROM tb_script_status_history " +
            "WHERE create_time BETWEEN #{startDate} AND #{endDate} " +
            "GROUP BY DATE(create_time), from_status, to_status " +
            "ORDER BY date DESC, from_status, to_status")
    List<Map<String, Object>> selectStatusChangeStats(@Param("startDate") LocalDateTime startDate, 
                                                     @Param("endDate") LocalDateTime endDate);

    /**
     * 查询最近的状态变更记录
     */
    @Select("SELECT * FROM tb_script_status_history " +
            "WHERE script_id = #{scriptId} " +
            "ORDER BY create_time DESC " +
            "LIMIT 1")
    ScriptStatusHistory selectLatestByScriptId(@Param("scriptId") Long scriptId);

    /**
     * 批量插入状态变更记录
     */
    int insertBatch(@Param("histories") List<ScriptStatusHistory> histories);
}
