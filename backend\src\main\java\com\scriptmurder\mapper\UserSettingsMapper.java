package com.scriptmurder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.scriptmurder.entity.UserSettings;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 用户设置Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Mapper
public interface UserSettingsMapper extends BaseMapper<UserSettings> {

    /**
     * 根据用户ID查询设置
     */
    @Select("SELECT * FROM tb_user_settings WHERE user_id = #{userId}")
    UserSettings selectByUserId(@Param("userId") Long userId);

    /**
     * 统计主题使用情况
     */
    @Select("SELECT theme, COUNT(*) as count FROM tb_user_settings GROUP BY theme")
    List<Map<String, Object>> selectThemeStatistics();

    /**
     * 统计隐私级别分布
     */
    @Select("SELECT privacy_level, COUNT(*) as count FROM tb_user_settings GROUP BY privacy_level")
    List<Map<String, Object>> selectPrivacyLevelStatistics();

    /**
     * 统计通知设置情况
     */
    @Select({
        "SELECT ",
        "  SUM(email_notifications) as email_enabled,",
        "  SUM(system_notifications) as system_enabled,",
        "  SUM(activity_notifications) as activity_enabled,",
        "  SUM(social_notifications) as social_enabled,",
        "  COUNT(*) as total_users",
        "FROM tb_user_settings"
    })
    Map<String, Object> selectNotificationStatistics();

    /**
     * 查询启用了特定通知类型的用户ID列表
     */
    @Select({
        "SELECT user_id FROM tb_user_settings WHERE ",
        "CASE #{notificationType}",
        "  WHEN 'email' THEN email_notifications = 1",
        "  WHEN 'system' THEN system_notifications = 1", 
        "  WHEN 'activity' THEN activity_notifications = 1",
        "  WHEN 'social' THEN social_notifications = 1",
        "  ELSE 1=0",
        "END"
    })
    List<Long> selectUserIdsByNotificationType(@Param("notificationType") String notificationType);

    /**
     * 批量更新特定字段
     * 注意：这个方法需要在Service层中根据fieldName动态构建SQL
     */
    int batchUpdateField(@Param("fieldName") String fieldName,
                        @Param("fieldValue") String fieldValue,
                        @Param("userIds") String userIds);

    /**
     * 查询使用特定主题的用户数量
     */
    @Select("SELECT COUNT(*) FROM tb_user_settings WHERE theme = #{theme}")
    int countByTheme(@Param("theme") String theme);

    /**
     * 查询特定隐私级别的用户数量
     */
    @Select("SELECT COUNT(*) FROM tb_user_settings WHERE privacy_level = #{privacyLevel}")
    int countByPrivacyLevel(@Param("privacyLevel") Integer privacyLevel);

    /**
     * 查询启用了所有通知的用户数量
     */
    @Select({
        "SELECT COUNT(*) FROM tb_user_settings WHERE ",
        "email_notifications = 1 AND system_notifications = 1 AND ",
        "activity_notifications = 1 AND social_notifications = 1"
    })
    int countUsersWithAllNotificationsEnabled();

    /**
     * 查询禁用了所有通知的用户数量
     */
    @Select({
        "SELECT COUNT(*) FROM tb_user_settings WHERE ",
        "email_notifications = 0 AND system_notifications = 0 AND ",
        "activity_notifications = 0 AND social_notifications = 0"
    })
    int countUsersWithAllNotificationsDisabled();

    /**
     * 查询最近更新设置的用户
     */
    @Select({
        "SELECT us.*, u.nick_name FROM tb_user_settings us ",
        "LEFT JOIN tb_user u ON us.user_id = u.id ",
        "ORDER BY us.update_time DESC LIMIT #{limit}"
    })
    List<Map<String, Object>> selectRecentlyUpdatedSettings(@Param("limit") Integer limit);
}
