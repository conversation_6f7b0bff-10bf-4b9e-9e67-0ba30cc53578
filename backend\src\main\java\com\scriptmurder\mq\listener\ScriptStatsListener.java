package com.scriptmurder.mq.listener;

import com.scriptmurder.config.RabbitMQConfig;
import com.scriptmurder.mq.message.ScriptStatsMessage;
import com.scriptmurder.service.IScriptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

/**
 * 剧本统计消息监听器
 *
 * <AUTHOR>
 */
@Component
@RabbitListener(queues = RabbitMQConfig.SCRIPT_STATS_QUEUE)
@Slf4j
public class ScriptStatsListener {

    @Autowired
    private IScriptService scriptService;

    @RabbitHandler
    public void handleScriptStats(ScriptStatsMessage message, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        try {
            log.info("接收到剧本统计消息: scriptId={}, statsType={}, delta={}", 
                message.getScriptId(), message.getStatsType(), message.getDelta());
            
            // 更新剧本统计信息
            scriptService.updateScriptStats(message.getScriptId());
            
            log.info("剧本统计更新成功: {}", message.getScriptId());
        } catch (Exception e) {
            log.error("处理剧本统计消息失败: scriptId={}", message.getScriptId(), e);
            // 统计更新失败不影响主流程，只记录日志
        }
    }
}