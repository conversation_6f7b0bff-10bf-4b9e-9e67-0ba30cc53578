package com.scriptmurder.mq.listener;

import com.scriptmurder.config.RabbitMQConfig;
import com.scriptmurder.mq.message.ScriptSyncMessage;
import com.scriptmurder.service.IScriptSearchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

/**
 * 剧本同步消息监听器
 *
 * <AUTHOR>
 */
@Component
@RabbitListener(queues = RabbitMQConfig.SCRIPT_SYNC_QUEUE)
@Slf4j
public class ScriptSyncListener {

    @Autowired
    private IScriptSearchService scriptSearchService;

    @RabbitHandler
    public void handleScriptSync(ScriptSyncMessage message, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        try {
            log.info("接收到剧本同步消息: action={}, scriptId={}", message.getAction(), message.getScriptId());
            
            switch (message.getAction()) {
                case "CREATE":
                case "UPDATE":
                    if (message.getScript() != null) {
                        scriptSearchService.syncScriptToEs(message.getScript());
                        log.info("剧本同步到ES成功: {}", message.getScriptId());
                    } else {
                        log.warn("剧本对象为空，跳过同步: {}", message.getScriptId());
                    }
                    break;
                case "DELETE":
                    scriptSearchService.deleteScriptFromEs(message.getScriptId());
                    log.info("从ES删除剧本成功: {}", message.getScriptId());
                    break;
                default:
                    log.warn("未知的同步操作: {}", message.getAction());
            }
        } catch (Exception e) {
            log.error("处理剧本同步消息失败: scriptId={}", message.getScriptId(), e);
            // 这里可以根据业务需求决定是否重新抛出异常来触发重试机制
            throw new RuntimeException("剧本同步处理失败", e);
        }
    }
}