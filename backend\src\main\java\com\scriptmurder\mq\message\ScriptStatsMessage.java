package com.scriptmurder.mq.message;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 剧本统计消息
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ScriptStatsMessage {

    /**
     * 剧本ID
     */
    private Long scriptId;

    /**
     * 统计类型：play_count, review_count, rating
     */
    private String statsType;

    /**
     * 变化值
     */
    private Integer delta;

    /**
     * 消息时间戳
     */
    private LocalDateTime timestamp;

    public static ScriptStatsMessage playCountIncrement(Long scriptId) {
        return new ScriptStatsMessage(scriptId, "play_count", 1, LocalDateTime.now());
    }

    public static ScriptStatsMessage reviewCountIncrement(Long scriptId) {
        return new ScriptStatsMessage(scriptId, "review_count", 1, LocalDateTime.now());
    }

    public static ScriptStatsMessage ratingUpdate(Long scriptId) {
        return new ScriptStatsMessage(scriptId, "rating", 0, LocalDateTime.now());
    }
}