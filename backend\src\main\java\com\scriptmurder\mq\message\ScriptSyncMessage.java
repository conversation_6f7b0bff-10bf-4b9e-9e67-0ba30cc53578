package com.scriptmurder.mq.message;

import com.scriptmurder.entity.Script;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 剧本同步消息
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ScriptSyncMessage {

    /**
     * 操作类型：CREATE, UPDATE, DELETE
     */
    private String action;

    /**
     * 剧本ID
     */
    private Long scriptId;

    /**
     * 剧本对象（删除操作时为空）
     */
    private Script script;

    /**
     * 消息时间戳
     */
    private LocalDateTime timestamp;

    public static ScriptSyncMessage create(Script script) {
        return new ScriptSyncMessage("CREATE", script.getId(), script, LocalDateTime.now());
    }

    public static ScriptSyncMessage update(Script script) {
        return new ScriptSyncMessage("UPDATE", script.getId(), script, LocalDateTime.now());
    }

    public static ScriptSyncMessage delete(Long scriptId) {
        return new ScriptSyncMessage("DELETE", scriptId, null, LocalDateTime.now());
    }
}