package com.scriptmurder.mq.producer;

import com.scriptmurder.config.RabbitMQConfig;
import com.scriptmurder.mq.message.ScriptStatsMessage;
import com.scriptmurder.mq.message.ScriptSyncMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 剧本消息发送服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ScriptMessageProducer {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    /**
     * 发送剧本同步消息
     */
    public void sendScriptSyncMessage(ScriptSyncMessage message) {
        try {
            rabbitTemplate.convertAndSend(
                RabbitMQConfig.SCRIPT_EXCHANGE,
                RabbitMQConfig.SCRIPT_SYNC_ROUTING_KEY,
                message
            );
            log.info("发送剧本同步消息成功: action={}, scriptId={}", message.getAction(), message.getScriptId());
        } catch (Exception e) {
            log.error("发送剧本同步消息失败: scriptId={}", message.getScriptId(), e);
        }
    }

    /**
     * 发送剧本统计消息
     */
    public void sendScriptStatsMessage(ScriptStatsMessage message) {
        try {
            rabbitTemplate.convertAndSend(
                RabbitMQConfig.SCRIPT_EXCHANGE,
                RabbitMQConfig.SCRIPT_STATS_ROUTING_KEY,
                message
            );
            log.info("发送剧本统计消息成功: scriptId={}, statsType={}", 
                message.getScriptId(), message.getStatsType());
        } catch (Exception e) {
            log.error("发送剧本统计消息失败: scriptId={}", message.getScriptId(), e);
        }
    }
}