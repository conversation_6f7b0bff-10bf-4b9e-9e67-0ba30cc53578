package com.scriptmurder.search.document;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 剧本搜索文档
 *
 * <AUTHOR>
 */
@Data
@Document(indexName = "script_murder_scripts")
@Setting(settingPath = "elasticsearch/script-settings.json")
@Mapping(mappingPath = "elasticsearch/script-mapping.json")
public class ScriptSearchDocument {

    @Id
    private String id;

    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String title;

    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String description;

    @Field(type = FieldType.Keyword)
    private String category;

    @Field(type = FieldType.Integer)
    private Integer playerCountMin;

    @Field(type = FieldType.Integer)
    private Integer playerCountMax;

    @Field(type = FieldType.Keyword)
    private String duration;

    @Field(type = FieldType.Integer)
    private Integer difficulty;

    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private List<String> tags;

    @Field(type = FieldType.Double)
    private Double price;

    @Field(type = FieldType.Integer)
    private Integer status;

    @Field(type = FieldType.Double)
    private Double averageRating;

    @Field(type = FieldType.Integer)
    private Integer reviewCount;

    @Field(type = FieldType.Integer)
    private Integer playCount;

    @Field(type = FieldType.Text)
    private String coverImage;

    @Field(type = FieldType.Date, format = DateFormat.date_time)
    private LocalDateTime createTime;

    @Field(type = FieldType.Date, format = DateFormat.date_time)
    private LocalDateTime updateTime;

    /**
     * 从Script实体转换为搜索文档
     */
    public static ScriptSearchDocument fromScript(com.scriptmurder.entity.Script script) {
        ScriptSearchDocument document = new ScriptSearchDocument();
        document.setId(script.getId().toString());
        document.setTitle(script.getTitle());
        document.setDescription(script.getDescription());
        document.setCategory(script.getCategory());
        document.setPlayerCountMin(script.getPlayerCountMin());
        document.setPlayerCountMax(script.getPlayerCountMax());
        document.setDuration(script.getDuration());
        document.setDifficulty(script.getDifficulty());
        
        // 处理标签
        if (script.getTags() != null && !script.getTags().isEmpty()) {
            document.setTags(List.of(script.getTags().split(",")));
        }
        
        document.setPrice(script.getPrice() != null ? script.getPrice().doubleValue() : null);
        document.setStatus(script.getStatus());
        document.setAverageRating(script.getAverageRating() != null ? script.getAverageRating().doubleValue() : null);
        document.setReviewCount(script.getReviewCount());
        document.setPlayCount(script.getPlayCount());
        document.setCoverImage(script.getCoverImage());
        document.setCreateTime(script.getCreateTime());
        document.setUpdateTime(script.getUpdateTime());
        
        return document;
    }
}