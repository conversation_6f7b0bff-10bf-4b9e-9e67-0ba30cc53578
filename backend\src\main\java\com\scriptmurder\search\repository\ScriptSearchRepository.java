package com.scriptmurder.search.repository;

import com.scriptmurder.search.document.ScriptSearchDocument;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 剧本搜索仓库
 *
 * <AUTHOR>
 */
@Repository
public interface ScriptSearchRepository extends ElasticsearchRepository<ScriptSearchDocument, String> {

    /**
     * 根据分类查找
     */
    List<ScriptSearchDocument> findByCategory(String category);

    /**
     * 根据状态查找
     */
    List<ScriptSearchDocument> findByStatus(Integer status);

    /**
     * 根据难度查找
     */
    List<ScriptSearchDocument> findByDifficulty(Integer difficulty);
}