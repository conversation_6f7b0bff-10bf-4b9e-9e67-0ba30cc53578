package com.scriptmurder.service;

import com.scriptmurder.dto.PageResponse;
import com.scriptmurder.dto.ScriptDTO;
import com.scriptmurder.dto.CategoryStatsDTO;

import java.util.List;

/**
 * 高级搜索服务接口
 *
 * <AUTHOR>
 */
public interface IAdvancedSearchService {

    /**
     * 高级搜索剧本
     *
     * @param searchParams 搜索参数
     * @return 搜索结果
     */
    PageResponse<ScriptDTO> advancedSearch(ScriptSearchParams searchParams);

    /**
     * 获取分类统计信息
     *
     * @return 分类统计列表
     */
    List<CategoryStatsDTO> getCategoryStats();

    /**
     * 获取热门搜索关键词
     *
     * @param limit 限制数量
     * @return 热门关键词列表
     */
    List<String> getHotSearchKeywords(int limit);

    /**
     * 记录搜索关键词
     *
     * @param keyword 关键词
     */
    void recordSearchKeyword(String keyword);

    /**
     * 获取推荐搜索词
     *
     * @param keyword 输入关键词
     * @param limit 限制数量
     * @return 推荐搜索词列表
     */
    List<String> getSuggestedKeywords(String keyword, int limit);
}