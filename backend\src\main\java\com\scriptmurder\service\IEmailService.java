package com.scriptmurder.service;

/**
 * 邮件服务接口
 */
public interface IEmailService {
    
    /**
     * 发送验证码邮件
     * @param email 邮箱地址
     * @param code 验证码
     * @return 是否发送成功
     */
    boolean sendVerificationCode(String email, String code);
    
    /**
     * 发送普通邮件
     * @param to 收件人
     * @param subject 主题
     * @param content 内容
     * @return 是否发送成功
     */
    boolean sendSimpleMail(String to, String subject, String content);
}
