package com.scriptmurder.service;

import com.scriptmurder.dto.Result;
import com.scriptmurder.entity.Follow;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
public interface IFollowService extends IService<Follow> {

    /**
     *
     * @param id
     * @param isFollow
     * @return
     */
    Result follow(Long id, Boolean isFollow);

    /**
     *
     * @param followUserId
     * @return
     */
    Result isFollow(Long followUserId);

    Result commonFollow(Long id);
}
