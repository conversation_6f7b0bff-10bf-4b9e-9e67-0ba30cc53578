package com.scriptmurder.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scriptmurder.dto.LoginHistoryDTO;
import com.scriptmurder.entity.LoginHistory;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 登录历史服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
public interface ILoginHistoryService extends IService<LoginHistory> {
    
    /**
     * 记录登录历史
     * @param userId 用户ID
     * @param request HTTP请求对象
     * @return 是否成功
     */
    boolean recordLogin(Long userId, HttpServletRequest request);

    /**
     * 记录登录失败
     * @param userId 用户ID（可能为空）
     * @param request HTTP请求对象
     * @param failureReason 失败原因
     * @return 是否成功
     */
    boolean recordLoginFailure(Long userId, HttpServletRequest request, String failureReason);
    
    /**
     * 记录登出
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean recordLogout(Long userId);

    /**
     * 批量登出用户的所有在线会话
     * @param userId 用户ID
     * @return 登出的会话数
     */
    int logoutAllUserSessions(Long userId);
    
    /**
     * 获取用户登录历史（分页）
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 分页结果
     */
    Page<LoginHistoryDTO> getLoginHistory(Long userId, Integer page, Integer size);

    /**
     * 获取用户最近的登录记录
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 登录记录列表
     */
    List<LoginHistoryDTO> getRecentLogins(Long userId, Integer limit);

    /**
     * 获取用户当前在线的会话
     * @param userId 用户ID
     * @return 在线会话列表
     */
    List<LoginHistoryDTO> getOnlineSessions(Long userId);
    
    /**
     * 清除用户登录历史
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean clearLoginHistory(Long userId);

    /**
     * 清除指定天数之前的历史记录
     * @param days 天数
     * @return 清除的记录数
     */
    int cleanupOldRecords(Integer days);

    /**
     * 获取用户登录统计信息
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> getUserLoginStatistics(Long userId);

    /**
     * 获取指定时间范围内的登录统计
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息列表
     */
    List<Map<String, Object>> getLoginStatisticsByDateRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取设备类型使用统计
     * @return 设备统计信息
     */
    List<Map<String, Object>> getDeviceTypeStatistics();

    /**
     * 获取浏览器使用统计
     * @param limit 限制数量
     * @return 浏览器统计信息
     */
    List<Map<String, Object>> getBrowserStatistics(Integer limit);

    /**
     * 获取用户会话统计
     * @param userId 用户ID
     * @return 会话统计信息
     */
    Map<String, Object> getUserSessionStatistics(Long userId);

    /**
     * 检测可疑登录
     * @param userId 用户ID
     * @return 可疑登录记录
     */
    List<LoginHistoryDTO> detectSuspiciousLogins(Long userId);

    /**
     * 获取最活跃的用户
     * @param days 统计天数
     * @param limit 限制数量
     * @return 活跃用户列表
     */
    List<Map<String, Object>> getMostActiveUsers(Integer days, Integer limit);

    /**
     * 检查用户是否在线
     * @param userId 用户ID
     * @return 是否在线
     */
    boolean isUserOnline(Long userId);

    /**
     * 获取在线用户数量
     * @return 在线用户数
     */
    long getOnlineUserCount();

    /**
     * 获取用户最后登录时间
     * @param userId 用户ID
     * @return 最后登录时间
     */
    LocalDateTime getLastLoginTime(Long userId);

    /**
     * 获取用户登录次数
     * @param userId 用户ID
     * @return 登录次数
     */
    int getUserLoginCount(Long userId);

    /**
     * 获取用户登录失败次数
     * @param userId 用户ID
     * @return 失败次数
     */
    int getUserLoginFailureCount(Long userId);

    /**
     * 检查IP是否被频繁登录失败
     * @param ip IP地址
     * @param timeWindow 时间窗口（分钟）
     * @param maxAttempts 最大尝试次数
     * @return 是否被限制
     */
    boolean isIpBlocked(String ip, Integer timeWindow, Integer maxAttempts);

    /**
     * 获取用户常用设备列表
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 常用设备列表
     */
    List<Map<String, Object>> getUserCommonDevices(Long userId, Integer limit);

    /**
     * 获取用户常用登录地点
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 常用地点列表
     */
    List<Map<String, Object>> getUserCommonLocations(Long userId, Integer limit);

    /**
     * 生成登录安全报告
     * @param userId 用户ID
     * @param days 统计天数
     * @return 安全报告
     */
    Map<String, Object> generateSecurityReport(Long userId, Integer days);

    /**
     * 导出用户登录历史
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 导出数据
     */
    List<LoginHistoryDTO> exportLoginHistory(Long userId, LocalDateTime startTime, LocalDateTime endTime);
}
