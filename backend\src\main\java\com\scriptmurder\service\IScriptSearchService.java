package com.scriptmurder.service;

import com.scriptmurder.dto.PageResponse;
import com.scriptmurder.dto.ScriptDTO;
import com.scriptmurder.dto.ScriptSearchDTO;
import com.scriptmurder.entity.Script;

import java.util.List;

/**
 * 剧本搜索服务接口
 *
 * <AUTHOR>
 */
public interface IScriptSearchService {

    /**
     * 搜索剧本
     */
    PageResponse<ScriptDTO> searchScripts(ScriptSearchDTO searchDTO);

    /**
     * 同步剧本到搜索引擎
     */
    void syncScriptToEs(Script script);

    /**
     * 批量同步剧本
     */
    void batchSyncScripts(List<Script> scripts);

    /**
     * 删除搜索文档
     */
    void deleteScriptFromEs(Long scriptId);

    /**
     * 获取搜索建议
     */
    List<String> getSearchSuggestions(String keyword);

    /**
     * 获取热门搜索
     */
    List<String> getHotSearchKeywords();

    /**
     * 初始化索引
     */
    void initializeIndex();

    /**
     * 重建索引
     */
    void rebuildIndex();
}