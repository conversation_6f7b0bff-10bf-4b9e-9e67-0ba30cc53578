package com.scriptmurder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.scriptmurder.dto.*;
import com.scriptmurder.entity.Script;

import java.util.List;

/**
 * 剧本服务接口
 *
 * <AUTHOR>
 */
public interface IScriptService extends IService<Script> {

    /**
     * 分页查询剧本列表
     */
    PageResponse<ScriptDTO> getScriptList(ScriptSearchDTO searchDTO);

    /**
     * 获取剧本详情
     */
    ScriptDetailDTO getScriptDetail(Long id);

    /**
     * 搜索剧本
     */
    PageResponse<ScriptDTO> searchScripts(ScriptSearchDTO searchDTO);

    /**
     * 获取热门剧本
     */
    List<ScriptDTO> getPopularScripts(Integer limit);

    /**
     * 获取推荐剧本
     */
    List<ScriptDTO> getRecommendedScripts(Long userId, Integer limit);

    /**
     * 更新剧本统计信息
     */
    void updateScriptStats(Long scriptId);

    /**
     * 获取剧本分类统计
     */
    List<CategoryStatsDTO> getCategoryStats();
}