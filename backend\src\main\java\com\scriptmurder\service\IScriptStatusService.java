package com.scriptmurder.service;

import com.scriptmurder.dto.PageResponse;
import com.scriptmurder.dto.ScriptStatusHistoryDTO;
import com.scriptmurder.dto.ScriptStatusStatsDTO;
import com.scriptmurder.entity.Script;

import java.util.List;
import java.util.Map;

/**
 * 剧本状态管理服务接口
 *
 * <AUTHOR>
 */
public interface IScriptStatusService {

    /**
     * 提交剧本审核
     *
     * @param scriptId 剧本ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean submitForReview(Long scriptId, Long userId);

    /**
     * 审核剧本
     *
     * @param scriptId 剧本ID
     * @param reviewerId 审核员ID
     * @param approved 是否通过
     * @param comment 审核意见
     * @return 是否成功
     */
    boolean reviewScript(Long scriptId, Long reviewerId, boolean approved, String comment);

    /**
     * 上架/下架剧本
     *
     * @param scriptId 剧本ID
     * @param operatorId 操作员ID
     * @param publish 是否上架
     * @param reason 操作原因
     * @return 是否成功
     */
    boolean togglePublishStatus(Long scriptId, Long operatorId, boolean publish, String reason);

    /**
     * 批量审核剧本
     *
     * @param scriptIds 剧本ID列表
     * @param reviewerId 审核员ID
     * @param approved 是否通过
     * @param comment 审核意见
     * @return 成功处理的数量
     */
    int batchReviewScripts(List<Long> scriptIds, Long reviewerId, boolean approved, String comment);

    /**
     * 批量上下架剧本
     *
     * @param scriptIds 剧本ID列表
     * @param operatorId 操作员ID
     * @param publish 是否上架
     * @param reason 操作原因
     * @return 成功处理的数量
     */
    int batchTogglePublishStatus(List<Long> scriptIds, Long operatorId, boolean publish, String reason);

    /**
     * 获取待审核剧本列表
     *
     * @param page 页码
     * @param size 每页数量
     * @return 分页结果
     */
    PageResponse<Script> getPendingReviewScripts(Integer page, Integer size);

    /**
     * 获取剧本状态变更历史
     *
     * @param scriptId 剧本ID
     * @param page 页码
     * @param size 每页数量
     * @return 分页结果
     */
    PageResponse<ScriptStatusHistoryDTO> getStatusHistory(Long scriptId, Integer page, Integer size);

    /**
     * 获取剧本状态统计
     *
     * @return 状态统计信息
     */
    ScriptStatusStatsDTO getStatusStats();

    /**
     * 获取审核效率统计
     *
     * @param days 统计天数
     * @return 审核效率数据
     */
    Map<String, Object> getReviewEfficiencyStats(Integer days);

    /**
     * 验证剧本是否可以进行状态转换
     *
     * @param scriptId 剧本ID
     * @param targetStatus 目标状态
     * @return 是否可以转换
     */
    boolean canTransitionToStatus(Long scriptId, Integer targetStatus);

    /**
     * 获取剧本的可用操作列表
     *
     * @param scriptId 剧本ID
     * @param userId 用户ID
     * @return 可用操作列表
     */
    List<String> getAvailableActions(Long scriptId, Long userId);

    /**
     * 自动审核剧本（基于规则）
     *
     * @param scriptId 剧本ID
     * @return 是否自动审核成功
     */
    boolean autoReviewScript(Long scriptId);

    /**
     * 获取用户的剧本状态分布
     *
     * @param userId 用户ID
     * @return 状态分布统计
     */
    Map<String, Integer> getUserScriptStatusDistribution(Long userId);

    /**
     * 发送状态变更通知
     *
     * @param scriptId 剧本ID
     * @param fromStatus 原状态
     * @param toStatus 新状态
     * @param operatorId 操作员ID
     * @param comment 备注
     */
    void sendStatusChangeNotification(Long scriptId, Integer fromStatus, Integer toStatus, 
                                    Long operatorId, String comment);

    /**
     * 检查剧本信息完整性
     *
     * @param scriptId 剧本ID
     * @return 检查结果
     */
    Map<String, Object> validateScriptCompleteness(Long scriptId);

    /**
     * 获取状态变更统计
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据
     */
    Map<String, Object> getStatusChangeStats(String startDate, String endDate);
}
