package com.scriptmurder.service;

import java.util.List;
import java.util.Map;

/**
 * 搜索历史服务接口
 *
 * <AUTHOR>
 */
public interface ISearchHistoryService {

    /**
     * 记录用户搜索历史
     *
     * @param userId 用户ID
     * @param keyword 搜索关键词
     * @param category 搜索分类
     * @param resultCount 搜索结果数量
     */
    void recordSearchHistory(Long userId, String keyword, String category, Integer resultCount);

    /**
     * 获取用户搜索历史
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 搜索历史列表
     */
    List<String> getUserSearchHistory(Long userId, Integer limit);

    /**
     * 清除用户搜索历史
     *
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean clearUserSearchHistory(Long userId);

    /**
     * 获取用户搜索统计
     *
     * @param userId 用户ID
     * @return 搜索统计信息
     */
    Map<String, Object> getUserSearchStats(Long userId);

    /**
     * 获取全局搜索趋势
     *
     * @param days 天数
     * @return 搜索趋势数据
     */
    Map<String, Object> getSearchTrends(Integer days);

    /**
     * 获取搜索性能统计
     *
     * @return 性能统计数据
     */
    Map<String, Object> getSearchPerformance();

    /**
     * 获取相关搜索推荐
     *
     * @param keyword 当前关键词
     * @param limit 限制数量
     * @return 相关搜索列表
     */
    List<String> getRelatedSearches(String keyword, Integer limit);

    /**
     * 获取搜索自动完成建议
     *
     * @param prefix 前缀
     * @param limit 限制数量
     * @return 自动完成建议
     */
    List<String> getAutoCompleteSuggestions(String prefix, Integer limit);

    /**
     * 记录搜索点击行为
     *
     * @param userId 用户ID
     * @param keyword 搜索关键词
     * @param scriptId 点击的剧本ID
     * @param position 点击位置
     */
    void recordSearchClick(Long userId, String keyword, Long scriptId, Integer position);

    /**
     * 获取搜索无结果的关键词
     *
     * @param limit 限制数量
     * @return 无结果关键词列表
     */
    List<String> getNoResultKeywords(Integer limit);
}
