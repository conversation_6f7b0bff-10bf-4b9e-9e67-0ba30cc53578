package com.scriptmurder.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scriptmurder.dto.FavoriteDTO;
import com.scriptmurder.entity.UserFavorite;

/**
 * <p>
 * 用户收藏表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-31
 */
public interface IUserFavoriteService extends IService<UserFavorite> {

    /**
     * 获取用户收藏列表
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页数量
     * @param type 收藏类型
     * @return 收藏列表
     */
    Page<FavoriteDTO> getUserFavorites(Long userId, Integer page, Integer size, String type);

    /**
     * 添加收藏
     * @param userId 用户ID
     * @param targetId 目标ID
     * @param targetType 目标类型
     * @return 是否成功
     */
    boolean addFavorite(Long userId, Long targetId, String targetType);

    /**
     * 取消收藏
     * @param userId 用户ID
     * @param favoriteId 收藏ID
     * @return 是否成功
     */
    boolean removeFavorite(Long userId, Long favoriteId);

    /**
     * 检查是否已收藏
     * @param userId 用户ID
     * @param targetId 目标ID
     * @param targetType 目标类型
     * @return 是否已收藏
     */
    boolean isFavorited(Long userId, Long targetId, String targetType);

    /**
     * 根据目标取消收藏
     * @param userId 用户ID
     * @param targetId 目标ID
     * @param targetType 目标类型
     * @return 是否成功
     */
    boolean removeFavoriteByTarget(Long userId, Long targetId, String targetType);
}
