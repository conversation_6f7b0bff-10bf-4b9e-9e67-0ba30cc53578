package com.scriptmurder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.scriptmurder.dto.LoginFormDTO;
import com.scriptmurder.dto.RegisterFormDTO;
import com.scriptmurder.dto.Result;
import com.scriptmurder.dto.UserUpdateDTO;
import com.scriptmurder.entity.User;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpSession;
import java.io.IOException;

/**
 * <p>
 *  服务类
 * </p>
 */
public interface IUserService extends IService<User> {

    /**
     * 发送短信验证码
     * @param phone
     * @param session
     * @return
     */
    Result sendCode(String phone, HttpSession session);

    /**
     * 发送邮箱验证码
     * @param email 邮箱地址
     * @return
     */
    Result sendEmailCode(String email);

    /**
     * 用户注册
     * @param registerForm 注册表单
     * @return
     */
    Result register(RegisterFormDTO registerForm);

    /**
     * 实现登录功能
     * @param loginForm
     * @param session
     * @return
     */
    Result login(LoginFormDTO loginForm, HttpSession session);

    /**
     * 用户登出
     * @param token 用户token
     * @return
     */
    Result logout(String token);

    Result sign();

    Result singCount();

    /**
     * 更新用户信息
     * @param userId 用户ID
     * @param updateDTO 更新信息
     * @return 是否成功
     */
    boolean updateUserProfile(Long userId, UserUpdateDTO updateDTO);

    /**
     * 上传头像
     * @param userId 用户ID
     * @param file 头像文件
     * @return 头像URL
     * @throws IOException IO异常
     */
    String uploadAvatar(Long userId, MultipartFile file) throws IOException;

    /**
     * 更新用户头像URL
     * @param userId 用户ID
     * @param avatarUrl 头像URL
     * @return 是否成功
     */
    boolean updateUserAvatar(Long userId, String avatarUrl);

    /**
     * 修改密码
     * @param userId 用户ID
     * @param oldPassword 原密码
     * @param newPassword 新密码
     * @return 是否成功
     */
    boolean changePassword(Long userId, String oldPassword, String newPassword);
}
