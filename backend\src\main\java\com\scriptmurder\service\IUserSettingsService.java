package com.scriptmurder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.scriptmurder.dto.UserSettingsDTO;
import com.scriptmurder.entity.UserSettings;

import java.util.List;
import java.util.Map;

/**
 * 用户设置服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
public interface IUserSettingsService extends IService<UserSettings> {
    
    /**
     * 获取用户设置
     * @param userId 用户ID
     * @return 用户设置DTO
     */
    UserSettingsDTO getUserSettings(Long userId);
    
    /**
     * 更新用户设置
     * @param userId 用户ID
     * @param settingsDTO 设置信息
     * @return 是否成功
     */
    boolean updateUserSettings(Long userId, UserSettingsDTO settingsDTO);
    
    /**
     * 重置为默认设置
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean resetToDefaults(Long userId);
    
    /**
     * 初始化用户设置（用户注册时调用）
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean initUserSettings(Long userId);

    /**
     * 批量初始化用户设置
     * @param userIds 用户ID列表
     * @return 成功初始化的数量
     */
    int batchInitUserSettings(List<Long> userIds);

    /**
     * 更新单个设置项
     * @param userId 用户ID
     * @param fieldName 字段名
     * @param fieldValue 字段值
     * @return 是否成功
     */
    boolean updateSingleSetting(Long userId, String fieldName, Object fieldValue);

    /**
     * 获取主题使用统计
     * @return 主题统计信息
     */
    List<Map<String, Object>> getThemeStatistics();

    /**
     * 获取隐私级别分布统计
     * @return 隐私级别统计信息
     */
    List<Map<String, Object>> getPrivacyLevelStatistics();

    /**
     * 获取通知设置统计
     * @return 通知设置统计信息
     */
    Map<String, Object> getNotificationStatistics();

    /**
     * 获取启用特定通知类型的用户ID列表
     * @param notificationType 通知类型 (email/system/activity/social)
     * @return 用户ID列表
     */
    List<Long> getUserIdsByNotificationType(String notificationType);

    /**
     * 批量更新用户的特定设置字段
     * @param userIds 用户ID列表
     * @param fieldName 字段名
     * @param fieldValue 字段值
     * @return 更新的记录数
     */
    int batchUpdateField(List<Long> userIds, String fieldName, String fieldValue);

    /**
     * 检查用户是否启用了特定通知
     * @param userId 用户ID
     * @param notificationType 通知类型
     * @return 是否启用
     */
    boolean isNotificationEnabled(Long userId, String notificationType);

    /**
     * 切换用户主题
     * @param userId 用户ID
     * @return 新的主题
     */
    String toggleTheme(Long userId);

    /**
     * 切换通知设置
     * @param userId 用户ID
     * @param notificationType 通知类型
     * @return 新的设置状态
     */
    boolean toggleNotification(Long userId, String notificationType);

    /**
     * 获取用户设置摘要
     * @param userId 用户ID
     * @return 设置摘要信息
     */
    Map<String, Object> getUserSettingsSummary(Long userId);

    /**
     * 验证设置数据的有效性
     * @param settingsDTO 设置DTO
     * @return 验证结果
     */
    Map<String, String> validateSettings(UserSettingsDTO settingsDTO);

    /**
     * 导出用户设置
     * @param userId 用户ID
     * @return 设置的JSON字符串
     */
    String exportUserSettings(Long userId);

    /**
     * 导入用户设置
     * @param userId 用户ID
     * @param settingsJson 设置的JSON字符串
     * @return 是否成功
     */
    boolean importUserSettings(Long userId, String settingsJson);

    /**
     * 获取设置变更历史（如果需要审计功能）
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 变更历史
     */
    List<Map<String, Object>> getSettingsChangeHistory(Long userId, Integer limit);

    /**
     * 同步设置到缓存
     * @param userId 用户ID
     */
    void syncSettingsToCache(Long userId);

    /**
     * 从缓存获取设置
     * @param userId 用户ID
     * @return 用户设置DTO
     */
    UserSettingsDTO getSettingsFromCache(Long userId);

    /**
     * 清除用户设置缓存
     * @param userId 用户ID
     */
    void clearSettingsCache(Long userId);
}
