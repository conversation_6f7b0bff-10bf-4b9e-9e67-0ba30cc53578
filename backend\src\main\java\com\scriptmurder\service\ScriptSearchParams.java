package com.scriptmurder.service;

/**
 * 搜索参数类
 */
public class ScriptSearchParams {
    private String keyword;
    private String category;
    private Integer playerCountMin;
    private Integer playerCountMax;
    private Integer difficulty;
    private String sortBy;
    private String sortOrder;
    private Integer page;
    private Integer size;
    
    // getters and setters
    public String getKeyword() { return keyword; }
    public void setKeyword(String keyword) { this.keyword = keyword; }
    
    public String getCategory() { return category; }
    public void setCategory(String category) { this.category = category; }
    
    public Integer getPlayerCountMin() { return playerCountMin; }
    public void setPlayerCountMin(Integer playerCountMin) { this.playerCountMin = playerCountMin; }
    
    public Integer getPlayerCountMax() { return playerCountMax; }
    public void setPlayerCountMax(Integer playerCountMax) { this.playerCountMax = playerCountMax; }
    
    public Integer getDifficulty() { return difficulty; }
    public void setDifficulty(Integer difficulty) { this.difficulty = difficulty; }
    
    public String getSortBy() { return sortBy; }
    public void setSortBy(String sortBy) { this.sortBy = sortBy; }
    
    public String getSortOrder() { return sortOrder; }
    public void setSortOrder(String sortOrder) { this.sortOrder = sortOrder; }
    
    public Integer getPage() { return page; }
    public void setPage(Integer page) { this.page = page; }
    
    public Integer getSize() { return size; }
    public void setSize(Integer size) { this.size = size; }
}