package com.scriptmurder.service.impl;

import com.scriptmurder.dto.CategoryStatsDTO;
import com.scriptmurder.dto.PageResponse;
import com.scriptmurder.dto.ScriptDTO;
import com.scriptmurder.search.document.ScriptSearchDocument;
import com.scriptmurder.service.IAdvancedSearchService;
import com.scriptmurder.service.ScriptSearchParams;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 高级搜索服务实现 - ES 7.x兼容版本
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AdvancedSearchServiceImpl implements IAdvancedSearchService {

    @Autowired
    private RestHighLevelClient elasticsearchClient;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Value("${script-murder.elasticsearch.index-prefix:script_murder}")
    private String indexPrefix;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private static final String SEARCH_KEYWORDS_KEY = "search:keywords";
    private static final String HOT_KEYWORDS_KEY = "search:hot_keywords";

    @Override
    public PageResponse<ScriptDTO> advancedSearch(ScriptSearchParams searchParams) {
        try {
            // 构建搜索请求
            SearchRequest searchRequest = new SearchRequest(indexPrefix + "_scripts");
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();

            // 构建查询条件
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

            // 关键词搜索
            if (StringUtils.hasText(searchParams.getKeyword())) {
                boolQuery.should(QueryBuilders.matchQuery("title", searchParams.getKeyword()).boost(2.0f))
                        .should(QueryBuilders.matchQuery("description", searchParams.getKeyword()))
                        .should(QueryBuilders.matchQuery("tags", searchParams.getKeyword()).boost(1.5f))
                        .minimumShouldMatch(1);
                
                // 记录搜索关键词
                recordSearchKeyword(searchParams.getKeyword());
            }

            // 分类过滤
            if (StringUtils.hasText(searchParams.getCategory())) {
                boolQuery.filter(QueryBuilders.termQuery("category.keyword", searchParams.getCategory()));
            }

            // 人数范围过滤
            if (searchParams.getPlayerCountMin() != null || searchParams.getPlayerCountMax() != null) {
                var rangeQuery = QueryBuilders.rangeQuery("playerCountMin");
                if (searchParams.getPlayerCountMin() != null) {
                    rangeQuery.gte(searchParams.getPlayerCountMin());
                }
                if (searchParams.getPlayerCountMax() != null) {
                    rangeQuery.lte(searchParams.getPlayerCountMax());
                }
                boolQuery.filter(rangeQuery);
            }

            // 难度过滤
            if (searchParams.getDifficulty() != null) {
                boolQuery.filter(QueryBuilders.termQuery("difficulty", searchParams.getDifficulty()));
            }

            // 只查询有效剧本
            boolQuery.filter(QueryBuilders.termQuery("status", 1));

            sourceBuilder.query(boolQuery);

            // 排序
            String sortBy = searchParams.getSortBy();
            if (StringUtils.hasText(sortBy)) {
                SortOrder sortOrder = "desc".equalsIgnoreCase(searchParams.getSortOrder()) 
                    ? SortOrder.DESC : SortOrder.ASC;
                
                switch (sortBy) {
                    case "rating":
                        sourceBuilder.sort("averageRating", sortOrder);
                        break;
                    case "playCount":
                        sourceBuilder.sort("playCount", sortOrder);
                        break;
                    case "createTime":
                        sourceBuilder.sort("createTime", sortOrder);
                        break;
                    default:
                        sourceBuilder.sort("_score", SortOrder.DESC);
                }
            } else {
                sourceBuilder.sort("_score", SortOrder.DESC);
            }

            // 分页
            int page = searchParams.getPage() != null ? searchParams.getPage() : 1;
            int size = searchParams.getSize() != null ? searchParams.getSize() : 20;
            sourceBuilder.from((page - 1) * size).size(size);

            searchRequest.source(sourceBuilder);

            // 执行搜索
            SearchResponse searchResponse = elasticsearchClient.search(searchRequest, RequestOptions.DEFAULT);

            // 解析结果
            List<ScriptDTO> scripts = Arrays.stream(searchResponse.getHits().getHits())
                    .map(this::convertToScriptDTO)
                    .collect(Collectors.toList());

            long total = searchResponse.getHits().getTotalHits().value;

            return PageResponse.of(
                    scripts,
                    (long) page,
                    (long) size,
                    total
            );

        } catch (Exception e) {
            log.error("高级搜索失败", e);
            return PageResponse.empty(1L, 20L);
        }
    }

    @Override
    public List<CategoryStatsDTO> getCategoryStats() {
        try {
            SearchRequest searchRequest = new SearchRequest(indexPrefix + "_scripts");
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            
            // 只查询有效剧本
            sourceBuilder.query(QueryBuilders.termQuery("status", 1));
            sourceBuilder.size(0); // 不需要返回文档，只要聚合结果
            
            // 分类聚合
            sourceBuilder.aggregation(
                AggregationBuilders.terms("categories")
                    .field("category.keyword")
                    .size(50)
            );

            searchRequest.source(sourceBuilder);
            SearchResponse response = elasticsearchClient.search(searchRequest, RequestOptions.DEFAULT);

            Terms categoriesAgg = response.getAggregations().get("categories");
            return categoriesAgg.getBuckets().stream()
                    .map(bucket -> CategoryStatsDTO.builder()
                            .category(bucket.getKeyAsString())
                            .count((int) bucket.getDocCount())
                            .displayName(bucket.getKeyAsString())
                            .build())
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取分类统计失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<String> getHotSearchKeywords(int limit) {
        try {
            Set<String> keywords = stringRedisTemplate.opsForZSet()
                    .reverseRange(HOT_KEYWORDS_KEY, 0, limit - 1);
            return keywords != null ? new ArrayList<>(keywords) : Collections.emptyList();
        } catch (Exception e) {
            log.error("获取热门关键词失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public void recordSearchKeyword(String keyword) {
        if (!StringUtils.hasText(keyword)) {
            return;
        }
        
        try {
            // 增加关键词搜索次数
            stringRedisTemplate.opsForZSet().incrementScore(HOT_KEYWORDS_KEY, keyword.toLowerCase(), 1);
            
            // 设置过期时间（30天）
            stringRedisTemplate.expire(HOT_KEYWORDS_KEY, 30, TimeUnit.DAYS);
            
        } catch (Exception e) {
            log.error("记录搜索关键词失败: {}", keyword, e);
        }
    }

    @Override
    public List<String> getSuggestedKeywords(String keyword, int limit) {
        if (!StringUtils.hasText(keyword)) {
            return getHotSearchKeywords(limit);
        }

        try {
            // 从热门关键词中查找匹配的
            Set<String> allKeywords = stringRedisTemplate.opsForZSet()
                    .reverseRange(HOT_KEYWORDS_KEY, 0, -1);
            
            if (allKeywords == null) {
                return Collections.emptyList();
            }

            String lowerKeyword = keyword.toLowerCase();
            return allKeywords.stream()
                    .filter(k -> k.contains(lowerKeyword))
                    .limit(limit)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取推荐关键词失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 转换搜索结果为DTO
     */
    private ScriptDTO convertToScriptDTO(SearchHit hit) {
        try {
            Map<String, Object> source = hit.getSourceAsMap();
            
            return ScriptDTO.builder()
                    .id(((Number) source.get("id")).longValue())
                    .title((String) source.get("title"))
                    .description((String) source.get("description"))
                    .category((String) source.get("category"))
                    .playerCountMin((Integer) source.get("playerCountMin"))
                    .playerCountMax((Integer) source.get("playerCountMax"))
                    .difficulty((Integer) source.get("difficulty"))
                    .duration(source.get("duration") != null ? source.get("duration").toString() : null)
                    .price(source.get("price") != null ? 
                           java.math.BigDecimal.valueOf(((Number) source.get("price")).doubleValue()) : null)
                    .averageRating(source.get("averageRating") != null ? 
                                   java.math.BigDecimal.valueOf(((Number) source.get("averageRating")).doubleValue()) : null)
                    .playCount((Integer) source.get("playCount"))
                    .reviewCount((Integer) source.get("reviewCount"))
                    .coverImage((String) source.get("imageUrl"))
                    .tagList(source.get("tags") != null ? 
                             (List<String>) source.get("tags") : Collections.emptyList())
                    .build();
        } catch (Exception e) {
            log.error("转换搜索结果失败", e);
            return ScriptDTO.builder().build();
        }
    }
}