package com.scriptmurder.service.impl;

import com.scriptmurder.service.IEmailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

/**
 * 邮件服务实现类
 */
@Service
@Slf4j
public class EmailServiceImpl implements IEmailService {

    @Autowired
    private JavaMailSender mailSender;

    @Value("${spring.mail.username}")
    private String from;

    @Override
    public boolean sendVerificationCode(String email, String code) {
        String subject = "剧本杀平台 - 邮箱验证码";
        String content = String.format(
            "您好！\n\n" +
            "您正在注册剧本杀平台账号，验证码为：%s\n\n" +
            "验证码有效期为5分钟，请及时使用。\n\n" +
            "如果这不是您的操作，请忽略此邮件。\n\n" +
            "剧本杀平台团队",
            code
        );
        
        return sendSimpleMail(email, subject, content);
    }

    @Override
    public boolean sendSimpleMail(String to, String subject, String content) {
        try {
            // 开发环境模拟邮件发送
            log.info("=== 模拟邮件发送 ===");
            log.info("收件人: {}", to);
            log.info("主题: {}", subject);
            log.info("内容: {}", content);
            log.info("==================");

            // 在实际生产环境中，取消注释以下代码来真正发送邮件
            /*
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(from);
            message.setTo(to);
            message.setSubject(subject);
            message.setText(content);

            mailSender.send(message);
            */

            log.info("邮件发送成功（模拟），收件人：{}", to);
            return true;
        } catch (Exception e) {
            log.error("邮件发送失败，收件人：{}，错误信息：{}", to, e.getMessage());
            return false;
        }
    }
}
