package com.scriptmurder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scriptmurder.dto.LoginHistoryDTO;
import com.scriptmurder.entity.LoginHistory;
import com.scriptmurder.mapper.LoginHistoryMapper;
import com.scriptmurder.service.ILoginHistoryService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 登录历史服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Slf4j
@Service
public class LoginHistoryServiceImpl extends ServiceImpl<LoginHistoryMapper, LoginHistory> 
        implements ILoginHistoryService {

    @Resource
    private LoginHistoryMapper loginHistoryMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recordLogin(Long userId, HttpServletRequest request) {
        try {
            LoginHistory loginHistory = new LoginHistory();
            loginHistory.setUserId(userId);
            loginHistory.setLoginIp(getClientIp(request));
            loginHistory.setLoginLocation(getLocationByIp(loginHistory.getLoginIp()));
            
            // 解析User-Agent
            String userAgentStr = request.getHeader("User-Agent");
            if (userAgentStr != null) {
                UserAgent userAgent = UserAgentUtil.parse(userAgentStr);
                loginHistory.setDeviceType(getDeviceType(userAgent));
                loginHistory.setBrowser(getBrowserInfo(userAgent));
            }
            
            loginHistory.setLoginTime(LocalDateTime.now());
            loginHistory.setStatus(1); // 成功
            loginHistory.setCreateTime(LocalDateTime.now());
            
            boolean success = save(loginHistory);
            
            if (success) {
                log.info("记录登录历史成功, userId: {}, ip: {}", userId, loginHistory.getLoginIp());
            }
            
            return success;
        } catch (Exception e) {
            log.error("记录登录历史失败, userId: {}", userId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recordLoginFailure(Long userId, HttpServletRequest request, String failureReason) {
        try {
            LoginHistory loginHistory = new LoginHistory();
            loginHistory.setUserId(userId);
            loginHistory.setLoginIp(getClientIp(request));
            loginHistory.setLoginLocation(getLocationByIp(loginHistory.getLoginIp()));
            
            // 解析User-Agent
            String userAgentStr = request.getHeader("User-Agent");
            if (userAgentStr != null) {
                UserAgent userAgent = UserAgentUtil.parse(userAgentStr);
                loginHistory.setDeviceType(getDeviceType(userAgent));
                loginHistory.setBrowser(getBrowserInfo(userAgent));
            }
            
            loginHistory.setLoginTime(LocalDateTime.now());
            loginHistory.setStatus(0); // 失败
            loginHistory.setFailureReason(failureReason);
            loginHistory.setCreateTime(LocalDateTime.now());
            
            boolean success = save(loginHistory);
            
            if (success) {
                log.info("记录登录失败历史成功, userId: {}, ip: {}, reason: {}", 
                        userId, loginHistory.getLoginIp(), failureReason);
            }
            
            return success;
        } catch (Exception e) {
            log.error("记录登录失败历史失败, userId: {}", userId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recordLogout(Long userId) {
        try {
            // 更新最近的登录记录的登出时间
            QueryWrapper<LoginHistory> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId)
                       .eq("status", 1)
                       .isNull("logout_time")
                       .orderByDesc("login_time")
                       .last("LIMIT 1");
            
            LoginHistory latestLogin = getOne(queryWrapper);
            if (latestLogin != null) {
                LocalDateTime logoutTime = LocalDateTime.now();
                int result = loginHistoryMapper.updateLogoutInfo(latestLogin.getId(), logoutTime);
                
                if (result > 0) {
                    log.info("记录登出成功, userId: {}, sessionId: {}", userId, latestLogin.getId());
                    return true;
                }
            }
            
            return false;
        } catch (Exception e) {
            log.error("记录登出失败, userId: {}", userId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int logoutAllUserSessions(Long userId) {
        try {
            int count = loginHistoryMapper.batchLogoutUserSessions(userId);
            log.info("批量登出用户会话成功, userId: {}, count: {}", userId, count);
            return count;
        } catch (Exception e) {
            log.error("批量登出用户会话失败, userId: {}", userId, e);
            return 0;
        }
    }

    @Override
    public Page<LoginHistoryDTO> getLoginHistory(Long userId, Integer page, Integer size) {
        try {
            Page<LoginHistory> historyPage = new Page<>(page, size);
            Page<LoginHistory> result = loginHistoryMapper.selectPageByUserId(historyPage, userId);
            
            // 转换为DTO
            List<LoginHistoryDTO> dtoList = result.getRecords().stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
            
            Page<LoginHistoryDTO> dtoPage = new Page<>();
            dtoPage.setRecords(dtoList);
            dtoPage.setTotal(result.getTotal());
            dtoPage.setCurrent(result.getCurrent());
            dtoPage.setSize(result.getSize());
            
            return dtoPage;
        } catch (Exception e) {
            log.error("获取登录历史失败, userId: {}", userId, e);
            return new Page<>();
        }
    }

    @Override
    public List<LoginHistoryDTO> getRecentLogins(Long userId, Integer limit) {
        try {
            List<LoginHistory> histories = loginHistoryMapper.selectRecentLoginsByUserId(userId, limit);
            return histories.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取最近登录记录失败, userId: {}", userId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<LoginHistoryDTO> getOnlineSessions(Long userId) {
        try {
            List<LoginHistory> sessions = loginHistoryMapper.selectOnlineSessionsByUserId(userId);
            return sessions.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取在线会话失败, userId: {}", userId, e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearLoginHistory(Long userId) {
        try {
            QueryWrapper<LoginHistory> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            
            boolean success = remove(queryWrapper);
            
            if (success) {
                log.info("清除登录历史成功, userId: {}", userId);
            }
            
            return success;
        } catch (Exception e) {
            log.error("清除登录历史失败, userId: {}", userId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanupOldRecords(Integer days) {
        try {
            int count = loginHistoryMapper.cleanupOldRecords(days);
            log.info("清理旧登录记录成功, days: {}, count: {}", days, count);
            return count;
        } catch (Exception e) {
            log.error("清理旧登录记录失败, days: {}", days, e);
            return 0;
        }
    }

    @Override
    public Map<String, Object> getUserLoginStatistics(Long userId) {
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            // 总登录次数
            int totalLogins = loginHistoryMapper.countSuccessfulLoginsByUserId(userId);
            statistics.put("totalLogins", totalLogins);
            
            // 失败次数
            int failedLogins = loginHistoryMapper.countFailedLoginsByUserId(userId);
            statistics.put("failedLogins", failedLogins);
            
            // 会话统计
            Map<String, Object> sessionStats = loginHistoryMapper.selectUserSessionStatistics(userId);
            if (sessionStats != null) {
                statistics.putAll(sessionStats);
            }
            
            // 最后登录时间
            LocalDateTime lastLoginTime = getLastLoginTime(userId);
            statistics.put("lastLoginTime", lastLoginTime);
            
            // 是否在线
            boolean isOnline = isUserOnline(userId);
            statistics.put("isOnline", isOnline);
            
            return statistics;
        } catch (Exception e) {
            log.error("获取用户登录统计失败, userId: {}", userId, e);
            return new HashMap<>();
        }
    }

    @Override
    public List<Map<String, Object>> getLoginStatisticsByDateRange(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            return loginHistoryMapper.selectLoginStatisticsByDateRange(startTime, endTime);
        } catch (Exception e) {
            log.error("获取时间范围登录统计失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, Object>> getDeviceTypeStatistics() {
        try {
            return loginHistoryMapper.selectDeviceTypeStatistics();
        } catch (Exception e) {
            log.error("获取设备类型统计失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, Object>> getBrowserStatistics(Integer limit) {
        try {
            return loginHistoryMapper.selectBrowserStatistics(limit);
        } catch (Exception e) {
            log.error("获取浏览器统计失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Object> getUserSessionStatistics(Long userId) {
        try {
            Map<String, Object> stats = loginHistoryMapper.selectUserSessionStatistics(userId);
            return stats != null ? stats : new HashMap<>();
        } catch (Exception e) {
            log.error("获取用户会话统计失败, userId: {}", userId, e);
            return new HashMap<>();
        }
    }

    @Override
    public List<LoginHistoryDTO> detectSuspiciousLogins(Long userId) {
        try {
            List<LoginHistory> suspiciousLogins = loginHistoryMapper.selectSuspiciousLogins(userId);
            return suspiciousLogins.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("检测可疑登录失败, userId: {}", userId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, Object>> getMostActiveUsers(Integer days, Integer limit) {
        try {
            return loginHistoryMapper.selectMostActiveUsers(days, limit);
        } catch (Exception e) {
            log.error("获取最活跃用户失败", e);
            return new ArrayList<>();
        }
    }

    // 私有辅助方法
    private LoginHistoryDTO convertToDTO(LoginHistory history) {
        LoginHistoryDTO dto = BeanUtil.copyProperties(history, LoginHistoryDTO.class);
        
        // 设置扩展字段
        dto.setStatusDesc(history.getStatusDesc());
        dto.setDeviceTypeDesc(history.getDeviceTypeDesc());
        dto.setFormattedSessionDuration(history.getFormattedSessionDuration());
        dto.setSimpleBrowserName(history.getSimpleBrowserName());
        dto.setLoginTimeDesc(history.getLoginTimeDesc());
        dto.setIsOnline(history.isOnline());
        
        return dto;
    }

    private String getClientIp(HttpServletRequest request) {
        // 这里应该使用实际的IP获取工具类
        // 暂时使用简单实现
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    private String getLocationByIp(String ip) {
        // 这里应该使用实际的IP地理位置服务
        // 暂时返回默认值
        return "未知地区";
    }

    private String getDeviceType(UserAgent userAgent) {
        if (userAgent.isMobile()) {
            return "Mobile";
        } else {
            // HuTool的UserAgent可能没有isTablet方法，我们通过User-Agent字符串判断
            String userAgentStr = userAgent.toString();
            if (userAgentStr != null && (
                userAgentStr.contains("iPad") ||
                userAgentStr.contains("Android") && userAgentStr.contains("Tablet") ||
                userAgentStr.contains("Kindle") ||
                userAgentStr.contains("PlayBook") ||
                userAgentStr.contains("Silk")
            )) {
                return "Tablet";
            } else {
                return "Desktop";
            }
        }
    }

    private String getBrowserInfo(UserAgent userAgent) {
        return userAgent.getBrowser().getName() + " " + userAgent.getVersion();
    }

    @Override
    public boolean isUserOnline(Long userId) {
        try {
            List<LoginHistory> onlineSessions = loginHistoryMapper.selectOnlineSessionsByUserId(userId);
            return !onlineSessions.isEmpty();
        } catch (Exception e) {
            log.error("检查用户在线状态失败, userId: {}", userId, e);
            return false;
        }
    }

    @Override
    public long getOnlineUserCount() {
        try {
            QueryWrapper<LoginHistory> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("status", 1)
                       .isNull("logout_time")
                       .groupBy("user_id");

            return count(queryWrapper);
        } catch (Exception e) {
            log.error("获取在线用户数量失败", e);
            return 0;
        }
    }

    @Override
    public LocalDateTime getLastLoginTime(Long userId) {
        try {
            QueryWrapper<LoginHistory> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId)
                       .eq("status", 1)
                       .orderByDesc("login_time")
                       .last("LIMIT 1");

            LoginHistory lastLogin = getOne(queryWrapper);
            return lastLogin != null ? lastLogin.getLoginTime() : null;
        } catch (Exception e) {
            log.error("获取最后登录时间失败, userId: {}", userId, e);
            return null;
        }
    }

    @Override
    public int getUserLoginCount(Long userId) {
        try {
            return loginHistoryMapper.countSuccessfulLoginsByUserId(userId);
        } catch (Exception e) {
            log.error("获取用户登录次数失败, userId: {}", userId, e);
            return 0;
        }
    }

    @Override
    public int getUserLoginFailureCount(Long userId) {
        try {
            return loginHistoryMapper.countFailedLoginsByUserId(userId);
        } catch (Exception e) {
            log.error("获取用户登录失败次数失败, userId: {}", userId, e);
            return 0;
        }
    }

    @Override
    public boolean isIpBlocked(String ip, Integer timeWindow, Integer maxAttempts) {
        try {
            QueryWrapper<LoginHistory> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("login_ip", ip)
                       .eq("status", 0)
                       .ge("login_time", LocalDateTime.now().minusMinutes(timeWindow));

            long failureCount = count(queryWrapper);
            return failureCount >= maxAttempts;
        } catch (Exception e) {
            log.error("检查IP是否被阻止失败, ip: {}", ip, e);
            return false;
        }
    }

    @Override
    public List<Map<String, Object>> getUserCommonDevices(Long userId, Integer limit) {
        try {
            QueryWrapper<LoginHistory> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId)
                       .eq("status", 1)
                       .isNotNull("device_type")
                       .groupBy("device_type", "browser")
                       .orderByDesc("COUNT(*)")
                       .last("LIMIT " + limit);

            // 这里需要自定义查询，暂时返回空列表
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("获取用户常用设备失败, userId: {}", userId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, Object>> getUserCommonLocations(Long userId, Integer limit) {
        try {
            QueryWrapper<LoginHistory> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId)
                       .eq("status", 1)
                       .isNotNull("login_location")
                       .groupBy("login_location")
                       .orderByDesc("COUNT(*)")
                       .last("LIMIT " + limit);

            // 这里需要自定义查询，暂时返回空列表
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("获取用户常用地点失败, userId: {}", userId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Object> generateSecurityReport(Long userId, Integer days) {
        try {
            Map<String, Object> report = new HashMap<>();

            // 基本统计
            Map<String, Object> basicStats = getUserLoginStatistics(userId);
            report.put("basicStatistics", basicStats);

            // 可疑登录
            List<LoginHistoryDTO> suspiciousLogins = detectSuspiciousLogins(userId);
            report.put("suspiciousLogins", suspiciousLogins);

            // 设备统计
            List<Map<String, Object>> devices = getUserCommonDevices(userId, 5);
            report.put("commonDevices", devices);

            // 地点统计
            List<Map<String, Object>> locations = getUserCommonLocations(userId, 5);
            report.put("commonLocations", locations);

            // 时间范围
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusDays(days);
            report.put("reportPeriod", Map.of(
                "startTime", startTime,
                "endTime", endTime,
                "days", days
            ));

            return report;
        } catch (Exception e) {
            log.error("生成安全报告失败, userId: {}", userId, e);
            return new HashMap<>();
        }
    }

    @Override
    public List<LoginHistoryDTO> exportLoginHistory(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            QueryWrapper<LoginHistory> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);

            if (startTime != null) {
                queryWrapper.ge("login_time", startTime);
            }
            if (endTime != null) {
                queryWrapper.le("login_time", endTime);
            }

            queryWrapper.orderByDesc("login_time");

            List<LoginHistory> histories = list(queryWrapper);
            return histories.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("导出登录历史失败, userId: {}", userId, e);
            return new ArrayList<>();
        }
    }
}
