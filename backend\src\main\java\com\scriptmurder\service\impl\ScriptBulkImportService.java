package com.scriptmurder.service.impl;

import com.scriptmurder.entity.Script;
import com.scriptmurder.mapper.ScriptMapper;
import com.scriptmurder.search.document.ScriptSearchDocument;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.xcontent.XContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * ES批量数据导入服务 (Elasticsearch 7.x 兼容版本)
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ScriptBulkImportService {

    @Autowired
    private RestHighLevelClient elasticsearchClient;

    @Autowired
    private ScriptMapper scriptMapper;

    @Value("${script-murder.elasticsearch.batch-size:1000}")
    private int batchSize;

    @Value("${script-murder.elasticsearch.index-prefix:script_murder}")
    private String indexPrefix;

    private final Executor bulkExecutor = Executors.newFixedThreadPool(4);
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 全量重建索引
     */
    public void rebuildAllIndex() {
        long startTime = System.currentTimeMillis();
        log.info("开始全量重建ES索引");

        try {
            // 1. 获取总数据量
            Long totalCount = scriptMapper.selectCount(null);
            log.info("需要同步的剧本总数: {}", totalCount);

            if (totalCount == 0) {
                log.info("没有数据需要同步");
                return;
            }

            // 2. 分页批量处理
            int pageSize = batchSize;
            int totalPages = (int) Math.ceil((double) totalCount / pageSize);
            
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (int page = 1; page <= totalPages; page++) {
                final int currentPage = page;
                
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        processBatch(currentPage, pageSize);
                    } catch (Exception e) {
                        log.error("处理第{}页数据失败", currentPage, e);
                    }
                }, bulkExecutor);
                
                futures.add(future);
            }

            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            long endTime = System.currentTimeMillis();
            log.info("全量重建ES索引完成，耗时: {}ms", endTime - startTime);

        } catch (Exception e) {
            log.error("全量重建ES索引失败", e);
            throw new RuntimeException("重建索引失败", e);
        }
    }

    /**
     * 增量同步数据
     */
    public void incrementalSync() {
        log.info("开始增量同步ES数据");
        
        try {
            // 获取最近更新的数据（使用MyBatis-Plus查询）
            QueryWrapper<Script> queryWrapper = new QueryWrapper<>();
            queryWrapper.gt("update_time", System.currentTimeMillis() - 3600000); // 最近1小时
            List<Script> recentUpdated = scriptMapper.selectList(queryWrapper);
            
            if (recentUpdated.isEmpty()) {
                log.info("没有需要增量同步的数据");
                return;
            }

            // 批量同步
            bulkIndex(recentUpdated);
            log.info("增量同步完成，同步数据量: {}", recentUpdated.size());
            
        } catch (Exception e) {
            log.error("增量同步失败", e);
        }
    }

    /**
     * 处理单个批次
     */
    private void processBatch(int page, int pageSize) {
        try {
            // 使用MyBatis-Plus分页查询
            Page<Script> pageParam = new Page<>(page, pageSize);
            Page<Script> scriptPage = scriptMapper.selectPage(pageParam, null);
            List<Script> scripts = scriptPage.getRecords();
            
            if (!scripts.isEmpty()) {
                bulkIndex(scripts);
                log.info("第{}页数据同步完成，数据量: {}", page, scripts.size());
            }
        } catch (Exception e) {
            log.error("处理第{}页数据异常", page, e);
            throw e;
        }
    }

    /**
     * 批量索引数据 (使用Elasticsearch 7.x API)
     */
    public void bulkIndex(List<Script> scripts) {
        try {
            BulkRequest bulkRequest = new BulkRequest();
            
            for (Script script : scripts) {
                ScriptSearchDocument document = ScriptSearchDocument.fromScript(script);
                String documentJson = objectMapper.writeValueAsString(document);
                
                IndexRequest indexRequest = new IndexRequest(indexPrefix + "_scripts")
                        .id(script.getId().toString())
                        .source(documentJson, XContentType.JSON);
                
                bulkRequest.add(indexRequest);
            }

            BulkResponse response = elasticsearchClient.bulk(bulkRequest, RequestOptions.DEFAULT);

            if (response.hasFailures()) {
                log.warn("批量索引存在错误: {}", response.buildFailureMessage());
            } else {
                log.debug("批量索引成功，数据量: {}", scripts.size());
            }

        } catch (Exception e) {
            log.error("批量索引失败", e);
            throw new RuntimeException("批量索引失败", e);
        }
    }

    /**
     * 批量删除索引文档 (使用Elasticsearch 7.x API)
     */
    public void bulkDelete(List<Long> scriptIds) {
        try {
            BulkRequest bulkRequest = new BulkRequest();
            
            for (Long scriptId : scriptIds) {
                org.elasticsearch.action.delete.DeleteRequest deleteRequest = 
                    new org.elasticsearch.action.delete.DeleteRequest(indexPrefix + "_scripts")
                        .id(scriptId.toString());
                
                bulkRequest.add(deleteRequest);
            }

            BulkResponse response = elasticsearchClient.bulk(bulkRequest, RequestOptions.DEFAULT);

            if (response.hasFailures()) {
                log.warn("批量删除存在错误: {}", response.buildFailureMessage());
            } else {
                log.debug("批量删除成功，数据量: {}", scriptIds.size());
            }

        } catch (Exception e) {
            log.error("批量删除失败", e);
            throw new RuntimeException("批量删除失败", e);
        }
    }

    /**
     * 获取索引名称
     */
    public String getScriptIndex() {
        return indexPrefix + "_scripts";
    }
}