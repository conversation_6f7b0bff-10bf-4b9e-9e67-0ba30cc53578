package com.scriptmurder.service.impl;

import com.scriptmurder.dto.PageResponse;
import com.scriptmurder.dto.ScriptDTO;
import com.scriptmurder.dto.ScriptSearchDTO;
import com.scriptmurder.entity.Script;
import com.scriptmurder.mapper.ScriptMapper;
import com.scriptmurder.search.document.ScriptSearchDocument;
import com.scriptmurder.search.repository.ScriptSearchRepository;
import com.scriptmurder.service.IScriptSearchService;
import com.scriptmurder.service.ISearchHistoryService;
import com.scriptmurder.service.IUserFavoriteService;
import com.scriptmurder.utils.UserHolder;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.text.Text;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MultiMatchQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightField;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 剧本搜索服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ScriptSearchServiceImpl implements IScriptSearchService {

    @Autowired
    private ElasticsearchRestTemplate elasticsearchTemplate;

    @Autowired
    private ScriptSearchRepository scriptSearchRepository;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Autowired
    private ScriptMapper scriptMapper;

    @Autowired
    private IUserFavoriteService userFavoriteService;

    @Autowired
    private ISearchHistoryService searchHistoryService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private static final String HOT_SEARCH_KEY = "script:hot_search";
    private static final String SEARCH_SUGGEST_KEY = "script:search_suggest:";

    @Override
    public PageResponse<ScriptDTO> searchScripts(ScriptSearchDTO searchDTO) {
        try {
            // 构建搜索请求
            SearchRequest searchRequest = buildSearchRequest(searchDTO);

            // 执行搜索
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

            // 解析搜索结果
            PageResponse<ScriptDTO> result = parseSearchResponse(searchResponse, searchDTO);

            // 记录搜索历史（包含结果数量）
            if (StringUtils.hasText(searchDTO.getKeyword())) {
                recordSearchKeyword(searchDTO.getKeyword());

                // 记录用户搜索历史
                if (UserHolder.getUser() != null) {
                    searchHistoryService.recordSearchHistory(
                        UserHolder.getUser().getId(),
                        searchDTO.getKeyword(),
                        searchDTO.getCategory(),
                        Math.toIntExact(result.getTotal())
                    );
                }
            }

            return result;

        } catch (IOException e) {
            log.error("Elasticsearch搜索异常", e);
            // 降级到数据库搜索
            return fallbackToDbSearch(searchDTO);
        }
    }

    @Override
    @Async
    public void syncScriptToEs(Script script) {
        try {
            ScriptSearchDocument document = ScriptSearchDocument.fromScript(script);
            scriptSearchRepository.save(document);
            log.info("同步剧本到ES成功: {}", script.getId());
        } catch (Exception e) {
            log.error("同步剧本到ES失败: {}", script.getId(), e);
        }
    }

    @Override
    @Async
    public void batchSyncScripts(List<Script> scripts) {
        try {
            List<ScriptSearchDocument> documents = scripts.stream()
                .map(ScriptSearchDocument::fromScript)
                .collect(Collectors.toList());
            
            scriptSearchRepository.saveAll(documents);
            log.info("批量同步剧本到ES成功，数量: {}", scripts.size());
        } catch (Exception e) {
            log.error("批量同步剧本到ES失败", e);
        }
    }

    @Override
    @Async
    public void deleteScriptFromEs(Long scriptId) {
        try {
            scriptSearchRepository.deleteById(scriptId.toString());
            log.info("从ES删除剧本成功: {}", scriptId);
        } catch (Exception e) {
            log.error("从ES删除剧本失败: {}", scriptId, e);
        }
    }

    @Override
    public List<String> getSearchSuggestions(String keyword) {
        if (!StringUtils.hasText(keyword)) {
            return new ArrayList<>();
        }

        try {
            // 构建搜索建议请求
            SearchRequest searchRequest = new SearchRequest("script_murder_scripts");
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            
            // 使用前缀查询获取建议
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.prefixQuery("title", keyword));
            boolQuery.should(QueryBuilders.prefixQuery("tags", keyword));
            
            sourceBuilder.query(boolQuery);
            sourceBuilder.size(10);
            sourceBuilder.fetchSource(new String[]{"title"}, null);
            
            searchRequest.source(sourceBuilder);
            
            SearchResponse response = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            
            return Arrays.stream(response.getHits().getHits())
                .map(hit -> (String) hit.getSourceAsMap().get("title"))
                .distinct()
                .limit(5)
                .collect(Collectors.toList());
                
        } catch (Exception e) {
            log.error("获取搜索建议失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<String> getHotSearchKeywords() {
        try {
            Set<String> hotKeywords = stringRedisTemplate.opsForZSet()
                .reverseRange(HOT_SEARCH_KEY, 0, 9);
            
            return new ArrayList<>(hotKeywords != null ? hotKeywords : new HashSet<>());
        } catch (Exception e) {
            log.error("获取热门搜索失败", e);
            return Arrays.asList("推理", "恐怖", "情感", "欢乐", "古风");
        }
    }

    @Override
    public void initializeIndex() {
        try {
            // 检查索引是否存在
            if (!elasticsearchTemplate.indexOps(ScriptSearchDocument.class).exists()) {
                // 创建索引
                elasticsearchTemplate.indexOps(ScriptSearchDocument.class).create();
                elasticsearchTemplate.indexOps(ScriptSearchDocument.class).putMapping();
                log.info("Elasticsearch索引创建成功");
            }
        } catch (Exception e) {
            log.error("初始化Elasticsearch索引失败", e);
        }
    }

    @Override
    public void rebuildIndex() {
        try {
            // 删除现有索引
            if (elasticsearchTemplate.indexOps(ScriptSearchDocument.class).exists()) {
                elasticsearchTemplate.indexOps(ScriptSearchDocument.class).delete();
            }
            
            // 创建新索引
            initializeIndex();
            
            // 同步所有剧本数据
            List<Script> allScripts = scriptMapper.selectList(null);
            batchSyncScripts(allScripts);
            
            log.info("重建Elasticsearch索引成功，同步剧本数量: {}", allScripts.size());
        } catch (Exception e) {
            log.error("重建Elasticsearch索引失败", e);
        }
    }

    /**
     * 构建搜索请求
     */
    private SearchRequest buildSearchRequest(ScriptSearchDTO searchDTO) {
        SearchRequest searchRequest = new SearchRequest("script_murder_scripts");
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();

        // 构建查询条件
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        // 关键词搜索
        if (StringUtils.hasText(searchDTO.getKeyword())) {
            MultiMatchQueryBuilder multiMatchQuery = QueryBuilders.multiMatchQuery(
                searchDTO.getKeyword(), "title^3", "description^2", "tags^1"
            ).type(MultiMatchQueryBuilder.Type.BEST_FIELDS);
            boolQuery.must(multiMatchQuery);
        }

        // 添加过滤条件
        addFilters(boolQuery, searchDTO);

        sourceBuilder.query(boolQuery);

        // 添加排序
        addSorting(sourceBuilder, searchDTO);

        // 添加高亮
        addHighlight(sourceBuilder);

        // 分页
        int from = (searchDTO.getPage() - 1) * searchDTO.getSize();
        sourceBuilder.from(from).size(searchDTO.getSize());

        searchRequest.source(sourceBuilder);
        return searchRequest;
    }

    /**
     * 添加过滤条件
     */
    private void addFilters(BoolQueryBuilder boolQuery, ScriptSearchDTO searchDTO) {
        // 状态过滤
        boolQuery.filter(QueryBuilders.termQuery("status", 1));

        // 类型过滤
        if (StringUtils.hasText(searchDTO.getCategory())) {
            boolQuery.filter(QueryBuilders.termQuery("category", searchDTO.getCategory()));
        }

        // 玩家数量过滤
        if (searchDTO.getPlayerCountMin() != null || searchDTO.getPlayerCountMax() != null) {
            RangeQueryBuilder playerMinQuery = QueryBuilders.rangeQuery("playerCountMin");
            RangeQueryBuilder playerMaxQuery = QueryBuilders.rangeQuery("playerCountMax");
            
            if (searchDTO.getPlayerCountMin() != null) {
                playerMinQuery.lte(searchDTO.getPlayerCountMin());
                boolQuery.filter(playerMinQuery);
            }
            if (searchDTO.getPlayerCountMax() != null) {
                playerMaxQuery.gte(searchDTO.getPlayerCountMax());
                boolQuery.filter(playerMaxQuery);
            }
        }

        // 难度过滤
        if (!CollectionUtils.isEmpty(searchDTO.getDifficulties())) {
            boolQuery.filter(QueryBuilders.termsQuery("difficulty", searchDTO.getDifficulties()));
        }

        // 价格过滤
        if (searchDTO.getPriceMin() != null || searchDTO.getPriceMax() != null) {
            RangeQueryBuilder priceQuery = QueryBuilders.rangeQuery("price");
            if (searchDTO.getPriceMin() != null) {
                priceQuery.gte(searchDTO.getPriceMin().doubleValue());
            }
            if (searchDTO.getPriceMax() != null) {
                priceQuery.lte(searchDTO.getPriceMax().doubleValue());
            }
            boolQuery.filter(priceQuery);
        }
    }

    /**
     * 添加排序
     */
    private void addSorting(SearchSourceBuilder sourceBuilder, ScriptSearchDTO searchDTO) {
        String sortBy = searchDTO.getSortBy();
        boolean isAsc = "asc".equalsIgnoreCase(searchDTO.getSortOrder());
        SortOrder sortOrder = isAsc ? SortOrder.ASC : SortOrder.DESC;

        SortBuilder<?> sortBuilder;
        switch (sortBy) {
            case "averageRating":
                sortBuilder = SortBuilders.fieldSort("averageRating").order(sortOrder);
                break;
            case "playCount":
                sortBuilder = SortBuilders.fieldSort("playCount").order(sortOrder);
                break;
            case "reviewCount":
                sortBuilder = SortBuilders.fieldSort("reviewCount").order(sortOrder);
                break;
            case "price":
                sortBuilder = SortBuilders.fieldSort("price").order(sortOrder);
                break;
            case "createTime":
            default:
                sortBuilder = SortBuilders.fieldSort("createTime").order(sortOrder);
                break;
        }

        sourceBuilder.sort(sortBuilder);
    }

    /**
     * 添加高亮
     */
    private void addHighlight(SearchSourceBuilder sourceBuilder) {
        HighlightBuilder highlightBuilder = new HighlightBuilder();
        highlightBuilder.field("title");
        highlightBuilder.field("description");
        highlightBuilder.preTags("<em class=\"highlight\">");
        highlightBuilder.postTags("</em>");
        highlightBuilder.fragmentSize(100);
        highlightBuilder.numOfFragments(3);
        
        sourceBuilder.highlighter(highlightBuilder);
    }

    /**
     * 解析搜索结果
     */
    private PageResponse<ScriptDTO> parseSearchResponse(SearchResponse response, ScriptSearchDTO searchDTO) {
        long total = response.getHits().getTotalHits().value;
        
        List<ScriptDTO> scriptDTOs = Arrays.stream(response.getHits().getHits())
            .map(this::convertSearchHitToDTO)
            .collect(Collectors.toList());

        long pages = (total + searchDTO.getSize() - 1) / searchDTO.getSize();

        return PageResponse.<ScriptDTO>builder()
            .records(scriptDTOs)
            .total(total)
            .current((long) searchDTO.getPage())
            .size((long) searchDTO.getSize())
            .pages(pages)
            .build();
    }

    /**
     * 转换搜索结果为DTO
     */
    private ScriptDTO convertSearchHitToDTO(SearchHit hit) {
        Map<String, Object> source = hit.getSourceAsMap();
        
        // 处理高亮
        Map<String, HighlightField> highlightFields = hit.getHighlightFields();
        String title = (String) source.get("title");
        String description = (String) source.get("description");
        
        if (highlightFields.containsKey("title")) {
            Text[] fragments = highlightFields.get("title").fragments();
            if (fragments.length > 0) {
                title = Arrays.stream(fragments)
                    .map(Text::toString)
                    .collect(Collectors.joining(""));
            }
        }
        if (highlightFields.containsKey("description")) {
            Text[] fragments = highlightFields.get("description").fragments();
            if (fragments.length > 0) {
                description = Arrays.stream(fragments)
                    .map(Text::toString)
                    .collect(Collectors.joining(""));
            }
        }

        // 检查是否收藏
        boolean isFavorite = false;
        if (UserHolder.getUser() != null) {
            Long scriptId = Long.valueOf(source.get("id").toString());
            isFavorite = userFavoriteService.isFavorited(UserHolder.getUser().getId(), scriptId, "script");
        }

        return ScriptDTO.builder()
            .id(Long.valueOf(source.get("id").toString()))
            .title(title)
            .description(description)
            .coverImage((String) source.get("coverImage"))
            .category((String) source.get("category"))
            .playerCountRange(source.get("playerCountMin") + "-" + source.get("playerCountMax") + "人")
            .playerCountMin((Integer) source.get("playerCountMin"))
            .playerCountMax((Integer) source.get("playerCountMax"))
            .duration((String) source.get("duration"))
            .difficulty((Integer) source.get("difficulty"))
            .tagList((List<String>) source.get("tags"))
            .price(source.get("price") != null ? BigDecimal.valueOf((Double) source.get("price")) : null)
            .averageRating(source.get("averageRating") != null ? BigDecimal.valueOf((Double) source.get("averageRating")) : null)
            .reviewCount((Integer) source.get("reviewCount"))
            .playCount((Integer) source.get("playCount"))
            .isFavorite(isFavorite)
            .createTime(parseDateTime((String) source.get("createTime")))
            .updateTime(parseDateTime((String) source.get("updateTime")))
            .build();
    }

    /**
     * 降级到数据库搜索
     */
    private PageResponse<ScriptDTO> fallbackToDbSearch(ScriptSearchDTO searchDTO) {
        log.warn("Elasticsearch搜索失败，降级到数据库搜索");
        // 这里可以调用数据库的模糊搜索逻辑
        // 为简化示例，返回空结果
        return PageResponse.<ScriptDTO>builder()
            .records(new ArrayList<>())
            .total(0L)
            .current((long) searchDTO.getPage())
            .size((long) searchDTO.getSize())
            .pages(0L)
            .build();
    }

    /**
     * 记录搜索关键词
     */
    private void recordSearchKeyword(String keyword) {
        try {
            stringRedisTemplate.opsForZSet().incrementScore(HOT_SEARCH_KEY, keyword, 1);
            stringRedisTemplate.expire(HOT_SEARCH_KEY, 7, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("记录搜索关键词失败", e);
        }
    }

    /**
     * 解析日期时间
     */
    private LocalDateTime parseDateTime(String dateTimeStr) {
        if (dateTimeStr == null) {
            return null;
        }
        try {
            return LocalDateTime.parse(dateTimeStr);
        } catch (Exception e) {
            return null;
        }
    }
}