package com.scriptmurder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scriptmurder.dto.*;
import com.scriptmurder.entity.*;
import com.scriptmurder.mapper.*;
import com.scriptmurder.service.IScriptService;
import com.scriptmurder.service.IScriptSearchService;
import com.scriptmurder.service.IUserFavoriteService;
import com.scriptmurder.utils.UserHolder;
import com.scriptmurder.mq.producer.ScriptMessageProducer;
import com.scriptmurder.mq.message.ScriptSyncMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 剧本服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ScriptServiceImpl extends ServiceImpl<ScriptMapper, Script> implements IScriptService {

    @Autowired
    private ScriptMapper scriptMapper;

    @Autowired
    private ScriptCharacterMapper scriptCharacterMapper;

    @Autowired
    private ScriptReviewMapper scriptReviewMapper;

    @Autowired
    private ScriptRuleMapper scriptRuleMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private IScriptSearchService scriptSearchService;

    @Autowired
    private IUserFavoriteService userFavoriteService;

    @Autowired
    private ScriptMessageProducer scriptMessageProducer;

    @Override
    public PageResponse<ScriptDTO> getScriptList(ScriptSearchDTO searchDTO) {
        // 构建查询条件
        QueryWrapper<Script> queryWrapper = buildQueryWrapper(searchDTO);
        
        // 分页查询
        Page<Script> page = new Page<>(searchDTO.getPage(), searchDTO.getSize());
        Page<Script> scriptPage = baseMapper.selectPage(page, queryWrapper);
        
        // 转换DTO
        List<ScriptDTO> scriptDTOs = scriptPage.getRecords().stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
        
        return PageResponse.<ScriptDTO>builder()
            .records(scriptDTOs)
            .total(scriptPage.getTotal())
            .current(scriptPage.getCurrent())
            .size(scriptPage.getSize())
            .pages(scriptPage.getPages())
            .build();
    }

    @Override
    @Cacheable(value = "script:detail", key = "#id", unless = "#result == null")
    public ScriptDetailDTO getScriptDetail(Long id) {
        // 获取剧本基本信息
        Script script = getById(id);
        if (script == null) {
            throw new RuntimeException("剧本不存在");
        }

        // 获取角色列表
        List<ScriptCharacter> characters = scriptCharacterMapper.selectByScriptId(id);
        List<ScriptCharacterDTO> characterDTOs = characters.stream()
            .map(this::convertCharacterToDTO)
            .collect(Collectors.toList());

        // 获取规则列表
        List<ScriptRule> rules = scriptRuleMapper.selectByScriptId(id);
        List<ScriptRuleDTO> ruleDTOs = rules.stream()
            .map(this::convertRuleToDTO)
            .collect(Collectors.toList());

        // 获取最新评价
        List<ScriptReview> reviews = scriptReviewMapper.selectRecentReviews(id, 5);
        List<ScriptReviewDTO> reviewDTOs = reviews.stream()
            .map(this::convertReviewToDTO)
            .collect(Collectors.toList());

        // 检查是否收藏
        boolean isFavorite = false;
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser != null) {
            isFavorite = userFavoriteService.isFavorited(currentUser.getId(), id, "script");
        }

        return ScriptDetailDTO.builder()
            .id(script.getId())
            .title(script.getTitle())
            .description(script.getDescription())
            .coverImage(script.getCoverImage())
            .images(parseImagesToList(script.getImages()))
            .category(script.getCategory())
            .playerCountMin(script.getPlayerCountMin())
            .playerCountMax(script.getPlayerCountMax())
            .duration(script.getDuration())
            .difficulty(script.getDifficulty())
            .ageRange(script.getAgeRange())
            .tagList(parseTagsToList(script.getTags()))
            .highlights(parseJsonToList(script.getHighlights()))
            .warnings(parseTagsToList(script.getWarnings()))
            .price(script.getPrice())
            .averageRating(script.getAverageRating())
            .reviewCount(script.getReviewCount())
            .playCount(script.getPlayCount())
            .isFavorite(isFavorite)
            .characters(characterDTOs)
            .rules(ruleDTOs)
            .recentReviews(reviewDTOs)
            .createTime(script.getCreateTime())
            .updateTime(script.getUpdateTime())
            .build();
    }

    @Override
    public PageResponse<ScriptDTO> searchScripts(ScriptSearchDTO searchDTO) {
        // 使用Elasticsearch进行搜索
        return scriptSearchService.searchScripts(searchDTO);
    }

    @Override
    @Cacheable(value = "script:popular", key = "#limit")
    public List<ScriptDTO> getPopularScripts(Integer limit) {
        List<Script> scripts = scriptMapper.selectHotScripts(limit);
        return scripts.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "script:recommend", key = "#userId + '_' + #limit")
    public List<ScriptDTO> getRecommendedScripts(Long userId, Integer limit) {
        List<Script> scripts = scriptMapper.selectRecommendedScripts(userId, limit);
        return scripts.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    @Override
    @CacheEvict(value = {"script:detail", "script:popular", "script:recommend"}, allEntries = true)
    public void updateScriptStats(Long scriptId) {
        scriptMapper.updateScriptStats(scriptId);
    }

    @Override
    @Cacheable(value = "script:category:stats", key = "'all'")
    public List<CategoryStatsDTO> getCategoryStats() {
        // 定义所有分类
        Map<String, String> categoryMap = Map.of(
            "推理", "推理",
            "恐怖", "恐怖",
            "情感", "情感",
            "欢乐", "欢乐",
            "古风", "古风",
            "现代", "现代"
        );

        List<CategoryStatsDTO> result = new ArrayList<>();
        
        for (Map.Entry<String, String> entry : categoryMap.entrySet()) {
            String category = entry.getKey();
            String displayName = entry.getValue();
            Integer count = scriptMapper.countByCategory(category);
            
            result.add(CategoryStatsDTO.builder()
                .category(category)
                .displayName(displayName)
                .count(count)
                .build());
        }
        
        return result;
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper<Script> buildQueryWrapper(ScriptSearchDTO searchDTO) {
        QueryWrapper<Script> queryWrapper = new QueryWrapper<>();
        
        // 基础条件
        queryWrapper.eq("status", 1); // 只查询上架状态的剧本
        
        // 类型筛选
        if (StringUtils.hasText(searchDTO.getCategory())) {
            queryWrapper.eq("category", searchDTO.getCategory());
        }
        
        // 玩家数量筛选
        if (searchDTO.getPlayerCountMin() != null) {
            queryWrapper.le("player_count_min", searchDTO.getPlayerCountMin());
        }
        if (searchDTO.getPlayerCountMax() != null) {
            queryWrapper.ge("player_count_max", searchDTO.getPlayerCountMax());
        }
        
        // 难度筛选
        if (!CollectionUtils.isEmpty(searchDTO.getDifficulties())) {
            queryWrapper.in("difficulty", searchDTO.getDifficulties());
        }
        
        // 价格筛选
        if (searchDTO.getPriceMin() != null) {
            queryWrapper.ge("price", searchDTO.getPriceMin());
        }
        if (searchDTO.getPriceMax() != null) {
            queryWrapper.le("price", searchDTO.getPriceMax());
        }
        
        // 排序
        handleSort(queryWrapper, searchDTO.getSortBy(), searchDTO.getSortOrder());
        
        return queryWrapper;
    }

    /**
     * 处理排序
     */
    private void handleSort(QueryWrapper<Script> queryWrapper, String sortBy, String sortOrder) {
        boolean isAsc = "asc".equalsIgnoreCase(sortOrder);
        
        switch (sortBy) {
            case "createTime":
                queryWrapper.orderBy(true, isAsc, "create_time");
                break;
            case "averageRating":
                queryWrapper.orderBy(true, isAsc, "average_rating");
                break;
            case "playCount":
                queryWrapper.orderBy(true, isAsc, "play_count");
                break;
            case "reviewCount":
                queryWrapper.orderBy(true, isAsc, "review_count");
                break;
            case "price":
                queryWrapper.orderBy(true, isAsc, "price");
                break;
            default:
                queryWrapper.orderByDesc("create_time");
        }
    }

    /**
     * 转换为ScriptDTO
     */
    private ScriptDTO convertToDTO(Script script) {
        // 检查是否收藏
        boolean isFavorite = false;
        UserDTO currentUser = UserHolder.getUser();
        if (currentUser != null) {
            isFavorite = userFavoriteService.isFavorited(currentUser.getId(), script.getId(), "script");
        }

        return ScriptDTO.builder()
            .id(script.getId())
            .title(script.getTitle())
            .description(script.getDescription())
            .coverImage(script.getCoverImage())
            .category(script.getCategory())
            .playerCountRange(script.getPlayerCountMin() + "-" + script.getPlayerCountMax() + "人")
            .playerCountMin(script.getPlayerCountMin())
            .playerCountMax(script.getPlayerCountMax())
            .duration(script.getDuration())
            .difficulty(script.getDifficulty())
            .ageRange(script.getAgeRange())
            .tagList(parseTagsToList(script.getTags()))
            .highlights(parseJsonToList(script.getHighlights()))
            .warnings(parseTagsToList(script.getWarnings()))
            .price(script.getPrice())
            .averageRating(script.getAverageRating())
            .reviewCount(script.getReviewCount())
            .playCount(script.getPlayCount())
            .isFavorite(isFavorite)
            .createTime(script.getCreateTime())
            .updateTime(script.getUpdateTime())
            .build();
    }

    /**
     * 转换角色为DTO
     */
    private ScriptCharacterDTO convertCharacterToDTO(ScriptCharacter character) {
        return ScriptCharacterDTO.builder()
            .id(character.getId())
            .name(character.getName())
            .title(character.getTitle())
            .gender(character.getGender())
            .age(character.getAge())
            .occupation(character.getOccupation())
            .description(character.getDescription())
            .avatar(character.getAvatar())
            .traits(character.getTraits())
            .difficulty(character.getDifficulty())
            .secretsCount(character.getSecretsCount())
            .sortOrder(character.getSortOrder())
            .build();
    }

    /**
     * 转换规则为DTO
     */
    private ScriptRuleDTO convertRuleToDTO(ScriptRule rule) {
        return ScriptRuleDTO.builder()
            .id(rule.getId())
            .ruleType(rule.getRuleType())
            .title(rule.getTitle())
            .content(rule.getContent())
            .sortOrder(rule.getSortOrder())
            .isImportant(rule.getIsImportant())
            .build();
    }

    /**
     * 转换评价为DTO
     */
    private ScriptReviewDTO convertReviewToDTO(ScriptReview review) {
        // 获取用户信息
        User user = userMapper.selectById(review.getUserId());
        
        return ScriptReviewDTO.builder()
            .id(review.getId())
            .userId(review.getUserId())
            .userNickname(review.getIsAnonymous() ? "匿名用户" : user.getNickName())
            .userAvatar(review.getIsAnonymous() ? null : user.getIcon())
            .rating(review.getRating())
            .title(review.getTitle())
            .content(review.getContent())
            .images(parseImagesToList(review.getImages()))
            .likedCount(review.getLikedCount())
            .helpfulCount(review.getHelpfulCount())
            .isAnonymous(review.getIsAnonymous())
            .tags(parseJsonToList(review.getTags()))
            .createTime(review.getCreateTime())
            .build();
    }

    /**
     * 解析标签字符串为列表
     */
    private List<String> parseTagsToList(String tags) {
        if (!StringUtils.hasText(tags)) {
            return new ArrayList<>();
        }
        return Arrays.asList(tags.split(","));
    }

    /**
     * 解析图片字符串为列表
     */
    private List<String> parseImagesToList(String images) {
        if (!StringUtils.hasText(images)) {
            return new ArrayList<>();
        }
        return Arrays.asList(images.split(","));
    }

    /**
     * 解析JSON字符串为列表
     */
    private List<String> parseJsonToList(String json) {
        if (!StringUtils.hasText(json)) {
            return new ArrayList<>();
        }
        // 这里简化处理，实际项目中应该使用JSON解析库
        return Arrays.asList(json.replace("[", "").replace("]", "").split(","));
    }
}