package com.scriptmurder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.scriptmurder.dto.PageResponse;
import com.scriptmurder.dto.ScriptStatusHistoryDTO;
import com.scriptmurder.dto.ScriptStatusStatsDTO;
import com.scriptmurder.entity.Script;
import com.scriptmurder.entity.ScriptStatusHistory;
import com.scriptmurder.enums.ScriptStatus;
import com.scriptmurder.exception.BusinessException;
import com.scriptmurder.mapper.ScriptMapper;
import com.scriptmurder.mapper.ScriptStatusHistoryMapper;
import com.scriptmurder.service.IScriptStatusService;
import com.scriptmurder.service.IScriptSearchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 剧本状态管理服务实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ScriptStatusServiceImpl implements IScriptStatusService {

    @Autowired
    private ScriptMapper scriptMapper;

    @Autowired
    private ScriptStatusHistoryMapper statusHistoryMapper;

    @Autowired
    private IScriptSearchService scriptSearchService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean submitForReview(Long scriptId, Long userId) {
        Script script = scriptMapper.selectById(scriptId);
        if (script == null) {
            throw new BusinessException("剧本不存在");
        }

        // 验证用户权限
        if (!script.getCreatorId().equals(userId)) {
            throw new BusinessException("无权限操作此剧本");
        }

        ScriptStatus currentStatus = ScriptStatus.fromCode(script.getStatus());
        
        // 验证状态转换合法性
        if (!canTransitionTo(currentStatus, ScriptStatus.REVIEWING)) {
            throw new BusinessException("当前状态不允许提交审核");
        }

        // 验证剧本信息完整性
        Map<String, Object> validation = validateScriptCompleteness(scriptId);
        if (!(Boolean) validation.get("isComplete")) {
            throw new BusinessException("剧本信息不完整，无法提交审核：" + validation.get("missingFields"));
        }

        // 更新状态
        script.setStatus(ScriptStatus.REVIEWING.getCode());
        script.setUpdateTime(LocalDateTime.now());
        scriptMapper.updateById(script);

        // 记录状态变更历史
        recordStatusChange(scriptId, currentStatus.getCode(), ScriptStatus.REVIEWING.getCode(),
                          userId, "USER", "提交审核", null);

        log.info("剧本提交审核成功: scriptId={}, userId={}", scriptId, userId);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean reviewScript(Long scriptId, Long reviewerId, boolean approved, String comment) {
        Script script = scriptMapper.selectById(scriptId);
        if (script == null) {
            throw new BusinessException("剧本不存在");
        }

        if (!ScriptStatus.REVIEWING.getCode().equals(script.getStatus())) {
            throw new BusinessException("剧本不在审核状态");
        }

        ScriptStatus newStatus = approved ? ScriptStatus.PUBLISHED : ScriptStatus.REJECTED;

        // 更新剧本状态
        script.setStatus(newStatus.getCode());
        script.setReviewerId(reviewerId);
        script.setReviewTime(LocalDateTime.now());
        script.setReviewComment(comment);
        script.setUpdateTime(LocalDateTime.now());
        scriptMapper.updateById(script);

        // 计算审核耗时
        Integer reviewDuration = calculateReviewDuration(script.getCreateTime(), script.getReviewTime());

        // 记录状态变更历史
        recordStatusChange(scriptId, ScriptStatus.REVIEWING.getCode(), newStatus.getCode(),
                          reviewerId, "ADMIN", approved ? "审核通过" : "审核拒绝", comment, reviewDuration);

        // 如果审核通过，更新搜索索引
        if (approved) {
            try {
                scriptSearchService.syncScriptToElasticsearch(script);
            } catch (Exception e) {
                log.error("更新搜索索引失败: scriptId={}", scriptId, e);
            }
        }

        log.info("剧本审核完成: scriptId={}, reviewerId={}, approved={}", scriptId, reviewerId, approved);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean togglePublishStatus(Long scriptId, Long operatorId, boolean publish, String reason) {
        Script script = scriptMapper.selectById(scriptId);
        if (script == null) {
            throw new BusinessException("剧本不存在");
        }

        ScriptStatus currentStatus = ScriptStatus.fromCode(script.getStatus());
        ScriptStatus targetStatus = publish ? ScriptStatus.PUBLISHED : ScriptStatus.UNPUBLISHED;

        if (!canTransitionTo(currentStatus, targetStatus)) {
            throw new BusinessException("当前状态不允许此操作");
        }

        // 更新状态
        script.setStatus(targetStatus.getCode());
        script.setUpdateTime(LocalDateTime.now());
        scriptMapper.updateById(script);

        // 记录状态变更历史
        recordStatusChange(scriptId, currentStatus.getCode(), targetStatus.getCode(),
                          operatorId, "ADMIN", publish ? "上架" : "下架", reason);

        // 更新搜索索引
        try {
            if (publish) {
                scriptSearchService.syncScriptToElasticsearch(script);
            } else {
                scriptSearchService.removeScriptFromElasticsearch(scriptId);
            }
        } catch (Exception e) {
            log.error("更新搜索索引失败: scriptId={}", scriptId, e);
        }

        log.info("剧本状态变更成功: scriptId={}, operatorId={}, publish={}", scriptId, operatorId, publish);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchReviewScripts(List<Long> scriptIds, Long reviewerId, boolean approved, String comment) {
        int successCount = 0;
        
        for (Long scriptId : scriptIds) {
            try {
                if (reviewScript(scriptId, reviewerId, approved, comment)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量审核失败: scriptId={}", scriptId, e);
            }
        }
        
        log.info("批量审核完成: 总数={}, 成功={}, 审核员={}", scriptIds.size(), successCount, reviewerId);
        return successCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchTogglePublishStatus(List<Long> scriptIds, Long operatorId, boolean publish, String reason) {
        int successCount = 0;
        
        for (Long scriptId : scriptIds) {
            try {
                if (togglePublishStatus(scriptId, operatorId, publish, reason)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量状态变更失败: scriptId={}", scriptId, e);
            }
        }
        
        log.info("批量状态变更完成: 总数={}, 成功={}, 操作员={}", scriptIds.size(), successCount, operatorId);
        return successCount;
    }

    @Override
    public PageResponse<Script> getPendingReviewScripts(Integer page, Integer size) {
        Page<Script> pageParam = new Page<>(page, size);
        QueryWrapper<Script> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", ScriptStatus.REVIEWING.getCode())
                   .orderByAsc("create_time");

        IPage<Script> result = scriptMapper.selectPage(pageParam, queryWrapper);
        
        return PageResponse.<Script>builder()
                .records(result.getRecords())
                .total(result.getTotal())
                .current(page.longValue())
                .size(size.longValue())
                .pages(result.getPages())
                .build();
    }

    @Override
    public PageResponse<ScriptStatusHistoryDTO> getStatusHistory(Long scriptId, Integer page, Integer size) {
        Page<ScriptStatusHistoryDTO> pageParam = new Page<>(page, size);
        IPage<ScriptStatusHistoryDTO> result = statusHistoryMapper.selectHistoryByScriptId(pageParam, scriptId);
        
        // 处理状态名称和操作类型名称
        result.getRecords().forEach(this::enrichHistoryDTO);
        
        return PageResponse.<ScriptStatusHistoryDTO>builder()
                .records(result.getRecords())
                .total(result.getTotal())
                .current(page.longValue())
                .size(size.longValue())
                .pages(result.getPages())
                .build();
    }

    @Override
    public ScriptStatusStatsDTO getStatusStats() {
        // 获取基础统计数据
        Map<String, Object> stats = statusHistoryMapper.selectStatusStats();
        Long totalScripts = ((Number) stats.get("total_count")).longValue();
        Long publishedCount = ((Number) stats.get("published_count")).longValue();
        Long unpublishedCount = ((Number) stats.get("unpublished_count")).longValue();
        Long reviewingCount = ((Number) stats.get("reviewing_count")).longValue();
        Long rejectedCount = ((Number) stats.get("rejected_count")).longValue();
        Long draftCount = ((Number) stats.get("draft_count")).longValue();

        // 计算百分比
        Map<String, Double> statusPercentage = new HashMap<>();
        if (totalScripts > 0) {
            statusPercentage.put("published", publishedCount * 100.0 / totalScripts);
            statusPercentage.put("unpublished", unpublishedCount * 100.0 / totalScripts);
            statusPercentage.put("reviewing", reviewingCount * 100.0 / totalScripts);
            statusPercentage.put("rejected", rejectedCount * 100.0 / totalScripts);
            statusPercentage.put("draft", draftCount * 100.0 / totalScripts);
        }

        // 构建状态分布详情
        List<ScriptStatusStatsDTO.StatusDistributionItem> statusDistribution = Arrays.asList(
            ScriptStatusStatsDTO.StatusDistributionItem.builder()
                .statusCode(1).statusName("已上架").count(publishedCount)
                .percentage(statusPercentage.getOrDefault("published", 0.0))
                .color("#52c41a").build(),
            ScriptStatusStatsDTO.StatusDistributionItem.builder()
                .statusCode(2).statusName("已下架").count(unpublishedCount)
                .percentage(statusPercentage.getOrDefault("unpublished", 0.0))
                .color("#faad14").build(),
            ScriptStatusStatsDTO.StatusDistributionItem.builder()
                .statusCode(3).statusName("审核中").count(reviewingCount)
                .percentage(statusPercentage.getOrDefault("reviewing", 0.0))
                .color("#1890ff").build(),
            ScriptStatusStatsDTO.StatusDistributionItem.builder()
                .statusCode(4).statusName("审核拒绝").count(rejectedCount)
                .percentage(statusPercentage.getOrDefault("rejected", 0.0))
                .color("#ff4d4f").build(),
            ScriptStatusStatsDTO.StatusDistributionItem.builder()
                .statusCode(5).statusName("草稿").count(draftCount)
                .percentage(statusPercentage.getOrDefault("draft", 0.0))
                .color("#d9d9d9").build()
        );

        // 获取其他统计数据
        Long todayNewScripts = statusHistoryMapper.selectTodayNewScripts();
        Long todayReviewedCount = statusHistoryMapper.selectTodayReviewedCount();
        Double avgReviewHours = statusHistoryMapper.selectAvgReviewHours();
        Double approvalRate = statusHistoryMapper.selectApprovalRate();

        // 获取最近趋势
        List<Map<String, Object>> trendsData = statusHistoryMapper.selectRecentTrends();
        List<ScriptStatusStatsDTO.StatusTrendItem> recentTrends = trendsData.stream()
            .map(this::convertToTrendItem)
            .collect(Collectors.toList());

        return ScriptStatusStatsDTO.builder()
                .totalScripts(totalScripts)
                .publishedCount(publishedCount)
                .unpublishedCount(unpublishedCount)
                .reviewingCount(reviewingCount)
                .rejectedCount(rejectedCount)
                .draftCount(draftCount)
                .statusPercentage(statusPercentage)
                .statusDistribution(statusDistribution)
                .todayNewScripts(todayNewScripts)
                .todayReviewedCount(todayReviewedCount)
                .pendingReviewCount(reviewingCount)
                .avgReviewHours(avgReviewHours)
                .approvalRate(approvalRate)
                .recentTrends(recentTrends)
                .build();
    }

    @Override
    public Map<String, Object> getReviewEfficiencyStats(Integer days) {
        List<Map<String, Object>> stats = statusHistoryMapper.selectReviewEfficiencyStats(days);
        
        Map<String, Object> result = new HashMap<>();
        result.put("period", days + "天");
        result.put("data", stats);
        result.put("summary", calculateEfficiencySummary(stats));
        
        return result;
    }

    @Override
    public boolean canTransitionToStatus(Long scriptId, Integer targetStatus) {
        Script script = scriptMapper.selectById(scriptId);
        if (script == null) {
            return false;
        }
        
        ScriptStatus currentStatus = ScriptStatus.fromCode(script.getStatus());
        ScriptStatus target = ScriptStatus.fromCode(targetStatus);
        
        return canTransitionTo(currentStatus, target);
    }

    @Override
    public List<String> getAvailableActions(Long scriptId, Long userId) {
        Script script = scriptMapper.selectById(scriptId);
        if (script == null) {
            return Collections.emptyList();
        }

        List<String> actions = new ArrayList<>();
        ScriptStatus currentStatus = ScriptStatus.fromCode(script.getStatus());
        
        // 根据当前状态和用户权限确定可用操作
        switch (currentStatus) {
            case DRAFT:
                if (script.getCreatorId().equals(userId)) {
                    actions.add("submit_review");
                    actions.add("edit");
                    actions.add("delete");
                }
                break;
            case REVIEWING:
                // 管理员可以审核
                actions.add("approve");
                actions.add("reject");
                break;
            case PUBLISHED:
                actions.add("unpublish");
                if (script.getCreatorId().equals(userId)) {
                    actions.add("edit");
                }
                break;
            case UNPUBLISHED:
                actions.add("publish");
                actions.add("submit_review");
                break;
            case REJECTED:
                if (script.getCreatorId().equals(userId)) {
                    actions.add("edit");
                    actions.add("submit_review");
                    actions.add("save_draft");
                }
                break;
        }
        
        return actions;
    }

    @Override
    public boolean autoReviewScript(Long scriptId) {
        // 自动审核逻辑（基于规则）
        Script script = scriptMapper.selectById(scriptId);
        if (script == null || !ScriptStatus.REVIEWING.getCode().equals(script.getStatus())) {
            return false;
        }

        // 检查是否满足自动审核条件
        Map<String, Object> validation = validateScriptCompleteness(scriptId);
        boolean isComplete = (Boolean) validation.get("isComplete");
        
        if (isComplete) {
            // 自动通过审核
            return reviewScript(scriptId, 0L, true, "系统自动审核通过");
        }
        
        return false;
    }

    @Override
    public Map<String, Integer> getUserScriptStatusDistribution(Long userId) {
        List<Map<String, Object>> distribution = statusHistoryMapper.selectUserScriptStatusDistribution(userId);
        
        Map<String, Integer> result = new HashMap<>();
        for (Map<String, Object> item : distribution) {
            Integer status = (Integer) item.get("status");
            Integer count = ((Number) item.get("count")).intValue();
            String statusName = ScriptStatus.fromCode(status).getName();
            result.put(statusName, count);
        }
        
        return result;
    }

    @Override
    public void sendStatusChangeNotification(Long scriptId, Integer fromStatus, Integer toStatus, 
                                           Long operatorId, String comment) {
        // TODO: 实现通知发送逻辑
        log.info("发送状态变更通知: scriptId={}, fromStatus={}, toStatus={}, operatorId={}", 
                scriptId, fromStatus, toStatus, operatorId);
    }

    @Override
    public Map<String, Object> validateScriptCompleteness(Long scriptId) {
        Script script = scriptMapper.selectById(scriptId);
        if (script == null) {
            return Map.of("isComplete", false, "missingFields", "剧本不存在");
        }

        List<String> missingFields = new ArrayList<>();
        
        if (!StringUtils.hasText(script.getTitle())) {
            missingFields.add("标题");
        }
        if (!StringUtils.hasText(script.getDescription())) {
            missingFields.add("描述");
        }
        if (!StringUtils.hasText(script.getCategory())) {
            missingFields.add("分类");
        }
        if (script.getPlayerCountMin() == null || script.getPlayerCountMax() == null) {
            missingFields.add("玩家人数");
        }
        if (script.getDifficulty() == null) {
            missingFields.add("难度等级");
        }
        if (script.getPrice() == null) {
            missingFields.add("价格");
        }

        boolean isComplete = missingFields.isEmpty();
        
        return Map.of(
            "isComplete", isComplete,
            "missingFields", String.join("、", missingFields),
            "completeness", isComplete ? 100 : (8 - missingFields.size()) * 100 / 8
        );
    }

    @Override
    public Map<String, Object> getStatusChangeStats(String startDate, String endDate) {
        LocalDateTime start = LocalDateTime.parse(startDate + " 00:00:00", 
                                                 DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        LocalDateTime end = LocalDateTime.parse(endDate + " 23:59:59", 
                                               DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        
        List<Map<String, Object>> stats = statusHistoryMapper.selectStatusChangeStats(start, end);
        
        Map<String, Object> result = new HashMap<>();
        result.put("period", startDate + " 至 " + endDate);
        result.put("data", stats);
        result.put("summary", calculateChangeStatsSummary(stats));
        
        return result;
    }

    /**
     * 验证状态转换合法性
     */
    private boolean canTransitionTo(ScriptStatus from, ScriptStatus to) {
        Map<ScriptStatus, Set<ScriptStatus>> validTransitions = Map.of(
            ScriptStatus.DRAFT, Set.of(ScriptStatus.REVIEWING),
            ScriptStatus.REVIEWING, Set.of(ScriptStatus.PUBLISHED, ScriptStatus.REJECTED),
            ScriptStatus.REJECTED, Set.of(ScriptStatus.REVIEWING, ScriptStatus.DRAFT),
            ScriptStatus.PUBLISHED, Set.of(ScriptStatus.UNPUBLISHED),
            ScriptStatus.UNPUBLISHED, Set.of(ScriptStatus.PUBLISHED, ScriptStatus.REVIEWING)
        );
        
        return validTransitions.getOrDefault(from, Set.of()).contains(to);
    }

    /**
     * 记录状态变更历史
     */
    private void recordStatusChange(Long scriptId, Integer fromStatus, Integer toStatus,
                                   Long operatorId, String operatorType, String reason, String comment) {
        recordStatusChange(scriptId, fromStatus, toStatus, operatorId, operatorType, reason, comment, null);
    }

    /**
     * 记录状态变更历史（带审核耗时）
     */
    private void recordStatusChange(Long scriptId, Integer fromStatus, Integer toStatus,
                                   Long operatorId, String operatorType, String reason, String comment, 
                                   Integer reviewDuration) {
        ScriptStatusHistory history = ScriptStatusHistory.builder()
                .scriptId(scriptId)
                .fromStatus(fromStatus)
                .toStatus(toStatus)
                .operatorId(operatorId)
                .operatorType(operatorType)
                .reason(reason)
                .comment(comment)
                .reviewDuration(reviewDuration)
                .createTime(LocalDateTime.now())
                .build();
        
        statusHistoryMapper.insert(history);
    }

    /**
     * 计算审核耗时（分钟）
     */
    private Integer calculateReviewDuration(LocalDateTime createTime, LocalDateTime reviewTime) {
        if (createTime == null || reviewTime == null) {
            return null;
        }
        
        return (int) java.time.Duration.between(createTime, reviewTime).toMinutes();
    }

    /**
     * 丰富历史记录DTO
     */
    private void enrichHistoryDTO(ScriptStatusHistoryDTO dto) {
        // 设置状态名称
        if (dto.getFromStatus() != null) {
            dto.setFromStatusName(ScriptStatus.fromCode(dto.getFromStatus()).getName());
        }
        if (dto.getToStatus() != null) {
            dto.setToStatusName(ScriptStatus.fromCode(dto.getToStatus()).getName());
        }
        
        // 设置操作类型名称
        String operatorTypeName = switch (dto.getOperatorType()) {
            case "USER" -> "用户";
            case "ADMIN" -> "管理员";
            case "SYSTEM" -> "系统";
            default -> "未知";
        };
        dto.setOperatorTypeName(operatorTypeName);
        
        // 设置审核耗时描述
        if (dto.getReviewDuration() != null) {
            int minutes = dto.getReviewDuration();
            if (minutes < 60) {
                dto.setReviewDurationDesc(minutes + "分钟");
            } else {
                int hours = minutes / 60;
                int remainingMinutes = minutes % 60;
                dto.setReviewDurationDesc(hours + "小时" + (remainingMinutes > 0 ? remainingMinutes + "分钟" : ""));
            }
        }
    }

    /**
     * 转换趋势数据项
     */
    private ScriptStatusStatsDTO.StatusTrendItem convertToTrendItem(Map<String, Object> data) {
        Long submittedCount = ((Number) data.getOrDefault("submitted_count", 0)).longValue();
        Long reviewedCount = ((Number) data.getOrDefault("reviewed_count", 0)).longValue();
        Long approvedCount = ((Number) data.getOrDefault("approved_count", 0)).longValue();
        Long rejectedCount = ((Number) data.getOrDefault("rejected_count", 0)).longValue();
        
        Double approvalRate = reviewedCount > 0 ? approvedCount * 100.0 / reviewedCount : 0.0;
        
        return ScriptStatusStatsDTO.StatusTrendItem.builder()
                .date(data.get("date").toString())
                .submittedCount(submittedCount)
                .reviewedCount(reviewedCount)
                .approvedCount(approvedCount)
                .rejectedCount(rejectedCount)
                .approvalRate(Math.round(approvalRate * 100.0) / 100.0)
                .build();
    }

    /**
     * 计算效率统计摘要
     */
    private Map<String, Object> calculateEfficiencySummary(List<Map<String, Object>> stats) {
        if (stats.isEmpty()) {
            return Map.of("totalSubmitted", 0, "totalReviewed", 0, "avgReviewMinutes", 0.0);
        }
        
        long totalSubmitted = stats.stream()
                .mapToLong(s -> ((Number) s.getOrDefault("submitted_count", 0)).longValue())
                .sum();
        long totalReviewed = stats.stream()
                .mapToLong(s -> ((Number) s.getOrDefault("reviewed_count", 0)).longValue())
                .sum();
        double avgReviewMinutes = stats.stream()
                .filter(s -> s.get("avg_review_minutes") != null)
                .mapToDouble(s -> ((Number) s.get("avg_review_minutes")).doubleValue())
                .average()
                .orElse(0.0);
        
        return Map.of(
            "totalSubmitted", totalSubmitted,
            "totalReviewed", totalReviewed,
            "avgReviewMinutes", Math.round(avgReviewMinutes * 100.0) / 100.0,
            "reviewRate", totalSubmitted > 0 ? Math.round(totalReviewed * 100.0 / totalSubmitted * 100.0) / 100.0 : 0.0
        );
    }

    /**
     * 计算状态变更统计摘要
     */
    private Map<String, Object> calculateChangeStatsSummary(List<Map<String, Object>> stats) {
        Map<String, Long> statusChanges = new HashMap<>();
        
        for (Map<String, Object> stat : stats) {
            String key = stat.get("from_status") + "->" + stat.get("to_status");
            Long count = ((Number) stat.get("count")).longValue();
            statusChanges.put(key, statusChanges.getOrDefault(key, 0L) + count);
        }
        
        return Map.of(
            "totalChanges", statusChanges.values().stream().mapToLong(Long::longValue).sum(),
            "changeTypes", statusChanges.size(),
            "topChanges", statusChanges.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(5)
                .collect(Collectors.toMap(
                    Map.Entry::getKey,
                    Map.Entry::getValue,
                    (e1, e2) -> e1,
                    LinkedHashMap::new
                ))
        );
    }
}
