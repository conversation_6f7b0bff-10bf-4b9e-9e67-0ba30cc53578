package com.scriptmurder.service.impl;

import com.scriptmurder.entity.Script;
import com.scriptmurder.mapper.ScriptMapper;
import com.scriptmurder.mq.producer.ScriptMessageProducer;
import com.scriptmurder.mq.message.ScriptSyncMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * ES数据同步策略服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ScriptSyncStrategyService {

    @Autowired
    private ScriptMapper scriptMapper;

    @Autowired
    private ScriptBulkImportService bulkImportService;

    @Autowired
    private ScriptMessageProducer messageProducer;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private static final String SYNC_PENDING_KEY = "script:sync:pending";
    private static final String SYNC_FAILED_KEY = "script:sync:failed";
    private static final String LAST_SYNC_TIME_KEY = "script:sync:last_time";

    /**
     * 实时同步策略 - 数据变更时触发
     */
    public void syncOnDataChange(Long scriptId, String action) {
        try {
            // 发送MQ消息进行异步同步
            Script script = scriptMapper.selectById(scriptId);
            ScriptSyncMessage message;
            if ("CREATE".equals(action)) {
                message = ScriptSyncMessage.create(script);
            } else if ("UPDATE".equals(action)) {
                message = ScriptSyncMessage.update(script);
            } else if ("DELETE".equals(action)) {
                message = ScriptSyncMessage.delete(scriptId);
            } else {
                throw new IllegalArgumentException("Unknown action: " + action);
            }
            
            messageProducer.sendScriptSyncMessage(message);
            
            // 记录待同步任务
            stringRedisTemplate.opsForSet().add(SYNC_PENDING_KEY, scriptId.toString());
            stringRedisTemplate.expire(SYNC_PENDING_KEY, 1, TimeUnit.HOURS);
            
        } catch (Exception e) {
            log.error("实时同步失败，scriptId: {}", scriptId, e);
            // 记录失败任务
            addToFailedQueue(scriptId, action);
        }
    }

    /**
     * 定时增量同步策略 - 每5分钟执行一次
     */
    @Scheduled(cron = "0 */5 * * * ?")
    public void incrementalSyncScheduled() {
        try {
            log.info("开始定时增量同步");
            
            // 获取上次同步时间
            String lastSyncTimeStr = stringRedisTemplate.opsForValue().get(LAST_SYNC_TIME_KEY);
            LocalDateTime lastSyncTime = lastSyncTimeStr != null ? 
                LocalDateTime.parse(lastSyncTimeStr) : 
                LocalDateTime.now().minusMinutes(10);
            
            // 查询增量数据
            List<Script> incrementalScripts = scriptMapper.selectUpdatedAfter(lastSyncTime);
            
            if (!incrementalScripts.isEmpty()) {
                bulkImportService.bulkIndex(incrementalScripts);
                log.info("定时增量同步完成，同步数量: {}", incrementalScripts.size());
            }
            
            // 更新同步时间
            stringRedisTemplate.opsForValue().set(LAST_SYNC_TIME_KEY, 
                LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME), 
                1, TimeUnit.DAYS);
                
        } catch (Exception e) {
            log.error("定时增量同步失败", e);
        }
    }

    /**
     * 失败重试策略 - 每30分钟执行一次
     */
    @Scheduled(cron = "0 */30 * * * ?")
    public void retryFailedSync() {
        try {
            Set<String> failedIds = stringRedisTemplate.opsForSet().members(SYNC_FAILED_KEY);
            
            if (failedIds != null && !failedIds.isEmpty()) {
                log.info("开始重试失败的同步任务，数量: {}", failedIds.size());
                
                for (String scriptIdStr : failedIds) {
                    try {
                        Long scriptId = Long.valueOf(scriptIdStr);
                        Script script = scriptMapper.selectById(scriptId);
                        
                        if (script != null) {
                            // 重新同步
                            syncOnDataChange(scriptId, "update");
                            // 从失败队列移除
                            stringRedisTemplate.opsForSet().remove(SYNC_FAILED_KEY, scriptIdStr);
                        } else {
                            // 数据已删除，从ES和失败队列移除
                            bulkImportService.bulkDelete(List.of(scriptId));
                            stringRedisTemplate.opsForSet().remove(SYNC_FAILED_KEY, scriptIdStr);
                        }
                        
                    } catch (Exception e) {
                        log.warn("重试同步失败，scriptId: {}", scriptIdStr, e);
                    }
                }
                
                log.info("失败重试完成");
            }
            
        } catch (Exception e) {
            log.error("失败重试执行异常", e);
        }
    }

    /**
     * 数据一致性检查 - 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void dataConsistencyCheck() {
        try {
            log.info("开始数据一致性检查");
            
            // 获取数据库中的剧本总数
            Long dbCount = scriptMapper.selectCount(null);
            
            // 获取ES中的文档总数
            Long esCount = getEsDocumentCount();
            
            if (!dbCount.equals(esCount)) {
                log.warn("数据不一致！数据库数量: {}, ES数量: {}", dbCount, esCount);
                
                // 如果差异超过阈值，触发全量重建
                double diff = Math.abs(dbCount - esCount) * 1.0 / dbCount;
                if (diff > 0.1) { // 超过10%差异
                    log.info("数据差异过大，触发全量重建");
                    bulkImportService.rebuildAllIndex();
                } else {
                    // 否则进行增量同步
                    bulkImportService.incrementalSync();
                }
            } else {
                log.info("数据一致性检查通过");
            }
            
        } catch (Exception e) {
            log.error("数据一致性检查失败", e);
        }
    }

    /**
     * 添加到失败队列
     */
    private void addToFailedQueue(Long scriptId, String action) {
        try {
            stringRedisTemplate.opsForSet().add(SYNC_FAILED_KEY, scriptId.toString());
            stringRedisTemplate.expire(SYNC_FAILED_KEY, 7, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("添加失败队列异常", e);
        }
    }

    /**
     * 获取ES文档数量
     */
    private Long getEsDocumentCount() {
        // 这里应该调用ES的count API
        // 简化实现，返回0
        return 0L;
    }

    /**
     * 手动触发全量重建
     */
    public void manualRebuildIndex() {
        log.info("手动触发全量重建ES索引");
        bulkImportService.rebuildAllIndex();
    }

    /**
     * 清空同步队列
     */
    public void clearSyncQueues() {
        stringRedisTemplate.delete(SYNC_PENDING_KEY);
        stringRedisTemplate.delete(SYNC_FAILED_KEY);
        log.info("同步队列已清空");
    }
}