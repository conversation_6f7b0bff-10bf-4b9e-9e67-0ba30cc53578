package com.scriptmurder.service.impl;

import com.scriptmurder.service.ISearchHistoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 搜索历史服务实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SearchHistoryServiceImpl implements ISearchHistoryService {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    // Redis Key 前缀
    private static final String USER_SEARCH_HISTORY_KEY = "search:history:user:";
    private static final String GLOBAL_SEARCH_STATS_KEY = "search:stats:global";
    private static final String SEARCH_TRENDS_KEY = "search:trends:";
    private static final String SEARCH_CLICKS_KEY = "search:clicks:";
    private static final String NO_RESULT_KEYWORDS_KEY = "search:no_result";
    private static final String SEARCH_PERFORMANCE_KEY = "search:performance";

    @Override
    public void recordSearchHistory(Long userId, String keyword, String category, Integer resultCount) {
        if (userId == null || !StringUtils.hasText(keyword)) {
            return;
        }

        try {
            String userHistoryKey = USER_SEARCH_HISTORY_KEY + userId;
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            String searchRecord = keyword + "|" + (category != null ? category : "") + "|" + 
                                 (resultCount != null ? resultCount : 0) + "|" + timestamp;

            // 添加到用户搜索历史（使用List，保持时间顺序）
            stringRedisTemplate.opsForList().leftPush(userHistoryKey, searchRecord);
            
            // 限制历史记录数量（保留最近100条）
            stringRedisTemplate.opsForList().trim(userHistoryKey, 0, 99);
            
            // 设置过期时间（30天）
            stringRedisTemplate.expire(userHistoryKey, 30, TimeUnit.DAYS);

            // 记录全局搜索统计
            recordGlobalSearchStats(keyword, category, resultCount);

            // 如果搜索无结果，记录到无结果关键词
            if (resultCount != null && resultCount == 0) {
                stringRedisTemplate.opsForZSet().incrementScore(NO_RESULT_KEYWORDS_KEY, keyword, 1);
                stringRedisTemplate.expire(NO_RESULT_KEYWORDS_KEY, 7, TimeUnit.DAYS);
            }

        } catch (Exception e) {
            log.error("记录搜索历史失败: userId={}, keyword={}", userId, keyword, e);
        }
    }

    @Override
    public List<String> getUserSearchHistory(Long userId, Integer limit) {
        if (userId == null) {
            return Collections.emptyList();
        }

        try {
            String userHistoryKey = USER_SEARCH_HISTORY_KEY + userId;
            List<String> records = stringRedisTemplate.opsForList().range(userHistoryKey, 0, limit - 1);
            
            if (records == null) {
                return Collections.emptyList();
            }

            // 提取关键词（去重）
            return records.stream()
                    .map(record -> record.split("\\|")[0])
                    .distinct()
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取用户搜索历史失败: userId={}", userId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public boolean clearUserSearchHistory(Long userId) {
        if (userId == null) {
            return false;
        }

        try {
            String userHistoryKey = USER_SEARCH_HISTORY_KEY + userId;
            Boolean result = stringRedisTemplate.delete(userHistoryKey);
            log.info("清除用户搜索历史: userId={}, result={}", userId, result);
            return Boolean.TRUE.equals(result);

        } catch (Exception e) {
            log.error("清除用户搜索历史失败: userId={}", userId, e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getUserSearchStats(Long userId) {
        if (userId == null) {
            return Collections.emptyMap();
        }

        try {
            String userHistoryKey = USER_SEARCH_HISTORY_KEY + userId;
            Long totalSearches = stringRedisTemplate.opsForList().size(userHistoryKey);
            
            List<String> recentSearches = getUserSearchHistory(userId, 10);
            
            return Map.of(
                "totalSearches", totalSearches != null ? totalSearches : 0,
                "recentSearches", recentSearches,
                "uniqueKeywords", recentSearches.size()
            );

        } catch (Exception e) {
            log.error("获取用户搜索统计失败: userId={}", userId, e);
            return Collections.emptyMap();
        }
    }

    @Override
    public Map<String, Object> getSearchTrends(Integer days) {
        try {
            // 模拟搜索趋势数据（实际应该从数据库或更详细的Redis结构中获取）
            return Map.of(
                "period", days + "天",
                "totalSearches", 5000 + days * 100,
                "uniqueKeywords", 200 + days * 10,
                "avgSearchesPerDay", 5000 / days,
                "topCategories", Arrays.asList("推理", "恐怖", "情感", "欢乐"),
                "searchGrowth", "+15%"
            );

        } catch (Exception e) {
            log.error("获取搜索趋势失败: days={}", days, e);
            return Collections.emptyMap();
        }
    }

    @Override
    public Map<String, Object> getSearchPerformance() {
        try {
            // 模拟性能数据（实际应该从监控系统获取）
            return Map.of(
                "avgResponseTime", "85ms",
                "successRate", "99.2%",
                "cacheHitRate", "78%",
                "totalQueries", 10000,
                "errorRate", "0.8%",
                "peakQPS", 150
            );

        } catch (Exception e) {
            log.error("获取搜索性能统计失败", e);
            return Collections.emptyMap();
        }
    }

    @Override
    public List<String> getRelatedSearches(String keyword, Integer limit) {
        if (!StringUtils.hasText(keyword)) {
            return Collections.emptyList();
        }

        try {
            // 简单实现：基于关键词相似性返回相关搜索
            // 实际应该基于用户行为数据和机器学习算法
            List<String> related = Arrays.asList(
                keyword + "推荐",
                keyword + "评价",
                keyword + "攻略",
                "热门" + keyword,
                keyword + "新手"
            );

            return related.stream().limit(limit).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取相关搜索失败: keyword={}", keyword, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<String> getAutoCompleteSuggestions(String prefix, Integer limit) {
        if (!StringUtils.hasText(prefix)) {
            return Collections.emptyList();
        }

        try {
            // 从热门搜索中查找匹配的前缀
            Set<String> allKeywords = stringRedisTemplate.opsForZSet()
                    .reverseRange("search:hot_keywords", 0, -1);
            
            if (allKeywords == null) {
                return Collections.emptyList();
            }

            String lowerPrefix = prefix.toLowerCase();
            return allKeywords.stream()
                    .filter(keyword -> keyword.toLowerCase().startsWith(lowerPrefix))
                    .limit(limit)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取自动完成建议失败: prefix={}", prefix, e);
            return Collections.emptyList();
        }
    }

    @Override
    public void recordSearchClick(Long userId, String keyword, Long scriptId, Integer position) {
        if (userId == null || !StringUtils.hasText(keyword) || scriptId == null) {
            return;
        }

        try {
            String clickKey = SEARCH_CLICKS_KEY + keyword;
            String clickRecord = userId + ":" + scriptId + ":" + position + ":" + 
                               LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            
            stringRedisTemplate.opsForList().leftPush(clickKey, clickRecord);
            stringRedisTemplate.expire(clickKey, 7, TimeUnit.DAYS);

        } catch (Exception e) {
            log.error("记录搜索点击失败: userId={}, keyword={}, scriptId={}", userId, keyword, scriptId, e);
        }
    }

    @Override
    public List<String> getNoResultKeywords(Integer limit) {
        try {
            Set<String> keywords = stringRedisTemplate.opsForZSet()
                    .reverseRange(NO_RESULT_KEYWORDS_KEY, 0, limit - 1);
            
            return keywords != null ? new ArrayList<>(keywords) : Collections.emptyList();

        } catch (Exception e) {
            log.error("获取无结果关键词失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 记录全局搜索统计
     */
    private void recordGlobalSearchStats(String keyword, String category, Integer resultCount) {
        try {
            String today = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
            String statsKey = GLOBAL_SEARCH_STATS_KEY + ":" + today;
            
            // 增加总搜索次数
            stringRedisTemplate.opsForHash().increment(statsKey, "totalSearches", 1);
            
            // 记录分类搜索次数
            if (StringUtils.hasText(category)) {
                stringRedisTemplate.opsForHash().increment(statsKey, "category:" + category, 1);
            }
            
            // 记录有结果/无结果搜索次数
            if (resultCount != null) {
                if (resultCount > 0) {
                    stringRedisTemplate.opsForHash().increment(statsKey, "successfulSearches", 1);
                } else {
                    stringRedisTemplate.opsForHash().increment(statsKey, "noResultSearches", 1);
                }
            }
            
            // 设置过期时间（30天）
            stringRedisTemplate.expire(statsKey, 30, TimeUnit.DAYS);

        } catch (Exception e) {
            log.error("记录全局搜索统计失败", e);
        }
    }
}
