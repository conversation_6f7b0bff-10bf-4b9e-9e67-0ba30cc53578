package com.scriptmurder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scriptmurder.dto.FavoriteDTO;
import com.scriptmurder.entity.UserFavorite;
import com.scriptmurder.mapper.UserFavoriteMapper;
import com.scriptmurder.service.IUserFavoriteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户收藏表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-31
 */
@Service
@Slf4j
public class UserFavoriteServiceImpl extends ServiceImpl<UserFavoriteMapper, UserFavorite> implements IUserFavoriteService {

    @Override
    public Page<FavoriteDTO> getUserFavorites(Long userId, Integer page, Integer size, String type) {
        try {
            QueryWrapper<UserFavorite> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            if (type != null && !type.isEmpty()) {
                queryWrapper.eq("target_type", type);
            }
            queryWrapper.orderByDesc("create_time");

            Page<UserFavorite> favoriteePage = new Page<>(page, size);
            Page<UserFavorite> result = page(favoriteePage, queryWrapper);

            // 转换为DTO
            List<FavoriteDTO> favoriteDTOs = result.getRecords().stream()
                    .map(favorite -> {
                        FavoriteDTO dto = BeanUtil.copyProperties(favorite, FavoriteDTO.class);
                        // TODO: 根据targetType和targetId获取目标信息
                        // 这里需要根据实际业务逻辑来获取剧本或房间的详细信息
                        return dto;
                    })
                    .collect(Collectors.toList());

            Page<FavoriteDTO> dtoPage = new Page<>(page, size);
            dtoPage.setRecords(favoriteDTOs);
            dtoPage.setTotal(result.getTotal());
            dtoPage.setPages(result.getPages());
            dtoPage.setCurrent(result.getCurrent());
            dtoPage.setSize(result.getSize());

            return dtoPage;
        } catch (Exception e) {
            log.error("获取用户收藏列表失败, userId: {}", userId, e);
            return new Page<>();
        }
    }

    @Override
    public boolean addFavorite(Long userId, Long targetId, String targetType) {
        try {
            // 检查是否已收藏
            if (isFavorited(userId, targetId, targetType)) {
                return false;
            }

            UserFavorite favorite = new UserFavorite();
            favorite.setUserId(userId);
            favorite.setTargetId(targetId);
            favorite.setTargetType(targetType);
            favorite.setCreateTime(LocalDateTime.now());

            return save(favorite);
        } catch (Exception e) {
            log.error("添加收藏失败, userId: {}, targetId: {}, targetType: {}", userId, targetId, targetType, e);
            return false;
        }
    }

    @Override
    public boolean removeFavorite(Long userId, Long favoriteId) {
        try {
            QueryWrapper<UserFavorite> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("id", favoriteId)
                    .eq("user_id", userId);

            return remove(queryWrapper);
        } catch (Exception e) {
            log.error("取消收藏失败, userId: {}, favoriteId: {}", userId, favoriteId, e);
            return false;
        }
    }

    @Override
    public boolean isFavorited(Long userId, Long targetId, String targetType) {
        try {
            QueryWrapper<UserFavorite> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId)
                    .eq("target_id", targetId)
                    .eq("target_type", targetType);

            return count(queryWrapper) > 0;
        } catch (Exception e) {
            log.error("检查收藏状态失败, userId: {}, targetId: {}, targetType: {}", userId, targetId, targetType, e);
            return false;
        }
    }

    @Override
    public boolean removeFavoriteByTarget(Long userId, Long targetId, String targetType) {
        try {
            QueryWrapper<UserFavorite> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId)
                    .eq("target_id", targetId)
                    .eq("target_type", targetType);

            return remove(queryWrapper);
        } catch (Exception e) {
            log.error("根据目标取消收藏失败, userId: {}, targetId: {}, targetType: {}", userId, targetId, targetType, e);
            return false;
        }
    }
}
