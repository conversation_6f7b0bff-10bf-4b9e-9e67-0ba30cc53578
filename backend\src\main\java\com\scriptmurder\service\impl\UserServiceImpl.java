package com.scriptmurder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scriptmurder.dto.LoginFormDTO;
import com.scriptmurder.dto.RegisterFormDTO;
import com.scriptmurder.dto.Result;
import com.scriptmurder.dto.UserDTO;
import com.scriptmurder.dto.UserUpdateDTO;
import com.scriptmurder.entity.User;
import com.scriptmurder.mapper.UserMapper;
import com.scriptmurder.service.IEmailService;
import com.scriptmurder.service.IUserService;
import com.scriptmurder.utils.RedisConstants;
import com.scriptmurder.utils.RegexUtils;
import com.scriptmurder.utils.SystemConstants;
import com.scriptmurder.utils.UserHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.BitFieldSubCommands;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpSession;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.scriptmurder.utils.RedisConstants.*;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
@Service
@Slf4j
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements IUserService {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private IEmailService emailService;

    @Value("${file.upload.path:/uploads/}")
    private String uploadPath;

    @Value("${file.upload.domain:http://localhost:8081}")
    private String uploadDomain;

    /**
     * 发送短信验证码
     *
     * @param phone
     * @param session
     * @return
     */
    public Result sendCode(String phone, HttpSession session) {
        // 1.先验证手机号是否合法，合法：继续；不合法：返回 fail
        if (RegexUtils.isPhoneInvalid(phone)) {
            return Result.fail("手机号格式错误");
        }
        // 2.设置短信验证码，用 hutool 工具箱中的类
        String code = RandomUtil.randomNumbers(6);

        // 3.将短信验证码存入 session --- 后续，改为 redis 存储
        // session.setAttribute("code", code);
        stringRedisTemplate.opsForValue().set(LOGIN_CODE_KEY + phone, code, LOGIN_CODE_TTL, TimeUnit.MINUTES);

        // 4.发送短信验证码
        log.info("验证码短信发送成功，验证码：{}", code);
        // 5.返回响应成功
        return Result.ok();
    }

    /**
     * 发送邮箱验证码
     *
     * @param email 邮箱地址
     * @return
     */
    @Override
    public Result sendEmailCode(String email) {
        // 1.验证邮箱格式
        if (RegexUtils.isEmailInvalid(email)) {
            return Result.fail("邮箱格式错误");
        }

        // 2.检查邮箱是否已注册
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("email", email);
        User existUser = getOne(queryWrapper);
        if (existUser != null) {
            return Result.fail("该邮箱已注册，请直接登录");
        }

        // 3.生成6位随机验证码
        String code = RandomUtil.randomNumbers(6);

        // 4.将验证码存入Redis（5分钟有效期）
        stringRedisTemplate.opsForValue().set(LOGIN_CODE_KEY + email, code, LOGIN_CODE_TTL, TimeUnit.MINUTES);

        // 5.发送邮件验证码
        boolean sent = emailService.sendVerificationCode(email, code);
        if (!sent) {
            return Result.fail("验证码发送失败，请稍后重试");
        }

        log.info("邮箱验证码发送成功，邮箱：{}，验证码：{}", email, code);
        return Result.ok("验证码发送成功");
    }

    /**
     * 用户注册
     *
     * @param registerForm 注册表单
     * @return
     */
    @Override
    public Result register(RegisterFormDTO registerForm) {
        String email = registerForm.getEmail();
        String password = registerForm.getPassword();
        String confirmPassword = registerForm.getConfirmPassword();
        String code = registerForm.getCode();

        // 1.验证邮箱格式
        if (RegexUtils.isEmailInvalid(email)) {
            return Result.fail("邮箱格式错误");
        }

        // 2.验证密码
        if (password == null || password.length() < 6) {
            return Result.fail("密码长度不能少于6位");
        }

        // 3.验证确认密码
        if (!password.equals(confirmPassword)) {
            return Result.fail("两次输入的密码不一致");
        }

        // 4.验证验证码
        String cacheCode = stringRedisTemplate.opsForValue().get(LOGIN_CODE_KEY + email);
        if (cacheCode == null || !cacheCode.equals(code)) {
            return Result.fail("验证码错误或已过期");
        }

        // 5.检查邮箱是否已注册
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("email", email);
        User existUser = getOne(queryWrapper);
        if (existUser != null) {
            return Result.fail("该邮箱已注册");
        }

        // 6.创建用户
        User user = new User();
        user.setEmail(email);
        user.setPassword(BCrypt.hashpw(password, BCrypt.gensalt()));
        user.setNickName(registerForm.getNickName() != null ?
            registerForm.getNickName() :
            SystemConstants.USER_NICK_NAME_PREFIX + RandomUtil.randomString(10));
        // 明确设置phone为null，避免空字符串唯一约束冲突
        user.setPhone(null);

        // 7.保存用户
        boolean saved = save(user);
        if (!saved) {
            return Result.fail("注册失败，请稍后重试");
        }

        // 8.删除验证码
        stringRedisTemplate.delete(LOGIN_CODE_KEY + email);

        log.info("用户注册成功，邮箱：{}", email);
        return Result.ok("注册成功");
    }

    /**
     * 登录功能
     *
     * @param loginForm
     * @param session
     * @return
     */
    @Override
    public Result login(LoginFormDTO loginForm, HttpSession session) {
        String email = loginForm.getEmail();
        String password = loginForm.getPassword();
        String phone = loginForm.getPhone();
        String code = loginForm.getCode();

        User user = null;

        // 判断登录方式：邮箱密码登录 或 手机验证码登录
        if (email != null && password != null) {
            // 邮箱密码登录
            user = loginWithEmailPassword(email, password);
            if (user == null) {
                return Result.fail("邮箱或密码错误");
            }
        } else if (phone != null && code != null) {
            // 手机验证码登录（保留原有功能）
            user = loginWithPhoneCode(phone, code, session);
            if (user == null) {
                return Result.fail("手机号或验证码错误");
            }
        } else {
            return Result.fail("请提供邮箱密码或手机验证码进行登录");
        }
        // 7.保存用户到 Redis 中
        // 7.1.随机生成 token 作为登录令牌
        String token = UUID.randomUUID().toString(true);

        // 7.2.将User对象转为Hash存储
        log.info("用户登录成功，用户信息: id={}, phone={}, nickName={}, icon={}",
                user.getId(), user.getPhone(), user.getNickName(), user.getIcon());

        UserDTO userDTO = BeanUtil.copyProperties(user, UserDTO.class);
        log.info("UserDTO映射结果: id={}, nickName={}, icon={}",
                userDTO.getId(), userDTO.getNickName(), userDTO.getIcon());

        Map<String, Object> userMap = BeanUtil.beanToMap(userDTO, new HashMap<>(),
                CopyOptions.create()
                        .setIgnoreNullValue(false) // 不忽略null值，确保字段完整
                        .setFieldValueEditor((filedName, fieldValue) ->
                                fieldValue == null ? "" : fieldValue.toString()));

        log.info("UserMap内容: {}", userMap);

        // 7.3 存储
        String tokenKey = LOGIN_USER_KEY + token;
        stringRedisTemplate.opsForHash().putAll(tokenKey, userMap);

        // 7.4 设置 token 有效期
        stringRedisTemplate.expire(tokenKey, LOGIN_USER_TTL, TimeUnit.MINUTES);

        // 7.5 返回 token
        return Result.ok(token);
    }

    /**
     * 邮箱密码登录
     */
    private User loginWithEmailPassword(String email, String password) {
        // 1.验证邮箱格式
        if (RegexUtils.isEmailInvalid(email)) {
            return null;
        }

        // 2.查询用户
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("email", email);
        User user = getOne(queryWrapper);

        // 3.验证用户存在且密码正确
        if (user == null || !BCrypt.checkpw(password, user.getPassword())) {
            return null;
        }

        return user;
    }

    /**
     * 手机验证码登录
     */
    private User loginWithPhoneCode(String phone, String code, HttpSession session) {
        // 1.校验手机号
        if (RegexUtils.isPhoneInvalid(phone)) {
            return null;
        }

        // 2.验证验证码
        String cacheCode = stringRedisTemplate.opsForValue().get(LOGIN_CODE_KEY + phone);
        if (cacheCode == null || !cacheCode.equals(code)) {
            return null;
        }

        // 3.验证成功，立即删除验证码
        stringRedisTemplate.delete(LOGIN_CODE_KEY + phone);

        // 4.查询用户，不存在则自动注册
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("phone", phone);
        User user = getOne(queryWrapper);

        if (user == null) {
            user = createUserWithPhone(phone);
        }

        return user;
    }

    @Override
    public Result logout(String token) {
        // 1. 获取当前用户
        UserDTO user = UserHolder.getUser();
        if (user == null) {
            return Result.fail("用户未登录");
        }

        // 2. 清除ThreadLocal中的用户信息
        UserHolder.removeUser();

        // 3. 清除Redis中的token
        if (token != null && !token.isEmpty()) {
            String tokenKey = LOGIN_USER_KEY + token;
            stringRedisTemplate.delete(tokenKey);
            log.info("用户登出成功，已清除token: {}", token);
        }

        return Result.ok("登出成功");
    }

    @Override
    public Result sign() {

        UserDTO user = UserHolder.getUser();
        if (user == null) {
            return Result.fail("用户未登录，无法签到");
        }

        // 1. 获取当前登录用户
        Long userId = user.getId();

        // 2. 获取当前日期
        LocalDateTime dateTimeNow = LocalDateTime.now();

        String keySuffix = dateTimeNow.format(DateTimeFormatter.ofPattern(":yyyyMM"));

        // 3. 拼接 key
        String key = USER_SIGN_KEY + userId + keySuffix;

        // 4. 获取今天是本月的第几天
        int dayOfMonth = dateTimeNow.getDayOfMonth();

        // 5. 将签到信息插入 redis
        Boolean isSigned = stringRedisTemplate.opsForValue().setBit(key, dayOfMonth - 1, true);

        if (isSigned != null && isSigned) {
            // 如果原来的值是 true，说明是重复签到
            return Result.fail("您今天已经签过到了，不能重复签到");
        }

        // 5. 返回成功信息
        return Result.ok();
    }

    @Override
    public Result singCount() {
        UserDTO user = UserHolder.getUser();
        if (user == null) {
            return Result.fail("用户未登录，无法签到");
        }

        // 1. 获取当前登录用户
        Long userId = user.getId();

        // 2. 获取当前日期
        LocalDateTime dateTimeNow = LocalDateTime.now();
        String keySuffix = dateTimeNow.format(DateTimeFormatter.ofPattern(":yyyyMM"));

        // 3. 拼接 key
        String key = USER_SIGN_KEY + userId + keySuffix;

        // 4. 获取今天是本月的第几天
        int dayOfMonth = dateTimeNow.getDayOfMonth();

        // 5. 获取本月截止到今天的所有签到记录，返回的是 一个 十进制数字
        List<Long> result = stringRedisTemplate.opsForValue().bitField(
                key,
                BitFieldSubCommands.create().get(BitFieldSubCommands.BitFieldType.unsigned(dayOfMonth)).valueAt(0));

        if (result == null || result.isEmpty()) {
            return Result.ok(0);
        }

        Long num = result.get(0);
        if (num == null || num == 0) {
            return Result.ok(0);
        }
        int count = 0;
        // 6. 循环遍历
        while (true) {
            // 6.1 让这个数字和 1 做 与 运算， 得到数字的最后一个 bit 位
            if ((num & 1) == 0) {
                // 如果为 0， 说明未签到，结束
                break;
            }
            // 如果不为 0, 说明已签到，继续
            count++;
            // 把数字右移一位，抛弃最后一位 bit 位，继续下一个 bit 位
            num >>>= 1;
        }

        return Result.ok(count);
    }

    /**
     * 第一次登录时创建用户
     *
     * @param phone
     * @return
     */
    private User createUserWithPhone(String phone) {
        // 1.创建用户
        User user = new User();

        // user phone
        user.setPhone(phone);
        // user name
        user.setNickName(SystemConstants.USER_NICK_NAME_PREFIX + RandomUtil.randomString(10));
        // 2.保存用户
        save(user);
        return user;
    }

    @Override
    public boolean updateUserProfile(Long userId, UserUpdateDTO updateDTO) {
        try {
            User user = new User();
            user.setId(userId);
            BeanUtil.copyProperties(updateDTO, user);
            user.setUpdateTime(LocalDateTime.now());

            boolean success = updateById(user);

            if (success) {
                // 更新成功后，清除Redis中的用户缓存，强制下次获取最新数据
                String tokenKey = RedisConstants.LOGIN_USER_KEY + "*";
                Set<String> keys = stringRedisTemplate.keys(tokenKey);
                if (keys != null) {
                    for (String key : keys) {
                        Map<Object, Object> userMap = stringRedisTemplate.opsForHash().entries(key);
                        if (userMap.get("id") != null && userMap.get("id").toString().equals(userId.toString())) {
                            stringRedisTemplate.delete(key);
                            log.info("清除用户缓存成功, userId: {}, key: {}", userId, key);
                            break;
                        }
                    }
                }
            }

            return success;
        } catch (Exception e) {
            log.error("更新用户资料失败, userId: {}", userId, e);
            return false;
        }
    }

    @Override
    public String uploadAvatar(Long userId, MultipartFile file) throws IOException {
        // 验证文件
        validateAvatarFile(file);

        // 创建上传目录
        String avatarDir = uploadPath + "avatar/";
        File dir = new File(avatarDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        // 生成文件名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !originalFilename.contains(".")) {
            throw new RuntimeException("文件名无效");
        }
        String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        String filename = userId + "_" + System.currentTimeMillis() + extension;

        // 保存文件
        File targetFile = new File(avatarDir + filename);
        file.transferTo(targetFile);

        // 生成访问URL
        String avatarUrl = uploadDomain + "/uploads/avatar/" + filename;

        // 更新用户头像
        User user = new User();
        user.setId(userId);
        user.setIcon(avatarUrl);
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);

        return avatarUrl;
    }

    @Override
    public boolean updateUserAvatar(Long userId, String avatarUrl) {
        try {
            User user = new User();
            user.setId(userId);
            user.setIcon(avatarUrl);
            user.setUpdateTime(LocalDateTime.now());
            return updateById(user);
        } catch (Exception e) {
            log.error("更新用户头像失败，用户ID: {}, 头像URL: {}", userId, avatarUrl, e);
            return false;
        }
    }

    @Override
    public boolean changePassword(Long userId, String oldPassword, String newPassword) {
        try {
            User user = getById(userId);
            if (user == null) {
                return false;
            }

            // 验证原密码
            if (!BCrypt.checkpw(oldPassword, user.getPassword())) {
                return false;
            }

            // 更新新密码
            String hashedPassword = BCrypt.hashpw(newPassword, BCrypt.gensalt());
            User updateUser = new User();
            updateUser.setId(userId);
            updateUser.setPassword(hashedPassword);
            updateUser.setUpdateTime(LocalDateTime.now());

            return updateById(updateUser);
        } catch (Exception e) {
            log.error("修改密码失败, userId: {}", userId, e);
            return false;
        }
    }

    private void validateAvatarFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new RuntimeException("文件不能为空");
        }

        // 检查文件大小 (2MB)
        if (file.getSize() > 2 * 1024 * 1024) {
            throw new RuntimeException("文件大小不能超过2MB");
        }

        // 检查文件类型
        String contentType = file.getContentType();
        if (!Arrays.asList("image/jpeg", "image/png", "image/gif").contains(contentType)) {
            throw new RuntimeException("只支持 JPG、PNG、GIF 格式的图片");
        }
    }
}
