package com.scriptmurder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scriptmurder.dto.UserSettingsDTO;
import com.scriptmurder.entity.UserSettings;
import com.scriptmurder.mapper.UserSettingsMapper;
import com.scriptmurder.service.IUserSettingsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * 用户设置服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Slf4j
@Service
public class UserSettingsServiceImpl extends ServiceImpl<UserSettingsMapper, UserSettings> 
        implements IUserSettingsService {

    @Resource
    private UserSettingsMapper userSettingsMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private static final String CACHE_KEY_PREFIX = "user:settings:";
    private static final long CACHE_EXPIRE_TIME = 24; // 24小时

    @Override
    public UserSettingsDTO getUserSettings(Long userId) {
        try {
            // 先从缓存获取
            UserSettingsDTO cachedSettings = getSettingsFromCache(userId);
            if (cachedSettings != null) {
                return cachedSettings;
            }

            // 从数据库获取
            UserSettings userSettings = userSettingsMapper.selectByUserId(userId);
            
            if (userSettings == null) {
                // 如果用户设置不存在，创建默认设置
                if (initUserSettings(userId)) {
                    userSettings = userSettingsMapper.selectByUserId(userId);
                } else {
                    return UserSettingsDTO.createDefault();
                }
            }
            
            UserSettingsDTO dto = BeanUtil.copyProperties(userSettings, UserSettingsDTO.class);
            
            // 同步到缓存
            syncSettingsToCache(userId, dto);
            
            return dto;
        } catch (Exception e) {
            log.error("获取用户设置失败, userId: {}", userId, e);
            return UserSettingsDTO.createDefault();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserSettings(Long userId, UserSettingsDTO settingsDTO) {
        try {
            // 验证设置数据
            Map<String, String> validationErrors = validateSettings(settingsDTO);
            if (!validationErrors.isEmpty()) {
                log.warn("用户设置验证失败, userId: {}, errors: {}", userId, validationErrors);
                return false;
            }

            QueryWrapper<UserSettings> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            
            UserSettings existingSettings = getOne(queryWrapper);
            
            if (existingSettings == null) {
                // 如果设置不存在，先创建默认设置
                if (!initUserSettings(userId)) {
                    return false;
                }
                existingSettings = getOne(queryWrapper);
            }
            
            // 只更新非空字段
            updateNonNullFields(existingSettings, settingsDTO);
            existingSettings.setUpdateTime(LocalDateTime.now());
            
            boolean success = updateById(existingSettings);
            
            if (success) {
                // 清除缓存
                clearSettingsCache(userId);
                log.info("用户设置更新成功, userId: {}", userId);
            }
            
            return success;
        } catch (Exception e) {
            log.error("更新用户设置失败, userId: {}", userId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetToDefaults(Long userId) {
        try {
            QueryWrapper<UserSettings> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            
            UserSettings defaultSettings = createDefaultUserSettings(userId);
            defaultSettings.setUpdateTime(LocalDateTime.now());
            
            UserSettings existingSettings = getOne(queryWrapper);
            if (existingSettings != null) {
                defaultSettings.setId(existingSettings.getId());
                defaultSettings.setCreateTime(existingSettings.getCreateTime());
                boolean success = updateById(defaultSettings);
                
                if (success) {
                    clearSettingsCache(userId);
                    log.info("用户设置重置成功, userId: {}", userId);
                }
                
                return success;
            } else {
                boolean success = save(defaultSettings);
                
                if (success) {
                    clearSettingsCache(userId);
                    log.info("用户设置初始化并重置成功, userId: {}", userId);
                }
                
                return success;
            }
        } catch (Exception e) {
            log.error("重置用户设置失败, userId: {}", userId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean initUserSettings(Long userId) {
        try {
            // 检查是否已存在
            QueryWrapper<UserSettings> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            
            if (getOne(queryWrapper) != null) {
                return true; // 已存在，无需初始化
            }
            
            UserSettings defaultSettings = createDefaultUserSettings(userId);
            boolean success = save(defaultSettings);
            
            if (success) {
                log.info("用户设置初始化成功, userId: {}", userId);
            }
            
            return success;
        } catch (Exception e) {
            log.error("初始化用户设置失败, userId: {}", userId, e);
            return false;
        }
    }

    @Override
    public int batchInitUserSettings(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return 0;
        }
        
        int successCount = 0;
        for (Long userId : userIds) {
            if (initUserSettings(userId)) {
                successCount++;
            }
        }
        
        log.info("批量初始化用户设置完成, 总数: {}, 成功: {}", userIds.size(), successCount);
        return successCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSingleSetting(Long userId, String fieldName, Object fieldValue) {
        try {
            QueryWrapper<UserSettings> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            
            UserSettings settings = getOne(queryWrapper);
            if (settings == null) {
                if (!initUserSettings(userId)) {
                    return false;
                }
                settings = getOne(queryWrapper);
            }
            
            // 使用反射或switch更新字段
            boolean updated = updateFieldByName(settings, fieldName, fieldValue);
            
            if (updated) {
                settings.setUpdateTime(LocalDateTime.now());
                boolean success = updateById(settings);
                
                if (success) {
                    clearSettingsCache(userId);
                    log.info("单个设置更新成功, userId: {}, field: {}, value: {}", userId, fieldName, fieldValue);
                }
                
                return success;
            }
            
            return false;
        } catch (Exception e) {
            log.error("更新单个设置失败, userId: {}, field: {}", userId, fieldName, e);
            return false;
        }
    }

    @Override
    public List<Map<String, Object>> getThemeStatistics() {
        try {
            return userSettingsMapper.selectThemeStatistics();
        } catch (Exception e) {
            log.error("获取主题统计失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, Object>> getPrivacyLevelStatistics() {
        try {
            return userSettingsMapper.selectPrivacyLevelStatistics();
        } catch (Exception e) {
            log.error("获取隐私级别统计失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Object> getNotificationStatistics() {
        try {
            return userSettingsMapper.selectNotificationStatistics();
        } catch (Exception e) {
            log.error("获取通知统计失败", e);
            return new HashMap<>();
        }
    }

    @Override
    public List<Long> getUserIdsByNotificationType(String notificationType) {
        try {
            return userSettingsMapper.selectUserIdsByNotificationType(notificationType);
        } catch (Exception e) {
            log.error("获取通知用户列表失败, type: {}", notificationType, e);
            return new ArrayList<>();
        }
    }

    @Override
    public int batchUpdateField(List<Long> userIds, String fieldName, String fieldValue) {
        if (userIds == null || userIds.isEmpty()) {
            return 0;
        }

        try {
            // 使用MyBatis-Plus的方式批量更新
            UpdateWrapper<UserSettings> updateWrapper = new UpdateWrapper<>();
            updateWrapper.in("user_id", userIds);

            UserSettings updateEntity = new UserSettings();
            updateEntity.setUpdateTime(LocalDateTime.now());

            // 根据字段名设置对应的值
            switch (fieldName.toLowerCase()) {
                case "theme":
                    updateEntity.setTheme(fieldValue);
                    break;
                case "language":
                    updateEntity.setLanguage(fieldValue);
                    break;
                case "timezone":
                    updateEntity.setTimezone(fieldValue);
                    break;
                default:
                    log.warn("不支持的字段名: {}", fieldName);
                    return 0;
            }

            boolean success = update(updateEntity, updateWrapper);
            int count = success ? userIds.size() : 0;

            // 清除相关用户的缓存
            userIds.forEach(this::clearSettingsCache);

            log.info("批量更新字段成功, field: {}, value: {}, count: {}", fieldName, fieldValue, count);
            return count;
        } catch (Exception e) {
            log.error("批量更新字段失败, field: {}", fieldName, e);
            return 0;
        }
    }

    @Override
    public boolean isNotificationEnabled(Long userId, String notificationType) {
        try {
            UserSettingsDTO settings = getUserSettings(userId);
            
            switch (notificationType.toLowerCase()) {
                case "email":
                    return Boolean.TRUE.equals(settings.getEmailNotifications());
                case "system":
                    return Boolean.TRUE.equals(settings.getSystemNotifications());
                case "activity":
                    return Boolean.TRUE.equals(settings.getActivityNotifications());
                case "social":
                    return Boolean.TRUE.equals(settings.getSocialNotifications());
                default:
                    return false;
            }
        } catch (Exception e) {
            log.error("检查通知设置失败, userId: {}, type: {}", userId, notificationType, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String toggleTheme(Long userId) {
        try {
            UserSettingsDTO settings = getUserSettings(userId);
            String newTheme = "dark".equals(settings.getTheme()) ? "light" : "dark";
            
            if (updateSingleSetting(userId, "theme", newTheme)) {
                return newTheme;
            }
            
            return settings.getTheme();
        } catch (Exception e) {
            log.error("切换主题失败, userId: {}", userId, e);
            return "dark"; // 默认返回深色主题
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleNotification(Long userId, String notificationType) {
        try {
            boolean currentValue = isNotificationEnabled(userId, notificationType);
            boolean newValue = !currentValue;

            String fieldName = notificationType.toLowerCase() + "Notifications";
            return updateSingleSetting(userId, fieldName, newValue);
        } catch (Exception e) {
            log.error("切换通知设置失败, userId: {}, type: {}", userId, notificationType, e);
            return false;
        }
    }

    // 私有辅助方法
    private UserSettings createDefaultUserSettings(Long userId) {
        UserSettings settings = new UserSettings();
        settings.setUserId(userId);
        settings.setTheme("dark");
        settings.setLanguage("zh-CN");
        settings.setTimezone("Asia/Shanghai");
        settings.setEmailNotifications(true);
        settings.setSystemNotifications(true);
        settings.setActivityNotifications(true);
        settings.setSocialNotifications(false);
        settings.setPrivacyLevel(1);
        settings.setAutoSave(true);
        settings.setCreateTime(LocalDateTime.now());
        settings.setUpdateTime(LocalDateTime.now());
        return settings;
    }

    private void updateNonNullFields(UserSettings target, UserSettingsDTO source) {
        if (source.getTheme() != null) target.setTheme(source.getTheme());
        if (source.getLanguage() != null) target.setLanguage(source.getLanguage());
        if (source.getTimezone() != null) target.setTimezone(source.getTimezone());
        if (source.getEmailNotifications() != null) target.setEmailNotifications(source.getEmailNotifications());
        if (source.getSystemNotifications() != null) target.setSystemNotifications(source.getSystemNotifications());
        if (source.getActivityNotifications() != null) target.setActivityNotifications(source.getActivityNotifications());
        if (source.getSocialNotifications() != null) target.setSocialNotifications(source.getSocialNotifications());
        if (source.getPrivacyLevel() != null) target.setPrivacyLevel(source.getPrivacyLevel());
        if (source.getAutoSave() != null) target.setAutoSave(source.getAutoSave());
    }

    private boolean updateFieldByName(UserSettings settings, String fieldName, Object fieldValue) {
        switch (fieldName.toLowerCase()) {
            case "theme":
                settings.setTheme((String) fieldValue);
                return true;
            case "language":
                settings.setLanguage((String) fieldValue);
                return true;
            case "timezone":
                settings.setTimezone((String) fieldValue);
                return true;
            case "emailnotifications":
                settings.setEmailNotifications((Boolean) fieldValue);
                return true;
            case "systemnotifications":
                settings.setSystemNotifications((Boolean) fieldValue);
                return true;
            case "activitynotifications":
                settings.setActivityNotifications((Boolean) fieldValue);
                return true;
            case "socialnotifications":
                settings.setSocialNotifications((Boolean) fieldValue);
                return true;
            case "privacylevel":
                settings.setPrivacyLevel((Integer) fieldValue);
                return true;
            case "autosave":
                settings.setAutoSave((Boolean) fieldValue);
                return true;
            default:
                return false;
        }
    }

    @Override
    public Map<String, String> validateSettings(UserSettingsDTO settingsDTO) {
        Map<String, String> errors = new HashMap<>();
        
        if (settingsDTO.getTheme() != null && !settingsDTO.getTheme().matches("^(dark|light)$")) {
            errors.put("theme", "主题只能是 dark 或 light");
        }
        
        if (settingsDTO.getLanguage() != null && !settingsDTO.getLanguage().matches("^[a-z]{2}-[A-Z]{2}$")) {
            errors.put("language", "语言格式不正确，应为 xx-XX 格式");
        }
        
        if (settingsDTO.getPrivacyLevel() != null && 
            (settingsDTO.getPrivacyLevel() < 1 || settingsDTO.getPrivacyLevel() > 3)) {
            errors.put("privacyLevel", "隐私级别必须在 1-3 之间");
        }
        
        return errors;
    }

    @Override
    public void syncSettingsToCache(Long userId) {
        UserSettingsDTO settings = getUserSettings(userId);
        syncSettingsToCache(userId, settings);
    }

    private void syncSettingsToCache(Long userId, UserSettingsDTO settings) {
        try {
            String key = CACHE_KEY_PREFIX + userId;
            String value = JSONUtil.toJsonStr(settings);
            stringRedisTemplate.opsForValue().set(key, value, CACHE_EXPIRE_TIME, TimeUnit.HOURS);
        } catch (Exception e) {
            log.warn("同步设置到缓存失败, userId: {}", userId, e);
        }
    }

    @Override
    public UserSettingsDTO getSettingsFromCache(Long userId) {
        try {
            String key = CACHE_KEY_PREFIX + userId;
            String value = stringRedisTemplate.opsForValue().get(key);
            
            if (value != null) {
                return JSONUtil.toBean(value, UserSettingsDTO.class);
            }
        } catch (Exception e) {
            log.warn("从缓存获取设置失败, userId: {}", userId, e);
        }
        
        return null;
    }

    @Override
    public void clearSettingsCache(Long userId) {
        try {
            String key = CACHE_KEY_PREFIX + userId;
            stringRedisTemplate.delete(key);
        } catch (Exception e) {
            log.warn("清除设置缓存失败, userId: {}", userId, e);
        }
    }

    @Override
    public Map<String, Object> getUserSettingsSummary(Long userId) {
        try {
            UserSettingsDTO settings = getUserSettings(userId);
            Map<String, Object> summary = new HashMap<>();

            summary.put("userId", userId);
            summary.put("theme", settings.getThemeDesc());
            summary.put("language", settings.getLanguage());
            summary.put("timezone", settings.getTimezone());
            summary.put("privacyLevel", settings.getPrivacyLevelDesc());
            summary.put("notificationSummary", settings.getNotificationSummary());
            summary.put("autoSave", settings.getAutoSave());

            return summary;
        } catch (Exception e) {
            log.error("获取用户设置摘要失败, userId: {}", userId, e);
            return new HashMap<>();
        }
    }

    @Override
    public String exportUserSettings(Long userId) {
        try {
            UserSettingsDTO settings = getUserSettings(userId);
            return JSONUtil.toJsonStr(settings);
        } catch (Exception e) {
            log.error("导出用户设置失败, userId: {}", userId, e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean importUserSettings(Long userId, String settingsJson) {
        try {
            UserSettingsDTO settings = JSONUtil.toBean(settingsJson, UserSettingsDTO.class);

            // 验证导入的设置
            Map<String, String> validationErrors = validateSettings(settings);
            if (!validationErrors.isEmpty()) {
                log.warn("导入的设置验证失败, userId: {}, errors: {}", userId, validationErrors);
                return false;
            }

            return updateUserSettings(userId, settings);
        } catch (Exception e) {
            log.error("导入用户设置失败, userId: {}", userId, e);
            return false;
        }
    }

    @Override
    public List<Map<String, Object>> getSettingsChangeHistory(Long userId, Integer limit) {
        // 这里可以实现设置变更历史功能，需要额外的审计表
        // 暂时返回空列表
        return new ArrayList<>();
    }
}
