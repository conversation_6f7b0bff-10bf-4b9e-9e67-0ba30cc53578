package com.scriptmurder.utils;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.regex.Pattern;

/**
 * IP地址工具类
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Slf4j
public class IpUtils {

    private static final String UNKNOWN = "unknown";
    private static final String LOCALHOST_IPV4 = "127.0.0.1";
    private static final String LOCALHOST_IPV6 = "0:0:0:0:0:0:0:1";
    private static final int IP_MAX_LENGTH = 15;

    // IP地址正则表达式
    private static final Pattern IP_PATTERN = Pattern.compile(
        "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$"
    );

    /**
     * 获取客户端真实IP地址
     * 
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    public static String getIpAddr(HttpServletRequest request) {
        if (request == null) {
            return UNKNOWN;
        }

        String ip = null;
        try {
            // 1. 检查 X-Forwarded-For 头
            ip = request.getHeader("X-Forwarded-For");
            if (isValidIp(ip)) {
                // X-Forwarded-For可能包含多个IP，取第一个
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                return ip;
            }

            // 2. 检查 X-Real-IP 头
            ip = request.getHeader("X-Real-IP");
            if (isValidIp(ip)) {
                return ip;
            }

            // 3. 检查 Proxy-Client-IP 头
            ip = request.getHeader("Proxy-Client-IP");
            if (isValidIp(ip)) {
                return ip;
            }

            // 4. 检查 WL-Proxy-Client-IP 头
            ip = request.getHeader("WL-Proxy-Client-IP");
            if (isValidIp(ip)) {
                return ip;
            }

            // 5. 检查 HTTP_CLIENT_IP 头
            ip = request.getHeader("HTTP_CLIENT_IP");
            if (isValidIp(ip)) {
                return ip;
            }

            // 6. 检查 HTTP_X_FORWARDED_FOR 头
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
            if (isValidIp(ip)) {
                return ip;
            }

            // 7. 最后使用 getRemoteAddr()
            ip = request.getRemoteAddr();
            if (LOCALHOST_IPV6.equals(ip)) {
                // 如果是IPv6的localhost，转换为IPv4
                ip = LOCALHOST_IPV4;
            }

        } catch (Exception e) {
            log.error("获取IP地址失败", e);
        }

        return ip != null ? ip : UNKNOWN;
    }

    /**
     * 验证IP地址是否有效
     * 
     * @param ip IP地址
     * @return 是否有效
     */
    private static boolean isValidIp(String ip) {
        return ip != null 
            && !ip.isEmpty() 
            && !UNKNOWN.equalsIgnoreCase(ip) 
            && ip.length() <= IP_MAX_LENGTH;
    }

    /**
     * 验证IP地址格式是否正确
     * 
     * @param ip IP地址
     * @return 是否为有效的IP格式
     */
    public static boolean isValidIpFormat(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }
        return IP_PATTERN.matcher(ip).matches();
    }

    /**
     * 检查是否为内网IP
     * 
     * @param ip IP地址
     * @return 是否为内网IP
     */
    public static boolean isInternalIp(String ip) {
        if (!isValidIpFormat(ip)) {
            return false;
        }

        try {
            InetAddress addr = InetAddress.getByName(ip);
            return addr.isSiteLocalAddress() || addr.isLoopbackAddress();
        } catch (UnknownHostException e) {
            log.warn("检查内网IP失败: {}", ip, e);
            return false;
        }
    }

    /**
     * 根据IP地址获取地理位置
     * 这里提供一个简单的实现，实际项目中应该使用专业的IP地理位置服务
     * 
     * @param ip IP地址
     * @return 地理位置描述
     */
    public static String getLocationByIp(String ip) {
        if (ip == null || ip.isEmpty() || UNKNOWN.equals(ip)) {
            return "未知地区";
        }

        // 检查是否为本地IP
        if (LOCALHOST_IPV4.equals(ip) || LOCALHOST_IPV6.equals(ip)) {
            return "本地";
        }

        // 检查是否为内网IP
        if (isInternalIp(ip)) {
            return "内网";
        }

        // 这里应该调用实际的IP地理位置服务
        // 例如：高德地图API、百度地图API、或者使用离线IP数据库
        // 暂时返回默认值
        return getLocationByIpRange(ip);
    }

    /**
     * 根据IP段简单判断地理位置
     * 这是一个简化的实现，实际应用中应该使用专业的IP地理位置数据库
     * 
     * @param ip IP地址
     * @return 地理位置
     */
    private static String getLocationByIpRange(String ip) {
        if (!isValidIpFormat(ip)) {
            return "未知地区";
        }

        try {
            String[] parts = ip.split("\\.");
            int firstOctet = Integer.parseInt(parts[0]);
            int secondOctet = Integer.parseInt(parts[1]);

            // 这里只是示例，实际应该使用完整的IP地理位置数据库
            if (firstOctet >= 1 && firstOctet <= 126) {
                return "A类网络地址";
            } else if (firstOctet >= 128 && firstOctet <= 191) {
                return "B类网络地址";
            } else if (firstOctet >= 192 && firstOctet <= 223) {
                return "C类网络地址";
            } else {
                return "其他网络地址";
            }
        } catch (Exception e) {
            log.warn("解析IP地理位置失败: {}", ip, e);
            return "未知地区";
        }
    }

    /**
     * 获取本机IP地址
     * 
     * @return 本机IP地址
     */
    public static String getLocalIp() {
        try {
            InetAddress addr = InetAddress.getLocalHost();
            return addr.getHostAddress();
        } catch (UnknownHostException e) {
            log.error("获取本机IP失败", e);
            return LOCALHOST_IPV4;
        }
    }

    /**
     * 获取本机主机名
     * 
     * @return 本机主机名
     */
    public static String getLocalHostName() {
        try {
            InetAddress addr = InetAddress.getLocalHost();
            return addr.getHostName();
        } catch (UnknownHostException e) {
            log.error("获取本机主机名失败", e);
            return "localhost";
        }
    }

    /**
     * IP地址脱敏处理
     * 将IP地址的最后一段替换为 *
     * 
     * @param ip 原始IP地址
     * @return 脱敏后的IP地址
     */
    public static String maskIp(String ip) {
        if (!isValidIpFormat(ip)) {
            return ip;
        }

        try {
            String[] parts = ip.split("\\.");
            if (parts.length == 4) {
                return parts[0] + "." + parts[1] + "." + parts[2] + ".*";
            }
        } catch (Exception e) {
            log.warn("IP脱敏处理失败: {}", ip, e);
        }

        return ip;
    }

    /**
     * 检查IP是否在指定的网段内
     * 
     * @param ip 要检查的IP
     * @param cidr CIDR格式的网段，如 ***********/24
     * @return 是否在网段内
     */
    public static boolean isIpInRange(String ip, String cidr) {
        try {
            String[] cidrParts = cidr.split("/");
            if (cidrParts.length != 2) {
                return false;
            }

            String networkIp = cidrParts[0];
            int prefixLength = Integer.parseInt(cidrParts[1]);

            InetAddress targetAddr = InetAddress.getByName(ip);
            InetAddress networkAddr = InetAddress.getByName(networkIp);

            byte[] targetBytes = targetAddr.getAddress();
            byte[] networkBytes = networkAddr.getAddress();

            if (targetBytes.length != networkBytes.length) {
                return false;
            }

            int bytesToCheck = prefixLength / 8;
            int bitsToCheck = prefixLength % 8;

            // 检查完整的字节
            for (int i = 0; i < bytesToCheck; i++) {
                if (targetBytes[i] != networkBytes[i]) {
                    return false;
                }
            }

            // 检查剩余的位
            if (bitsToCheck > 0 && bytesToCheck < targetBytes.length) {
                int mask = 0xFF << (8 - bitsToCheck);
                return (targetBytes[bytesToCheck] & mask) == (networkBytes[bytesToCheck] & mask);
            }

            return true;
        } catch (Exception e) {
            log.warn("检查IP网段失败: ip={}, cidr={}", ip, cidr, e);
            return false;
        }
    }
}
