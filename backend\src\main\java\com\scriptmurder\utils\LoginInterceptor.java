package com.scriptmurder.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scriptmurder.dto.ApiResponse;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class LoginInterceptor implements HandlerInterceptor {

    // 在请求被分发到 Controller (Handler) 之前执行
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        // 1. 判断是否需要拦截（ThreadLocal中是否有用户）
        if (UserHolder.getUser() == null) {
            // 设置响应状态码和内容类型
            response.setStatus(401);
            response.setContentType("application/json;charset=UTF-8");

            // 返回JSON格式的错误信息
            ApiResponse<String> errorResponse = ApiResponse.unauthorized("用户未登录，请先登录");
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonResponse = objectMapper.writeValueAsString(errorResponse);

            response.getWriter().write(jsonResponse);
            response.getWriter().flush();

            // 拦截
            return false;
        }

        // 有用户，则放行
        return true;
    }
}
