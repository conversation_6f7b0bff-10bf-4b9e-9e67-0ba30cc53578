package com.scriptmurder.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.scriptmurder.dto.UserDTO;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class RefreshTokenInterceptor implements HandlerInterceptor {
    private StringRedisTemplate stringRedisTemplate;

    public RefreshTokenInterceptor(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }

    // 在请求被分发到 Controller (Handler) 之前执行
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        // 1.获取请求头中的 token
        String authorization = request.getHeader("authorization");
        if (StrUtil.isBlankIfStr(authorization)) {
            // token 为空，直接放行，由LoginInterceptor处理
            return true;
        }

        // 2.解析Bearer token
        String token = null;
        if (authorization.startsWith("Bearer ")) {
            token = authorization.substring(7);
        } else {
            // 格式不正确，直接放行，由LoginInterceptor处理
            return true;
        }

        // 3. 基于 TOKEN 获取 redis 中的用户
        String key = RedisConstants.LOGIN_USER_KEY + token;
        Map<Object, Object> userMap = stringRedisTemplate.opsForHash().entries(key);

        // 4. 判断用户是否存在
        if (userMap.isEmpty()) {
            // 用户不存在，直接放行，由LoginInterceptor处理
            return true;
        }

        // 5. 将查到的 Hash 数据转为 UserDTO 类型对象
        UserDTO userDTO = BeanUtil.fillBeanWithMap(userMap, new UserDTO(), false);

        // 6. 检查用户信息是否有效
        if (userDTO.getId() == null) {
            // 尝试从userMap中获取id并手动设置
            Object idObj = userMap.get("id");
            if (idObj != null) {
                try {
                    Long id = Long.valueOf(idObj.toString());
                    userDTO.setId(id);
                } catch (NumberFormatException e) {
                    // 转换失败，用户信息无效，直接放行
                    return true;
                }
            } else {
                // 用户信息无效，直接放行，由LoginInterceptor处理
                return true;
            }
        }

        // 7.存在，保存用户信息到 ThreadLocal
        UserHolder.saveUser(userDTO);

        // 8.刷新 token 有效期
        stringRedisTemplate.expire(key, RedisConstants.LOGIN_USER_TTL, TimeUnit.MINUTES);

        // 9.放行
        return true;
    }

    // 在整个请求处理完成之后（包括视图渲染完毕），也就是 DispatcherServlet 发送响应给客户端之后执行
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {

        // 移除用户
        UserHolder.removeUser();
    }
}
