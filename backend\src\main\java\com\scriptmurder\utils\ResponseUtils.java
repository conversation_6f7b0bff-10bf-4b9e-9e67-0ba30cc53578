package com.scriptmurder.utils;

import com.scriptmurder.dto.Result;
import com.scriptmurder.dto.PageResponse;
import com.scriptmurder.enums.ResponseCode;

import java.util.List;

/**
 * 响应工具类
 * 提供便捷的响应构建方法，统一使用Result类
 * 
 * <AUTHOR>
 * @since 1.0
 */
public class ResponseUtils {
    
    // ==================== 成功响应 ====================
    
    /**
     * 成功响应（无数据）
     */
    public static <T> Result<T> success() {
        return Result.success();
    }
    
    /**
     * 成功响应（带数据）
     */
    public static <T> Result<T> success(T data) {
        return Result.success(data);
    }
    
    /**
     * 成功响应（自定义消息）
     */
    public static <T> Result<T> success(String message, T data) {
        return Result.success(message, data);
    }
    
    /**
     * 分页成功响应
     */
    public static <T> Result<PageResponse<T>> successPage(List<T> records, Long current, Long size, Long total) {
        PageResponse<T> pageData = PageResponse.of(records, current, size, total);
        return Result.success(pageData);
    }
    
    /**
     * 分页成功响应（自定义消息）
     */
    public static <T> Result<PageResponse<T>> successPage(String message, List<T> records, Long current, Long size, Long total) {
        PageResponse<T> pageData = PageResponse.of(records, current, size, total);
        return Result.success(message, pageData);
    }
    
    // ==================== 错误响应 ====================
    
    /**
     * 错误响应
     */
    public static <T> Result<T> error(String message) {
        return Result.error(message);
    }
    
    /**
     * 错误响应（自定义错误码）
     */
    public static <T> Result<T> error(Integer code, String message) {
        return Result.error(code, message);
    }
    
    /**
     * 错误响应（使用枚举）
     */
    public static <T> Result<T> error(ResponseCode responseCode) {
        return Result.error(responseCode.getCode(), responseCode.getMessage());
    }
    
    /**
     * 错误响应（使用枚举和自定义消息）
     */
    public static <T> Result<T> error(ResponseCode responseCode, String message) {
        return Result.error(responseCode.getCode(), message);
    }
    
    // ==================== 常用业务响应 ====================
    
    /**
     * 用户不存在
     */
    public static <T> Result<T> userNotFound() {
        return Result.error(ResponseCode.USER_NOT_FOUND.getCode(), ResponseCode.USER_NOT_FOUND.getMessage());
    }
    
    /**
     * 用户已存在
     */
    public static <T> Result<T> userAlreadyExists() {
        return Result.error(ResponseCode.USER_ALREADY_EXISTS.getCode(), ResponseCode.USER_ALREADY_EXISTS.getMessage());
    }
    
    /**
     * 剧本不存在
     */
    public static <T> Result<T> scriptNotFound() {
        return Result.error(ResponseCode.SCRIPT_NOT_FOUND.getCode(), ResponseCode.SCRIPT_NOT_FOUND.getMessage());
    }
    
    /**
     * 房间不存在
     */
    public static <T> Result<T> lobbyNotFound() {
        return Result.error(ResponseCode.LOBBY_NOT_FOUND.getCode(), ResponseCode.LOBBY_NOT_FOUND.getMessage());
    }
    
    /**
     * 房间已满
     */
    public static <T> Result<T> lobbyFull() {
        return Result.error(ResponseCode.LOBBY_FULL.getCode(), ResponseCode.LOBBY_FULL.getMessage());
    }
    
    /**
     * 权限不足
     */
    public static <T> Result<T> permissionDenied() {
        return Result.error(ResponseCode.PERMISSION_DENIED.getCode(), ResponseCode.PERMISSION_DENIED.getMessage());
    }
    
    /**
     * Token无效
     */
    public static <T> Result<T> tokenInvalid() {
        return Result.error(ResponseCode.TOKEN_INVALID.getCode(), ResponseCode.TOKEN_INVALID.getMessage());
    }
    
    /**
     * Token过期
     */
    public static <T> Result<T> tokenExpired() {
        return Result.error(ResponseCode.TOKEN_EXPIRED.getCode(), ResponseCode.TOKEN_EXPIRED.getMessage());
    }
    
    /**
     * 参数错误
     */
    public static <T> Result<T> badRequest(String message) {
        return Result.badRequest(message);
    }
    
    /**
     * 未授权
     */
    public static <T> Result<T> unauthorized() {
        return Result.unauthorized(null);
    }
    
    /**
     * 未授权（自定义消息）
     */
    public static <T> Result<T> unauthorized(String message) {
        return Result.unauthorized(message);
    }
    
    /**
     * 禁止访问
     */
    public static <T> Result<T> forbidden() {
        return Result.forbidden(null);
    }
    
    /**
     * 禁止访问（自定义消息）
     */
    public static <T> Result<T> forbidden(String message) {
        return Result.forbidden(message);
    }
    
    /**
     * 资源不存在
     */
    public static <T> Result<T> notFound() {
        return Result.notFound(null);
    }
    
    /**
     * 资源不存在（自定义消息）
     */
    public static <T> Result<T> notFound(String message) {
        return Result.notFound(message);
    }
    
    /**
     * 服务器内部错误
     */
    public static <T> Result<T> serverError() {
        return Result.serverError(null);
    }
    
    /**
     * 服务器内部错误（自定义消息）
     */
    public static <T> Result<T> serverError(String message) {
        return Result.serverError(message);
    }
}
