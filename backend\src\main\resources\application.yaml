# ===================================================================
# 服务器配置
# ===================================================================
server:
  port: 8081

# ===================================================================
# Spring Boot 核心配置
# ===================================================================
spring:
  # 应用名称
  application:
    name: script-murder-backend

  # 激活配置文件
  profiles:
    active: local

  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************
    username: root
    password: 123456

  # Redis配置
  redis:
    host: 127.0.0.1
    port: 6379
    password: 123321
    lettuce:
      pool:
        max-active: 10
        max-idle: 10
        min-idle: 1
        time-between-eviction-runs: 10s

  # JSON序列化配置
  jackson:
    default-property-inclusion: NON_NULL # JSON处理时忽略非空字段
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false # 禁用时间戳格式
    deserialization:
      fail-on-unknown-properties: false

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

  # 邮件配置
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: your-email-auth-code
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true

  # Elasticsearch 7.x 配置
  elasticsearch:
    uris: http://localhost:9200
    username:
    password:
    socket-timeout: 30s
    connection-timeout: 5s

  # RabbitMQ配置
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    virtual-host: /
    connection-timeout: 15000
    publisher-confirm-type: correlated
    publisher-returns: true
    listener:
      simple:
        acknowledge-mode: manual
        retry:
          enabled: true
          initial-interval: 1000
          max-attempts: 3
          multiplier: 2

# ===================================================================
# MyBatis Plus 配置
# ===================================================================
mybatis-plus:
  type-aliases-package: com.scriptmurder.entity # 别名扫描包

# ===================================================================
# 日志配置
# ===================================================================
logging:
  level:
    '[com.scriptmurder]': debug
    '[org.springframework.data.elasticsearch]': debug
    '[org.elasticsearch.client]': debug

# ===================================================================
# 应用监控配置
# ===================================================================
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,caches
  endpoint:
    health:
      show-details: always
      show-components: always
  health:
    redis:
      enabled: true
    elasticsearch:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

# ===================================================================
# 阿里云OSS配置 (请在 application-local.yaml 中配置实际值)
# ===================================================================
aliyun:
  oss:
    endpoint: your-oss-endpoint
    access-key-id: your-access-key-id
    access-key-secret: your-access-key-secret
    bucket-name: your-bucket-name

# ===================================================================
# Script Murder 业务配置
# ===================================================================
script-murder:
  # 博客图片上传配置
  blog:
    upload:
      # 上传方式：local(本地存储) 或 oss(阿里云OSS)
      type: oss
      # 是否启用博客图片上传功能
      enabled: true

      # 本地存储配置（当type=local时使用）
      local:
        upload-dir: C:\Users\<USER>\Desktop\interview\project\HmdpReconstruction\uploads\images
        url-prefix: http://localhost:8081/images

      # OSS存储配置（当type=oss时使用）
      oss:
        path-prefix: script-murder/blog/images

      # 通用配置
      max-file-size: 5242880 # 最大文件大小（字节，5MB）
      allowed-extensions: # 允许的文件扩展名
        - jpg
        - jpeg
        - png
        - gif
        - bmp
        - webp

  # Elasticsearch 8.x 配置
  elasticsearch:
    index-prefix: script_murder
    batch-size: 1000
    # ES 8.x 新增配置选项
    request-timeout: 10s
    max-connections: 100
    max-connections-per-route: 10

  # RabbitMQ配置
  rabbitmq:
    exchange: script.exchange
    queue:
      sync: script.sync.queue
      stats: script.stats.queue

# ===================================================================
# 文件上传配置
# ===================================================================
file:
  upload:
    # 文件上传根目录
    path: C:\Users\<USER>\Desktop\interview\project\HmdpReconstruction\uploads\
    # 访问域名
    domain: http://localhost:8081

