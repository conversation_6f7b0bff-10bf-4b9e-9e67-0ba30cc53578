-- 剧本状态管理相关表结构
-- 版本: V1.4
-- 日期: 2025-08-04
-- 描述: 添加剧本状态管理功能

-- 1. 修改剧本表，添加状态管理字段
ALTER TABLE `tb_script` 
ADD COLUMN `creator_id` BIGINT COMMENT '创建者ID' AFTER `play_count`,
ADD COLUMN `reviewer_id` BIGINT COMMENT '审核员ID' AFTER `creator_id`,
ADD COLUMN `review_time` DATETIME COMMENT '审核时间' AFTER `reviewer_id`,
ADD COLUMN `review_comment` TEXT COMMENT '审核意见' AFTER `review_time`;

-- 2. 创建剧本状态变更历史表
CREATE TABLE `tb_script_status_history` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `script_id` BIGINT NOT NULL COMMENT '剧本ID',
  `from_status` INT COMMENT '原状态',
  `to_status` INT NOT NULL COMMENT '新状态',
  `operator_id` BIGINT COMMENT '操作员ID',
  `operator_type` VARCHAR(20) NOT NULL DEFAULT 'USER' COMMENT '操作员类型(USER/ADMIN/SYSTEM)',
  `reason` VARCHAR(100) COMMENT '操作原因',
  `comment` TEXT COMMENT '操作备注',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `operator_ip` VARCHAR(50) COMMENT '操作IP地址',
  `device_info` VARCHAR(200) COMMENT '操作设备信息',
  `review_duration` INT COMMENT '审核耗时(分钟)',
  `extra_info` JSON COMMENT '扩展信息',
  PRIMARY KEY (`id`),
  KEY `idx_script_id` (`script_id`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_status_change` (`from_status`, `to_status`),
  CONSTRAINT `fk_script_status_script` FOREIGN KEY (`script_id`) REFERENCES `tb_script` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='剧本状态变更历史表';

-- 3. 更新现有剧本的状态字段默认值
UPDATE `tb_script` SET `status` = 1 WHERE `status` IS NULL;

-- 4. 为现有剧本设置创建者ID（假设为系统用户ID=1）
UPDATE `tb_script` SET `creator_id` = 1 WHERE `creator_id` IS NULL;

-- 5. 添加索引优化查询性能
ALTER TABLE `tb_script` 
ADD INDEX `idx_status` (`status`),
ADD INDEX `idx_creator_id` (`creator_id`),
ADD INDEX `idx_reviewer_id` (`reviewer_id`),
ADD INDEX `idx_review_time` (`review_time`),
ADD INDEX `idx_status_creator` (`status`, `creator_id`);

-- 6. 创建剧本状态统计视图
CREATE VIEW `v_script_status_stats` AS
SELECT 
    status,
    CASE status
        WHEN 1 THEN '已上架'
        WHEN 2 THEN '已下架'
        WHEN 3 THEN '审核中'
        WHEN 4 THEN '审核拒绝'
        WHEN 5 THEN '草稿'
        ELSE '未知'
    END as status_name,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM tb_script), 2) as percentage
FROM tb_script 
GROUP BY status;

-- 7. 创建审核效率统计视图
CREATE VIEW `v_review_efficiency` AS
SELECT 
    DATE(h.create_time) as date,
    COUNT(CASE WHEN h.to_status = 3 THEN 1 END) as submitted_count,
    COUNT(CASE WHEN h.to_status IN (1, 4) THEN 1 END) as reviewed_count,
    ROUND(AVG(CASE 
        WHEN s.review_time IS NOT NULL AND h.to_status IN (1, 4)
        THEN TIMESTAMPDIFF(HOUR, s.create_time, s.review_time) 
    END), 2) as avg_review_hours,
    ROUND(
        COUNT(CASE WHEN h.to_status = 1 THEN 1 END) * 100.0 / 
        NULLIF(COUNT(CASE WHEN h.to_status IN (1, 4) THEN 1 END), 0), 2
    ) as approval_rate
FROM tb_script_status_history h
LEFT JOIN tb_script s ON h.script_id = s.id
WHERE h.create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(h.create_time)
ORDER BY date DESC;

-- 8. 插入初始状态变更记录（为现有剧本）
INSERT INTO `tb_script_status_history` (
    `script_id`, `from_status`, `to_status`, `operator_id`, `operator_type`, 
    `reason`, `comment`, `create_time`
)
SELECT 
    id, NULL, status, creator_id, 'SYSTEM', 
    '系统初始化', '数据迁移时自动创建的状态记录', create_time
FROM `tb_script`
WHERE id NOT IN (SELECT DISTINCT script_id FROM `tb_script_status_history`);

-- 9. 创建状态管理相关的存储过程

-- 获取剧本状态分布统计
DELIMITER //
CREATE PROCEDURE `sp_get_script_status_distribution`()
BEGIN
    SELECT 
        status,
        CASE status
            WHEN 1 THEN '已上架'
            WHEN 2 THEN '已下架'
            WHEN 3 THEN '审核中'
            WHEN 4 THEN '审核拒绝'
            WHEN 5 THEN '草稿'
            ELSE '未知'
        END as status_name,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM tb_script), 2) as percentage
    FROM tb_script 
    GROUP BY status
    ORDER BY status;
END //

-- 获取用户剧本状态分布
CREATE PROCEDURE `sp_get_user_script_status_distribution`(IN user_id BIGINT)
BEGIN
    SELECT 
        status,
        CASE status
            WHEN 1 THEN '已上架'
            WHEN 2 THEN '已下架'
            WHEN 3 THEN '审核中'
            WHEN 4 THEN '审核拒绝'
            WHEN 5 THEN '草稿'
            ELSE '未知'
        END as status_name,
        COUNT(*) as count
    FROM tb_script 
    WHERE creator_id = user_id
    GROUP BY status
    ORDER BY status;
END //

-- 获取审核效率统计
CREATE PROCEDURE `sp_get_review_efficiency_stats`(IN days INT)
BEGIN
    SELECT 
        DATE(h.create_time) as date,
        COUNT(CASE WHEN h.to_status = 3 THEN 1 END) as submitted_count,
        COUNT(CASE WHEN h.to_status IN (1, 4) THEN 1 END) as reviewed_count,
        ROUND(AVG(CASE 
            WHEN s.review_time IS NOT NULL AND h.to_status IN (1, 4)
            THEN TIMESTAMPDIFF(MINUTE, s.create_time, s.review_time) 
        END), 2) as avg_review_minutes,
        ROUND(
            COUNT(CASE WHEN h.to_status = 1 THEN 1 END) * 100.0 / 
            NULLIF(COUNT(CASE WHEN h.to_status IN (1, 4) THEN 1 END), 0), 2
        ) as approval_rate
    FROM tb_script_status_history h
    LEFT JOIN tb_script s ON h.script_id = s.id
    WHERE h.create_time >= DATE_SUB(NOW(), INTERVAL days DAY)
    GROUP BY DATE(h.create_time)
    ORDER BY date DESC;
END //

DELIMITER ;

-- 10. 创建触发器，自动记录状态变更
DELIMITER //
CREATE TRIGGER `tr_script_status_change` 
AFTER UPDATE ON `tb_script`
FOR EACH ROW
BEGIN
    -- 只有当状态发生变化时才记录
    IF OLD.status != NEW.status THEN
        INSERT INTO `tb_script_status_history` (
            `script_id`, `from_status`, `to_status`, `operator_id`, `operator_type`,
            `reason`, `comment`, `create_time`, `review_duration`
        ) VALUES (
            NEW.id, OLD.status, NEW.status, NEW.reviewer_id, 'SYSTEM',
            '状态变更', NEW.review_comment, NOW(),
            CASE 
                WHEN NEW.review_time IS NOT NULL AND OLD.status = 3 AND NEW.status IN (1, 4)
                THEN TIMESTAMPDIFF(MINUTE, OLD.create_time, NEW.review_time)
                ELSE NULL
            END
        );
    END IF;
END //
DELIMITER ;

-- 11. 添加约束确保数据完整性
ALTER TABLE `tb_script` 
ADD CONSTRAINT `chk_script_status` CHECK (`status` IN (1, 2, 3, 4, 5)),
ADD CONSTRAINT `chk_script_difficulty` CHECK (`difficulty` BETWEEN 1 AND 5),
ADD CONSTRAINT `chk_script_player_count` CHECK (`player_count_min` <= `player_count_max`);

ALTER TABLE `tb_script_status_history`
ADD CONSTRAINT `chk_status_history_operator_type` CHECK (`operator_type` IN ('USER', 'ADMIN', 'SYSTEM')),
ADD CONSTRAINT `chk_status_history_status` CHECK (`to_status` IN (1, 2, 3, 4, 5));

-- 12. 创建定时清理历史记录的事件（可选）
-- 保留最近1年的状态变更历史
CREATE EVENT IF NOT EXISTS `ev_cleanup_status_history`
ON SCHEDULE EVERY 1 MONTH
STARTS '2025-09-01 02:00:00'
DO
  DELETE FROM `tb_script_status_history` 
  WHERE `create_time` < DATE_SUB(NOW(), INTERVAL 1 YEAR);

-- 启用事件调度器
SET GLOBAL event_scheduler = ON;
