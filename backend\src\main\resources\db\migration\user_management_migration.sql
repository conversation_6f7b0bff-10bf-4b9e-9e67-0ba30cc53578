-- 用户管理模块数据库迁移脚本
-- 执行时间: 2025-01-31
-- 描述: 为用户管理模块添加新字段和收藏表

-- 1. 为 tb_user 表添加新字段
ALTER TABLE `tb_user` 
ADD COLUMN `gender` tinyint DEFAULT '0' COMMENT '性别：0-未知，1-男，2-女',
ADD COLUMN `birthday` date DEFAULT NULL COMMENT '生日',
ADD COLUMN `city` varchar(50) DEFAULT NULL COMMENT '城市',
ADD COLUMN `signature` varchar(200) DEFAULT NULL COMMENT '个性签名',
ADD COLUMN `level` int DEFAULT '1' COMMENT '用户等级',
ADD COLUMN `experience` int DEFAULT '0' COMMENT '经验值';

-- 2. 创建用户收藏表
CREATE TABLE IF NOT EXISTS `tb_user_favorite` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `target_id` bigint NOT NULL COMMENT '收藏目标ID',
  `target_type` varchar(20) NOT NULL COMMENT '收藏类型：script-剧本，room-房间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_user_target` (`user_id`, `target_id`, `target_type`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收藏表';

-- 3. 验证表结构
-- 查看 tb_user 表结构
-- DESC tb_user;

-- 查看 tb_user_favorite 表结构  
-- DESC tb_user_favorite;

-- 4. 插入测试数据（可选）
-- INSERT INTO tb_user_favorite (user_id, target_id, target_type) VALUES (1, 1, 'script');
-- INSERT INTO tb_user_favorite (user_id, target_id, target_type) VALUES (1, 2, 'room');
