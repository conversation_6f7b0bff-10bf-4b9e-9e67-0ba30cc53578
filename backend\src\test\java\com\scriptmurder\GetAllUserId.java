package com.scriptmurder;

import com.scriptmurder.entity.User;
import com.scriptmurder.service.IUserService;
import org.json.JSONObject; // 可以保留，用于解析
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.ResponseEntity; // 关键：使用 Spring 的 ResponseEntity

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// 关键改动1：为了动态获取端口，避免写死8081，推荐使用 RANDOM_PORT
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class GetAllUserId {

    @Autowired
    private IUserService userService;

    @Autowired
    private TestRestTemplate restTemplate;

    // 关键改动2：注入随机端口
    @org.springframework.boot.web.server.LocalServerPort
    private int port;

    @Test
    public void function() throws IOException {
        // 关键改动3：动态构建URL
        String loginUrl = "http://localhost:8081/user/login";
        String tokenFilePath = "tokens.txt";

        // 从数据库中获取所有用户
        List<User> users = userService.list();

        // 使用 try-with-resources 确保 writer 被自动关闭
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(tokenFilePath))) {
            for (User user : users) {
                // 1. 构建请求体 (使用 Map，TestRestTemplate 会自动转为 JSON)
                Map<String, String> requestBody = new HashMap<>();
                requestBody.put("phone", user.getPhone());
                requestBody.put("code", "123456"); // 使用我们在上一轮调试中约定的“万能码”

                // 2. 发送请求，并接收为 ResponseEntity<String> 类型
                ResponseEntity<String> response = restTemplate.postForEntity(loginUrl, requestBody, String.class);

                // --- 打印诊断信息 ---
                System.out.println("--- 请求手机号: " + user.getPhone() + " ---");
                // 使用 ResponseEntity 的方法获取状态码
                System.out.println("响应状态码: " + response.getStatusCode());
                // 使用 ResponseEntity 的方法获取响应体
                System.out.println("响应体: " + response.getBody());
                System.out.println("------------------------------------");

                // 3. 处理响应
                // is2xxSuccessful() 涵盖了 200, 201 等所有成功状态
                if (response.getStatusCode().is2xxSuccessful()) {
                    String responseBody = response.getBody();
                    String token = parseTokenFromJson(responseBody);

                    if (token != null) {
                        System.out.println("登录成功，Token: " + token);
                        writer.write(token);
                        writer.newLine();
                    } else {
                        System.out.println("登录成功但无法从响应体中解析出Token: " + responseBody);
                    }
                } else {
                    System.out.println("手机号 " + user.getPhone() + " 登录失败，服务器返回错误。");
                }
            }
        }
        // catch 块只需要捕获 IOException，因为其他异常在方法签名中声明了
        // 如果想更细致地处理，可以捕获其他异常
    }

    // 解析JSON响应获取token的方法，这个方法保持不变，是正确的
    private static String parseTokenFromJson(String json) {
        if (json == null) {
            return null;
        }
        try {
            JSONObject jsonObject = new JSONObject(json);
            // 使用 optString 更安全，如果"data"字段不存在，不会抛异常而是返回默认值(null)
            return jsonObject.optString("data", null);
        } catch (Exception e) {
            System.err.println("解析JSON失败: " + json);
            e.printStackTrace();
            return null;
        }
    }
}
