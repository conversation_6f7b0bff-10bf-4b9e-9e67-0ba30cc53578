package com.scriptmurder;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Resource;

/**
 * 数据加载测试类
 * 
 * <AUTHOR>
 * @since 1.0
 */
@SpringBootTest
public class LoadShopData {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Test
    public void testRedisConnection() {
        // 测试Redis连接
        stringRedisTemplate.opsForValue().set("test:connection", "success");
        String result = stringRedisTemplate.opsForValue().get("test:connection");
        System.out.println("Redis connection test result: " + result);
        
        // 清理测试数据
        stringRedisTemplate.delete("test:connection");
    }
    
    @Test
    public void loadScriptData() {
        // TODO: 实现剧本数据加载
        System.out.println("Script data loading test - to be implemented");
    }
}
