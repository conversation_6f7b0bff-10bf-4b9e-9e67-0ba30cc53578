package com.scriptmurder;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;

@SpringBootTest
public class RedisConnectionTest {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Test
    public void testRedisConnection() {
        try {
            // 测试基本的 Redis 操作
            stringRedisTemplate.opsForValue().set("test:connection", "success");
            String result = stringRedisTemplate.opsForValue().get("test:connection");
            System.out.println("Redis 连接测试结果: " + result);
            
            // 清理测试数据
            stringRedisTemplate.delete("test:connection");
            
            System.out.println("Redis 连接测试成功！");
        } catch (Exception e) {
            System.err.println("Redis 连接测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
