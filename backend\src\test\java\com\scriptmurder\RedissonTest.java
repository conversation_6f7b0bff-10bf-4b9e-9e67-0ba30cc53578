package com.scriptmurder;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Slf4j
@SpringBootTest
public class RedissonTest {

    @Resource
    private RedissonClient redissonClient;

    private RLock lock;

    @BeforeEach
    void setUp() {
        lock = redissonClient.getLock("order");
    }

    @Test
    public void method01() throws InterruptedException {
        boolean isLock = lock.tryLock(1L, TimeUnit.SECONDS);

        if (!isLock) {
            log.error("获取锁失败。。。1");
            return;
        }
        try {
            log.info("获取锁成功。。。1");
            method02();
            log.info("继续执行业务。。。1");
        } finally {
            lock.unlock();
            log.warn("准备释放锁。。。1");
            log.info("释放锁。。。1");
        }
    }

    public void method02() {
        boolean isLock = lock.tryLock();

        if (!isLock) {
            log.error("获取锁失败。。。2");
            return;
        }

        try {
            log.info("获取锁成功。。。2");
            log.info("执行业务。。。2");
        } finally {
            log.warn("准备释放锁。。。2");
            lock.unlock();
        }
    }
}
