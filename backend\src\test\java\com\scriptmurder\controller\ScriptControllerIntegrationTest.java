package com.scriptmurder.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scriptmurder.dto.PageResponse;
import com.scriptmurder.dto.ScriptDTO;
import com.scriptmurder.dto.ScriptDetailDTO;
import com.scriptmurder.dto.ScriptSearchDTO;
import com.scriptmurder.service.IScriptSearchService;
import com.scriptmurder.service.IScriptService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.hamcrest.Matchers.hasItem;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 剧本控制器集成测试
 *
 * <AUTHOR>
 */
@WebMvcTest(ScriptController.class)
class ScriptControllerIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private IScriptService scriptService;

    @MockBean
    private IScriptSearchService scriptSearchService;

    private ScriptDTO mockScriptDTO;
    private ScriptDetailDTO mockScriptDetailDTO;
    private PageResponse<ScriptDTO> mockPageResponse;

    @BeforeEach
    void setUp() {
        mockScriptDTO = ScriptDTO.builder()
            .id(1L)
            .title("测试剧本")
            .description("这是一个测试剧本")
            .category("推理")
            .playerCountRange("4-6人")
            .duration("3小时")
            .difficulty(3)
            .price(BigDecimal.valueOf(68.0))
            .averageRating(BigDecimal.valueOf(4.5))
            .reviewCount(100)
            .playCount(500)
            .isFavorite(false)
            .createTime(LocalDateTime.now())
            .updateTime(LocalDateTime.now())
            .build();

        mockScriptDetailDTO = ScriptDetailDTO.builder()
            .id(1L)
            .title("测试剧本")
            .description("这是一个测试剧本详情")
            .category("推理")
            .playerCountMin(4)
            .playerCountMax(6)
            .duration("3小时")
            .difficulty(3)
            .price(BigDecimal.valueOf(68.0))
            .averageRating(BigDecimal.valueOf(4.5))
            .reviewCount(100)
            .playCount(500)
            .isFavorite(false)
            .characters(Arrays.asList())
            .rules(Arrays.asList())
            .recentReviews(Arrays.asList())
            .createTime(LocalDateTime.now())
            .updateTime(LocalDateTime.now())
            .build();

        mockPageResponse = PageResponse.<ScriptDTO>builder()
            .records(Arrays.asList(mockScriptDTO))
            .total(1L)
            .current(1L)
            .size(10L)
            .pages(1L)
            .build();
    }

    @Test
    void testGetScriptList_Success() throws Exception {
        // 设置Mock行为
        when(scriptService.getScriptList(any(ScriptSearchDTO.class)))
            .thenReturn(mockPageResponse);

        // 执行请求并验证
        mockMvc.perform(get("/api/scripts/list")
                .param("category", "推理")
                .param("page", "1")
                .param("size", "10")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.data.total").value(1))
            .andExpect(jsonPath("$.data.records").isArray())
            .andExpect(jsonPath("$.data.records[0].id").value(1))
            .andExpect(jsonPath("$.data.records[0].title").value("测试剧本"))
            .andExpect(jsonPath("$.data.records[0].category").value("推理"));
    }

    @Test
    void testSearchScripts_Success() throws Exception {
        // 设置Mock行为
        when(scriptService.searchScripts(any(ScriptSearchDTO.class)))
            .thenReturn(mockPageResponse);

        // 执行请求并验证
        mockMvc.perform(get("/api/scripts/search")
                .param("keyword", "推理")
                .param("page", "1")
                .param("size", "10")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.data.total").value(1))
            .andExpect(jsonPath("$.data.records[0].title").value("测试剧本"));
    }

    @Test
    void testGetScriptDetail_Success() throws Exception {
        // 设置Mock行为
        when(scriptService.getScriptDetail(1L)).thenReturn(mockScriptDetailDTO);

        // 执行请求并验证
        mockMvc.perform(get("/api/scripts/1")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.data.id").value(1))
            .andExpect(jsonPath("$.data.title").value("测试剧本"))
            .andExpect(jsonPath("$.data.characters").isArray())
            .andExpect(jsonPath("$.data.rules").isArray())
            .andExpect(jsonPath("$.data.recentReviews").isArray());
    }

    @Test
    void testGetPopularScripts_Success() throws Exception {
        // 设置Mock行为
        List<ScriptDTO> mockScripts = Arrays.asList(mockScriptDTO);
        when(scriptService.getPopularScripts(10)).thenReturn(mockScripts);

        // 执行请求并验证
        mockMvc.perform(get("/api/scripts/popular")
                .param("limit", "10")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.data").isArray())
            .andExpect(jsonPath("$.data[0].title").value("测试剧本"));
    }

    @Test
    void testGetSearchSuggestions_Success() throws Exception {
        // 设置Mock行为
        List<String> mockSuggestions = Arrays.asList("推理剧本", "推理悬疑");
        when(scriptSearchService.getSearchSuggestions("推理"))
            .thenReturn(mockSuggestions);

        // 执行请求并验证
        mockMvc.perform(get("/api/scripts/search/suggestions")
                .param("keyword", "推理")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.data").isArray())
            .andExpect(jsonPath("$.data[0]").value("推理剧本"));
    }

    @Test
    void testGetHotSearchKeywords_Success() throws Exception {
        // 设置Mock行为
        List<String> mockHotKeywords = Arrays.asList("推理", "恐怖", "情感");
        when(scriptSearchService.getHotSearchKeywords()).thenReturn(mockHotKeywords);

        // 执行请求并验证
        mockMvc.perform(get("/api/scripts/search/hot")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.data").isArray())
            .andExpect(jsonPath("$.data").value(hasItem("推理")));
    }

    @Test
    void testRebuildSearchIndex_Success() throws Exception {
        // 执行请求并验证
        mockMvc.perform(post("/api/scripts/search/index/rebuild")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    void testGetScriptList_InvalidParameters() throws Exception {
        // 测试无效参数
        mockMvc.perform(get("/api/scripts/list")
                .param("page", "0")  // 无效页码
                .param("size", "101") // 超出限制
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isBadRequest());
    }
}