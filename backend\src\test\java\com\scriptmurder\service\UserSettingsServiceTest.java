package com.scriptmurder.service;

import com.scriptmurder.dto.UserSettingsDTO;
import com.scriptmurder.service.impl.UserSettingsServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

/**
 * 用户设置服务测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class UserSettingsServiceTest {

    @Resource
    private IUserSettingsService userSettingsService;

    @Test
    public void testCreateDefaultSettings() {
        // 测试创建默认设置
        UserSettingsDTO defaultSettings = UserSettingsDTO.createDefault();
        
        assert defaultSettings != null;
        assert "dark".equals(defaultSettings.getTheme());
        assert "zh-CN".equals(defaultSettings.getLanguage());
        assert defaultSettings.getEmailNotifications();
        assert defaultSettings.getSystemNotifications();
        assert defaultSettings.getActivityNotifications();
        assert !defaultSettings.getSocialNotifications();
        assert defaultSettings.getPrivacyLevel() == 1;
        assert defaultSettings.getAutoSave();
    }

    @Test
    public void testValidateSettings() {
        UserSettingsDTO settings = new UserSettingsDTO();
        settings.setTheme("dark");
        settings.setLanguage("zh-CN");
        settings.setPrivacyLevel(1);
        
        assert settings.isValid();
        
        // 测试无效主题
        settings.setTheme("invalid");
        assert !settings.isValid();
        
        // 测试无效语言格式
        settings.setTheme("dark");
        settings.setLanguage("invalid");
        assert !settings.isValid();
        
        // 测试无效隐私级别
        settings.setLanguage("zh-CN");
        settings.setPrivacyLevel(5);
        assert !settings.isValid();
    }
}
