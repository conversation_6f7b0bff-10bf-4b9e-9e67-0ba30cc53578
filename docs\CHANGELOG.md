# 版本更新日志

本文档记录了剧本杀应用项目的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
版本号遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 计划新增
- 实时消息推送功能
- 房间语音聊天功能
- 剧本推荐算法优化
- 移动端适配优化

### 计划修改
- 用户界面重新设计
- API性能优化
- 数据库查询优化

## [1.0.0-beta.4] - 2025-08-04

### 新增
- 高级搜索系统
  - 新增AdvancedSearchController专门处理高级搜索
  - 支持多条件组合搜索和聚合统计
  - 实现复杂筛选和自定义排序功能
- 搜索历史记录系统
  - 新增ISearchHistoryService和SearchHistoryServiceImpl
  - 用户个人搜索历史记录和管理
  - 搜索趋势分析和性能统计
- 搜索体验优化
  - 增强搜索建议和自动完成功能
  - 实现相关搜索推荐
  - 添加搜索点击行为追踪
- 搜索数据分析
  - 搜索关键词热度统计
  - 无结果关键词分析
  - 搜索性能监控指标

### 修改
- 优化ScriptSearchParams参数类，增加Lombok注解和验证
- 增强搜索服务集成，自动记录搜索历史
- 改进搜索接口响应格式和错误处理

### 修复
- 修复搜索功能中的技术债务
- 解决搜索参数验证问题
- 优化搜索性能和缓存策略

### 技术改进
- 搜索功能完善度从60%提升到95%
- 新增8个高级搜索相关接口
- 实现完整的搜索数据分析体系

## [1.0.0-beta.5] - 2025-08-04

### 新增
- 剧本状态管理系统
  - 新增ScriptStatus枚举定义5种状态
  - 实现完整的状态转换规则和验证机制
  - 新增ScriptStatusService状态管理服务
  - 支持剧本提交审核、审核通过/拒绝、上下架操作
- 审核管理系统
  - 实现剧本审核工作流和队列管理
  - 支持批量审核和批量状态变更
  - 新增审核效率统计和通过率分析
  - 基于规则的自动审核机制
- 状态变更历史系统
  - 新增ScriptStatusHistory实体记录所有状态变更
  - 完整的操作审计日志和历史追踪
  - 支持审核耗时统计和操作回溯
- 管理员后台API
  - 新增AdminController提供管理员功能
  - 实现剧本管理、审核管理、统计分析接口
  - 支持系统监控和维护功能
- 权限控制增强
  - 基于角色的精细化权限管理
  - 创建者和管理员权限分离
  - 动态权限检查和操作验证

### 修改
- 扩展Script实体，新增状态管理相关字段
- 优化数据库结构，添加状态管理索引
- 改进API响应格式，统一错误处理

### 数据库变更
- 新增tb_script_status_history状态历史表
- Script表新增creator_id、reviewer_id、review_time、review_comment字段
- 新增状态管理相关索引和约束
- 创建状态统计视图和存储过程

### 技术改进
- 剧本管理系统完成度从95%提升到98%
- 新增15个状态管理相关API接口
- 实现完整的审核工作流和权限控制体系
- 数据库性能优化和查询效率提升

## [1.0.0-beta.3] - 2025-08-01

### 新增
- 社区动态功能
  - 用户可以发布游戏体验、剧本评价等动态
  - 支持图片上传和剧本关联
  - 动态点赞、评论、分享功能
- 关注/粉丝系统
  - 用户可以关注感兴趣的其他用户
  - 关注者动态优先展示
  - 粉丝数量统计
- 消息通知功能
  - 房间状态变更通知
  - 动态互动通知
  - 系统公告推送

### 修改
- 优化房间列表加载性能
- 改进剧本搜索算法
- 更新用户界面设计
- 增强移动端体验

### 修复
- 修复房间满员后仍可加入的问题
- 解决用户头像上传失败的bug
- 修复剧本评分计算错误
- 解决并发情况下的数据一致性问题

### 安全性
- 加强用户输入验证
- 优化JWT Token安全策略
- 增加API访问频率限制

## [1.0.0-beta.2] - 2025-07-25

### 新增
- 剧本评价系统
  - 用户可以对参与过的剧本进行评分和评价
  - 支持评价标签和体验描述
  - 评价有用性投票功能
- 用户中心完善
  - 个人信息管理
  - 游戏历史记录
  - 收藏剧本功能
- 房间高级功能
  - 角色分配和准备状态管理
  - 房间聊天功能
  - 房间设置和权限控制

### 修改
- 优化剧本详情页面布局
- 改进房间创建流程
- 更新API响应格式
- 增强错误处理机制

### 修复
- 修复用户登录状态丢失问题
- 解决剧本图片加载缓慢的问题
- 修复房间玩家列表更新延迟
- 解决分页查询的边界问题

### 废弃
- 旧版用户认证接口（将在v1.1.0中移除）

## [1.0.0-beta.1] - 2025-07-20

### 新增
- 用户认证模块
  - 手机号注册和登录
  - 短信验证码服务
  - JWT Token认证
  - 用户权限管理
- 剧本管理模块
  - 剧本信息展示和搜索
  - 剧本分类和标签
  - 剧本详情和角色信息
  - 剧本规则和推荐信息
- 房间管理模块
  - 房间创建和配置
  - 玩家加入和退出
  - 房间状态管理
  - 基础聊天功能
- 基础设施
  - 统一API响应格式
  - 全局异常处理
  - 数据库设计和初始化
  - Redis缓存集成
  - 文件上传服务

### 技术栈
- 后端：Spring Boot 2.7 + MySQL 8.0 + Redis 6.0
- 前端：Vue 3 + TypeScript + Element Plus
- 部署：Docker + Nginx

## [0.1.0] - 2023-12-15

### 新增
- 项目初始化
- 基础项目结构搭建
- 开发环境配置
- 数据库表结构设计
- 基础API框架

---

## 版本说明

### 版本号格式
- **主版本号**：不兼容的API修改
- **次版本号**：向下兼容的功能性新增
- **修订号**：向下兼容的问题修正
- **预发布版本**：alpha、beta、rc等

### 变更类型
- **新增 (Added)**：新功能
- **修改 (Changed)**：对现有功能的变更
- **废弃 (Deprecated)**：即将移除的功能
- **移除 (Removed)**：已移除的功能
- **修复 (Fixed)**：问题修复
- **安全性 (Security)**：安全相关的修复

### 发布计划

#### v1.0.0 (计划：2025-08-25)
- 完整的核心功能
- 生产环境部署
- 性能优化和安全加固
- 完整的测试覆盖

#### v1.1.0 (计划：2025-09-20)
- 高级搜索和推荐功能
- 数据统计和分析
- 管理后台功能
- API版本升级

#### v1.2.0 (计划：2025-10-15)
- 移动端原生应用
- 第三方登录集成
- 支付功能集成
- 国际化支持

### 升级指南

#### 从 beta.2 升级到 beta.3
1. 更新数据库结构：
   ```sql
   -- 执行数据库迁移脚本
   source sql/migrations/beta.2_to_beta.3.sql
   ```

2. 更新配置文件：
   ```yaml
   # 新增消息推送配置
   notification:
     enabled: true
     provider: websocket
   ```

3. 更新前端依赖：
   ```bash
   cd frontend
   npm update
   ```

#### 从 beta.1 升级到 beta.2
1. 数据库迁移：
   ```sql
   source sql/migrations/beta.1_to_beta.2.sql
   ```

2. API变更：
   - 用户认证接口路径变更：`/auth/login` → `/api/auth/login`
   - 响应格式统一为新的ApiResponse格式

3. 前端适配：
   - 更新API调用路径
   - 适配新的响应格式

### 已知问题

#### v1.0.0-beta.3
- 大量并发用户时可能出现性能问题
- 移动端部分页面适配不完善
- 图片上传在弱网环境下可能失败

#### v1.0.0-beta.2
- 房间聊天功能在网络不稳定时可能断连
- 剧本搜索结果排序算法需要优化

### 贡献者

感谢以下贡献者对本项目的贡献：

- **开发团队** - 核心功能开发
- **前端团队** - 用户界面设计和实现
- **测试团队** - 质量保证和测试
- **运维团队** - 部署和运维支持

### 反馈和支持

- 🐛 **Bug报告**: [GitHub Issues](https://github.com/example/hmdp-reconstruction/issues)
- 💡 **功能建议**: [GitHub Discussions](https://github.com/example/hmdp-reconstruction/discussions)
- 📧 **技术支持**: <EMAIL>
- 📖 **文档问题**: <EMAIL>

---

**文档维护**: 开发团队  
**最后更新**: 2025-08-03
