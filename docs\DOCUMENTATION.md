# 剧本杀平台完整文档

## 📋 目录

- [1. 项目概述](#1-项目概述)
- [2. 架构设计](#2-架构设计)
- [3. 开发环境搭建](#3-开发环境搭建)
- [4. 模块开发状态](#4-模块开发状态)
- [5. API 接口规范](#5-api-接口规范)
- [6. 技术栈详解](#6-技术栈详解)
- [7. 部署指南](#7-部署指南)
- [8. 开发规范](#8-开发规范)

---

## 1. 项目概述

### 1.1 项目背景

**剧本杀社交平台（Script Murder Platform）** 是一个现代化的全栈Web应用，专门为剧本杀游戏爱好者打造。该项目从传统HMDP电商平台重构而来，融合了社交网络、内容管理、实时通信等多种技术特性。

### 1.2 核心功能

- 🎭 **剧本管理** - 剧本浏览、搜索、详情、评价系统
- 🏠 **房间系统** - 创建房间、加入房间、角色分配
- 👥 **社区功能** - 用户动态、关注、评论、点赞
- 🔐 **用户认证** - 手机验证码登录、JWT认证、权限控制
- ⚙️ **用户设置** - 个人信息管理、隐私设置、主题切换

### 1.3 项目状态

| 模块 | 状态 | 完成度 | 说明 |
|------|------|--------|------|
| 🔐 用户认证 | ✅ 完成 | 100% | 手机验证码登录、JWT认证、权限控制 |
| 👤 用户管理 | ✅ 完成 | 100% | 个人信息、头像上传、设置管理 |
| 🎭 剧本管理 | 🚧 开发中 | 60% | 基础CRUD、搜索功能已完成 |
| 🏠 房间系统 | 📋 规划中 | 0% | 待开发 |
| 👥 社交功能 | 📋 规划中 | 0% | 待开发 |

---

## 2. 架构设计

### 2.1 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Vue3)    │    │   后端 (Spring)  │    │   数据层         │
│                 │    │                 │    │                 │
│ ├─ 用户界面      │◄──►│ ├─ Controller   │◄──►│ ├─ MySQL 8.0    │
│ ├─ 状态管理      │    │ ├─ Service      │    │ ├─ Redis 6.0    │
│ ├─ 路由管理      │    │ ├─ Repository   │    │ └─ Elasticsearch│
│ └─ 组件库       │    │ └─ Security     │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 技术栈

#### 后端技术栈
- **框架**: Spring Boot 2.7.18 (Java 17)
- **数据库**: MySQL 8.0 + MyBatis-Plus 3.5.3
- **缓存**: Redis 6.0 + Redisson 3.23.4
- **搜索**: Elasticsearch 7.17.9 + IK分词器
- **消息队列**: RabbitMQ 3.12
- **认证**: JWT + 自定义拦截器
- **文档**: Knife4j 4.4.0
- **文件存储**: 本地存储 + 阿里云OSS

#### 前端技术栈
- **框架**: Vue 3.4 + TypeScript
- **构建**: Vite
- **UI库**: Element Plus 2.4.4
- **状态管理**: Pinia 2.1.7
- **路由**: Vue Router 4.2.5
- **HTTP**: Axios 1.6.2

### 2.3 数据库设计

#### 核心表结构
```sql
-- 用户相关表
tb_user              # 用户基本信息
tb_user_info         # 用户详细信息
tb_user_settings     # 用户设置
tb_login_history     # 登录历史

-- 剧本相关表
tb_script           # 剧本基本信息
tb_script_character # 剧本角色
tb_script_review    # 剧本评价
tb_script_rule      # 剧本规则

-- 社交功能表
tb_follow           # 关注关系
tb_user_favorite    # 用户收藏
```

---

## 3. 开发环境搭建

### 3.1 环境要求

- **Java**: JDK 17+
- **Node.js**: 16+ (推荐 18.x)
- **MySQL**: 8.0+
- **Redis**: 6.0+
- **Maven**: 3.6+

### 3.2 快速启动

#### 数据库配置
```bash
# 1. 创建数据库
mysql -u root -p -e "CREATE DATABASE hmdp CHARACTER SET utf8mb4;"

# 2. 导入数据库结构
mysql -u root -p hmdp < sql/hmdp.sql

# 3. 启动Redis
redis-server
```

#### 后端启动
```bash
cd backend
mvn spring-boot:run
```

#### 前端启动
```bash
cd frontend
npm install
npm run dev
```

#### 访问地址
- 🌐 **前端应用**: http://localhost:3000
- 📡 **后端API**: http://localhost:8081
- 📚 **API文档**: http://localhost:8081/doc.html

---

## 4. 模块开发状态

### 4.1 用户认证模块 ✅ (已完成)

#### 功能清单
- [x] 手机验证码发送
- [x] 用户登录/注册
- [x] JWT Token 认证
- [x] 权限拦截保护
- [x] 前端状态管理
- [x] 路由权限控制

#### 核心接口
```
POST /user/code     # 发送验证码
POST /user/login    # 用户登录
POST /user/logout   # 用户登出
GET  /user/me       # 获取用户信息
```

#### 技术实现
- **后端**: Spring Security + JWT + Redis
- **前端**: Pinia + Vue Router + Axios
- **数据库**: tb_user, tb_user_info
- **缓存**: Redis (验证码存储)

### 4.2 用户管理模块 ✅ (已完成)

#### 功能清单
- [x] 个人信息管理
- [x] 头像上传功能
- [x] 密码修改
- [x] 用户设置管理
- [x] 收藏功能
- [x] 登录历史记录

#### 核心接口
```
GET  /user/profile          # 获取用户资料
PUT  /user/profile          # 更新用户资料
POST /user/avatar           # 上传头像
PUT  /user/password         # 修改密码
GET  /user/favorites        # 获取收藏列表
POST /user/favorites        # 添加收藏
GET  /user/settings         # 获取用户设置
PUT  /user/settings         # 更新用户设置
```

### 4.3 剧本管理模块 🚧 (开发中 60%)

#### 已完成功能
- [x] 剧本基础CRUD
- [x] 剧本分类管理
- [x] 基础搜索功能
- [x] 剧本详情页面

#### 待完成功能
- [ ] 高级搜索和筛选
- [ ] 剧本评价系统
- [ ] 剧本推荐算法
- [ ] 剧本上传功能

#### 核心接口
```
GET  /scripts               # 获取剧本列表
POST /scripts               # 创建剧本
GET  /scripts/{id}          # 获取剧本详情
PUT  /scripts/{id}          # 更新剧本
GET  /scripts/search        # 搜索剧本
POST /scripts/{id}/reviews  # 添加评价
```

---

## 5. API 接口规范

### 5.1 统一响应格式

#### 成功响应
```json
{
  "success": true,
  "errorCode": null,
  "errorMsg": null,
  "data": {
    // 业务数据
  }
}
```

#### 错误响应
```json
{
  "success": false,
  "errorCode": "USER_NOT_FOUND",
  "errorMsg": "用户不存在",
  "data": null
}
```

### 5.2 分页响应格式

```json
{
  "success": true,
  "data": {
    "list": [...],
    "total": 100,
    "pages": 10,
    "current": 1,
    "size": 10
  }
}
```

### 5.3 认证机制

#### JWT Token 格式
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 权限控制
- **公开接口**: 无需认证
- **用户接口**: 需要有效JWT Token
- **管理接口**: 需要管理员权限

---

## 6. 技术栈详解

### 6.1 后端架构分层

```
Controller 层    # 接收请求，参数校验，调用Service
    ↓
Service 层       # 业务逻辑处理，事务管理
    ↓
Mapper 层        # 数据访问，SQL操作
    ↓
Database         # MySQL + Redis + Elasticsearch
```

### 6.2 前端架构模式

```
Vue3 Composition API + TypeScript
├── Views (页面组件)
├── Components (可复用组件)
├── Composables (组合式函数)
├── Stores (Pinia状态管理)
├── API (接口调用)
└── Utils (工具函数)
```

### 6.3 缓存策略

#### 多级缓存架构
```
浏览器缓存 → CDN缓存 → Redis缓存 → 数据库
```

#### 缓存使用场景
- **用户Session**: Redis, 30分钟过期
- **验证码**: Redis, 5分钟过期
- **热点数据**: Redis, 1小时过期
- **搜索结果**: Redis, 10分钟过期

---

## 7. 部署指南

### 7.1 环境配置

#### 开发环境 (application-local.yaml)
```yaml
server:
  port: 8081

spring:
  datasource:
    url: ********************************
    username: root
    password: 123456
  
  redis:
    host: localhost
    port: 6379
    password: 123321
```

#### 生产环境配置
- 使用环境变量管理敏感信息
- 数据库连接池优化
- Redis集群配置
- 日志级别调整

### 7.2 部署方式

#### Docker部署
```bash
# 构建镜像
docker build -t script-murder-backend ./backend
docker build -t script-murder-frontend ./frontend

# 启动服务
docker-compose up -d
```

#### 传统部署
```bash
# 后端打包
cd backend && mvn clean package

# 前端构建
cd frontend && npm run build

# 服务器部署
java -jar backend-1.0.0.jar
```

---

## 8. 开发规范

### 8.1 代码规范

#### Java后端规范
- 遵循阿里巴巴Java开发手册
- 所有类使用 `<AUTHOR> 标注
- 包命名统一使用 `com.scriptmurder.*`
- 数据库表名统一使用 `tb_` 前缀

#### 前端规范
- 使用 TypeScript 确保类型安全
- 组件命名采用 PascalCase
- 文件命名采用 kebab-case
- 遵循 Vue 3 Composition API 最佳实践

### 8.2 Git规范

#### 提交信息格式
```
<type>(<scope>): <description>

[optional body]

[optional footer]
```

#### Type 类型
- **feat**: 新功能
- **fix**: 修复bug
- **docs**: 文档更新
- **style**: 代码格式
- **refactor**: 重构
- **test**: 测试相关

#### 示例
```
feat(auth): add phone verification login

- Implement SMS verification code sending
- Add JWT token generation and validation
- Update login form with phone input

Closes #123
```

### 8.3 测试规范

#### 后端测试
```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=UserServiceTest

# 生成测试报告
mvn surefire-report:report
```

#### 前端测试
```bash
# 单元测试
npm run test

# 端到端测试
npm run test:e2e

# 测试覆盖率
npm run test:coverage
```

---

## 📞 支持与反馈

- **GitHub Issues**: [项目Issues](https://github.com/example/script-murder-platform/issues)
- **开发文档**: 本文档持续更新
- **技术支持**: 查看CLAUDE.md获取AI开发指导

---

**最后更新**: 2025-08-03  
**文档版本**: v1.0.0  
**维护者**: an