# 管理端模块架构设计文档

## 📋 设计概览

**设计日期**: 2025-08-04  
**设计范围**: 剧本杀平台管理端模块  
**架构模式**: 模块化分离 + 权限控制  

## 🎯 设计目标

### 核心目标
1. **功能分离**: 管理端与用户端功能完全分离
2. **权限安全**: 严格的管理员权限控制和验证
3. **模块化**: 独立的管理端模块，便于维护和扩展
4. **统一管理**: 集中的管理员后台，统一的操作界面

### 技术目标
- 保持现有架构的一致性
- 最小化对现有代码的影响
- 支持细粒度的权限控制
- 提供完整的管理功能API

## 🏗️ 当前项目架构分析

### 现有后端结构
```
backend/src/main/java/com/scriptmurder/
├── ScriptMurderApplication.java          # 主启动类
├── config/                               # 配置类
│   ├── MVCConfig.java                   # MVC配置(拦截器)
│   ├── RedisConfig.java                 # Redis配置
│   └── ElasticsearchConfig.java         # ES配置
├── controller/                          # 控制器层
│   ├── AuthController.java              # 用户认证
│   ├── ScriptController.java            # 剧本管理
│   ├── AdminController.java             # 管理员功能(已存在)
│   └── ScriptStatusController.java      # 状态管理
├── service/                             # 服务层
├── mapper/                              # 数据访问层
├── entity/                              # 实体类
├── dto/                                 # 数据传输对象
├── utils/                               # 工具类
│   ├── LoginInterceptor.java            # 登录拦截器
│   ├── RefreshTokenInterceptor.java     # Token刷新
│   └── UserHolder.java                  # 用户上下文
└── enums/                               # 枚举类
```

### 现有权限控制机制
1. **双拦截器架构**:
   - `RefreshTokenInterceptor`: Token刷新和用户信息获取
   - `LoginInterceptor`: 登录状态验证

2. **用户上下文**:
   - `UserHolder.getUser()`: 获取当前用户信息
   - ThreadLocal存储用户信息

3. **权限验证**:
   - 目前只有登录/未登录两种状态
   - 缺少角色和权限的细分

## 🎯 管理端模块设计方案

### 方案选择: **模块化分离架构**

#### 优势分析
- ✅ **代码分离**: 管理端和用户端代码完全分离
- ✅ **权限清晰**: 独立的权限控制体系
- ✅ **维护性好**: 模块独立，便于维护和扩展
- ✅ **安全性高**: 管理端有独立的安全控制

#### 架构设计
```
backend/src/main/java/com/scriptmurder/
├── admin/                               # 管理端模块 (新增)
│   ├── controller/                      # 管理端控制器
│   │   ├── AdminAuthController.java     # 管理员认证
│   │   ├── AdminScriptController.java   # 剧本管理
│   │   ├── AdminUserController.java     # 用户管理
│   │   ├── AdminSystemController.java   # 系统管理
│   │   └── AdminDashboardController.java # 仪表板
│   ├── service/                         # 管理端服务
│   │   ├── IAdminAuthService.java       # 管理员认证服务
│   │   ├── IAdminUserService.java       # 用户管理服务
│   │   ├── IAdminScriptService.java     # 剧本管理服务
│   │   └── IAdminSystemService.java     # 系统管理服务
│   ├── dto/                             # 管理端DTO
│   │   ├── AdminLoginDTO.java           # 管理员登录
│   │   ├── AdminUserDTO.java            # 用户管理
│   │   └── AdminDashboardDTO.java       # 仪表板数据
│   ├── interceptor/                     # 管理端拦截器
│   │   ├── AdminAuthInterceptor.java    # 管理员认证拦截器
│   │   └── AdminPermissionInterceptor.java # 权限验证拦截器
│   └── enums/                           # 管理端枚举
│       ├── AdminRole.java               # 管理员角色
│       └── AdminPermission.java         # 管理员权限
├── entity/                              # 实体类 (扩展)
│   ├── Admin.java                       # 管理员实体 (新增)
│   ├── AdminRole.java                   # 管理员角色 (新增)
│   ├── AdminPermission.java             # 权限实体 (新增)
│   └── User.java                        # 用户实体 (扩展角色字段)
└── config/
    └── AdminMVCConfig.java              # 管理端MVC配置 (新增)
```

## 🔐 权限控制体系设计

### 1. 角色权限模型

#### 管理员角色定义
```java
public enum AdminRole {
    SUPER_ADMIN(1, "超级管理员", "拥有所有权限"),
    CONTENT_ADMIN(2, "内容管理员", "管理剧本和内容"),
    USER_ADMIN(3, "用户管理员", "管理用户和社区"),
    SYSTEM_ADMIN(4, "系统管理员", "系统配置和监控"),
    AUDIT_ADMIN(5, "审核管理员", "内容审核和举报处理");
}
```

#### 权限细分
```java
public enum AdminPermission {
    // 用户管理权限
    USER_VIEW("user:view", "查看用户"),
    USER_EDIT("user:edit", "编辑用户"),
    USER_DELETE("user:delete", "删除用户"),
    USER_BAN("user:ban", "封禁用户"),
    
    // 剧本管理权限
    SCRIPT_VIEW("script:view", "查看剧本"),
    SCRIPT_EDIT("script:edit", "编辑剧本"),
    SCRIPT_DELETE("script:delete", "删除剧本"),
    SCRIPT_AUDIT("script:audit", "审核剧本"),
    
    // 系统管理权限
    SYSTEM_CONFIG("system:config", "系统配置"),
    SYSTEM_MONITOR("system:monitor", "系统监控"),
    SYSTEM_LOG("system:log", "查看日志");
}
```

### 2. 数据库设计

#### 管理员表 (tb_admin)
```sql
CREATE TABLE `tb_admin` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `username` VARCHAR(50) NOT NULL COMMENT '管理员用户名',
  `password` VARCHAR(255) NOT NULL COMMENT '密码(加密)',
  `real_name` VARCHAR(50) COMMENT '真实姓名',
  `email` VARCHAR(100) COMMENT '邮箱',
  `phone` VARCHAR(20) COMMENT '手机号',
  `avatar` VARCHAR(255) COMMENT '头像',
  `status` TINYINT DEFAULT 1 COMMENT '状态(1-正常 0-禁用)',
  `last_login_time` DATETIME COMMENT '最后登录时间',
  `last_login_ip` VARCHAR(50) COMMENT '最后登录IP',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`)
) COMMENT='管理员表';
```

#### 管理员角色表 (tb_admin_role)
```sql
CREATE TABLE `tb_admin_role` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `role_name` VARCHAR(50) NOT NULL COMMENT '角色名称',
  `role_code` VARCHAR(50) NOT NULL COMMENT '角色编码',
  `description` VARCHAR(200) COMMENT '角色描述',
  `status` TINYINT DEFAULT 1 COMMENT '状态',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_code` (`role_code`)
) COMMENT='管理员角色表';
```

#### 管理员角色关联表 (tb_admin_role_rel)
```sql
CREATE TABLE `tb_admin_role_rel` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `admin_id` BIGINT NOT NULL COMMENT '管理员ID',
  `role_id` BIGINT NOT NULL COMMENT '角色ID',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_admin_role` (`admin_id`, `role_id`)
) COMMENT='管理员角色关联表';
```

### 3. 权限验证机制

#### 管理员认证拦截器
```java
@Component
public class AdminAuthInterceptor implements HandlerInterceptor {
    
    @Override
    public boolean preHandle(HttpServletRequest request, 
                           HttpServletResponse response, 
                           Object handler) throws Exception {
        
        // 1. 获取管理员Token
        String adminToken = request.getHeader("Admin-Authorization");
        
        // 2. 验证Token有效性
        if (!isValidAdminToken(adminToken)) {
            response.setStatus(401);
            return false;
        }
        
        // 3. 获取管理员信息并存储到上下文
        AdminUser adminUser = getAdminUserFromToken(adminToken);
        AdminUserHolder.setAdminUser(adminUser);
        
        return true;
    }
}
```

#### 权限验证注解
```java
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequirePermission {
    String[] value() default {};
    AdminRole[] roles() default {};
    boolean requireAll() default false;
}
```

## 📡 API接口设计

### 1. 管理员认证接口
```
POST /api/admin/auth/login          # 管理员登录
POST /api/admin/auth/logout         # 管理员登出
GET  /api/admin/auth/me             # 获取当前管理员信息
POST /api/admin/auth/change-password # 修改密码
```

### 2. 用户管理接口
```
GET    /api/admin/users             # 用户列表
GET    /api/admin/users/{id}        # 用户详情
PUT    /api/admin/users/{id}        # 编辑用户
DELETE /api/admin/users/{id}        # 删除用户
POST   /api/admin/users/{id}/ban    # 封禁用户
POST   /api/admin/users/{id}/unban  # 解封用户
```

### 3. 剧本管理接口
```
GET    /api/admin/scripts           # 剧本列表
GET    /api/admin/scripts/{id}      # 剧本详情
PUT    /api/admin/scripts/{id}      # 编辑剧本
DELETE /api/admin/scripts/{id}      # 删除剧本
POST   /api/admin/scripts/{id}/audit # 审核剧本
GET    /api/admin/scripts/pending   # 待审核剧本
```

### 4. 系统管理接口
```
GET  /api/admin/system/stats        # 系统统计
GET  /api/admin/system/logs         # 系统日志
GET  /api/admin/system/config       # 系统配置
PUT  /api/admin/system/config       # 更新配置
POST /api/admin/system/cache/clear  # 清除缓存
```

### 5. 仪表板接口
```
GET /api/admin/dashboard/overview   # 概览数据
GET /api/admin/dashboard/charts     # 图表数据
GET /api/admin/dashboard/recent     # 最近活动
```

## 🔧 实施计划

### Phase 1: 基础架构搭建 (1-2天)
1. 📋 创建管理端目录结构
   - 创建 `admin/` 主目录
   - 建立 `controller/`, `service/`, `dto/`, `interceptor/` 子目录
   - 配置包扫描和组件注册

2. 📋 设计数据库表结构
   - 创建管理员相关表的SQL脚本
   - 设计角色权限关联表
   - 添加必要的索引和约束

3. 📋 实现基础的管理员实体和枚举
   - 创建 `Admin`, `AdminRole`, `AdminPermission` 实体
   - 实现角色和权限枚举类
   - 配置MyBatis-Plus映射

4. 📋 配置管理端拦截器
   - 实现 `AdminAuthInterceptor`
   - 配置管理端路由拦截规则
   - 集成到现有MVC配置中

### Phase 2: 认证授权系统 (2-3天)
1. 📋 实现管理员登录认证
   - 管理员登录接口
   - Token生成和验证机制
   - 密码加密和验证

2. 📋 实现权限验证机制
   - 基于注解的权限控制
   - 权限验证拦截器
   - 角色权限缓存机制

3. 📋 创建权限注解和拦截器
   - `@RequirePermission` 注解
   - `@RequireRole` 注解
   - AOP权限验证切面

4. 📋 实现管理员上下文管理
   - `AdminUserHolder` 工具类
   - ThreadLocal管理员信息存储
   - 请求结束后的上下文清理

### Phase 3: 核心管理功能 (3-4天)
1. 📋 用户管理功能
   - 用户列表查询和筛选
   - 用户详情查看和编辑
   - 用户封禁和解封功能
   - 用户行为统计分析

2. 📋 剧本管理功能
   - 剧本列表管理和搜索
   - 剧本审核工作流
   - 批量操作功能
   - 剧本统计和分析

3. 📋 系统管理功能
   - 系统配置管理
   - 缓存管理和清理
   - 日志查看和分析
   - 系统监控指标

4. 📋 仪表板统计功能
   - 实时数据概览
   - 图表数据接口
   - 趋势分析功能
   - 告警和通知

### Phase 4: 安全加固和测试 (1-2天)
1. 📋 安全性测试和加固
   - 权限绕过测试
   - SQL注入防护测试
   - XSS攻击防护测试
   - 敏感信息泄露检查

2. 📋 权限控制测试
   - 角色权限矩阵测试
   - 权限继承测试
   - 权限缓存一致性测试
   - 并发权限验证测试

3. 📋 接口文档完善
   - Swagger API文档
   - 权限说明文档
   - 错误码定义文档
   - 使用示例文档

4. 📋 单元测试编写
   - 认证授权测试
   - 权限验证测试
   - 业务逻辑测试
   - 集成测试用例

## 🛡️ 安全考虑

### 1. 认证安全
- 管理员密码强制复杂度要求
- 登录失败次数限制和账户锁定
- 管理员Token独立于用户Token
- Token过期时间较短(2小时)

### 2. 权限安全
- 最小权限原则
- 操作日志记录
- 敏感操作二次验证
- IP白名单限制

### 3. 数据安全
- 敏感数据脱敏显示
- 操作审计日志
- 数据备份和恢复
- SQL注入防护

## 📊 监控和日志

### 1. 操作日志
```java
@Entity
public class AdminOperationLog {
    private Long id;
    private Long adminId;
    private String operation;
    private String module;
    private String details;
    private String ip;
    private LocalDateTime createTime;
}
```

### 2. 监控指标
- 管理员登录频率
- 操作响应时间
- 权限验证成功率
- 系统资源使用情况

## 🎉 总结

本设计方案采用模块化分离架构，在保持现有系统稳定性的基础上，新增独立的管理端模块。通过完善的权限控制体系和安全机制，为平台提供强大的管理功能。

### 核心优势
1. **架构清晰**: 管理端与用户端完全分离
2. **权限完善**: 基于角色的细粒度权限控制
3. **安全可靠**: 多层次的安全防护机制
4. **易于扩展**: 模块化设计便于功能扩展
5. **维护友好**: 独立模块便于维护和升级

### 技术亮点
- 双Token机制(用户Token + 管理员Token)
- 基于注解的权限控制
- 完整的操作审计日志
- 灵活的角色权限配置
- 统一的管理端API规范
