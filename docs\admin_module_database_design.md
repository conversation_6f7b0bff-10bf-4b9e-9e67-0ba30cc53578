# 管理端模块数据库设计

## 📊 数据库设计概览

**设计原则**: RBAC (Role-Based Access Control) 权限模型  
**设计目标**: 灵活的权限控制 + 完整的操作审计  
**兼容性**: 与现有用户系统完全兼容  

## 🗃️ 表结构设计

### 1. 管理员基础表 (tb_admin)

```sql
CREATE TABLE `tb_admin` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `username` VARCHAR(50) NOT NULL COMMENT '管理员用户名',
  `password` VARCHAR(255) NOT NULL COMMENT '密码(BCrypt加密)',
  `real_name` VARCHAR(50) COMMENT '真实姓名',
  `email` VARCHAR(100) COMMENT '邮箱地址',
  `phone` VARCHAR(20) COMMENT '手机号码',
  `avatar` VARCHAR(255) COMMENT '头像URL',
  `status` TINYINT DEFAULT 1 COMMENT '状态(1-正常 0-禁用 2-锁定)',
  `login_failure_count` INT DEFAULT 0 COMMENT '登录失败次数',
  `last_login_time` DATETIME COMMENT '最后登录时间',
  `last_login_ip` VARCHAR(50) COMMENT '最后登录IP',
  `password_update_time` DATETIME COMMENT '密码最后修改时间',
  `creator_id` BIGINT COMMENT '创建者ID',
  `remark` VARCHAR(500) COMMENT '备注信息',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';
```

### 2. 管理员角色表 (tb_admin_role)

```sql
CREATE TABLE `tb_admin_role` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `role_name` VARCHAR(50) NOT NULL COMMENT '角色名称',
  `role_code` VARCHAR(50) NOT NULL COMMENT '角色编码',
  `description` VARCHAR(200) COMMENT '角色描述',
  `level` INT DEFAULT 1 COMMENT '角色级别(数字越大权限越高)',
  `status` TINYINT DEFAULT 1 COMMENT '状态(1-正常 0-禁用)',
  `is_system` TINYINT DEFAULT 0 COMMENT '是否系统角色(1-是 0-否)',
  `creator_id` BIGINT COMMENT '创建者ID',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_code` (`role_code`),
  KEY `idx_status` (`status`),
  KEY `idx_level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员角色表';
```

### 3. 权限表 (tb_admin_permission)

```sql
CREATE TABLE `tb_admin_permission` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `permission_name` VARCHAR(100) NOT NULL COMMENT '权限名称',
  `permission_code` VARCHAR(100) NOT NULL COMMENT '权限编码',
  `permission_type` TINYINT DEFAULT 1 COMMENT '权限类型(1-菜单 2-按钮 3-接口)',
  `parent_id` BIGINT DEFAULT 0 COMMENT '父权限ID',
  `module_name` VARCHAR(50) COMMENT '所属模块',
  `resource_url` VARCHAR(200) COMMENT '资源URL',
  `method` VARCHAR(10) COMMENT 'HTTP方法',
  `icon` VARCHAR(100) COMMENT '图标',
  `sort_order` INT DEFAULT 0 COMMENT '排序',
  `description` VARCHAR(200) COMMENT '权限描述',
  `status` TINYINT DEFAULT 1 COMMENT '状态(1-正常 0-禁用)',
  `is_system` TINYINT DEFAULT 0 COMMENT '是否系统权限',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_permission_code` (`permission_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_module_name` (`module_name`),
  KEY `idx_permission_type` (`permission_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';
```

### 4. 角色权限关联表 (tb_admin_role_permission)

```sql
CREATE TABLE `tb_admin_role_permission` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `role_id` BIGINT NOT NULL COMMENT '角色ID',
  `permission_id` BIGINT NOT NULL COMMENT '权限ID',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_permission_id` (`permission_id`),
  CONSTRAINT `fk_role_permission_role` FOREIGN KEY (`role_id`) REFERENCES `tb_admin_role` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_role_permission_permission` FOREIGN KEY (`permission_id`) REFERENCES `tb_admin_permission` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';
```

### 5. 管理员角色关联表 (tb_admin_user_role)

```sql
CREATE TABLE `tb_admin_user_role` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `admin_id` BIGINT NOT NULL COMMENT '管理员ID',
  `role_id` BIGINT NOT NULL COMMENT '角色ID',
  `creator_id` BIGINT COMMENT '分配者ID',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_admin_role` (`admin_id`, `role_id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_role_id` (`role_id`),
  CONSTRAINT `fk_admin_user_role_admin` FOREIGN KEY (`admin_id`) REFERENCES `tb_admin` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_admin_user_role_role` FOREIGN KEY (`role_id`) REFERENCES `tb_admin_role` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员角色关联表';
```

### 6. 操作日志表 (tb_admin_operation_log)

```sql
CREATE TABLE `tb_admin_operation_log` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `admin_id` BIGINT COMMENT '操作管理员ID',
  `admin_username` VARCHAR(50) COMMENT '操作管理员用户名',
  `operation_type` VARCHAR(50) NOT NULL COMMENT '操作类型',
  `operation_name` VARCHAR(100) NOT NULL COMMENT '操作名称',
  `module_name` VARCHAR(50) COMMENT '操作模块',
  `method` VARCHAR(10) COMMENT 'HTTP方法',
  `request_url` VARCHAR(500) COMMENT '请求URL',
  `request_params` TEXT COMMENT '请求参数',
  `response_data` TEXT COMMENT '响应数据',
  `ip_address` VARCHAR(50) COMMENT 'IP地址',
  `user_agent` VARCHAR(500) COMMENT '用户代理',
  `status` TINYINT DEFAULT 1 COMMENT '操作状态(1-成功 0-失败)',
  `error_message` TEXT COMMENT '错误信息',
  `execution_time` INT COMMENT '执行时间(毫秒)',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_module_name` (`module_name`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员操作日志表';
```

### 7. 管理员登录日志表 (tb_admin_login_log)

```sql
CREATE TABLE `tb_admin_login_log` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `admin_id` BIGINT COMMENT '管理员ID',
  `username` VARCHAR(50) COMMENT '登录用户名',
  `ip_address` VARCHAR(50) COMMENT '登录IP',
  `user_agent` VARCHAR(500) COMMENT '用户代理',
  `login_location` VARCHAR(100) COMMENT '登录地点',
  `browser` VARCHAR(50) COMMENT '浏览器',
  `os` VARCHAR(50) COMMENT '操作系统',
  `status` TINYINT DEFAULT 1 COMMENT '登录状态(1-成功 0-失败)',
  `failure_reason` VARCHAR(200) COMMENT '失败原因',
  `login_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
  PRIMARY KEY (`id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_username` (`username`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_login_time` (`login_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员登录日志表';
```

## 🔧 初始化数据脚本

### 1. 初始化系统角色

```sql
INSERT INTO `tb_admin_role` (`role_name`, `role_code`, `description`, `level`, `is_system`) VALUES
('超级管理员', 'SUPER_ADMIN', '拥有系统所有权限', 99, 1),
('内容管理员', 'CONTENT_ADMIN', '负责剧本内容审核和管理', 80, 1),
('用户管理员', 'USER_ADMIN', '负责用户管理和社区维护', 70, 1),
('系统管理员', 'SYSTEM_ADMIN', '负责系统配置和监控', 60, 1),
('审核管理员', 'AUDIT_ADMIN', '负责内容审核和举报处理', 50, 1);
```

### 2. 初始化系统权限

```sql
-- 用户管理权限
INSERT INTO `tb_admin_permission` (`permission_name`, `permission_code`, `permission_type`, `module_name`, `resource_url`, `method`, `is_system`) VALUES
('用户管理', 'user:manage', 1, 'user', '/api/admin/users', 'GET', 1),
('查看用户', 'user:view', 2, 'user', '/api/admin/users/*', 'GET', 1),
('编辑用户', 'user:edit', 2, 'user', '/api/admin/users/*', 'PUT', 1),
('删除用户', 'user:delete', 2, 'user', '/api/admin/users/*', 'DELETE', 1),
('封禁用户', 'user:ban', 2, 'user', '/api/admin/users/*/ban', 'POST', 1),

-- 剧本管理权限
('剧本管理', 'script:manage', 1, 'script', '/api/admin/scripts', 'GET', 1),
('查看剧本', 'script:view', 2, 'script', '/api/admin/scripts/*', 'GET', 1),
('编辑剧本', 'script:edit', 2, 'script', '/api/admin/scripts/*', 'PUT', 1),
('删除剧本', 'script:delete', 2, 'script', '/api/admin/scripts/*', 'DELETE', 1),
('审核剧本', 'script:audit', 2, 'script', '/api/admin/scripts/*/audit', 'POST', 1),

-- 系统管理权限
('系统管理', 'system:manage', 1, 'system', '/api/admin/system', 'GET', 1),
('系统配置', 'system:config', 2, 'system', '/api/admin/system/config', 'PUT', 1),
('系统监控', 'system:monitor', 2, 'system', '/api/admin/system/monitor', 'GET', 1),
('查看日志', 'system:log', 2, 'system', '/api/admin/system/logs', 'GET', 1);
```

### 3. 初始化角色权限关联

```sql
-- 超级管理员拥有所有权限
INSERT INTO `tb_admin_role_permission` (`role_id`, `permission_id`)
SELECT 1, id FROM `tb_admin_permission` WHERE `is_system` = 1;

-- 内容管理员权限
INSERT INTO `tb_admin_role_permission` (`role_id`, `permission_id`)
SELECT 2, id FROM `tb_admin_permission` WHERE `module_name` IN ('script') AND `is_system` = 1;

-- 用户管理员权限
INSERT INTO `tb_admin_role_permission` (`role_id`, `permission_id`)
SELECT 3, id FROM `tb_admin_permission` WHERE `module_name` IN ('user') AND `is_system` = 1;

-- 系统管理员权限
INSERT INTO `tb_admin_role_permission` (`role_id`, `permission_id`)
SELECT 4, id FROM `tb_admin_permission` WHERE `module_name` IN ('system') AND `is_system` = 1;
```

### 4. 创建默认超级管理员

```sql
-- 创建默认管理员 (密码: admin123)
INSERT INTO `tb_admin` (`username`, `password`, `real_name`, `email`, `status`) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXiGiNqA6sOHhpCKbHHwC8nVJ0y', '系统管理员', '<EMAIL>', 1);

-- 分配超级管理员角色
INSERT INTO `tb_admin_user_role` (`admin_id`, `role_id`) VALUES (1, 1);
```

## 📈 索引优化策略

### 1. 查询优化索引

```sql
-- 管理员表优化索引
ALTER TABLE `tb_admin` ADD INDEX `idx_username_status` (`username`, `status`);
ALTER TABLE `tb_admin` ADD INDEX `idx_email_status` (`email`, `status`);

-- 权限查询优化索引
ALTER TABLE `tb_admin_permission` ADD INDEX `idx_module_type_status` (`module_name`, `permission_type`, `status`);

-- 日志查询优化索引
ALTER TABLE `tb_admin_operation_log` ADD INDEX `idx_admin_time` (`admin_id`, `create_time`);
ALTER TABLE `tb_admin_operation_log` ADD INDEX `idx_module_time` (`module_name`, `create_time`);

-- 登录日志优化索引
ALTER TABLE `tb_admin_login_log` ADD INDEX `idx_admin_time` (`admin_id`, `login_time`);
ALTER TABLE `tb_admin_login_log` ADD INDEX `idx_ip_time` (`ip_address`, `login_time`);
```

### 2. 分区策略 (可选)

```sql
-- 操作日志表按月分区
ALTER TABLE `tb_admin_operation_log` 
PARTITION BY RANGE (TO_DAYS(create_time)) (
    PARTITION p202501 VALUES LESS THAN (TO_DAYS('2025-02-01')),
    PARTITION p202502 VALUES LESS THAN (TO_DAYS('2025-03-01')),
    PARTITION p202503 VALUES LESS THAN (TO_DAYS('2025-04-01')),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

## 🔍 查询优化视图

### 1. 管理员权限视图

```sql
CREATE VIEW `v_admin_permissions` AS
SELECT 
    a.id as admin_id,
    a.username,
    ar.role_code,
    ar.role_name,
    ap.permission_code,
    ap.permission_name,
    ap.module_name,
    ap.resource_url,
    ap.method
FROM `tb_admin` a
LEFT JOIN `tb_admin_user_role` aur ON a.id = aur.admin_id
LEFT JOIN `tb_admin_role` ar ON aur.role_id = ar.id
LEFT JOIN `tb_admin_role_permission` arp ON ar.id = arp.role_id
LEFT JOIN `tb_admin_permission` ap ON arp.permission_id = ap.id
WHERE a.status = 1 AND ar.status = 1 AND ap.status = 1;
```

### 2. 操作统计视图

```sql
CREATE VIEW `v_admin_operation_stats` AS
SELECT 
    DATE(create_time) as operation_date,
    admin_id,
    admin_username,
    module_name,
    operation_type,
    COUNT(*) as operation_count,
    AVG(execution_time) as avg_execution_time,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success_count,
    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as failure_count
FROM `tb_admin_operation_log`
WHERE create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(create_time), admin_id, module_name, operation_type;
```

## 🧹 数据清理策略

### 1. 日志清理存储过程

```sql
DELIMITER //
CREATE PROCEDURE `sp_clean_admin_logs`(IN days_to_keep INT)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 清理操作日志
    DELETE FROM `tb_admin_operation_log` 
    WHERE `create_time` < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    -- 清理登录日志
    DELETE FROM `tb_admin_login_log` 
    WHERE `login_time` < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    COMMIT;
END //
DELIMITER ;
```

### 2. 定时清理事件

```sql
-- 创建定时清理事件 (保留90天日志)
CREATE EVENT IF NOT EXISTS `ev_clean_admin_logs`
ON SCHEDULE EVERY 1 DAY
STARTS '2025-08-05 02:00:00'
DO
  CALL sp_clean_admin_logs(90);

-- 启用事件调度器
SET GLOBAL event_scheduler = ON;
```

## 📊 数据统计查询

### 1. 管理员活跃度统计

```sql
SELECT 
    a.username,
    a.real_name,
    COUNT(DISTINCT DATE(aol.create_time)) as active_days,
    COUNT(aol.id) as total_operations,
    MAX(aol.create_time) as last_operation_time
FROM `tb_admin` a
LEFT JOIN `tb_admin_operation_log` aol ON a.id = aol.admin_id
WHERE aol.create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY a.id
ORDER BY active_days DESC, total_operations DESC;
```

### 2. 权限使用统计

```sql
SELECT 
    ap.module_name,
    ap.permission_name,
    COUNT(aol.id) as usage_count,
    COUNT(DISTINCT aol.admin_id) as user_count
FROM `tb_admin_permission` ap
LEFT JOIN `tb_admin_operation_log` aol ON ap.resource_url = aol.request_url
WHERE aol.create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY ap.id
ORDER BY usage_count DESC;
```

这个数据库设计提供了完整的RBAC权限控制体系，支持灵活的角色权限配置和详细的操作审计，为管理端模块提供了坚实的数据基础。
