# 管理端模块文件结构清单

## 📁 目录结构设计

### 完整的管理端模块结构
```
backend/src/main/java/com/scriptmurder/
├── admin/                                    # 管理端模块根目录
│   ├── controller/                           # 管理端控制器
│   │   ├── AdminAuthController.java          # 管理员认证控制器
│   │   ├── AdminUserController.java          # 用户管理控制器
│   │   ├── AdminScriptController.java        # 剧本管理控制器
│   │   ├── AdminSystemController.java        # 系统管理控制器
│   │   ├── AdminDashboardController.java     # 仪表板控制器
│   │   ├── AdminContentController.java       # 内容管理控制器
│   │   └── AdminReportController.java        # 举报管理控制器
│   │
│   ├── service/                              # 管理端服务层
│   │   ├── IAdminAuthService.java            # 管理员认证服务接口
│   │   ├── IAdminUserService.java            # 用户管理服务接口
│   │   ├── IAdminScriptService.java          # 剧本管理服务接口
│   │   ├── IAdminSystemService.java          # 系统管理服务接口
│   │   ├── IAdminDashboardService.java       # 仪表板服务接口
│   │   ├── IAdminContentService.java         # 内容管理服务接口
│   │   ├── IAdminReportService.java          # 举报管理服务接口
│   │   └── impl/                             # 服务实现类
│   │       ├── AdminAuthServiceImpl.java     # 管理员认证服务实现
│   │       ├── AdminUserServiceImpl.java     # 用户管理服务实现
│   │       ├── AdminScriptServiceImpl.java   # 剧本管理服务实现
│   │       ├── AdminSystemServiceImpl.java   # 系统管理服务实现
│   │       ├── AdminDashboardServiceImpl.java # 仪表板服务实现
│   │       ├── AdminContentServiceImpl.java  # 内容管理服务实现
│   │       └── AdminReportServiceImpl.java   # 举报管理服务实现
│   │
│   ├── dto/                                  # 管理端数据传输对象
│   │   ├── request/                          # 请求DTO
│   │   │   ├── AdminLoginDTO.java            # 管理员登录请求
│   │   │   ├── AdminUserQueryDTO.java        # 用户查询请求
│   │   │   ├── AdminScriptQueryDTO.java      # 剧本查询请求
│   │   │   ├── AdminUserEditDTO.java         # 用户编辑请求
│   │   │   ├── AdminScriptEditDTO.java       # 剧本编辑请求
│   │   │   └── AdminSystemConfigDTO.java     # 系统配置请求
│   │   └── response/                         # 响应DTO
│   │       ├── AdminUserDTO.java             # 管理员用户信息
│   │       ├── AdminDashboardDTO.java        # 仪表板数据
│   │       ├── AdminUserListDTO.java         # 用户列表数据
│   │       ├── AdminScriptListDTO.java       # 剧本列表数据
│   │       ├── AdminSystemStatsDTO.java      # 系统统计数据
│   │       └── AdminOperationLogDTO.java     # 操作日志数据
│   │
│   ├── interceptor/                          # 管理端拦截器
│   │   ├── AdminAuthInterceptor.java         # 管理员认证拦截器
│   │   ├── AdminPermissionInterceptor.java   # 权限验证拦截器
│   │   └── AdminOperationLogInterceptor.java # 操作日志拦截器
│   │
│   ├── annotation/                           # 管理端注解
│   │   ├── RequirePermission.java            # 权限要求注解
│   │   ├── RequireRole.java                  # 角色要求注解
│   │   ├── OperationLog.java                 # 操作日志注解
│   │   └── AdminApi.java                     # 管理端API标识注解
│   │
│   ├── aspect/                               # 管理端切面
│   │   ├── AdminPermissionAspect.java        # 权限验证切面
│   │   └── AdminOperationLogAspect.java      # 操作日志切面
│   │
│   ├── enums/                                # 管理端枚举
│   │   ├── AdminRole.java                    # 管理员角色枚举
│   │   ├── AdminPermission.java              # 管理员权限枚举
│   │   ├── AdminOperationType.java           # 操作类型枚举
│   │   └── AdminModuleType.java              # 模块类型枚举
│   │
│   ├── utils/                                # 管理端工具类
│   │   ├── AdminUserHolder.java              # 管理员用户上下文
│   │   ├── AdminTokenUtil.java               # 管理员Token工具
│   │   ├── AdminPermissionUtil.java          # 权限验证工具
│   │   └── AdminLogUtil.java                 # 日志记录工具
│   │
│   └── exception/                            # 管理端异常
│       ├── AdminAuthException.java           # 管理员认证异常
│       ├── AdminPermissionException.java     # 权限异常
│       └── AdminOperationException.java      # 操作异常
│
├── entity/                                   # 实体类 (扩展)
│   ├── Admin.java                            # 管理员实体 (新增)
│   ├── AdminRole.java                        # 管理员角色实体 (新增)
│   ├── AdminPermission.java                  # 权限实体 (新增)
│   ├── AdminRolePermission.java              # 角色权限关联 (新增)
│   ├── AdminUserRole.java                    # 用户角色关联 (新增)
│   ├── AdminOperationLog.java                # 操作日志实体 (新增)
│   └── User.java                             # 用户实体 (扩展)
│
├── mapper/                                   # 数据访问层 (扩展)
│   ├── AdminMapper.java                      # 管理员Mapper (新增)
│   ├── AdminRoleMapper.java                  # 角色Mapper (新增)
│   ├── AdminPermissionMapper.java            # 权限Mapper (新增)
│   ├── AdminOperationLogMapper.java          # 操作日志Mapper (新增)
│   └── UserMapper.java                       # 用户Mapper (扩展)
│
├── config/                                   # 配置类 (扩展)
│   ├── AdminMVCConfig.java                   # 管理端MVC配置 (新增)
│   ├── AdminSecurityConfig.java              # 管理端安全配置 (新增)
│   └── MVCConfig.java                        # 原有MVC配置 (扩展)
│
└── constants/                                # 常量类 (扩展)
    ├── AdminConstants.java                   # 管理端常量 (新增)
    └── RedisConstants.java                   # Redis常量 (扩展)
```

### 数据库脚本文件
```
backend/src/main/resources/
├── db/
│   └── migration/
│       ├── V1.5__Add_Admin_Module.sql        # 管理端模块数据库脚本
│       ├── V1.6__Add_Admin_Permissions.sql   # 权限数据初始化脚本
│       └── V1.7__Add_Admin_Indexes.sql       # 管理端索引优化脚本
│
└── mapper/                                   # MyBatis映射文件
    ├── AdminMapper.xml                       # 管理员Mapper映射
    ├── AdminRoleMapper.xml                   # 角色Mapper映射
    ├── AdminPermissionMapper.xml             # 权限Mapper映射
    └── AdminOperationLogMapper.xml           # 操作日志Mapper映射
```

## 📋 核心文件功能说明

### 1. 控制器层 (Controller)

#### AdminAuthController.java
```java
@RestController
@RequestMapping("/api/admin/auth")
@AdminApi
public class AdminAuthController {
    // 管理员登录、登出、密码修改等认证相关功能
}
```

#### AdminUserController.java
```java
@RestController
@RequestMapping("/api/admin/users")
@AdminApi
@RequireRole(AdminRole.USER_ADMIN)
public class AdminUserController {
    // 用户管理：查询、编辑、封禁、统计等功能
}
```

#### AdminScriptController.java
```java
@RestController
@RequestMapping("/api/admin/scripts")
@AdminApi
@RequireRole(AdminRole.CONTENT_ADMIN)
public class AdminScriptController {
    // 剧本管理：审核、编辑、删除、统计等功能
}
```

### 2. 服务层 (Service)

#### IAdminAuthService.java
```java
public interface IAdminAuthService {
    AdminLoginResult login(AdminLoginDTO loginDTO);
    boolean logout(String token);
    AdminUserDTO getCurrentAdmin();
    boolean changePassword(Long adminId, String oldPassword, String newPassword);
}
```

#### IAdminUserService.java
```java
public interface IAdminUserService {
    PageResponse<AdminUserListDTO> getUserList(AdminUserQueryDTO queryDTO);
    AdminUserDTO getUserDetail(Long userId);
    boolean updateUser(Long userId, AdminUserEditDTO editDTO);
    boolean banUser(Long userId, String reason);
    Map<String, Object> getUserStats();
}
```

### 3. 权限控制

#### RequirePermission.java
```java
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequirePermission {
    AdminPermission[] value() default {};
    boolean requireAll() default false;
}
```

#### AdminPermissionAspect.java
```java
@Aspect
@Component
public class AdminPermissionAspect {
    @Around("@annotation(requirePermission)")
    public Object checkPermission(ProceedingJoinPoint joinPoint, 
                                RequirePermission requirePermission) throws Throwable {
        // 权限验证逻辑
    }
}
```

### 4. 数据传输对象 (DTO)

#### AdminLoginDTO.java
```java
@Data
public class AdminLoginDTO {
    @NotBlank(message = "用户名不能为空")
    private String username;
    
    @NotBlank(message = "密码不能为空")
    private String password;
    
    private String captcha;
    private String captchaKey;
}
```

#### AdminDashboardDTO.java
```java
@Data
@Builder
public class AdminDashboardDTO {
    private Long totalUsers;
    private Long totalScripts;
    private Long todayActiveUsers;
    private Long pendingAudits;
    private List<ChartDataDTO> userGrowthChart;
    private List<ChartDataDTO> scriptStatsChart;
}
```

## 🔧 配置文件修改

### AdminMVCConfig.java
```java
@Configuration
public class AdminMVCConfig implements WebMvcConfigurer {
    
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 管理员认证拦截器
        registry.addInterceptor(new AdminAuthInterceptor())
                .addPathPatterns("/api/admin/**")
                .excludePathPatterns("/api/admin/auth/login")
                .order(10);
                
        // 管理员权限拦截器
        registry.addInterceptor(new AdminPermissionInterceptor())
                .addPathPatterns("/api/admin/**")
                .excludePathPatterns("/api/admin/auth/**")
                .order(11);
    }
}
```

### 扩展现有MVCConfig.java
```java
@Configuration
public class MVCConfig implements WebMvcConfigurer {
    
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 原有用户端拦截器配置...
        
        // 排除管理端路径，避免冲突
        registry.addInterceptor(new LoginInterceptor())
                .excludePathPatterns(
                    // 原有排除路径...
                    "/api/admin/**"  // 新增：排除管理端路径
                ).order(1);
    }
}
```

## 📊 数据库表设计

### 核心表结构
1. **tb_admin** - 管理员基本信息
2. **tb_admin_role** - 管理员角色定义
3. **tb_admin_permission** - 权限定义
4. **tb_admin_role_permission** - 角色权限关联
5. **tb_admin_user_role** - 用户角色关联
6. **tb_admin_operation_log** - 操作日志记录

### 索引设计
- 管理员用户名唯一索引
- 角色编码唯一索引
- 权限编码唯一索引
- 操作日志时间索引
- 用户角色关联复合索引

## 🎯 实施优先级

### 高优先级 (必须实现)
1. 管理员认证系统
2. 基础权限控制
3. 用户管理功能
4. 剧本审核功能

### 中优先级 (重要功能)
1. 系统监控和统计
2. 操作日志记录
3. 仪表板数据展示
4. 批量操作功能

### 低优先级 (增强功能)
1. 高级权限配置
2. 自定义角色创建
3. 数据导出功能
4. 系统配置管理

## 📝 开发注意事项

### 1. 命名规范
- 所有管理端类以 `Admin` 前缀开头
- 包名使用 `admin` 子包
- API路径以 `/api/admin/` 开头

### 2. 权限设计
- 采用RBAC模型 (Role-Based Access Control)
- 支持权限继承和组合
- 提供细粒度的操作权限控制

### 3. 安全考虑
- 管理员Token与用户Token完全分离
- 敏感操作需要二次验证
- 完整的操作审计日志

### 4. 性能优化
- 权限信息缓存
- 分页查询优化
- 数据库索引优化
- 接口响应时间监控

这个文件结构设计确保了管理端模块的完整性和可维护性，为后续的开发实施提供了清晰的指导。
