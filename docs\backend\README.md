# 后端开发文档

## 项目概述

剧本杀平台后端基于 Spring Boot 2.7.x 构建，采用分层架构设计，提供完整的 RESTful API 服务。

## 技术栈

### 核心框架
- **Spring Boot 2.7.18**: 主框架
- **Spring Security**: 安全认证
- **Spring Web**: Web服务
- **Spring Data**: 数据访问

### 数据存储
- **MySQL 8.0+**: 主数据库
- **Redis 6.0+**: 缓存和会话存储
- **Elasticsearch 8.x+**: 搜索引擎
- **MyBatis Plus**: ORM框架

### 工具库
- **Hutool**: Java工具库
- **Lombok**: 代码简化
- **Knife4j**: API文档
- **Jackson**: JSON处理

## 项目结构

```
backend/
├── src/main/java/com/scriptmurder/
│   ├── ScriptMurderApplication.java    # 主启动类
│   ├── config/                         # 配置类
│   │   ├── CorsConfig.java            # 跨域配置
│   │   ├── ElasticsearchConfig.java   # Elasticsearch配置
│   │   ├── ElasticsearchHealthIndicator.java # ES健康检查
│   │   ├── Knife4jConfiguration.java  # API文档配置
│   │   ├── RedisConfig.java           # Redis配置
│   │   └── SecurityConfig.java        # 安全配置
│   ├── controller/                     # 控制器层
│   │   ├── UserController.java        # 用户控制器
│   │   ├── ScriptController.java      # 剧本控制器
│   │   └── LobbyController.java       # 房间控制器
│   ├── service/                        # 服务层
│   │   ├── UserService.java           # 用户服务接口
│   │   ├── impl/                      # 服务实现
│   │   │   ├── UserServiceImpl.java   # 用户服务实现
│   │   │   └── ...
│   ├── mapper/                         # 数据访问层
│   │   ├── UserMapper.java            # 用户数据访问
│   │   └── ...
│   ├── search/                         # 搜索模块
│   │   ├── document/                   # Elasticsearch文档
│   │   │   └── ScriptSearchDocument.java # 剧本搜索文档
│   │   └── repository/                 # 搜索仓库
│   │       └── ScriptSearchRepository.java # 剧本搜索仓库
│   ├── entity/                         # 实体类
│   │   ├── User.java                  # 用户实体
│   │   ├── Script.java                # 剧本实体
│   │   └── ...
│   ├── dto/                           # 数据传输对象
│   │   ├── ApiResponse.java           # 统一响应格式
│   │   ├── UserLoginDTO.java          # 用户登录DTO
│   │   └── ...
│   ├── enums/                         # 枚举类
│   │   ├── ResponseCode.java          # 响应码枚举
│   │   └── ...
│   ├── exception/                     # 异常处理
│   │   ├── BusinessException.java     # 业务异常
│   │   └── GlobalExceptionHandler.java # 全局异常处理
│   └── utils/                         # 工具类
│       ├── JwtUtils.java              # JWT工具
│       ├── RedisUtils.java            # Redis工具
│       └── ...
├── src/main/resources/
│   ├── application.yaml               # 主配置文件
│   ├── application-local.yaml         # 本地配置
│   ├── elasticsearch/                 # Elasticsearch配置
│   │   ├── script-settings.json       # 索引设置
│   │   └── script-mapping.json        # 字段映射
│   ├── mapper/                        # MyBatis映射文件
│   └── db/                           # 数据库脚本
└── pom.xml                           # Maven配置
```

## 核心模块

### 1. 用户认证模块 ✅ **已完成**

#### 功能特性
- 手机验证码登录/注册
- JWT Token 认证
- 用户信息管理
- 安全登出

#### 主要接口
```
POST /user/code          # 发送验证码
POST /user/login         # 用户登录
GET  /user/me           # 获取用户信息
POST /user/logout       # 用户登出
```

#### 实现细节
- 验证码存储在Redis中，有效期5分钟
- JWT Token包含用户ID和过期时间
- 使用Spring Security进行权限控制
- 支持Token自动刷新机制

### 2. 剧本管理模块 🚧 **开发中**

#### 功能特性
- 剧本CRUD操作
- 剧本分类管理
- 剧本搜索功能（基于Elasticsearch）
- 剧本评价系统

#### 主要接口
```
GET    /scripts         # 获取剧本列表
GET    /scripts/{id}    # 获取剧本详情
POST   /scripts        # 创建剧本（管理员）
PUT    /scripts/{id}   # 更新剧本（管理员）
DELETE /scripts/{id}   # 删除剧本（管理员）
GET    /scripts/search # 搜索剧本
```

#### 搜索功能
- 基于Elasticsearch实现全文搜索
- 支持中文分词（IK Analyzer）
- 支持多字段搜索（标题、描述、标签）
- 支持过滤和排序功能

### 3. 房间管理模块 📋 **规划中**

#### 计划功能
- 房间创建与管理
- 玩家加入/退出
- 角色分配
- 游戏状态管理

## 数据库设计

### 核心表结构

#### 用户表 (tb_user)
```sql
CREATE TABLE tb_user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    phone VARCHAR(11) UNIQUE NOT NULL COMMENT '手机号',
    nick_name VARCHAR(32) DEFAULT '' COMMENT '昵称',
    icon VARCHAR(255) DEFAULT '' COMMENT '头像',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 剧本表 (tb_script)
```sql
CREATE TABLE tb_script (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(128) NOT NULL COMMENT '剧本标题',
    description TEXT COMMENT '剧本描述',
    player_count INT NOT NULL COMMENT '玩家人数',
    duration INT COMMENT '游戏时长(分钟)',
    difficulty TINYINT DEFAULT 1 COMMENT '难度等级',
    category_id BIGINT COMMENT '分类ID',
    status TINYINT DEFAULT 1 COMMENT '状态',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## API 规范

### 统一响应格式
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {},
    "error": false,
    "success": true
}
```

### 响应码规范
- `200`: 操作成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

### 认证机制
- 使用 JWT Token 进行认证
- Token 放在请求头 `Authorization: Bearer <token>`
- Token 有效期为7天，支持自动刷新

## 开发规范

### 代码规范
1. **命名规范**: 使用驼峰命名法
2. **注释规范**: 类和方法必须有注释
3. **异常处理**: 统一使用全局异常处理
4. **日志规范**: 使用SLF4J进行日志记录

### 开发流程
1. **需求分析**: 明确功能需求和接口设计
2. **数据库设计**: 设计表结构和索引
3. **接口开发**: 按照RESTful规范开发API
4. **单元测试**: 编写测试用例
5. **文档更新**: 更新API文档

## 部署配置

### 环境配置
- **开发环境**: application-dev.yaml
- **测试环境**: application-test.yaml
- **生产环境**: application-prod.yaml

### 数据库配置
```yaml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************
    username: root
    password: your_password
```

### Redis配置
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    password: your_password
    database: 0
```

### Elasticsearch 8.x 配置

#### 基本配置
```yaml
spring:
  elasticsearch:
    uris: http://localhost:9200
    username: 
    password: 
    socket-timeout: 30s
    connection-timeout: 5s
    # ES 8.x 新增配置
    path-prefix: 
    headers:
      Accept: application/vnd.elasticsearch+json;compatible-with=8
      Content-Type: application/vnd.elasticsearch+json;compatible-with=8

script-murder:
  elasticsearch:
    index-prefix: script_murder
    batch-size: 1000
    # ES 8.x 新增配置选项
    request-timeout: 10s
    max-connections: 100
    max-connections-per-route: 10
```

#### 配置说明

**连接配置**:
- `uris`: Elasticsearch服务器地址，支持多节点配置
- `username`/`password`: 认证信息（ES 8.x推荐使用API Key或Token认证）
- `socket-timeout`: Socket超时时间
- `connection-timeout`: 连接超时时间
- `path-prefix`: URL路径前缀（可选）
- `headers`: 请求头配置，ES 8.x支持兼容性模式

**业务配置**:
- `index-prefix`: 索引前缀，用于区分不同环境
- `batch-size`: 批量操作大小
- `request-timeout`: 请求超时时间
- `max-connections`: 最大连接数
- `max-connections-per-route`: 每个路由的最大连接数

**重要变化（ES 7.x → 8.x）**:
- **客户端变更**: 使用新的`ElasticsearchClient`替代已弃用的`RestHighLevelClient`
- **API变更**: 采用流式API，提供更好的类型安全
- **认证增强**: 推荐使用API Key或Service Token
- **兼容性**: 支持向后兼容模式

#### 索引设置 (script-settings.json)
```json
{
  "analysis": {
    "analyzer": {
      "ik_max_word": {
        "type": "ik_max_word"
      },
      "ik_smart": {
        "type": "ik_smart"
      }
    }
  },
  "number_of_shards": 1,
  "number_of_replicas": 0
}
```

#### 字段映射 (script-mapping.json)
```json
{
  "properties": {
    "title": {
      "type": "text",
      "analyzer": "ik_max_word",
      "search_analyzer": "ik_smart"
    },
    "description": {
      "type": "text",
      "analyzer": "ik_max_word"
    },
    "tags": {
      "type": "text",
      "analyzer": "ik_max_word",
      "fields": {
        "keyword": {
          "type": "keyword"
        }
      }
    }
  }
}
```

#### 安装和配置步骤

1. **安装Elasticsearch 8.x**
   ```bash
   # 下载并启动Elasticsearch 8.x
   wget https://artifacts.elastic.co/downloads/elasticsearch/elasticsearch-8.11.0-linux-x86_64.tar.gz
   tar -xzf elasticsearch-8.11.0-linux-x86_64.tar.gz
   cd elasticsearch-8.11.0/
   
   # ES 8.x 默认启用安全特性，首次启动会生成密码
   ./bin/elasticsearch
   ```

2. **配置安全设置（ES 8.x新特性）**
   ```bash
   # 重置elastic用户密码
   ./bin/elasticsearch-reset-password -u elastic
   
   # 生成API Key（推荐用于应用连接）
   curl -X POST "localhost:9200/_security/api_key" \
   -u elastic:your_password \
   -H "Content-Type: application/json" \
   -d '{
     "name": "script-murder-api-key",
     "expiration": "365d"
   }'
   
   # 或者禁用安全特性（仅开发环境）
   # 在 config/elasticsearch.yml 中添加：
   # xpack.security.enabled: false
   ```

3. **安装IK中文分词插件**
   ```bash
   # 在Elasticsearch目录下执行
   ./bin/elasticsearch-plugin install https://github.com/medcl/elasticsearch-analysis-ik/releases/download/v8.11.0/elasticsearch-analysis-ik-8.11.0.zip
   ```

4. **验证安装**
   ```bash
   # 检查Elasticsearch状态
   curl -X GET "localhost:9200/_cluster/health?pretty" -u elastic:your_password
   
   # 检查IK插件
   curl -X GET "localhost:9200/_cat/plugins" -u elastic:your_password
   ```

5. **创建索引**
   应用启动时会自动创建索引，也可手动创建：
   ```bash
   curl -X PUT "localhost:9200/script_murder_scripts" \
   -u elastic:your_password \
   -H 'Content-Type: application/json' \
   -d @backend/src/main/resources/elasticsearch/script-settings.json
   ```

#### 健康检查
- 访问 `http://localhost:8081/actuator/health` 查看ES连接状态
- ES 8.x健康检查提供更详细的集群信息：集群名称、节点数、数据节点数
- 日志级别设置为debug可查看详细连接信息

#### ES 8.x 升级注意事项

**代码层面**:
- 替换`RestHighLevelClient`为`ElasticsearchClient`
- 更新Maven依赖到`elasticsearch-java:8.11.0`
- 修改健康检查逻辑以适配新API

**配置层面**:
- 更新连接配置，支持ES 8.x新特性
- 考虑启用安全认证（生产环境强烈推荐）
- 配置兼容性头信息

**部署层面**:
- ES 8.x默认启用安全特性，需要配置认证
- IK分词插件需要使用对应ES版本
- 索引模板可能需要调整以适配新版本

#### ES数据导入和同步策略

**数据导入方式**:
1. **全量重建**: 适用于初始化和数据修复
   ```bash
   POST /admin/es/rebuild-index
   ```

2. **增量同步**: 定时同步最近更新的数据
   ```bash
   POST /admin/es/incremental-sync
   ```

3. **实时同步**: 基于MQ的异步同步，在数据CRUD时自动触发

**同步策略配置**:
- **实时同步**: 数据变更时立即发送MQ消息
- **定时同步**: 每5分钟执行增量同步
- **失败重试**: 每30分钟重试失败的同步任务
- **一致性检查**: 每日凌晨2点检查数据一致性

**批量操作优化**:
```yaml
script-murder:
  elasticsearch:
    batch-size: 1000          # 批量操作大小
    max-connections: 100      # 最大连接数
    request-timeout: 10s      # 请求超时时间
```

**搜索功能优化**:
- **智能搜索**: 支持模糊匹配、同义词扩展
- **多字段搜索**: 标题权重3、描述权重2、标签权重1
- **搜索建议**: 基于前缀匹配的实时建议
- **结果缓存**: Redis缓存热门搜索结果
- **聚合分析**: 提供分类、难度、价格范围统计

**性能监控**:
- 同步状态监控: `GET /admin/es/sync-status`
- 索引统计信息: `GET /admin/es/index-stats`
- 数据一致性检查: `POST /admin/es/data-consistency-check`

#### 常见问题
- **连接失败**: 检查Elasticsearch是否启动，端口是否正确，ES 8.x是否启用了安全认证
- **认证失败**: ES 8.x默认启用安全，需要配置用户名密码或API Key
- **中文搜索异常**: 确认IK分词插件版本与ES版本匹配
- **索引创建失败**: 检查映射文件格式和字段定义，ES 8.x对映射更加严格
- **性能问题**: 调整连接池配置，ES 8.x提供更好的连接管理
- **同步延迟**: 检查MQ队列状态，调整批量大小和并发数
- **数据不一致**: 执行数据一致性检查，必要时全量重建索引

## 常见问题

### Q: 如何添加新的API接口？
A: 
1. 在对应的Controller中添加方法
2. 使用@ApiOperation注解添加接口描述
3. 更新API文档

### Q: 如何处理跨域问题？
A: 项目已配置CORS，支持前端跨域访问

### Q: 如何进行数据库迁移？
A: 使用Flyway或Liquibase进行版本化数据库迁移

## 相关链接

- [API文档](../04_api_reference/rest_api.md)
- [数据库设计](./database_design.md)
- [部署指南](./deployment_guide.md)
