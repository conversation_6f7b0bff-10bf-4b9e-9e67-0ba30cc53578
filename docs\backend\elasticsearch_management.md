# Elasticsearch 8.x 管理指南

## 概述

本文档提供剧本杀平台 Elasticsearch 8.x 的完整管理指南，包括数据导入、同步策略、性能优化和运维操作。

## 目录

1. [架构概览](#架构概览)
2. [数据导入策略](#数据导入策略)
3. [同步机制](#同步机制)
4. [搜索优化](#搜索优化)
5. [管理接口](#管理接口)
6. [监控和运维](#监控和运维)
7. [故障排查](#故障排查)
8. [性能调优](#性能调优)

## 架构概览

### 核心组件

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MySQL 数据库   │    │   RabbitMQ      │    │ Elasticsearch   │
│   (主数据源)     │    │   (消息队列)     │    │   (搜索引擎)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                │
                     ┌─────────────────┐
                     │  Spring Boot    │
                     │   应用服务      │
                     └─────────────────┘
```

### 数据流转

1. **写入流程**: MySQL → RabbitMQ → Elasticsearch
2. **搜索流程**: 前端 → Spring Boot → Elasticsearch
3. **降级流程**: Elasticsearch故障 → MySQL直接查询

## 数据导入策略

### 1. 全量重建索引

**适用场景**:
- 初始化ES索引
- 数据结构变更
- 数据一致性修复

**执行方式**:
```bash
# API调用
POST /admin/es/rebuild-index

# 或者通过管理接口
curl -X POST "http://localhost:8081/admin/es/rebuild-index" \
  -H "Authorization: Bearer your-admin-token"
```

**实现机制**:
```java
public void rebuildAllIndex() {
    // 1. 获取总数据量
    Long totalCount = scriptMapper.selectCount(null);
    
    // 2. 分页批量处理 (默认1000条/批)
    int pageSize = batchSize;
    int totalPages = (int) Math.ceil((double) totalCount / pageSize);
    
    // 3. 并发处理多个批次
    List<CompletableFuture<Void>> futures = new ArrayList<>();
    for (int page = 1; page <= totalPages; page++) {
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            processBatch(currentPage, pageSize);
        }, bulkExecutor);
        futures.add(future);
    }
    
    // 4. 等待所有批次完成
    CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
}
```

### 2. 增量同步

**适用场景**:
- 定期数据同步
- 数据补偿机制
- 小量数据更新

**执行方式**:
```bash
# 手动触发
POST /admin/es/incremental-sync

# 自动执行 (每5分钟)
@Scheduled(cron = "0 */5 * * * ?")
```

**同步逻辑**:
```java
public void incrementalSync() {
    // 获取最近1小时更新的数据
    List<Script> recentUpdated = scriptMapper.selectRecentUpdated(1);
    
    if (!recentUpdated.isEmpty()) {
        bulkIndex(recentUpdated);
    }
}
```

### 3. 实时同步

**触发时机**:
- 剧本创建时
- 剧本更新时
- 剧本删除时
- 评分统计变更时

**实现方式**:
```java
@Service
public class ScriptServiceImpl {
    
    @CacheEvict(value = "script:detail", key = "#script.id")
    public void saveScript(Script script) {
        // 1. 保存到数据库
        scriptMapper.insert(script);
        
        // 2. 发送同步消息
        ScriptSyncMessage message = ScriptSyncMessage.builder()
            .scriptId(script.getId())
            .action("create")
            .timestamp(LocalDateTime.now())
            .build();
        messageProducer.sendScriptSyncMessage(message);
    }
}
```

## 同步机制

### 消息队列配置

```yaml
spring:
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    virtual-host: /
    publisher-confirm-type: correlated
    publisher-returns: true

script-murder:
  rabbitmq:
    exchange: script.exchange
    queue:
      sync: script.sync.queue
      stats: script.stats.queue
```

### 同步消息处理

```java
@RabbitListener(queues = RabbitMQConfig.SCRIPT_SYNC_QUEUE)
public void handleScriptSync(ScriptSyncMessage message) {
    try {
        switch (message.getAction()) {
            case "create":
            case "update":
                Script script = scriptMapper.selectById(message.getScriptId());
                if (script != null) {
                    scriptSearchService.syncScriptToEs(script);
                }
                break;
                
            case "delete":
                scriptSearchService.deleteScriptFromEs(message.getScriptId());
                break;
        }
        
        // 从待同步队列移除
        stringRedisTemplate.opsForSet().remove(SYNC_PENDING_KEY, 
            message.getScriptId().toString());
            
    } catch (Exception e) {
        log.error("同步消息处理失败", e);
        // 添加到失败队列
        addToFailedQueue(message.getScriptId(), message.getAction());
    }
}
```

### 失败重试机制

```java
@Scheduled(cron = "0 */30 * * * ?")
public void retryFailedSync() {
    Set<String> failedIds = stringRedisTemplate.opsForSet().members(SYNC_FAILED_KEY);
    
    if (failedIds != null && !failedIds.isEmpty()) {
        for (String scriptIdStr : failedIds) {
            try {
                Long scriptId = Long.valueOf(scriptIdStr);
                Script script = scriptMapper.selectById(scriptId);
                
                if (script != null) {
                    // 重新同步
                    syncOnDataChange(scriptId, "update");
                    // 从失败队列移除
                    stringRedisTemplate.opsForSet().remove(SYNC_FAILED_KEY, scriptIdStr);
                }
            } catch (Exception e) {
                log.warn("重试同步失败，scriptId: {}", scriptIdStr, e);
            }
        }
    }
}
```

## 搜索优化

### 1. 智能搜索

**多字段权重搜索**:
```java
Query multiMatchQuery = Query.of(q -> q
    .multiMatch(mm -> mm
        .query(keyword)
        .fields("title^3", "description^2", "tags^1")  // 权重设置
        .type(TextQueryType.BestFields)
        .fuzziness("AUTO")  // 模糊匹配
        .operator(Operator.And)
    )
);
```

**聚合分析**:
```java
SearchRequest aggregationRequest = SearchRequest.of(s -> s
    .index(indexPrefix + "_scripts")
    .query(buildBaseQuery(searchDTO))
    .size(0)
    .aggregations("categories", Aggregation.of(a -> a
        .terms(t -> t.field("category.keyword"))
    ))
    .aggregations("price_ranges", Aggregation.of(a -> a
        .range(r -> r
            .field("price")
            .ranges(range -> range.to(50.0).key("low"))
            .ranges(range -> range.from(50.0).to(100.0).key("medium"))
            .ranges(range -> range.from(100.0).key("high"))
        )
    ))
);
```

### 2. 搜索缓存

**缓存策略**:
- 热门搜索结果缓存5分钟
- 搜索建议缓存1小时
- 聚合结果缓存30分钟

```java
public PageResponse<ScriptDTO> searchWithCache(ScriptSearchDTO searchDTO) {
    String cacheKey = SEARCH_CACHE_KEY + generateCacheKey(searchDTO);
    
    // 尝试从缓存获取
    PageResponse<ScriptDTO> cached = getCachedResult(cacheKey);
    if (cached != null) {
        return cached;
    }
    
    // 执行搜索
    PageResponse<ScriptDTO> result = performSearch(searchDTO);
    
    // 缓存结果 (热门搜索才缓存)
    if (isHotSearch(searchDTO)) {
        cacheSearchResult(cacheKey, result, 5, TimeUnit.MINUTES);
    }
    
    return result;
}
```

### 3. 个性化推荐

**基于评分的推荐**:
```java
SearchRequest recommendRequest = SearchRequest.of(s -> s
    .index(indexPrefix + "_scripts")
    .query(Query.of(q -> q
        .functionScore(fs -> fs
            .query(baseQuery)
            .functions(func -> func
                .filter(Query.of(filter -> filter
                    .range(r -> r.field("averageRating").gte(FieldValue.of(4.0)))
                ))
                .weight(2.0)  // 高评分权重加倍
            )
            .functions(func -> func
                .filter(Query.of(filter -> filter
                    .range(r -> r.field("playCount").gte(FieldValue.of(100)))
                ))
                .weight(1.5)  // 热门剧本权重增加
            )
            .scoreMode(FunctionScoreMode.Sum)
            .boostMode(FunctionBoostMode.Sum)
        )
    ))
    .sort(SortOptions.of(sort -> sort.score(s1 -> s1.order(SortOrder.Desc))))
    .size(10)
);
```

## 管理接口

### 索引管理

```bash
# 重建索引
POST /admin/es/rebuild-index

# 增量同步
POST /admin/es/incremental-sync

# 数据一致性检查
POST /admin/es/data-consistency-check

# 清空同步队列
POST /admin/es/clear-sync-queues
```

### 状态查询

```bash
# 同步状态
GET /admin/es/sync-status
{
  "lastSyncTime": "2024-01-01 12:00:00",
  "pendingCount": 0,
  "failedCount": 0,
  "indexHealth": "green"
}

# 索引统计
GET /admin/es/index-stats
{
  "documentCount": 1500,
  "indexSize": "25MB",
  "shardCount": 1,
  "replicaCount": 0
}
```

## 监控和运维

### 关键指标监控

1. **数据一致性**:
   - 数据库记录数 vs ES文档数
   - 最后同步时间
   - 同步失败数量

2. **性能指标**:
   - 搜索响应时间
   - 搜索QPS
   - 索引吞吐量
   - JVM内存使用

3. **系统健康**:
   - ES集群状态
   - 分片健康状态
   - 磁盘使用率

### 定时任务

```java
// 每5分钟增量同步
@Scheduled(cron = "0 */5 * * * ?")
public void incrementalSyncScheduled()

// 每30分钟失败重试
@Scheduled(cron = "0 */30 * * * ?")
public void retryFailedSync()

// 每日数据一致性检查
@Scheduled(cron = "0 0 2 * * ?")
public void dataConsistencyCheck()
```

### 日志配置

```yaml
logging:
  level:
    '[com.scriptmurder.service.impl.ScriptSearchServiceImpl]': debug
    '[com.scriptmurder.service.impl.ScriptBulkImportService]': info
    '[com.scriptmurder.mq.listener]': info
    '[org.elasticsearch.client]': warn
```

## 故障排查

### 常见问题

#### 1. 连接失败
**现象**: 应用启动时ES连接超时
**排查步骤**:
```bash
# 检查ES是否启动
curl -X GET "localhost:9200/_cluster/health?pretty"

# 检查网络连通性
telnet localhost 9200

# 查看ES日志
tail -f elasticsearch-8.11.0/logs/elasticsearch.log
```

**解决方案**:
- 确认ES服务已启动
- 检查防火墙配置
- 验证认证配置

#### 2. 认证失败
**现象**: 401 Unauthorized错误
**排查步骤**:
```bash
# 重置密码
./bin/elasticsearch-reset-password -u elastic

# 生成API Key
curl -X POST "localhost:9200/_security/api_key" \
  -u elastic:password \
  -H "Content-Type: application/json" \
  -d '{"name": "script-murder-key", "expiration": "365d"}'
```

#### 3. 同步延迟
**现象**: ES数据比数据库延迟较多
**排查步骤**:
```bash
# 检查同步状态
GET /admin/es/sync-status

# 查看MQ队列积压
curl -u guest:guest http://localhost:15672/api/queues/%2f/script.sync.queue

# 检查同步失败队列
redis-cli SMEMBERS script:sync:failed
```

**解决方案**:
- 增加MQ消费者数量
- 调整批量大小
- 手动触发增量同步

#### 4. 搜索性能慢
**现象**: 搜索响应时间超过3秒
**排查步骤**:
```bash
# 查看慢查询日志
grep "took_millis" elasticsearch.log | tail -20

# 检查索引状态
curl -X GET "localhost:9200/_cat/indices?v"

# 分析查询性能
curl -X GET "localhost:9200/script_murder_scripts/_search?explain=true"
```

**解决方案**:
- 优化查询语句
- 增加缓存
- 考虑分片策略

## 性能调优

### 1. ES配置优化

```yaml
# elasticsearch.yml
cluster.name: script-murder-cluster
node.name: node-1

# 内存设置 (建议设置为系统内存的50%)
bootstrap.memory_lock: true

# 分片设置
index.number_of_shards: 1
index.number_of_replicas: 0

# 搜索线程池
thread_pool.search.size: 4
thread_pool.search.queue_size: 1000

# 批量操作设置
indices.memory.index_buffer_size: 20%
```

### 2. 应用配置优化

```yaml
script-murder:
  elasticsearch:
    # 批量大小 (根据数据量和内存调整)
    batch-size: 1000
    
    # 连接池设置
    max-connections: 100
    max-connections-per-route: 10
    
    # 超时设置
    request-timeout: 10s
    connection-timeout: 5s
    socket-timeout: 30s
```

### 3. JVM参数优化

```bash
# 启动参数
-Xms2g -Xmx2g
-XX:+UseG1GC
-XX:G1HeapRegionSize=16m
-XX:+DisableExplicitGC
-XX:+HeapDumpOnOutOfMemoryError
```

### 4. 监控脚本

```bash
#!/bin/bash
# es_monitor.sh

# 检查集群健康
HEALTH=$(curl -s "localhost:9200/_cluster/health" | jq -r '.status')
echo "ES集群状态: $HEALTH"

# 检查索引大小
SIZE=$(curl -s "localhost:9200/_cat/indices/script_murder_scripts?h=store.size&bytes=mb")
echo "索引大小: ${SIZE}MB"

# 检查文档数量
COUNT=$(curl -s "localhost:9200/script_murder_scripts/_count" | jq -r '.count')
echo "文档数量: $COUNT"

# 检查同步队列
PENDING=$(redis-cli SCARD script:sync:pending)
FAILED=$(redis-cli SCARD script:sync:failed)
echo "待同步: $PENDING, 失败: $FAILED"
```

## 总结

本文档提供了Elasticsearch 8.x在剧本杀平台中的完整管理方案，包括：

1. **多层次数据导入策略**: 全量重建、增量同步、实时同步
2. **完善的同步机制**: MQ异步处理、失败重试、一致性检查
3. **智能搜索优化**: 多字段权重、缓存机制、个性化推荐
4. **全面的监控运维**: 状态监控、性能指标、故障排查

通过合理配置和使用这些功能，可以确保搜索服务的高性能、高可用和数据一致性。

---

**维护人员**: 开发团队  
**最后更新**: 2024年1月  
**版本**: v1.0