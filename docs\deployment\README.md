# 部署指南

## 概述

本文档详细说明了剧本杀平台的部署流程，包括开发环境、测试环境和生产环境的配置。

## 环境要求

### 系统要求
- **操作系统**: Linux (推荐 Ubuntu 20.04+) / Windows 10+ / macOS 10.15+
- **内存**: 最低 4GB，推荐 8GB+
- **存储**: 最低 20GB 可用空间
- **网络**: 稳定的互联网连接

### 软件依赖

#### 后端环境
- **Java**: JDK 8 或 JDK 11
- **Maven**: 3.6+
- **MySQL**: 8.0+
- **Redis**: 6.0+

#### 前端环境
- **Node.js**: 16.x 或 18.x
- **npm**: 8.x+ 或 yarn 1.22+

#### 可选组件
- **Docker**: 20.10+ (容器化部署)
- **Nginx**: 1.18+ (反向代理)

## 开发环境部署

### 1. 环境准备

#### 安装 Java
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install openjdk-11-jdk

# CentOS/RHEL
sudo yum install java-11-openjdk-devel

# 验证安装
java -version
```

#### 安装 Node.js
```bash
# 使用 nvm 安装 (推荐)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18

# 验证安装
node --version
npm --version
```

#### 安装 MySQL
```bash
# Ubuntu/Debian
sudo apt install mysql-server

# 启动服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation
```

#### 安装 Redis
```bash
# Ubuntu/Debian
sudo apt install redis-server

# 启动服务
sudo systemctl start redis
sudo systemctl enable redis

# 验证安装
redis-cli ping
```

### 2. 数据库配置

#### 创建数据库
```sql
-- 连接到 MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE hmdp CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'hmdp_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON hmdp.* TO 'hmdp_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 导入初始数据
```bash
# 进入项目目录
cd HmdpReconstruction

# 导入数据库结构
mysql -u hmdp_user -p hmdp < backend/src/main/resources/db/hmdp.sql
```

### 3. 后端部署

#### 配置文件
```bash
# 复制配置文件
cd backend/src/main/resources
cp application-local.yaml.example application-local.yaml

# 编辑配置文件
vim application-local.yaml
```

配置示例：
```yaml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************
    username: hmdp_user
    password: your_password
  
  redis:
    host: localhost
    port: 6379
    password: # Redis密码，如果有的话
    database: 0
```

#### 启动后端服务
```bash
# 进入后端目录
cd backend

# 编译项目
mvn clean compile

# 启动服务
mvn spring-boot:run

# 或者打包后运行
mvn clean package -DskipTests
java -jar target/script-murder-backend-0.0.1-SNAPSHOT.jar
```

### 4. 前端部署

#### 安装依赖
```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install
# 或使用 yarn
yarn install
```

#### 配置环境变量
```bash
# 创建环境配置文件
cp .env.example .env.development

# 编辑配置
vim .env.development
```

配置示例：
```bash
# API 基础地址
VITE_API_BASE_URL=http://localhost:8081

# 应用标题
VITE_APP_TITLE=剧本杀平台 - 开发环境

# 是否启用调试模式
VITE_DEBUG=true
```

#### 启动前端服务
```bash
# 开发模式启动
npm run dev
# 或
yarn dev

# 访问地址: http://localhost:5173
```

## 生产环境部署

### 1. 使用 Docker 部署

#### 创建 Docker Compose 文件
```yaml
# docker-compose.yml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: hmdp-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: hmdp
      MYSQL_USER: hmdp_user
      MYSQL_PASSWORD: user_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./backend/src/main/resources/db:/docker-entrypoint-initdb.d
    networks:
      - hmdp-network

  redis:
    image: redis:6.2-alpine
    container_name: hmdp-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - hmdp-network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: hmdp-backend
    ports:
      - "8081:8081"
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_DATASOURCE_URL: ************************************************************
      SPRING_DATASOURCE_USERNAME: hmdp_user
      SPRING_DATASOURCE_PASSWORD: user_password
      SPRING_REDIS_HOST: redis
    depends_on:
      - mysql
      - redis
    networks:
      - hmdp-network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: hmdp-frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - hmdp-network

volumes:
  mysql_data:
  redis_data:

networks:
  hmdp-network:
    driver: bridge
```

#### 后端 Dockerfile
```dockerfile
# backend/Dockerfile
FROM openjdk:11-jre-slim

WORKDIR /app

COPY target/script-murder-backend-0.0.1-SNAPSHOT.jar app.jar

EXPOSE 8081

ENTRYPOINT ["java", "-jar", "app.jar"]
```

#### 前端 Dockerfile
```dockerfile
# frontend/Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine

COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

#### 启动服务
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 2. 传统部署方式

#### 后端部署
```bash
# 1. 打包应用
cd backend
mvn clean package -DskipTests

# 2. 创建部署目录
sudo mkdir -p /opt/hmdp/backend
sudo cp target/script-murder-backend-0.0.1-SNAPSHOT.jar /opt/hmdp/backend/

# 3. 创建启动脚本
sudo tee /opt/hmdp/backend/start.sh << EOF
#!/bin/bash
cd /opt/hmdp/backend
nohup java -jar script-murder-backend-0.0.1-SNAPSHOT.jar \
  --spring.profiles.active=prod \
  > app.log 2>&1 &
echo $! > app.pid
EOF

sudo chmod +x /opt/hmdp/backend/start.sh

# 4. 启动服务
sudo /opt/hmdp/backend/start.sh
```

#### 前端部署
```bash
# 1. 构建前端
cd frontend
npm run build

# 2. 部署到 Nginx
sudo cp -r dist/* /var/www/html/

# 3. 配置 Nginx
sudo tee /etc/nginx/sites-available/hmdp << EOF
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/html;
    index index.html;

    location / {
        try_files \$uri \$uri/ /index.html;
    }

    location /api {
        proxy_pass http://localhost:8081;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

# 4. 启用站点
sudo ln -s /etc/nginx/sites-available/hmdp /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 监控和维护

### 1. 日志管理

#### 后端日志
```bash
# 查看应用日志
tail -f /opt/hmdp/backend/app.log

# 日志轮转配置
sudo tee /etc/logrotate.d/hmdp << EOF
/opt/hmdp/backend/app.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
}
EOF
```

#### 前端日志
```bash
# Nginx 访问日志
tail -f /var/log/nginx/access.log

# Nginx 错误日志
tail -f /var/log/nginx/error.log
```

### 2. 性能监控

#### 系统监控
```bash
# 安装监控工具
sudo apt install htop iotop nethogs

# 监控系统资源
htop

# 监控磁盘 I/O
sudo iotop

# 监控网络使用
sudo nethogs
```

#### 应用监控
```bash
# 检查 Java 进程
jps -l

# 查看 JVM 内存使用
jstat -gc <pid>

# 生成堆转储
jmap -dump:format=b,file=heap.hprof <pid>
```

### 3. 备份策略

#### 数据库备份
```bash
# 创建备份脚本
sudo tee /opt/hmdp/backup.sh << EOF
#!/bin/bash
DATE=\$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/opt/hmdp/backups"
mkdir -p \$BACKUP_DIR

# 备份数据库
mysqldump -u hmdp_user -p hmdp > \$BACKUP_DIR/hmdp_\$DATE.sql

# 压缩备份文件
gzip \$BACKUP_DIR/hmdp_\$DATE.sql

# 删除7天前的备份
find \$BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete
EOF

sudo chmod +x /opt/hmdp/backup.sh

# 设置定时备份
sudo crontab -e
# 添加以下行（每天凌晨2点备份）
# 0 2 * * * /opt/hmdp/backup.sh
```

## 故障排查

### 常见问题

#### 1. 后端启动失败
```bash
# 检查端口占用
sudo netstat -tlnp | grep 8081

# 检查 Java 版本
java -version

# 检查配置文件
cat backend/src/main/resources/application.yaml
```

#### 2. 数据库连接失败
```bash
# 检查 MySQL 服务状态
sudo systemctl status mysql

# 测试数据库连接
mysql -u hmdp_user -p -h localhost hmdp

# 检查防火墙设置
sudo ufw status
```

#### 3. 前端访问异常
```bash
# 检查 Nginx 状态
sudo systemctl status nginx

# 检查 Nginx 配置
sudo nginx -t

# 查看错误日志
sudo tail -f /var/log/nginx/error.log
```

### 性能优化

#### 数据库优化
```sql
-- 添加索引
CREATE INDEX idx_user_phone ON tb_user(phone);
CREATE INDEX idx_script_category ON tb_script(category_id);

-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SET GLOBAL slow_query_log = 'ON';
```

#### 应用优化
```bash
# JVM 参数优化
java -Xms2g -Xmx4g -XX:+UseG1GC \
  -jar script-murder-backend-0.0.1-SNAPSHOT.jar
```

## 安全配置

### 1. 防火墙设置
```bash
# 启用防火墙
sudo ufw enable

# 允许必要端口
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS

# 限制数据库访问
sudo ufw deny 3306
sudo ufw deny 6379
```

### 2. SSL 证书配置
```bash
# 安装 Certbot
sudo apt install certbot python3-certbot-nginx

# 获取 SSL 证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 总结

本部署指南涵盖了从开发环境到生产环境的完整部署流程。建议：

1. **开发阶段**: 使用本地环境进行开发和测试
2. **测试阶段**: 使用 Docker 进行集成测试
3. **生产阶段**: 根据实际需求选择容器化或传统部署方式

定期进行备份、监控和维护，确保系统稳定运行。

---

## Elasticsearch 专项部署指南

### 1. Elasticsearch 8.x 安装配置

#### 系统要求
- **内存**: 最低4GB，推荐8GB+
- **磁盘**: SSD存储，至少20GB可用空间
- **JVM**: 内置OpenJDK，无需单独安装

#### 安装步骤

##### Linux 安装
```bash
# 1. 下载 Elasticsearch 8.11.0
wget https://artifacts.elastic.co/downloads/elasticsearch/elasticsearch-8.11.0-linux-x86_64.tar.gz
tar -xzf elasticsearch-8.11.0-linux-x86_64.tar.gz
cd elasticsearch-8.11.0/

# 2. 创建专用用户（安全要求）
sudo useradd elasticsearch
sudo chown -R elasticsearch:elasticsearch /path/to/elasticsearch-8.11.0/

# 3. 创建数据和日志目录
sudo mkdir -p /var/lib/elasticsearch
sudo mkdir -p /var/log/elasticsearch
sudo chown -R elasticsearch:elasticsearch /var/lib/elasticsearch
sudo chown -R elasticsearch:elasticsearch /var/log/elasticsearch
```

##### Docker 安装
```bash
# 使用官方镜像
docker run -d \
  --name elasticsearch \
  -p 9200:9200 \
  -p 9300:9300 \
  -e "discovery.type=single-node" \
  -e "xpack.security.enabled=false" \
  -e "ES_JAVA_OPTS=-Xms2g -Xmx2g" \
  -v es_data:/usr/share/elasticsearch/data \
  elasticsearch:8.11.0
```

#### 配置文件设置

##### 基础配置 (config/elasticsearch.yml)
```yaml
# 集群配置
cluster.name: script-murder-cluster
node.name: node-1

# 路径配置
path.data: /var/lib/elasticsearch
path.logs: /var/log/elasticsearch

# 网络配置
network.host: 0.0.0.0
http.port: 9200

# 发现配置（单节点）
discovery.type: single-node

# 内存配置
bootstrap.memory_lock: true

# 安全配置（开发环境可禁用）
xpack.security.enabled: false
xpack.security.transport.ssl.enabled: false
xpack.security.http.ssl.enabled: false

# 生产环境安全配置
# xpack.security.enabled: true
# xpack.security.transport.ssl.enabled: true
# xpack.security.http.ssl.enabled: true
```

##### JVM 内存配置 (config/jvm.options)
```bash
# 堆内存设置（建议设置为系统内存的50%）
-Xms2g
-Xmx2g

# GC 配置
-XX:+UseG1GC
-XX:G1HeapRegionSize=16m
-XX:+DisableExplicitGC
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=/var/log/elasticsearch/

# 其他优化
-Dfile.encoding=UTF-8
-Djava.awt.headless=true
```

### 2. IK中文分词插件安装

```bash
# 在 Elasticsearch 目录下执行
./bin/elasticsearch-plugin install https://github.com/medcl/elasticsearch-analysis-ik/releases/download/v8.11.0/elasticsearch-analysis-ik-8.11.0.zip

# 验证安装
./bin/elasticsearch-plugin list

# 重启 Elasticsearch 服务
sudo systemctl restart elasticsearch
```

### 3. 系统服务配置

#### 创建系统服务文件
```ini
# /etc/systemd/system/elasticsearch.service
[Unit]
Description=Elasticsearch
Documentation=https://www.elastic.co
Wants=network-online.target
After=network-online.target

[Service]
Type=notify
RuntimeDirectory=elasticsearch
PrivateTmp=true
Environment=ES_HOME=/opt/elasticsearch
Environment=ES_PATH_CONF=/opt/elasticsearch/config
Environment=PID_DIR=/var/run/elasticsearch
WorkingDirectory=/opt/elasticsearch

User=elasticsearch
Group=elasticsearch

ExecStart=/opt/elasticsearch/bin/elasticsearch
StandardOutput=journal
StandardError=inherit

LimitNOFILE=65535
LimitNPROC=32768
LimitAS=infinity
LimitFSIZE=infinity

KillSignal=SIGTERM
KillMode=process
KillTimeout=0

TimeoutStopSec=0

[Install]
WantedBy=multi-user.target
```

#### 启动和管理服务
```bash
# 重新加载系统服务
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start elasticsearch

# 设置开机自启
sudo systemctl enable elasticsearch

# 查看服务状态
sudo systemctl status elasticsearch

# 查看日志
sudo journalctl -u elasticsearch -f
```

### 4. 安全配置（生产环境）

#### 启用安全功能
```bash
# 1. 启用安全配置
echo "xpack.security.enabled: true" >> config/elasticsearch.yml

# 2. 重启服务
sudo systemctl restart elasticsearch

# 3. 设置内置用户密码
./bin/elasticsearch-setup-passwords interactive

# 4. 生成 API Key（推荐）
curl -X POST "localhost:9200/_security/api_key" \
  -u elastic:your_password \
  -H "Content-Type: application/json" \
  -d '{
    "name": "script-murder-api-key",
    "expiration": "365d",
    "role_descriptors": {
      "script_murder_role": {
        "cluster": ["monitor"],
        "indices": [
          {
            "names": ["script_murder_*"],
            "privileges": ["read", "write", "create_index", "delete_index"]
          }
        ]
      }
    }
  }'
```

### 5. 监控和维护

#### 健康检查脚本
```bash
#!/bin/bash
# /opt/elasticsearch/scripts/health_check.sh

ES_URL="http://localhost:9200"
LOG_FILE="/var/log/elasticsearch/health_check.log"
EMAIL="<EMAIL>"

# 检查集群状态
check_cluster_health() {
    local response=$(curl -s "$ES_URL/_cluster/health")
    local status=$(echo $response | jq -r '.status')
    
    echo "$(date): Cluster health: $status" >> $LOG_FILE
    
    if [[ "$status" != "green" && "$status" != "yellow" ]]; then
        echo "ALERT: Elasticsearch cluster status is $status" | \
        mail -s "ES Cluster Alert" $EMAIL
    fi
}

# 检查磁盘使用率
check_disk_usage() {
    local response=$(curl -s "$ES_URL/_cat/nodes?h=disk.used_percent&format=json")
    local usage=$(echo $response | jq -r '.[0].disk_used_percent' | sed 's/%//')
    
    echo "$(date): Disk usage: ${usage}%" >> $LOG_FILE
    
    if [[ $usage -gt 85 ]]; then
        echo "ALERT: Elasticsearch disk usage is ${usage}%" | \
        mail -s "ES Disk Alert" $EMAIL
    fi
}

# 检查索引状态
check_indices() {
    local response=$(curl -s "$ES_URL/_cat/indices/script_murder_*?h=health,status,index&format=json")
    
    echo $response | jq -r '.[] | "\(.index): \(.health) \(.status)"' | \
    while read line; do
        echo "$(date): Index status - $line" >> $LOG_FILE
        
        if [[ $line == *"red"* ]]; then
            echo "ALERT: Index in red status - $line" | \
            mail -s "ES Index Alert" $EMAIL
        fi
    done
}

# 执行检查
check_cluster_health
check_disk_usage
check_indices

echo "$(date): Health check completed" >> $LOG_FILE
```

#### 性能监控
```bash
# 查看集群统计
curl -X GET "localhost:9200/_cluster/stats?pretty"

# 查看节点统计
curl -X GET "localhost:9200/_nodes/stats?pretty"

# 查看索引统计
curl -X GET "localhost:9200/script_murder_*/_stats?pretty"

# 查看热点线程
curl -X GET "localhost:9200/_nodes/hot_threads"
```

#### 日志轮转配置
```bash
# /etc/logrotate.d/elasticsearch
/var/log/elasticsearch/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 elasticsearch elasticsearch
    postrotate
        systemctl reload elasticsearch > /dev/null 2>&1 || true
    endscript
}
```

### 6. 备份和恢复

#### 配置快照仓库
```bash
# 1. 创建备份目录
sudo mkdir -p /backup/elasticsearch
sudo chown elasticsearch:elasticsearch /backup/elasticsearch

# 2. 配置快照仓库
curl -X PUT "localhost:9200/_snapshot/backup_repo" \
  -H 'Content-Type: application/json' \
  -d '{
    "type": "fs",
    "settings": {
      "location": "/backup/elasticsearch",
      "compress": true
    }
  }'
```

#### 自动备份脚本
```bash
#!/bin/bash
# /opt/elasticsearch/scripts/backup.sh

ES_URL="http://localhost:9200"
REPO_NAME="backup_repo"
DATE=$(date +%Y%m%d_%H%M%S)
SNAPSHOT_NAME="snapshot_$DATE"
RETENTION_DAYS=30

# 创建快照
create_snapshot() {
    echo "Creating snapshot: $SNAPSHOT_NAME"
    
    curl -X PUT "$ES_URL/_snapshot/$REPO_NAME/$SNAPSHOT_NAME?wait_for_completion=true" \
      -H 'Content-Type: application/json' \
      -d '{
        "indices": "script_murder_*",
        "ignore_unavailable": true,
        "include_global_state": false,
        "metadata": {
          "taken_by": "automated_backup",
          "taken_because": "scheduled backup"
        }
      }'
    
    if [[ $? -eq 0 ]]; then
        echo "Snapshot created successfully: $SNAPSHOT_NAME"
    else
        echo "ERROR: Failed to create snapshot: $SNAPSHOT_NAME"
        exit 1
    fi
}

# 清理旧快照
cleanup_old_snapshots() {
    echo "Cleaning up snapshots older than $RETENTION_DAYS days"
    
    # 获取所有快照
    local snapshots=$(curl -s "$ES_URL/_snapshot/$REPO_NAME/_all" | \
                     jq -r ".snapshots[] | select(.start_time_in_millis < $(date -d "$RETENTION_DAYS days ago" +%s)000) | .snapshot")
    
    for snapshot in $snapshots; do
        echo "Deleting old snapshot: $snapshot"
        curl -X DELETE "$ES_URL/_snapshot/$REPO_NAME/$snapshot"
    done
}

# 执行备份
create_snapshot
cleanup_old_snapshots

echo "Backup completed: $(date)"
```

### 7. 故障排查

#### 常见问题诊断

##### 服务启动失败
```bash
# 检查服务状态
sudo systemctl status elasticsearch

# 查看详细日志
sudo journalctl -u elasticsearch --no-pager

# 检查配置文件
/opt/elasticsearch/bin/elasticsearch -t

# 检查文件权限
ls -la /var/lib/elasticsearch
ls -la /var/log/elasticsearch
```

##### 内存不足
```bash
# 检查 JVM 内存设置
cat /opt/elasticsearch/config/jvm.options | grep -E "^-Xm"

# 检查系统内存
free -h

# 检查进程内存使用
ps aux | grep elasticsearch

# 调整内存设置
sudo sed -i 's/-Xms.*/-Xms4g/' /opt/elasticsearch/config/jvm.options
sudo sed -i 's/-Xmx.*/-Xmx4g/' /opt/elasticsearch/config/jvm.options
```

##### 磁盘空间不足
```bash
# 检查磁盘使用
df -h /var/lib/elasticsearch

# 清理旧索引
curl -X DELETE "localhost:9200/script_murder_*_$(date -d '30 days ago' +%Y%m%d)*"

# 强制合并段文件
curl -X POST "localhost:9200/script_murder_*/_forcemerge?max_num_segments=1"
```

### 8. 性能调优

#### 系统级优化
```bash
# 1. 设置系统参数
echo 'vm.max_map_count=262144' >> /etc/sysctl.conf
echo 'vm.swappiness=1' >> /etc/sysctl.conf
sysctl -p

# 2. 设置文件描述符限制
echo 'elasticsearch soft nofile 65536' >> /etc/security/limits.conf
echo 'elasticsearch hard nofile 65536' >> /etc/security/limits.conf

# 3. 禁用交换分区
sudo swapoff -a
sudo sed -i '/ swap / s/^\(.*\)$/#\1/g' /etc/fstab
```

#### ES配置优化
```yaml
# 高性能配置示例
indices.memory.index_buffer_size: 30%
indices.queries.cache.size: 20%
thread_pool.search.size: 4
thread_pool.search.queue_size: 1000
cluster.routing.allocation.disk.threshold_enabled: true
cluster.routing.allocation.disk.watermark.low: 85%
cluster.routing.allocation.disk.watermark.high: 90%
```

通过以上配置，您可以成功部署和运维Elasticsearch 8.x集群，为剧本杀平台提供高性能的搜索服务。
