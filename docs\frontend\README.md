# 前端开发文档

## 项目概述

剧本杀平台前端基于 Vue 3 + TypeScript 构建，采用组件化开发模式，提供现代化的用户界面和良好的用户体验。

## 技术栈

### 核心框架
- **Vue 3.3+**: 前端框架
- **TypeScript 5.0+**: 类型安全
- **Vite 4.x**: 构建工具
- **Vue Router 4**: 路由管理
- **Pinia**: 状态管理

### UI组件库
- **Element Plus**: 主要UI组件库
- **自定义组件**: 业务特定组件

### 工具库
- **Axios**: HTTP客户端
- **SCSS**: CSS预处理器
- **ESLint**: 代码检查
- **Prettier**: 代码格式化

## 项目结构

```
frontend/
├── public/                    # 静态资源
├── src/
│   ├── main.ts               # 应用入口
│   ├── App.vue               # 根组件
│   ├── views/                # 页面组件
│   │   ├── Home/             # 首页
│   │   │   ├── index.vue     # 首页主组件
│   │   │   └── components/   # 首页子组件
│   │   ├── Auth/             # 认证页面
│   │   │   ├── Login.vue     # 登录页
│   │   │   └── Register.vue  # 注册页
│   │   ├── Script/           # 剧本相关页面
│   │   ├── Lobby/            # 房间相关页面
│   │   └── User/             # 用户相关页面
│   ├── components/           # 通用组件
│   │   ├── common/           # 公共组件
│   │   │   ├── AppHeader.vue # 顶部导航
│   │   │   ├── AppFooter.vue # 底部组件
│   │   │   └── ...
│   │   └── layout/           # 布局组件
│   │       ├── DefaultLayout.vue
│   │       └── AuthLayout.vue
│   ├── stores/               # 状态管理
│   │   ├── index.ts          # Store入口
│   │   └── modules/          # 模块化Store
│   │       ├── user.ts       # 用户状态
│   │       ├── auth.ts       # 认证状态
│   │       └── app.ts        # 应用状态
│   ├── api/                  # API接口
│   │   ├── index.ts          # API入口
│   │   ├── user.ts           # 用户API
│   │   ├── script.ts         # 剧本API
│   │   └── types.ts          # API类型定义
│   ├── router/               # 路由配置
│   │   └── index.ts          # 路由定义
│   ├── composables/          # 组合式函数
│   │   ├── useAuth.ts        # 认证相关
│   │   └── useApi.ts         # API相关
│   ├── utils/                # 工具函数
│   │   ├── request.ts        # HTTP请求封装
│   │   ├── storage.ts        # 本地存储
│   │   └── constants.ts      # 常量定义
│   ├── assets/               # 静态资源
│   │   ├── styles/           # 样式文件
│   │   ├── images/           # 图片资源
│   │   └── icons/            # 图标资源
│   └── types/                # TypeScript类型定义
│       ├── api.ts            # API类型
│       ├── user.ts           # 用户类型
│       └── global.d.ts       # 全局类型
├── package.json              # 项目配置
├── vite.config.ts            # Vite配置
├── tsconfig.json             # TypeScript配置
└── .eslintrc.js              # ESLint配置
```

## 核心模块

### 1. 用户认证模块 ✅ **已完成**

#### 功能特性
- 手机验证码登录
- 用户状态管理
- 路由权限控制
- 自动登录保持

#### 主要组件
- `Login.vue`: 登录页面
- `Register.vue`: 注册页面
- `AppHeader.vue`: 顶部导航（含用户状态）

#### 状态管理
```typescript
// stores/modules/user.ts
interface UserState {
  currentUser: User | null
  isLoggedIn: boolean
  token: string | null
}
```

### 2. 首页模块 ✅ **已完成**

#### 功能特性
- 英雄区域展示
- 热门剧本推荐
- 实时拼车信息
- 社区动态预览

#### 主要组件
- `HeroSection.vue`: 英雄区域
- `FeaturedScripts.vue`: 推荐剧本
- `LiveLobbyFeed.vue`: 拼车信息
- `CommunityPreview.vue`: 社区预览

### 3. 剧本管理模块 🚧 **开发中**

#### 计划功能
- 剧本列表展示
- 剧本详情页面
- 剧本搜索筛选
- 剧本评价功能

### 4. 房间管理模块 📋 **规划中**

#### 计划功能
- 房间创建界面
- 房间列表展示
- 房间详情管理
- 实时状态更新

## 状态管理

### Pinia Store 结构

#### 用户状态 (User Store)
```typescript
export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    currentUser: null,
    isLoggedIn: false,
    userProfile: null
  }),
  
  getters: {
    userNickname: (state) => state.currentUser?.nickname || '',
    userAvatar: (state) => state.currentUser?.avatar || '',
    userLevel: (state) => state.currentUser?.level || 1
  },
  
  actions: {
    setCurrentUser(user: User) {
      this.currentUser = user
      this.isLoggedIn = true
    },
    
    clearUserData() {
      this.currentUser = null
      this.isLoggedIn = false
    }
  }
})
```

#### 应用状态 (App Store)
```typescript
export const useAppStore = defineStore('app', {
  state: (): AppState => ({
    isLoading: false,
    loadingText: '',
    isOnline: true,
    theme: 'dark'
  })
})
```

## 路由配置

### 路由结构
```typescript
const routes = [
  {
    path: '/',
    component: DefaultLayout,
    children: [
      { path: '', name: 'Home', component: HomePage },
      { path: '/lobby', name: 'Lobby', component: LobbyPage },
      { path: '/scripts', name: 'Scripts', component: ScriptListPage },
      // ... 其他路由
    ]
  },
  {
    path: '/auth',
    component: AuthLayout,
    children: [
      { path: 'login', name: 'Login', component: LoginPage },
      { path: 'register', name: 'Register', component: RegisterPage }
    ]
  }
]
```

### 路由守卫
- **认证检查**: 检查用户登录状态
- **权限控制**: 根据用户角色控制访问
- **页面标题**: 自动设置页面标题

## API 集成

### HTTP 请求封装
```typescript
// utils/request.ts
const request = axios.create({
  baseURL: 'http://localhost:8081',
  timeout: 10000
})

// 请求拦截器
request.interceptors.request.use(config => {
  const token = localStorage.getItem('auth_token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// 响应拦截器
request.interceptors.response.use(
  response => response.data,
  error => {
    // 统一错误处理
    return Promise.reject(error)
  }
)
```

### API 模块化
```typescript
// api/user.ts
export const userApi = {
  // 发送验证码
  sendCode: (phone: string) => 
    request.post('/user/code', null, { params: { phone } }),
  
  // 用户登录
  login: (data: LoginData) => 
    request.post('/user/login', data),
  
  // 获取用户信息
  getCurrentUser: () => 
    request.get('/user/me'),
  
  // 用户登出
  logout: () => 
    request.post('/user/logout')
}
```

## 组件开发规范

### 组件命名
- **页面组件**: PascalCase (如 `UserProfile.vue`)
- **通用组件**: PascalCase + 前缀 (如 `AppHeader.vue`)
- **业务组件**: PascalCase (如 `ScriptCard.vue`)

### 组件结构
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 导入依赖
import { ref, computed, onMounted } from 'vue'

// 类型定义
interface Props {
  // props 类型
}

// Props 和 Emits
const props = defineProps<Props>()
const emit = defineEmits<{
  // 事件类型
}>()

// 响应式数据
const data = ref()

// 计算属性
const computed = computed(() => {
  // 计算逻辑
})

// 方法
const method = () => {
  // 方法实现
}

// 生命周期
onMounted(() => {
  // 初始化逻辑
})
</script>

<style lang="scss" scoped>
/* 组件样式 */
</style>
```

## 样式规范

### SCSS 变量
```scss
// assets/styles/variables.scss
$primary-color: #00F5D4;
$secondary-color: #FF00E4;
$background-color: #1A1A2E;
$text-color: #FFFFFF;
$border-radius: 8px;
```

### 响应式设计
```scss
// 断点定义
$breakpoints: (
  mobile: 768px,
  tablet: 1024px,
  desktop: 1200px
);

// 媒体查询混入
@mixin mobile {
  @media (max-width: 768px) {
    @content;
  }
}
```

## 开发工具配置

### Vite 配置
```typescript
// vite.config.ts
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },
  server: {
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:8081',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  }
})
```

### TypeScript 配置
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "ESNext",
    "strict": true,
    "jsx": "preserve",
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "skipLibCheck": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  }
}
```

## 性能优化

### 代码分割
- 路由级别的代码分割
- 组件懒加载
- 第三方库按需引入

### 缓存策略
- HTTP 缓存配置
- 本地存储优化
- 组件缓存 (keep-alive)

## 测试策略

### 单元测试
- 使用 Vitest 进行单元测试
- 组件测试覆盖率 > 80%
- 工具函数测试覆盖率 > 90%

### E2E 测试
- 使用 Cypress 进行端到端测试
- 关键用户流程测试
- 跨浏览器兼容性测试

## 部署配置

### 构建配置
```bash
# 开发环境
npm run dev

# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

### 环境变量
```bash
# .env.development
VITE_API_BASE_URL=http://localhost:8081
VITE_APP_TITLE=剧本杀平台 - 开发环境

# .env.production
VITE_API_BASE_URL=https://api.example.com
VITE_APP_TITLE=剧本杀平台
```

## 常见问题

### Q: 如何添加新的页面？
A: 
1. 在 `views` 目录下创建页面组件
2. 在 `router/index.ts` 中添加路由配置
3. 更新导航菜单

### Q: 如何处理API错误？
A: 使用统一的错误处理机制，在 axios 拦截器中处理

### Q: 如何优化首屏加载速度？
A: 
1. 使用路由懒加载
2. 优化图片资源
3. 启用 gzip 压缩
4. 使用 CDN 加速

## 相关链接

- [组件库文档](./components.md)
- [API接口文档](../04_api_reference/rest_api.md)
- [部署指南](./deployment_guide.md)
