# 房间系统数据一致性解决方案

## 📋 文档信息

**模块**: 房间系统 - 数据一致性  
**版本**: v1.0  
**日期**: 2025-08-03  
**作者**: an  

## 🎯 数据一致性挑战

房间系统面临的主要数据一致性问题：

### 1. 并发加入房间问题

**场景**: 多用户同时申请加入同一房间，导致超过房间人数限制

```
时刻T1: 房间当前人数 4/6
时刻T2: 用户A和用户B同时点击"加入房间"
时刻T3: 两个请求都读取到当前人数为4，判断可以加入
时刻T4: 两个请求都执行加入操作
结果: 房间人数变成6，但实际应该是5（违反了并发控制）
```

### 2. 房主离开时的权限转移问题

**场景**: 房主离开房间时，需要原子性地转移房主权限

```
时刻T1: 房主A决定离开房间
时刻T2: 系统选择成员B作为新房主
时刻T3: 在权限转移过程中，成员C也离开了房间
时刻T4: 如果处理不当，可能出现房间无房主的状态
```

### 3. 状态转换与人员变动冲突

**场景**: 房间状态变更和成员变动同时发生

```
时刻T1: 房间人数达到最小要求，准备从RECRUITING转为PREPARING
时刻T2: 同时有成员离开房间
时刻T3: 状态转换和成员离开操作产生冲突
结果: 可能出现状态不一致的情况
```

## 🔒 分布式锁解决方案

### Redis分布式锁架构

```java
/**
 * 房间数据一致性管理器
 */
@Service
public class RoomConsistencyManager {
    
    @Autowired
    private RedissonClient redissonClient;
    
    @Autowired
    private RoomRepository roomRepository;
    
    @Autowired
    private RoomMemberRepository roomMemberRepository;
    
    /**
     * 原子性加入房间操作
     */
    @Transactional
    public RoomJoinResult joinRoom(Long roomId, Long userId) {
        String lockKey = "room:join:" + roomId;
        RLock lock = redissonClient.getLock(lockKey);
        
        try {
            // 获取分布式锁，最多等待3秒，锁定10秒
            if (lock.tryLock(3, 10, TimeUnit.SECONDS)) {
                return doJoinRoom(roomId, userId);
            } else {
                return RoomJoinResult.failure("房间操作繁忙，请稍后重试");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return RoomJoinResult.failure("操作被中断");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
    
    /**
     * 执行加入房间的核心逻辑
     */
    private RoomJoinResult doJoinRoom(Long roomId, Long userId) {
        // 1. 检查房间是否存在
        Room room = roomRepository.findById(roomId)
            .orElseThrow(() -> new BusinessException("房间不存在"));
        
        // 2. 检查房间状态
        if (room.getStatus() != RoomStatus.RECRUITING) {
            return RoomJoinResult.failure("房间不在招募状态");
        }
        
        // 3. 检查用户是否已在房间中
        boolean isAlreadyMember = roomMemberRepository
            .existsByRoomIdAndUserIdAndStatus(roomId, userId, MemberStatus.ACTIVE);
        if (isAlreadyMember) {
            return RoomJoinResult.failure("您已在房间中");
        }
        
        // 4. 检查房间人数限制
        int currentCount = roomMemberRepository.countByRoomIdAndStatus(roomId, MemberStatus.ACTIVE);
        if (currentCount >= room.getMaxPlayers()) {
            return RoomJoinResult.failure("房间已满");
        }
        
        // 5. 创建成员记录
        RoomMember member = RoomMember.builder()
            .roomId(roomId)
            .userId(userId)
            .role(MemberRole.MEMBER)
            .status(MemberStatus.ACTIVE)
            .joinTime(LocalDateTime.now())
            .build();
        
        roomMemberRepository.save(member);
        
        // 6. 更新房间当前人数
        room.setCurrentPlayers(currentCount + 1);
        room.setUpdateTime(LocalDateTime.now());
        roomRepository.save(room);
        
        // 7. 检查是否需要自动开始游戏
        if (room.getCurrentPlayers() >= room.getMaxPlayers()) {
            // 人满自动转入准备状态
            room.setStatus(RoomStatus.PREPARING);
            roomRepository.save(room);
        }
        
        // 8. 发送房间变更通知
        roomEventPublisher.publishMemberJoinEvent(roomId, userId);
        
        return RoomJoinResult.success("加入房间成功");
    }
}
```

### 房主权限转移的原子性保证

```java
/**
 * 房主权限转移管理器
 */
@Service
public class RoomHostTransferManager {
    
    /**
     * 原子性房主离开操作
     */
    @Transactional
    public HostLeaveResult hostLeaveRoom(Long roomId, Long hostId) {
        String lockKey = "room:host_transfer:" + roomId;
        RLock lock = redissonClient.getLock(lockKey);
        
        try {
            if (lock.tryLock(5, 15, TimeUnit.SECONDS)) {
                return doHostLeaveRoom(roomId, hostId);
            } else {
                return HostLeaveResult.failure("房主转移操作繁忙");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return HostLeaveResult.failure("操作被中断");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
    
    private HostLeaveResult doHostLeaveRoom(Long roomId, Long hostId) {
        // 1. 验证当前用户是房主
        Room room = roomRepository.findById(roomId)
            .orElseThrow(() -> new BusinessException("房间不存在"));
        
        if (!room.getHostId().equals(hostId)) {
            return HostLeaveResult.failure("您不是房主");
        }
        
        // 2. 查找可以转移的成员（按加入时间排序）
        List<RoomMember> activeMembers = roomMemberRepository
            .findByRoomIdAndStatusAndUserIdNotOrderByJoinTimeAsc(
                roomId, MemberStatus.ACTIVE, hostId);
        
        if (activeMembers.isEmpty()) {
            // 房间没有其他成员，直接解散房间
            return dissolveRoom(roomId, hostId, "房主离开且无其他成员");
        }
        
        // 3. 选择新房主（最早加入的成员）
        RoomMember newHost = activeMembers.get(0);
        
        // 4. 原子性执行权限转移
        // 4.1 更新房间房主
        room.setHostId(newHost.getUserId());
        room.setUpdateTime(LocalDateTime.now());
        roomRepository.save(room);
        
        // 4.2 更新新房主的角色
        newHost.setRole(MemberRole.HOST);
        roomMemberRepository.save(newHost);
        
        // 4.3 移除原房主
        roomMemberRepository.deleteByRoomIdAndUserId(roomId, hostId);
        
        // 4.4 更新房间人数
        room.setCurrentPlayers(room.getCurrentPlayers() - 1);
        roomRepository.save(room);
        
        // 5. 检查房间状态是否需要调整
        checkAndAdjustRoomStatus(room);
        
        // 6. 发送通知
        roomEventPublisher.publishHostTransferEvent(roomId, hostId, newHost.getUserId());
        
        return HostLeaveResult.success("房主权限已转移", newHost.getUserId());
    }
    
    /**
     * 解散房间
     */
    private HostLeaveResult dissolveRoom(Long roomId, Long hostId, String reason) {
        Room room = roomRepository.findById(roomId).orElse(null);
        if (room != null) {
            room.setStatus(RoomStatus.CANCELLED);
            room.setUpdateTime(LocalDateTime.now());
            roomRepository.save(room);
            
            // 清理所有成员记录
            roomMemberRepository.deleteByRoomId(roomId);
            
            roomEventPublisher.publishRoomDissolveEvent(roomId, reason);
        }
        
        return HostLeaveResult.dissolved("房间已解散");
    }
}
```

## 🔄 状态一致性保证机制

### 最终一致性实现

```java
/**
 * 房间状态最终一致性控制器
 */
@Component
public class RoomEventualConsistencyController {
    
    @Autowired
    private RabbitTemplate rabbitTemplate;
    
    /**
     * 发送状态一致性检查消息
     */
    public void scheduleConsistencyCheck(Long roomId, String operation) {
        ConsistencyCheckMessage message = ConsistencyCheckMessage.builder()
            .roomId(roomId)
            .operation(operation)
            .timestamp(System.currentTimeMillis())
            .retryCount(0)
            .build();
        
        // 延迟5秒后执行一致性检查
        rabbitTemplate.convertAndSend(
            RabbitMQConstants.ROOM_CONSISTENCY_EXCHANGE,
            RabbitMQConstants.ROOM_CONSISTENCY_ROUTING_KEY,
            message,
            messageProcessor -> {
                messageProcessor.getMessageProperties().setDelay(5000);
                return messageProcessor;
            }
        );
    }
    
    /**
     * 处理一致性检查消息
     */
    @RabbitListener(queues = RabbitMQConstants.ROOM_CONSISTENCY_QUEUE)
    public void handleConsistencyCheck(ConsistencyCheckMessage message) {
        try {
            boolean isConsistent = checkRoomConsistency(message.getRoomId());
            if (!isConsistent) {
                repairRoomConsistency(message.getRoomId());
            }
        } catch (Exception e) {
            // 重试机制
            if (message.getRetryCount() < 3) {
                message.setRetryCount(message.getRetryCount() + 1);
                rabbitTemplate.convertAndSend(
                    RabbitMQConstants.ROOM_CONSISTENCY_EXCHANGE,
                    RabbitMQConstants.ROOM_CONSISTENCY_ROUTING_KEY,
                    message,
                    messageProcessor -> {
                        messageProcessor.getMessageProperties().setDelay(10000 * message.getRetryCount());
                        return messageProcessor;
                    }
                );
            } else {
                log.error("房间一致性检查失败，达到最大重试次数: roomId={}", message.getRoomId(), e);
            }
        }
    }
    
    /**
     * 检查房间数据一致性
     */
    private boolean checkRoomConsistency(Long roomId) {
        Room room = roomRepository.findById(roomId).orElse(null);
        if (room == null) {
            return true; // 房间不存在，认为是一致的
        }
        
        // 检查人数一致性
        int actualMemberCount = roomMemberRepository.countByRoomIdAndStatus(roomId, MemberStatus.ACTIVE);
        if (room.getCurrentPlayers() != actualMemberCount) {
            log.warn("房间人数不一致: roomId={}, expected={}, actual={}", 
                roomId, room.getCurrentPlayers(), actualMemberCount);
            return false;
        }
        
        // 检查房主存在性
        boolean hostExists = roomMemberRepository.existsByRoomIdAndUserIdAndRole(
            roomId, room.getHostId(), MemberRole.HOST);
        if (!hostExists) {
            log.warn("房间房主不存在: roomId={}, hostId={}", roomId, room.getHostId());
            return false;
        }
        
        // 检查状态逻辑一致性
        if (room.getStatus() == RoomStatus.PREPARING && actualMemberCount < room.getMinPlayers()) {
            log.warn("房间状态与人数不符: roomId={}, status={}, memberCount={}, minPlayers={}", 
                roomId, room.getStatus(), actualMemberCount, room.getMinPlayers());
            return false;
        }
        
        return true;
    }
    
    /**
     * 修复房间数据一致性
     */
    private void repairRoomConsistency(Long roomId) {
        String lockKey = "room:repair:" + roomId;
        RLock lock = redissonClient.getLock(lockKey);
        
        try {
            if (lock.tryLock(3, 10, TimeUnit.SECONDS)) {
                doRepairRoomConsistency(roomId);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
    
    @Transactional
    private void doRepairRoomConsistency(Long roomId) {
        Room room = roomRepository.findById(roomId).orElse(null);
        if (room == null) {
            return;
        }
        
        // 修复人数不一致
        int actualMemberCount = roomMemberRepository.countByRoomIdAndStatus(roomId, MemberStatus.ACTIVE);
        if (room.getCurrentPlayers() != actualMemberCount) {
            room.setCurrentPlayers(actualMemberCount);
            log.info("修复房间人数: roomId={}, corrected to {}", roomId, actualMemberCount);
        }
        
        // 修复房主不存在问题
        boolean hostExists = roomMemberRepository.existsByRoomIdAndUserIdAndRole(
            roomId, room.getHostId(), MemberRole.HOST);
        if (!hostExists && actualMemberCount > 0) {
            // 选择最早加入的成员作为新房主
            RoomMember newHost = roomMemberRepository
                .findByRoomIdAndStatusOrderByJoinTimeAsc(roomId, MemberStatus.ACTIVE)
                .stream()
                .findFirst()
                .orElse(null);
            
            if (newHost != null) {
                room.setHostId(newHost.getUserId());
                newHost.setRole(MemberRole.HOST);
                roomMemberRepository.save(newHost);
                log.info("修复房间房主: roomId={}, newHostId={}", roomId, newHost.getUserId());
            }
        }
        
        // 修复状态不一致问题
        if (room.getStatus() == RoomStatus.PREPARING && actualMemberCount < room.getMinPlayers()) {
            room.setStatus(RoomStatus.RECRUITING);
            log.info("修复房间状态: roomId={}, changed to RECRUITING due to insufficient players", roomId);
        }
        
        room.setUpdateTime(LocalDateTime.now());
        roomRepository.save(room);
        
        // 发送修复完成通知
        roomEventPublisher.publishRoomRepairedEvent(roomId);
    }
}
```

## 📊 缓存一致性策略

### Redis缓存管理

```java
/**
 * 房间缓存一致性管理器
 */
@Service
public class RoomCacheConsistencyManager {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 更新房间缓存
     */
    public void updateRoomCache(Room room) {
        String cacheKey = RedisConstants.ROOM_CACHE_KEY + room.getId();
        
        try {
            // 使用事务确保缓存操作的原子性
            redisTemplate.execute(new SessionCallback<Object>() {
                @Override
                public Object execute(RedisOperations operations) throws DataAccessException {
                    operations.multi();
                    
                    // 更新房间基本信息
                    operations.opsForHash().putAll(cacheKey, convertRoomToHash(room));
                    
                    // 设置过期时间
                    operations.expire(cacheKey, Duration.ofMinutes(30));
                    
                    // 更新房间列表缓存
                    updateRoomListCache(room);
                    
                    return operations.exec();
                }
            });
        } catch (Exception e) {
            log.error("更新房间缓存失败: roomId={}", room.getId(), e);
        }
    }
    
    /**
     * 删除房间缓存
     */
    public void deleteRoomCache(Long roomId) {
        String cacheKey = RedisConstants.ROOM_CACHE_KEY + roomId;
        redisTemplate.delete(cacheKey);
        
        // 同时清理相关的列表缓存
        clearRelatedListCaches(roomId);
    }
    
    /**
     * 缓存双删策略
     */
    public void doubleDeleteCache(Long roomId) {
        // 第一次删除
        deleteRoomCache(roomId);
        
        // 延迟删除，防止脏数据
        CompletableFuture.delayedExecutor(1, TimeUnit.SECONDS)
            .execute(() -> deleteRoomCache(roomId));
    }
}
```

## 🚨 异常场景处理

### 网络分区处理

```java
/**
 * 网络分区恢复处理器
 */
@Component
public class NetworkPartitionRecoveryHandler {
    
    /**
     * 检测到网络分区恢复后的数据修复
     */
    @EventListener
    public void handleNetworkPartitionRecovery(NetworkPartitionRecoveryEvent event) {
        log.info("检测到网络分区恢复，开始数据一致性修复");
        
        // 1. 重新同步所有活跃房间的状态
        List<Room> activeRooms = roomRepository.findByStatusIn(
            Arrays.asList(RoomStatus.RECRUITING, RoomStatus.PREPARING, RoomStatus.PLAYING)
        );
        
        for (Room room : activeRooms) {
            try {
                reconcileRoomState(room.getId());
            } catch (Exception e) {
                log.error("房间状态修复失败: roomId={}", room.getId(), e);
            }
        }
        
        // 2. 清理可能的脏缓存
        clearAllRoomCaches();
        
        // 3. 重建房间索引
        rebuildRoomIndexes();
    }
    
    /**
     * 协调房间状态
     */
    private void reconcileRoomState(Long roomId) {
        String lockKey = "room:reconcile:" + roomId;
        RLock lock = redissonClient.getLock(lockKey);
        
        try {
            if (lock.tryLock(5, 30, TimeUnit.SECONDS)) {
                // 从数据库获取权威状态
                Room dbRoom = roomRepository.findById(roomId).orElse(null);
                if (dbRoom == null) {
                    return;
                }
                
                // 检查并修复数据一致性
                roomConsistencyController.checkAndRepairConsistency(roomId);
                
                // 重新发布房间状态到缓存
                roomCacheManager.refreshRoomCache(roomId);
                
                // 通知所有连接的客户端
                roomEventPublisher.publishRoomStateReconciled(roomId, dbRoom.getStatus());
                
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
```

## 📈 监控与告警

### 数据一致性监控指标

```java
@Component
public class ConsistencyMetricsCollector {
    
    private final MeterRegistry meterRegistry;
    private final Gauge inconsistentRoomsGauge;
    private final Counter repairOperationsCounter;
    
    public ConsistencyMetricsCollector(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        
        this.inconsistentRoomsGauge = Gauge.builder("room.inconsistent.count")
            .description("不一致房间数量")
            .register(meterRegistry, this, ConsistencyMetricsCollector::getInconsistentRoomCount);
        
        this.repairOperationsCounter = Counter.builder("room.repair.operations")
            .description("数据修复操作次数")
            .register(meterRegistry);
    }
    
    /**
     * 获取不一致房间数量
     */
    private double getInconsistentRoomCount() {
        // 实现逻辑：检查所有活跃房间的一致性
        return roomConsistencyChecker.countInconsistentRooms();
    }
    
    /**
     * 记录修复操作
     */
    public void recordRepairOperation(String repairType, boolean success) {
        repairOperationsCounter.increment(
            Tags.of("type", repairType, "success", String.valueOf(success))
        );
    }
}
```

## 🔧 配置参数

```yaml
# 房间系统数据一致性配置
room:
  consistency:
    # 分布式锁配置
    lock:
      wait-time: 3s          # 获取锁最大等待时间
      lease-time: 10s        # 锁持有时间
      retry-interval: 100ms  # 重试间隔
    
    # 一致性检查配置
    check:
      interval: 30s          # 检查间隔
      batch-size: 100        # 批处理大小
      max-retry: 3           # 最大重试次数
    
    # 缓存一致性配置
    cache:
      ttl: 30m              # 缓存过期时间
      double-delete-delay: 1s # 双删延迟时间
```

---

**相关文档**: [实时通信设计](./04_room_system_realtime.md)  
**下一步**: 完成WebSocket通信机制设计