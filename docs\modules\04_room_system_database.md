# 房间系统数据库设计

## 📋 文档信息

**模块**: 房间系统 - 数据库设计  
**版本**: v1.0  
**日期**: 2025-08-03  
**作者**: an  

## 🎯 设计原则

1. **高性能**: 优化查询性能，支持高并发访问
2. **数据一致性**: 确保房间状态和成员关系的一致性
3. **可扩展性**: 支持未来功能扩展
4. **查询友好**: 设计常用查询场景的索引

## 📊 数据库表设计

### 1. 房间主表 (tb_room)

```sql
CREATE TABLE `tb_room` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '房间ID',
  `script_id` bigint(20) NOT NULL COMMENT '剧本ID',
  `host_id` bigint(20) NOT NULL COMMENT '房主用户ID',
  `title` varchar(100) NOT NULL COMMENT '房间标题',
  `status` tinyint(2) NOT NULL DEFAULT 0 COMMENT '房间状态:0-招募中,1-准备中,2-游戏中,3-已结束,4-已取消,5-已暂停',
  `max_players` int(3) NOT NULL COMMENT '最大玩家数',
  `min_players` int(3) NOT NULL DEFAULT 4 COMMENT '最少玩家数',
  `current_players` int(3) NOT NULL DEFAULT 0 COMMENT '当前玩家数',
  `scheduled_start_time` datetime NOT NULL COMMENT '预定开始时间',
  `actual_start_time` datetime NULL COMMENT '实际开始时间',
  `end_time` datetime NULL COMMENT '结束时间',
  `location_type` varchar(20) NOT NULL COMMENT '地点类型:ONLINE-线上,OFFLINE-线下,HYBRID-混合',
  `location_detail` varchar(200) NULL COMMENT '详细地点信息',
  `price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '房间费用',
  `description` text NULL COMMENT '房间描述',
  `requirements` text NULL COMMENT '参与要求',
  `room_config` json NULL COMMENT '房间配置(JSON格式)',
  `timeout_minutes` int(5) NOT NULL DEFAULT 30 COMMENT '房间超时时间(分钟)',
  `auto_start` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否人满自动开始:0-否,1-是',
  `is_private` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否私密房间:0-公开,1-私密',
  `password` varchar(20) NULL COMMENT '房间密码(私密房间)',
  `tags` varchar(500) NULL COMMENT '房间标签,逗号分隔',
  `view_count` int(10) NOT NULL DEFAULT 0 COMMENT '浏览次数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_script_status` (`script_id`, `status`),
  KEY `idx_host_id` (`host_id`),
  KEY `idx_status_start_time` (`status`, `scheduled_start_time`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_location_type` (`location_type`),
  CONSTRAINT `fk_room_script` FOREIGN KEY (`script_id`) REFERENCES `tb_script` (`id`),
  CONSTRAINT `fk_room_host` FOREIGN KEY (`host_id`) REFERENCES `tb_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='房间信息表';
```

### 2. 房间成员表 (tb_room_member)

```sql
CREATE TABLE `tb_room_member` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `room_id` bigint(20) NOT NULL COMMENT '房间ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `role` tinyint(2) NOT NULL DEFAULT 1 COMMENT '角色:0-房主,1-普通成员',
  `status` tinyint(2) NOT NULL DEFAULT 0 COMMENT '状态:0-待审核,1-已加入,2-已拒绝,3-已离开,4-被踢出',
  `character_id` bigint(20) NULL COMMENT '分配的角色ID',
  `character_confirmed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '角色是否确认:0-未确认,1-已确认',
  `join_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
  `leave_time` datetime NULL COMMENT '离开时间',
  `leave_reason` varchar(100) NULL COMMENT '离开原因',
  `apply_message` varchar(200) NULL COMMENT '申请加入留言',
  `ip_address` varchar(45) NULL COMMENT '申请时IP地址',
  `user_agent` varchar(500) NULL COMMENT '申请时User-Agent',
  `extra_data` json NULL COMMENT '扩展数据(JSON格式)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_room_user` (`room_id`, `user_id`),
  KEY `idx_user_status` (`user_id`, `status`),
  KEY `idx_room_status` (`room_id`, `status`),
  KEY `idx_character_id` (`character_id`),
  KEY `idx_join_time` (`join_time`),
  CONSTRAINT `fk_room_member_room` FOREIGN KEY (`room_id`) REFERENCES `tb_room` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_room_member_user` FOREIGN KEY (`user_id`) REFERENCES `tb_user` (`id`),
  CONSTRAINT `fk_room_member_character` FOREIGN KEY (`character_id`) REFERENCES `tb_script_character` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='房间成员表';
```

### 3. 房间消息表 (tb_room_message)

```sql
CREATE TABLE `tb_room_message` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `room_id` bigint(20) NOT NULL COMMENT '房间ID',
  `from_user_id` bigint(20) NULL COMMENT '发送者用户ID(系统消息为空)',
  `message_type` tinyint(2) NOT NULL COMMENT '消息类型:0-系统,1-文字,2-表情,3-图片,4-语音',
  `content` text NOT NULL COMMENT '消息内容',
  `extra_data` json NULL COMMENT '扩展数据(如图片URL、语音时长等)',
  `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否系统消息:0-否,1-是',
  `is_private` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否私聊消息:0-否,1-是',
  `to_user_id` bigint(20) NULL COMMENT '私聊目标用户ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_room_time` (`room_id`, `create_time`),
  KEY `idx_from_user` (`from_user_id`),
  KEY `idx_to_user` (`to_user_id`),
  KEY `idx_message_type` (`message_type`),
  CONSTRAINT `fk_room_message_room` FOREIGN KEY (`room_id`) REFERENCES `tb_room` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_room_message_from_user` FOREIGN KEY (`from_user_id`) REFERENCES `tb_user` (`id`),
  CONSTRAINT `fk_room_message_to_user` FOREIGN KEY (`to_user_id`) REFERENCES `tb_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='房间消息表';
```

### 4. 房间状态历史表 (tb_room_state_history)

```sql
CREATE TABLE `tb_room_state_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '历史记录ID',
  `room_id` bigint(20) NOT NULL COMMENT '房间ID',
  `from_status` tinyint(2) NOT NULL COMMENT '原状态',
  `to_status` tinyint(2) NOT NULL COMMENT '新状态',
  `change_reason` varchar(100) NULL COMMENT '状态变更原因',
  `operator_id` bigint(20) NULL COMMENT '操作者用户ID',
  `change_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '变更时间',
  `extra_info` json NULL COMMENT '额外信息',
  PRIMARY KEY (`id`),
  KEY `idx_room_time` (`room_id`, `change_time`),
  KEY `idx_operator` (`operator_id`),
  KEY `idx_to_status` (`to_status`),
  CONSTRAINT `fk_room_state_room` FOREIGN KEY (`room_id`) REFERENCES `tb_room` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_room_state_operator` FOREIGN KEY (`operator_id`) REFERENCES `tb_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='房间状态变更历史表';
```

### 5. 游戏进程表 (tb_room_game_process)

```sql
CREATE TABLE `tb_room_game_process` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '进程ID',
  `room_id` bigint(20) NOT NULL COMMENT '房间ID',
  `game_phase` varchar(50) NOT NULL COMMENT '游戏阶段',
  `phase_data` json NULL COMMENT '阶段数据',
  `start_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '阶段开始时间',
  `end_time` datetime NULL COMMENT '阶段结束时间',
  `duration_seconds` int(10) NULL COMMENT '阶段持续时间(秒)',
  `is_current` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否当前阶段:0-否,1-是',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_room_current` (`room_id`, `is_current`),
  KEY `idx_room_phase` (`room_id`, `game_phase`),
  KEY `idx_start_time` (`start_time`),
  CONSTRAINT `fk_room_process_room` FOREIGN KEY (`room_id`) REFERENCES `tb_room` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='房间游戏进程表';
```

### 6. 房间评价表 (tb_room_review)

```sql
CREATE TABLE `tb_room_review` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `room_id` bigint(20) NOT NULL COMMENT '房间ID',
  `user_id` bigint(20) NOT NULL COMMENT '评价用户ID',
  `script_rating` tinyint(1) NOT NULL COMMENT '剧本评分(1-5)',
  `host_rating` tinyint(1) NOT NULL COMMENT '房主评分(1-5)',
  `experience_rating` tinyint(1) NOT NULL COMMENT '体验评分(1-5)',
  `overall_rating` decimal(3,1) NOT NULL COMMENT '综合评分',
  `review_content` text NULL COMMENT '评价内容',
  `is_anonymous` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否匿名评价:0-否,1-是',
  `tags` varchar(200) NULL COMMENT '评价标签,逗号分隔',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_room_user` (`room_id`, `user_id`),
  KEY `idx_room_rating` (`room_id`, `overall_rating`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_room_review_room` FOREIGN KEY (`room_id`) REFERENCES `tb_room` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_room_review_user` FOREIGN KEY (`user_id`) REFERENCES `tb_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='房间评价表';
```

## 📈 索引优化策略

### 主要查询场景与索引

| 查询场景 | 使用频率 | 索引设计 | 说明 |
|----------|----------|----------|------|
| 剧本房间列表 | 极高 | `idx_script_status` | 按剧本查询可用房间 |
| 用户房间历史 | 高 | `idx_user_status` | 查询用户参与的房间 |
| 房间状态筛选 | 高 | `idx_status_start_time` | 按状态和时间筛选 |
| 房主房间管理 | 中 | `idx_host_id` | 房主查看自己的房间 |
| 地点类型筛选 | 中 | `idx_location_type` | 按地点类型筛选房间 |
| 房间消息查询 | 高 | `idx_room_time` | 查询房间聊天记录 |
| 状态变更记录 | 低 | `idx_room_time` | 查询房间状态历史 |

### 复合索引设计原则

```sql
-- 覆盖索引设计示例
ALTER TABLE tb_room ADD INDEX idx_search_cover (
    status, 
    location_type, 
    scheduled_start_time, 
    current_players, 
    max_players
);

-- 联合查询优化
ALTER TABLE tb_room_member ADD INDEX idx_room_user_status (
    room_id, 
    user_id, 
    status
);
```

## 🔧 分区策略

### 时间分区设计

```sql
-- 房间消息表按月分区
ALTER TABLE tb_room_message PARTITION BY RANGE (YEAR(create_time) * 100 + MONTH(create_time)) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    PARTITION p202503 VALUES LESS THAN (202504),
    -- ... 预创建12个月分区
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 房间状态历史表按月分区
ALTER TABLE tb_room_state_history PARTITION BY RANGE (YEAR(change_time) * 100 + MONTH(change_time)) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    -- ... 类似设计
);
```

## 📊 数据迁移脚本

### 从现有系统迁移

```sql
-- 1. 房间数据迁移脚本
INSERT INTO tb_room (
    script_id, host_id, title, status, max_players, min_players,
    scheduled_start_time, location_type, price, description,
    create_time, update_time
)
SELECT 
    script_id, 
    creator_id as host_id,
    CONCAT('剧本房间-', script_id) as title,
    CASE 
        WHEN status = 'WAITING' THEN 0
        WHEN status = 'PREPARING' THEN 1
        WHEN status = 'PLAYING' THEN 2
        WHEN status = 'FINISHED' THEN 3
        ELSE 4
    END as status,
    max_players,
    COALESCE(min_players, 4) as min_players,
    start_time as scheduled_start_time,
    'ONLINE' as location_type,
    COALESCE(price, 0) as price,
    description,
    create_time,
    update_time
FROM legacy_room_table
WHERE create_time >= '2025-01-01';

-- 2. 成员数据迁移脚本
INSERT INTO tb_room_member (
    room_id, user_id, role, status, join_time, create_time
)
SELECT 
    r.id as room_id,
    lrm.user_id,
    CASE WHEN lrm.user_id = r.host_id THEN 0 ELSE 1 END as role,
    CASE 
        WHEN lrm.status = 'ACTIVE' THEN 1
        WHEN lrm.status = 'LEFT' THEN 3
        ELSE 1
    END as status,
    lrm.join_time,
    lrm.create_time
FROM legacy_room_member lrm
INNER JOIN tb_room r ON r.script_id = lrm.script_id 
    AND r.host_id = lrm.room_creator_id;
```

## 🚀 性能优化建议

### 1. 查询优化

```sql
-- 使用覆盖索引的房间列表查询
SELECT id, title, current_players, max_players, scheduled_start_time, status
FROM tb_room 
WHERE script_id = ? 
  AND status IN (0, 1) 
  AND scheduled_start_time > NOW()
ORDER BY create_time DESC
LIMIT 20;

-- 优化的成员查询
SELECT rm.*, u.username, u.avatar
FROM tb_room_member rm
INNER JOIN tb_user u ON rm.user_id = u.id
WHERE rm.room_id = ? AND rm.status = 1
ORDER BY rm.join_time ASC;
```

### 2. 批量操作优化

```sql
-- 批量更新房间人数
UPDATE tb_room r 
SET current_players = (
    SELECT COUNT(*) 
    FROM tb_room_member rm 
    WHERE rm.room_id = r.id AND rm.status = 1
)
WHERE r.id IN (?, ?, ?, ...);
```

### 3. 缓存策略

```java
// Redis缓存key设计
public class RoomCacheKeys {
    public static final String ROOM_INFO = "room:info:%d";           // 房间基本信息
    public static final String ROOM_MEMBERS = "room:members:%d";     // 房间成员列表
    public static final String ROOM_STATUS = "room:status:%d";       // 房间状态
    public static final String USER_ROOMS = "user:rooms:%d";         // 用户房间列表
    public static final String SCRIPT_ROOMS = "script:rooms:%d";     // 剧本房间列表
    
    // 缓存过期时间
    public static final Duration ROOM_INFO_TTL = Duration.ofMinutes(30);
    public static final Duration ROOM_MEMBERS_TTL = Duration.ofMinutes(10);
    public static final Duration ROOM_STATUS_TTL = Duration.ofMinutes(5);
}
```

## 📋 数据一致性保证

### 事务处理示例

```sql
-- 加入房间的原子性操作
START TRANSACTION;

-- 1. 检查房间状态和人数
SELECT status, current_players, max_players 
FROM tb_room 
WHERE id = ? AND status = 0 
FOR UPDATE;

-- 2. 插入成员记录
INSERT INTO tb_room_member (room_id, user_id, role, status, join_time)
VALUES (?, ?, 1, 1, NOW());

-- 3. 更新房间人数
UPDATE tb_room 
SET current_players = current_players + 1,
    update_time = NOW()
WHERE id = ?;

-- 4. 检查是否需要状态变更
UPDATE tb_room 
SET status = 1, update_time = NOW()
WHERE id = ? AND current_players >= max_players;

COMMIT;
```

## 🔍 监控指标

### 数据库性能监控

```sql
-- 创建监控视图
CREATE VIEW v_room_stats AS
SELECT 
    DATE(create_time) as date,
    COUNT(*) as total_rooms,
    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as recruiting_rooms,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as preparing_rooms,
    SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as playing_rooms,
    SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as finished_rooms,
    AVG(current_players) as avg_players
FROM tb_room
WHERE create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(create_time)
ORDER BY date DESC;
```

## 🛠️ 维护脚本

### 定期清理脚本

```sql
-- 清理90天前的房间消息
DELETE FROM tb_room_message 
WHERE create_time < DATE_SUB(NOW(), INTERVAL 90 DAY);

-- 清理已取消房间的相关数据(保留30天)
DELETE rm FROM tb_room_member rm
INNER JOIN tb_room r ON rm.room_id = r.id
WHERE r.status = 4 
  AND r.update_time < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 归档已结束的房间数据
INSERT INTO tb_room_archive 
SELECT * FROM tb_room 
WHERE status = 3 
  AND end_time < DATE_SUB(NOW(), INTERVAL 180 DAY);
```

---

**相关文档**: [异常处理策略](./04_room_system_exception_handling.md)  
**下一步**: 完成API接口设计文档