# 房间系统异常处理策略

## 📋 文档信息

**模块**: 房间系统 - 异常处理  
**版本**: v1.0  
**日期**: 2025-08-03  
**作者**: an  

## 🎯 异常处理目标

1. **系统稳定性**: 确保异常情况下系统不崩溃
2. **数据一致性**: 异常发生时保持数据完整性
3. **用户体验**: 提供友好的错误提示和恢复机制
4. **可监控性**: 完善的异常监控和告警机制

## 🚨 异常场景分析

### 1. 网络异常场景

#### WebSocket连接中断

**场景描述**:
- 用户网络不稳定导致WebSocket连接中断
- 服务器重启导致所有连接断开
- 负载均衡切换导致连接丢失

**处理策略**:

```java
/**
 * WebSocket连接异常处理器
 */
@Component
public class WebSocketExceptionHandler {
    
    @Autowired
    private RoomWebSocketManager webSocketManager;
    
    @Autowired
    private RoomStateManager roomStateManager;
    
    /**
     * 处理连接中断
     */
    @EventListener
    public void handleConnectionLost(WebSocketConnectionLostEvent event) {
        Long userId = event.getUserId();
        String reason = event.getReason();
        
        log.warn("WebSocket连接中断: userId={}, reason={}", userId, reason);
        
        try {
            // 1. 清理连接状态
            webSocketManager.forceRemoveConnection(userId);
            
            // 2. 标记用户为离线状态
            markUserOffline(userId);
            
            // 3. 检查用户所在房间状态
            List<Long> userRooms = getUserActiveRooms(userId);
            for (Long roomId : userRooms) {
                handleUserDisconnectFromRoom(roomId, userId);
            }
            
        } catch (Exception e) {
            log.error("处理WebSocket连接中断异常失败: userId={}", userId, e);
        }
    }
    
    /**
     * 处理用户从房间断开连接
     */
    private void handleUserDisconnectFromRoom(Long roomId, Long userId) {
        try {
            Room room = roomRepository.findById(roomId).orElse(null);
            if (room == null) {
                return;
            }
            
            // 根据房间状态和用户角色采取不同策略
            switch (room.getStatus()) {
                case RECRUITING:
                    // 招募阶段：给用户5分钟重连时间
                    scheduleUserReconnectCheck(roomId, userId, 5 * 60 * 1000);
                    break;
                    
                case PREPARING:
                    // 准备阶段：给用户3分钟重连时间
                    scheduleUserReconnectCheck(roomId, userId, 3 * 60 * 1000);
                    break;
                    
                case PLAYING:
                    // 游戏中：立即暂停游戏，等待重连
                    pauseGameForDisconnection(roomId, userId);
                    break;
                    
                default:
                    // 其他状态：直接移除用户
                    removeUserFromRoom(roomId, userId, "连接中断");
            }
            
        } catch (Exception e) {
            log.error("处理用户房间断连失败: roomId={}, userId={}", roomId, userId, e);
        }
    }
    
    /**
     * 暂停游戏等待重连
     */
    private void pauseGameForDisconnection(Long roomId, Long userId) {
        // 检查是否是关键玩家（房主或当前发言人等）
        boolean isKeyPlayer = checkIfKeyPlayer(roomId, userId);
        
        if (isKeyPlayer) {
            // 关键玩家断线，暂停游戏
            roomStateManager.changeRoomState(roomId, RoomStatus.PLAYING, 
                RoomStatus.SUSPENDED, "关键玩家断线");
            
            // 通知其他玩家
            WebSocketMessage message = WebSocketMessage.builder()
                .type(MessageType.SYSTEM)
                .content("游戏已暂停，等待玩家重新连接...")
                .roomId(roomId)
                .timestamp(System.currentTimeMillis())
                .build();
            
            webSocketManager.broadcastToRoom(roomId, message);
            
            // 设置重连超时时间（2分钟）
            scheduleGameResumeTimeout(roomId, userId, 2 * 60 * 1000);
        }
    }
}
```

#### API请求超时

**场景描述**:
- 数据库连接池耗尽导致请求超时
- 外部服务调用超时
- 高并发导致响应缓慢

**处理策略**:

```java
/**
 * API超时异常处理
 */
@ControllerAdvice
public class ApiTimeoutExceptionHandler {
    
    /**
     * 处理请求超时异常
     */
    @ExceptionHandler(TimeoutException.class)
    public ResponseEntity<ApiResponse<Void>> handleTimeout(TimeoutException e) {
        log.error("API请求超时", e);
        
        ApiResponse<Void> response = ApiResponse.error(
            ErrorCode.REQUEST_TIMEOUT, 
            "请求超时，请稍后重试"
        );
        
        return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT).body(response);
    }
    
    /**
     * 处理数据库连接超时
     */
    @ExceptionHandler(DataAccessException.class)
    public ResponseEntity<ApiResponse<Void>> handleDataAccessTimeout(DataAccessException e) {
        log.error("数据库访问异常", e);
        
        // 检查是否是连接超时
        if (e.getCause() instanceof SQLTimeoutException) {
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                .body(ApiResponse.error(ErrorCode.DATABASE_TIMEOUT, "服务暂时不可用，请稍后重试"));
        }
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(ApiResponse.error(ErrorCode.DATABASE_ERROR, "数据访问错误"));
    }
}
```

### 2. 业务逻辑异常

#### 房间状态冲突

**场景描述**:
- 多用户同时操作导致状态不一致
- 房间状态与实际情况不符
- 并发修改导致数据冲突

**处理策略**:

```java
/**
 * 房间状态冲突处理器
 */
@Service
public class RoomStateConflictHandler {
    
    /**
     * 处理状态冲突异常
     */
    @EventListener
    public void handleStateConflict(RoomStateConflictEvent event) {
        Long roomId = event.getRoomId();
        RoomStatus expectedStatus = event.getExpectedStatus();
        RoomStatus actualStatus = event.getActualStatus();
        
        log.warn("房间状态冲突: roomId={}, expected={}, actual={}", 
            roomId, expectedStatus, actualStatus);
        
        try {
            // 1. 获取房间当前真实状态
            Room room = roomRepository.findById(roomId).orElse(null);
            if (room == null) {
                log.error("房间不存在: roomId={}", roomId);
                return;
            }
            
            // 2. 分析冲突原因
            StateConflictAnalysis analysis = analyzeStateConflict(room, expectedStatus, actualStatus);
            
            // 3. 根据分析结果采取修复策略
            switch (analysis.getConflictType()) {
                case CONCURRENT_MODIFICATION:
                    handleConcurrentModification(room, analysis);
                    break;
                case DATA_INCONSISTENCY:
                    handleDataInconsistency(room, analysis);
                    break;
                case TIMING_ISSUE:
                    handleTimingIssue(room, analysis);
                    break;
                default:
                    handleUnknownConflict(room, analysis);
            }
            
        } catch (Exception e) {
            log.error("处理房间状态冲突失败: roomId={}", roomId, e);
        }
    }
    
    /**
     * 处理并发修改冲突
     */
    private void handleConcurrentModification(Room room, StateConflictAnalysis analysis) {
        // 使用分布式锁重新执行操作
        roomLockService.executeWithRoomLock(room.getId(), () -> {
            // 重新获取最新状态
            Room latestRoom = roomRepository.findById(room.getId()).orElse(null);
            if (latestRoom == null) {
                return null;
            }
            
            // 验证状态转换是否仍然有效
            if (isStateTransitionValid(latestRoom, analysis.getTargetStatus())) {
                // 执行状态转换
                roomStateManager.forceChangeRoomState(
                    room.getId(), 
                    latestRoom.getStatus(), 
                    analysis.getTargetStatus(), 
                    "冲突修复"
                );
            }
            
            return null;
        });
    }
    
    /**
     * 处理数据不一致
     */
    private void handleDataInconsistency(Room room, StateConflictAnalysis analysis) {
        // 触发数据一致性修复
        roomConsistencyManager.scheduleConsistencyRepair(room.getId());
        
        // 通知相关用户
        WebSocketMessage message = WebSocketMessage.builder()
            .type(MessageType.SYSTEM)
            .content("房间状态正在同步，请稍等...")
            .roomId(room.getId())
            .timestamp(System.currentTimeMillis())
            .build();
        
        webSocketManager.broadcastToRoom(room.getId(), message);
    }
}
```

#### 人数限制冲突

**场景描述**:
- 多人同时加入导致超过房间人数限制
- 房间人数统计与实际不符
- 人员变动过程中的并发问题

**处理策略**:

```java
/**
 * 人数限制冲突处理器
 */
@Service
public class RoomCapacityConflictHandler {
    
    /**
     * 处理超员问题
     */
    public void handleOverCapacity(Long roomId) {
        roomLockService.executeWithMemberLock(roomId, () -> {
            Room room = roomRepository.findById(roomId).orElse(null);
            if (room == null) {
                return null;
            }
            
            // 获取实际成员数量
            int actualMemberCount = roomMemberRepository
                .countByRoomIdAndStatus(roomId, MemberStatus.ACTIVE);
            
            if (actualMemberCount <= room.getMaxPlayers()) {
                // 更新房间人数统计
                room.setCurrentPlayers(actualMemberCount);
                roomRepository.save(room);
                return null;
            }
            
            // 确实超员，需要移除多余成员
            List<RoomMember> members = roomMemberRepository
                .findByRoomIdAndStatusOrderByJoinTimeDesc(roomId, MemberStatus.ACTIVE);
            
            int removeCount = actualMemberCount - room.getMaxPlayers();
            List<RoomMember> toRemove = members.subList(0, removeCount);
            
            for (RoomMember member : toRemove) {
                // 移除最后加入的成员
                removeUserFromRoom(roomId, member.getUserId(), "房间超员自动移除");
                
                // 发送通知
                sendOverCapacityNotification(member.getUserId(), roomId);
            }
            
            return null;
        });
    }
    
    /**
     * 发送超员通知
     */
    private void sendOverCapacityNotification(Long userId, Long roomId) {
        WebSocketMessage message = WebSocketMessage.builder()
            .type(MessageType.SYSTEM)
            .content("抱歉，由于房间超员，您已被自动移出房间")
            .timestamp(System.currentTimeMillis())
            .extra(Map.of("roomId", roomId, "reason", "OVER_CAPACITY"))
            .build();
        
        webSocketManager.sendToUser(userId, message);
    }
}
```

### 3. 系统级异常

#### 内存溢出处理

**场景描述**:
- WebSocket连接过多导致内存不足
- 缓存数据过多导致OOM
- 大量并发请求导致内存溢出

**处理策略**:

```java
/**
 * 内存管理异常处理器
 */
@Component
public class MemoryExceptionHandler {
    
    @EventListener
    public void handleOutOfMemoryError(OutOfMemoryErrorEvent event) {
        log.error("系统内存不足，开始紧急清理");
        
        try {
            // 1. 清理过期的WebSocket连接
            webSocketManager.cleanupStaleConnections();
            
            // 2. 清理Redis缓存中的过期数据
            redisTemplate.execute((RedisCallback<Void>) connection -> {
                connection.flushDb(); // 紧急情况下清空缓存
                return null;
            });
            
            // 3. 强制垃圾回收
            System.gc();
            
            // 4. 发送告警通知
            alertService.sendAlert(AlertType.SYSTEM_CRITICAL, "系统内存不足，已执行紧急清理");
            
        } catch (Exception e) {
            log.error("内存异常处理失败", e);
        }
    }
    
    /**
     * WebSocket连接数量监控
     */
    @Scheduled(fixedDelay = 30000)
    public void monitorWebSocketConnections() {
        int connectionCount = webSocketManager.getActiveConnectionCount();
        
        if (connectionCount > MAX_WEBSOCKET_CONNECTIONS) {
            log.warn("WebSocket连接数过多: {}", connectionCount);
            
            // 清理最老的连接
            webSocketManager.cleanupOldestConnections(connectionCount - MAX_WEBSOCKET_CONNECTIONS);
        }
    }
}
```

#### 数据库连接池耗尽

**场景描述**:
- 高并发导致数据库连接池满
- 长时间运行的查询占用连接
- 连接泄露导致可用连接减少

**处理策略**:

```java
/**
 * 数据库连接异常处理器
 */
@Component
public class DatabaseConnectionExceptionHandler {
    
    @EventListener
    public void handleConnectionPoolExhaustion(ConnectionPoolExhaustionEvent event) {
        log.error("数据库连接池耗尽");
        
        try {
            // 1. 启用降级模式
            enableDegradedMode();
            
            // 2. 清理长时间运行的查询
            killLongRunningQueries();
            
            // 3. 暂停非关键业务
            pauseNonCriticalOperations();
            
            // 4. 发送告警
            alertService.sendAlert(AlertType.DATABASE_CRITICAL, "数据库连接池耗尽");
            
        } catch (Exception e) {
            log.error("处理数据库连接异常失败", e);
        }
    }
    
    /**
     * 启用降级模式
     */
    private void enableDegradedMode() {
        // 设置降级标志
        redisTemplate.opsForValue().set("system:degraded_mode", "true", Duration.ofMinutes(10));
        
        // 关闭非关键功能
        systemConfigService.disableNonCriticalFeatures();
        
        log.info("系统已进入降级模式");
    }
}
```

## 🔄 异常恢复机制

### 自动恢复策略

```java
/**
 * 异常自动恢复服务
 */
@Service
public class ExceptionRecoveryService {
    
    /**
     * 房间状态自动恢复
     */
    @Scheduled(fixedDelay = 60000) // 每分钟检查一次
    public void autoRecoverRoomStates() {
        try {
            // 查找异常状态的房间
            List<Room> abnormalRooms = findAbnormalRooms();
            
            for (Room room : abnormalRooms) {
                try {
                    recoverRoomState(room);
                } catch (Exception e) {
                    log.error("房间状态恢复失败: roomId={}", room.getId(), e);
                }
            }
            
        } catch (Exception e) {
            log.error("自动恢复检查失败", e);
        }
    }
    
    /**
     * 查找异常状态的房间
     */
    private List<Room> findAbnormalRooms() {
        List<Room> abnormalRooms = new ArrayList<>();
        
        // 1. 查找超时的招募房间
        LocalDateTime recruitTimeout = LocalDateTime.now().minusMinutes(60);
        abnormalRooms.addAll(roomRepository.findByStatusAndCreateTimeBefore(
            RoomStatus.RECRUITING, recruitTimeout));
        
        // 2. 查找超时的准备房间
        LocalDateTime prepareTimeout = LocalDateTime.now().minusMinutes(30);
        abnormalRooms.addAll(roomRepository.findByStatusAndUpdateTimeBefore(
            RoomStatus.PREPARING, prepareTimeout));
        
        // 3. 查找长时间暂停的房间
        LocalDateTime suspendTimeout = LocalDateTime.now().minusMinutes(10);
        abnormalRooms.addAll(roomRepository.findByStatusAndUpdateTimeBefore(
            RoomStatus.SUSPENDED, suspendTimeout));
        
        return abnormalRooms;
    }
    
    /**
     * 恢复房间状态
     */
    private void recoverRoomState(Room room) {
        switch (room.getStatus()) {
            case RECRUITING:
                // 招募超时，取消房间
                roomStateManager.changeRoomState(
                    room.getId(), RoomStatus.RECRUITING, RoomStatus.CANCELLED, "招募超时自动取消");
                break;
                
            case PREPARING:
                // 准备超时，根据人数决定
                int memberCount = roomMemberRepository.countByRoomIdAndStatus(
                    room.getId(), MemberStatus.ACTIVE);
                if (memberCount >= room.getMinPlayers()) {
                    // 人数够，强制开始
                    roomStateManager.changeRoomState(
                        room.getId(), RoomStatus.PREPARING, RoomStatus.PLAYING, "准备超时强制开始");
                } else {
                    // 人数不够，回到招募
                    roomStateManager.changeRoomState(
                        room.getId(), RoomStatus.PREPARING, RoomStatus.RECRUITING, "准备超时回到招募");
                }
                break;
                
            case SUSPENDED:
                // 暂停超时，取消游戏
                roomStateManager.changeRoomState(
                    room.getId(), RoomStatus.SUSPENDED, RoomStatus.CANCELLED, "暂停超时自动取消");
                break;
        }
    }
}
```

### 手动干预机制

```java
/**
 * 管理员干预控制器
 */
@RestController
@RequestMapping("/api/admin/room")
@PreAuthorize("hasRole('ADMIN')")
public class RoomAdminController {
    
    /**
     * 强制修复房间状态
     */
    @PostMapping("/{roomId}/force-repair")
    public ApiResponse<Void> forceRepairRoom(@PathVariable Long roomId,
                                           @RequestBody RoomRepairRequest request) {
        try {
            Room room = roomRepository.findById(roomId)
                .orElseThrow(() -> new BusinessException("房间不存在"));
            
            // 记录管理员操作
            adminOperationLogger.log(getCurrentUserId(), "FORCE_REPAIR_ROOM", 
                Map.of("roomId", roomId, "request", request));
            
            // 执行修复操作
            switch (request.getRepairType()) {
                case STATE_RESET:
                    forceResetRoomState(room, request.getTargetStatus());
                    break;
                case MEMBER_SYNC:
                    forceSyncRoomMembers(room);
                    break;
                case DATA_REPAIR:
                    forceRepairRoomData(room);
                    break;
            }
            
            return ApiResponse.success();
            
        } catch (Exception e) {
            log.error("强制修复房间失败: roomId={}", roomId, e);
            return ApiResponse.error("修复失败: " + e.getMessage());
        }
    }
    
    /**
     * 强制解散房间
     */
    @PostMapping("/{roomId}/force-dissolve")
    public ApiResponse<Void> forceDissolveRoom(@PathVariable Long roomId,
                                             @RequestParam String reason) {
        try {
            // 记录管理员操作
            adminOperationLogger.log(getCurrentUserId(), "FORCE_DISSOLVE_ROOM", 
                Map.of("roomId", roomId, "reason", reason));
            
            // 强制解散房间
            roomStateManager.forceDissolveRoom(roomId, reason);
            
            return ApiResponse.success();
            
        } catch (Exception e) {
            log.error("强制解散房间失败: roomId={}", roomId, e);
            return ApiResponse.error("解散失败: " + e.getMessage());
        }
    }
}
```

## 📊 异常监控与告警

### 异常指标收集

```java
/**
 * 异常指标收集器
 */
@Component
public class ExceptionMetricsCollector {
    
    private final MeterRegistry meterRegistry;
    private final Counter roomExceptionCounter;
    private final Gauge abnormalRoomsGauge;
    
    public ExceptionMetricsCollector(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        
        this.roomExceptionCounter = Counter.builder("room.exceptions")
            .description("房间系统异常次数")
            .register(meterRegistry);
        
        this.abnormalRoomsGauge = Gauge.builder("room.abnormal.count")
            .description("异常状态房间数量")
            .register(meterRegistry, this, ExceptionMetricsCollector::getAbnormalRoomCount);
    }
    
    /**
     * 记录异常
     */
    public void recordException(String exceptionType, String component, Throwable throwable) {
        roomExceptionCounter.increment(
            Tags.of(
                "type", exceptionType,
                "component", component,
                "exception", throwable.getClass().getSimpleName()
            )
        );
    }
    
    /**
     * 获取异常房间数量
     */
    private double getAbnormalRoomCount() {
        return roomRepository.countAbnormalRooms();
    }
}
```

### 告警规则配置

```yaml
# 异常告警配置
room:
  alerts:
    # WebSocket连接异常
    websocket:
      connection-loss-rate-threshold: 0.1  # 连接丢失率阈值10%
      max-connection-count: 10000          # 最大连接数
      
    # 房间状态异常
    room-state:
      abnormal-room-threshold: 100         # 异常房间数阈值
      timeout-check-interval: 60s         # 超时检查间隔
      
    # 系统资源异常
    system:
      memory-usage-threshold: 0.85         # 内存使用率阈值85%
      db-connection-threshold: 0.9         # 数据库连接使用率阈值90%
      
    # 通知方式
    notification:
      email: ["<EMAIL>"]
      webhook: "https://webhook.example.com/alerts"
      sms: ["+86138xxxxxxxx"]
```

## 🔧 异常处理最佳实践

### 1. 异常分类处理

```java
/**
 * 异常分类处理器
 */
@Component
public class ExceptionClassificationHandler {
    
    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public void handleBusinessException(BusinessException e) {
        // 业务异常通常不需要重试，直接返回错误信息给用户
        log.warn("业务异常: {}", e.getMessage());
        
        // 记录异常指标
        exceptionMetricsCollector.recordException("BUSINESS", "ROOM_SERVICE", e);
    }
    
    /**
     * 处理系统异常
     */
    @ExceptionHandler(SystemException.class)
    public void handleSystemException(SystemException e) {
        // 系统异常需要重试和告警
        log.error("系统异常", e);
        
        // 记录异常指标
        exceptionMetricsCollector.recordException("SYSTEM", "ROOM_SERVICE", e);
        
        // 发送告警
        alertService.sendAlert(AlertType.SYSTEM_ERROR, e.getMessage());
        
        // 尝试自动恢复
        exceptionRecoveryService.attemptRecovery(e);
    }
}
```

### 2. 重试机制

```java
/**
 * 重试配置
 */
@Configuration
@EnableRetry
public class RetryConfig {
    
    @Bean
    @Primary
    public RetryTemplate retryTemplate() {
        RetryTemplate retryTemplate = new RetryTemplate();
        
        // 重试策略：最多重试3次，指数退避
        ExponentialBackOffPolicy backOffPolicy = new ExponentialBackOffPolicy();
        backOffPolicy.setInitialInterval(1000);  // 初始延迟1秒
        backOffPolicy.setMultiplier(2.0);        // 每次延迟翻倍
        backOffPolicy.setMaxInterval(10000);     // 最大延迟10秒
        
        SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy();
        retryPolicy.setMaxAttempts(3);
        
        retryTemplate.setBackOffPolicy(backOffPolicy);
        retryTemplate.setRetryPolicy(retryPolicy);
        
        return retryTemplate;
    }
}

/**
 * 重试注解使用示例
 */
@Service
public class RoomService {
    
    @Retryable(
        value = {DataAccessException.class, RedisConnectionException.class},
        maxAttempts = 3,
        backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public void createRoom(CreateRoomRequest request) {
        // 可能失败的操作
    }
    
    @Recover
    public void recoverCreateRoom(DataAccessException e, CreateRoomRequest request) {
        // 重试失败后的恢复操作
        log.error("创建房间最终失败: {}", request, e);
        
        // 发送失败通知
        notificationService.sendCreateRoomFailure(request.getUserId(), e.getMessage());
    }
}
```

---

**相关文档**: [房间系统概述](./04_room_system_overview.md)  
**下一步**: 准备开始实现房间系统后端API