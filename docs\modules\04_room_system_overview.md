# 剧本杀房间系统设计概述

## 📋 文档信息

**模块名称**: 房间系统 (Room System)  
**文档版本**: v1.0  
**创建日期**: 2025-08-03  
**作者**: an  
**状态**: 设计阶段  

## 🎯 系统概述

房间系统是剧本杀平台的核心模块，负责组织玩家进行游戏的完整生命周期管理。系统统一了"拼团"和"房间"概念，通过房间状态流转实现从玩家招募到游戏结束的完整流程。

## 🏗️ 系统架构

### 核心概念定义

**房间 (Room)**: 游戏的基本组织单位，包含完整的生命周期管理  
**房主 (Host)**: 房间创建者，拥有房间管理权限  
**成员 (Member)**: 加入房间的普通玩家  
**状态流转**: 房间在不同阶段的状态变化和管理

### 业务流程图

```
用户创建房间 → 设置房间信息 → 发布招募 → 玩家申请加入 → 房主审核
     ↓                                                          ↓
房间进入招募状态 ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← 
     ↓
人员满员/房主手动开始 → 角色分配 → 规则确认 → 开始游戏 → 游戏进行 → 游戏结束
     ↓                                              ↓
房间进入准备状态 → → → → → → → → → → → → → → → → → → → 房间归档
```

## 🔄 房间状态设计

### 状态枚举定义

| 状态码 | 状态名称 | 中文描述 | 说明 |
|--------|----------|----------|------|
| 0 | RECRUITING | 招募中 | 房间创建后，等待玩家加入 |
| 1 | PREPARING | 准备中 | 人员确定，进行角色分配和规则设定 |
| 2 | PLAYING | 游戏中 | 正在进行游戏 |
| 3 | FINISHED | 已结束 | 游戏完成，等待评价和结算 |
| 4 | CANCELLED | 已取消 | 房主解散或系统超时取消 |
| 5 | SUSPENDED | 已暂停 | 临时暂停，可恢复 |

### 状态流转规则

```
RECRUITING → PREPARING  (人员满足 || 房主手动开始)
RECRUITING → CANCELLED  (房主解散 || 超时 || 人员不足)

PREPARING → PLAYING     (角色分配完成 && 规则确认)
PREPARING → RECRUITING  (有人离开导致人员不足)
PREPARING → CANCELLED   (房主解散 || 超时)

PLAYING → FINISHED      (游戏正常结束)
PLAYING → SUSPENDED     (临时暂停)
PLAYING → CANCELLED     (异常中断)

SUSPENDED → PLAYING     (恢复游戏)
SUSPENDED → CANCELLED   (放弃游戏)

FINISHED → (不可逆状态)
CANCELLED → (不可逆状态)
```

## 👥 玩家角色设计

### 房主权限

- **房间管理**: 修改房间信息、解散房间
- **成员管理**: 审核申请、踢出成员、转让房主
- **游戏控制**: 开始游戏、分配角色、暂停/恢复游戏
- **时间控制**: 延长准备时间、调整游戏时间

### 成员权限

- **基础操作**: 查看房间信息、发送消息
- **角色选择**: 查看可选角色、表达角色偏好
- **退出权限**: 主动离开房间

### 角色转换规则

1. **房主离开**: 自动转让给最早加入的成员
2. **房主转让**: 房主可主动指定新房主
3. **成员离开**: 根据房间状态采取不同处理策略

## 📊 核心数据结构

### 房间实体 (Room Entity)

```java
@Entity
@Table(name = "tb_room")
public class Room {
    private Long id;                    // 房间ID
    private Long scriptId;              // 剧本ID
    private Long hostId;                // 房主ID
    private String title;               // 房间标题
    private RoomStatus status;          // 房间状态
    private Integer maxPlayers;         // 最大人数
    private Integer currentPlayers;     // 当前人数
    private Integer minPlayers;         // 最少人数
    private LocalDateTime startTime;    // 预定开始时间
    private LocalDateTime actualStartTime; // 实际开始时间
    private LocalDateTime endTime;      // 结束时间
    private String location;            // 游戏地点
    private BigDecimal price;           // 费用
    private String description;         // 房间描述
    private String requirements;        // 参与要求
    private Integer roomTimeout;        // 房间超时时间(分钟)
    private LocalDateTime createTime;   // 创建时间
    private LocalDateTime updateTime;   // 更新时间
}
```

### 房间成员关系 (RoomMember Entity)

```java
@Entity
@Table(name = "tb_room_member")
public class RoomMember {
    private Long id;                    // 主键
    private Long roomId;                // 房间ID
    private Long userId;                // 用户ID
    private MemberRole role;            // 成员角色(HOST/MEMBER)
    private MemberStatus status;        // 成员状态
    private Long characterId;           // 分配的角色ID
    private LocalDateTime joinTime;     // 加入时间
    private LocalDateTime leaveTime;    // 离开时间
    private String leaveReason;         // 离开原因
}
```

## 🚨 关键挑战与解决方案

### 1. 并发操作问题

**挑战**: 多用户同时加入/离开房间时的数据一致性  
**解决方案**: Redis分布式锁 + 乐观锁机制

### 2. 状态同步问题

**挑战**: 房间状态变化需要实时通知所有成员  
**解决方案**: WebSocket + Redis Pub/Sub

### 3. 异常处理问题

**挑战**: 网络中断、用户掉线等异常情况处理  
**解决方案**: 心跳检测 + 自动重连 + 状态恢复

### 4. 性能优化问题

**挑战**: 大量房间并发访问的性能瓶颈  
**解决方案**: 多级缓存 + 数据库连接池 + 异步处理

## 📈 性能指标要求

| 指标项 | 目标值 | 备注 |
|--------|--------|------|
| 房间创建响应时间 | < 500ms | P95 |
| 加入房间响应时间 | < 300ms | P95 |
| 状态同步延迟 | < 100ms | WebSocket |
| 并发房间数 | 1000+ | 系统支持 |
| 单房间最大人数 | 20人 | 业务限制 |
| 房间状态一致性 | 99.9%+ | 数据准确性 |

## 🔗 相关文档链接

- [房间状态管理详细设计](./04_room_system_state_management.md)
- [分布式锁与数据一致性](./04_room_system_consistency.md)
- [实时通信设计](./04_room_system_realtime.md)
- [异常处理策略](./04_room_system_exception_handling.md)
- [数据库设计详细说明](./04_room_system_database.md)

---

**下一步计划**: 完成详细设计文档后，开始后端API开发
**预计完成时间**: 2025-08-10
**开发优先级**: 高