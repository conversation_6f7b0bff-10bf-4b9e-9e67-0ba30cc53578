# 房间系统实时通信设计

## 📋 文档信息

**模块**: 房间系统 - 实时通信  
**版本**: v1.0  
**日期**: 2025-08-03  
**作者**: an  

## 🎯 实时通信需求

### 核心场景

1. **房间状态变化通知** - 招募中→准备中→游戏中→已结束
2. **成员变动通知** - 有人加入、离开、房主变更
3. **角色分配通知** - 角色分配、角色确认
4. **游戏进度同步** - 游戏阶段、计时器、投票结果
5. **聊天消息推送** - 房间内文字聊天、表情互动
6. **系统消息推送** - 异常提醒、规则提示

### 技术要求

- **低延迟**: 消息推送延迟 < 100ms
- **高并发**: 支持单房间20人同时在线
- **高可用**: 99.9%连接稳定性
- **消息可靠**: 关键消息确保到达

## 🔧 WebSocket架构设计

### 整体架构图

```
前端WebSocket客户端 ←→ Nginx(负载均衡) ←→ Spring Boot WebSocket服务
                                              ↓
                                        Redis Pub/Sub
                                              ↓
                                        消息分发中心 ←→ 数据库
```

### WebSocket连接管理

```java
/**
 * WebSocket连接管理器
 */
@Component
public class RoomWebSocketManager {
    
    // 存储所有WebSocket连接 - ConcurrentHashMap确保线程安全
    private final Map<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    
    // 用户ID到SessionID的映射
    private final Map<Long, String> userSessionMap = new ConcurrentHashMap<>();
    
    // 房间ID到用户列表的映射
    private final Map<Long, Set<Long>> roomUsersMap = new ConcurrentHashMap<>();
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 用户连接WebSocket
     */
    public void addConnection(String sessionId, Long userId, WebSocketSession session) {
        sessions.put(sessionId, session);
        userSessionMap.put(userId, sessionId);
        
        // 记录连接信息到Redis（用于集群环境）
        String redisKey = RedisConstants.WEBSOCKET_CONNECTION_KEY + userId;
        ConnectionInfo connectionInfo = ConnectionInfo.builder()
            .userId(userId)
            .sessionId(sessionId)
            .serverId(getServerId())
            .connectTime(System.currentTimeMillis())
            .build();
        
        redisTemplate.opsForValue().set(redisKey, connectionInfo, Duration.ofHours(24));
        
        log.info("WebSocket连接建立: userId={}, sessionId={}", userId, sessionId);
    }
    
    /**
     * 用户断开WebSocket连接
     */
    public void removeConnection(String sessionId) {
        WebSocketSession session = sessions.remove(sessionId);
        if (session != null) {
            Long userId = findUserIdBySessionId(sessionId);
            if (userId != null) {
                userSessionMap.remove(userId);
                removeUserFromAllRooms(userId);
                
                // 从Redis中移除连接信息
                String redisKey = RedisConstants.WEBSOCKET_CONNECTION_KEY + userId;
                redisTemplate.delete(redisKey);
                
                log.info("WebSocket连接断开: userId={}, sessionId={}", userId, sessionId);
            }
        }
    }
    
    /**
     * 用户加入房间
     */
    public void joinRoom(Long userId, Long roomId) {
        roomUsersMap.computeIfAbsent(roomId, k -> ConcurrentHashMap.newKeySet()).add(userId);
        
        // 更新Redis中的房间成员信息
        String redisKey = RedisConstants.ROOM_ONLINE_USERS_KEY + roomId;
        redisTemplate.opsForSet().add(redisKey, userId);
        redisTemplate.expire(redisKey, Duration.ofHours(24));
        
        log.info("用户加入房间WebSocket频道: userId={}, roomId={}", userId, roomId);
    }
    
    /**
     * 用户离开房间
     */
    public void leaveRoom(Long userId, Long roomId) {
        Set<Long> roomUsers = roomUsersMap.get(roomId);
        if (roomUsers != null) {
            roomUsers.remove(userId);
            if (roomUsers.isEmpty()) {
                roomUsersMap.remove(roomId);
            }
        }
        
        // 从Redis中移除
        String redisKey = RedisConstants.ROOM_ONLINE_USERS_KEY + roomId;
        redisTemplate.opsForSet().remove(redisKey, userId);
        
        log.info("用户离开房间WebSocket频道: userId={}, roomId={}", userId, roomId);
    }
    
    /**
     * 向指定用户发送消息
     */
    public boolean sendToUser(Long userId, WebSocketMessage message) {
        String sessionId = userSessionMap.get(userId);
        if (sessionId != null) {
            WebSocketSession session = sessions.get(sessionId);
            if (session != null && session.isOpen()) {
                try {
                    String messageJson = JsonUtils.toJson(message);
                    session.sendMessage(new TextMessage(messageJson));
                    return true;
                } catch (Exception e) {
                    log.error("发送WebSocket消息失败: userId={}, message={}", userId, message, e);
                    return false;
                }
            }
        }
        
        // 本地没有连接，尝试通过Redis集群发送
        return sendThroughCluster(userId, message);
    }
    
    /**
     * 向房间内所有用户广播消息
     */
    public void broadcastToRoom(Long roomId, WebSocketMessage message) {
        Set<Long> roomUsers = roomUsersMap.get(roomId);
        if (roomUsers != null && !roomUsers.isEmpty()) {
            // 本地用户直接发送
            int localSentCount = 0;
            for (Long userId : roomUsers) {
                if (sendToUser(userId, message)) {
                    localSentCount++;
                }
            }
            
            log.info("房间消息广播完成: roomId={}, localUsers={}, sent={}", 
                roomId, roomUsers.size(), localSentCount);
        }
        
        // 通过Redis Pub/Sub发送给集群中的其他节点
        publishToCluster(roomId, message);
    }
    
    /**
     * 通过Redis集群发送消息
     */
    private boolean sendThroughCluster(Long userId, WebSocketMessage message) {
        try {
            ClusterMessage clusterMessage = ClusterMessage.builder()
                .type(ClusterMessageType.SEND_TO_USER)
                .targetUserId(userId)
                .payload(message)
                .fromServerId(getServerId())
                .timestamp(System.currentTimeMillis())
                .build();
            
            redisTemplate.convertAndSend(
                RedisConstants.WEBSOCKET_CLUSTER_CHANNEL, 
                clusterMessage
            );
            return true;
        } catch (Exception e) {
            log.error("集群消息发送失败: userId={}", userId, e);
            return false;
        }
    }
    
    /**
     * 向集群广播房间消息
     */
    private void publishToCluster(Long roomId, WebSocketMessage message) {
        try {
            ClusterMessage clusterMessage = ClusterMessage.builder()
                .type(ClusterMessageType.BROADCAST_TO_ROOM)
                .targetRoomId(roomId)
                .payload(message)
                .fromServerId(getServerId())
                .timestamp(System.currentTimeMillis())
                .build();
            
            redisTemplate.convertAndSend(
                RedisConstants.WEBSOCKET_CLUSTER_CHANNEL, 
                clusterMessage
            );
        } catch (Exception e) {
            log.error("集群广播消息失败: roomId={}", roomId, e);
        }
    }
}
```

### WebSocket消息处理器

```java
/**
 * WebSocket消息处理器
 */
@Component
@ServerEndpoint(value = "/ws/room/{token}")
public class RoomWebSocketEndpoint {
    
    private static RoomWebSocketManager webSocketManager;
    private static JwtTokenUtil jwtTokenUtil;
    private static RoomMessageHandler messageHandler;
    
    // Spring依赖注入
    @Autowired
    public void setDependencies(RoomWebSocketManager webSocketManager,
                              JwtTokenUtil jwtTokenUtil,
                              RoomMessageHandler messageHandler) {
        RoomWebSocketEndpoint.webSocketManager = webSocketManager;
        RoomWebSocketEndpoint.jwtTokenUtil = jwtTokenUtil;
        RoomWebSocketEndpoint.messageHandler = messageHandler;
    }
    
    /**
     * 连接建立时调用
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("token") String token) {
        try {
            // 验证JWT Token
            Long userId = jwtTokenUtil.getUserIdFromToken(token);
            if (userId == null) {
                session.close(new CloseReason(CloseReason.CloseCodes.CANNOT_ACCEPT, "无效的token"));
                return;
            }
            
            // 创建WebSocket会话包装器
            WebSocketSession webSocketSession = new StandardWebSocketSession(session);
            
            // 添加连接
            webSocketManager.addConnection(session.getId(), userId, webSocketSession);
            
            // 发送连接成功消息
            WebSocketMessage welcomeMessage = WebSocketMessage.builder()
                .type(MessageType.SYSTEM)
                .content("连接成功")
                .timestamp(System.currentTimeMillis())
                .build();
            
            webSocketManager.sendToUser(userId, welcomeMessage);
            
        } catch (Exception e) {
            log.error("WebSocket连接建立失败", e);
            try {
                session.close(new CloseReason(CloseReason.CloseCodes.UNEXPECTED_CONDITION, "连接失败"));
            } catch (IOException ioException) {
                log.error("关闭WebSocket连接失败", ioException);
            }
        }
    }
    
    /**
     * 收到客户端消息时调用
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        try {
            // 解析消息
            ClientMessage clientMessage = JsonUtils.fromJson(message, ClientMessage.class);
            
            // 获取用户ID
            Long userId = getUserIdFromSession(session.getId());
            if (userId == null) {
                log.warn("未找到用户ID: sessionId={}", session.getId());
                return;
            }
            
            // 处理消息
            messageHandler.handleClientMessage(userId, clientMessage);
            
        } catch (Exception e) {
            log.error("处理WebSocket消息失败: message={}", message, e);
        }
    }
    
    /**
     * 连接关闭时调用
     */
    @OnClose
    public void onClose(Session session) {
        try {
            webSocketManager.removeConnection(session.getId());
        } catch (Exception e) {
            log.error("WebSocket连接关闭处理失败", e);
        }
    }
    
    /**
     * 发生错误时调用
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("WebSocket发生错误: sessionId={}", session.getId(), error);
        try {
            webSocketManager.removeConnection(session.getId());
        } catch (Exception e) {
            log.error("WebSocket错误处理失败", e);
        }
    }
}
```

## 📨 消息类型设计

### 消息类型枚举

```java
public enum MessageType {
    // 系统消息
    SYSTEM("system"),                    // 系统通知
    ERROR("error"),                      // 错误消息
    
    // 房间状态相关
    ROOM_STATE_CHANGE("room_state_change"),     // 房间状态变更
    ROOM_INFO_UPDATE("room_info_update"),       // 房间信息更新
    
    // 成员相关
    MEMBER_JOIN("member_join"),                 // 成员加入
    MEMBER_LEAVE("member_leave"),               // 成员离开
    HOST_TRANSFER("host_transfer"),             // 房主转移
    
    // 角色分配相关
    ROLE_ASSIGNMENT("role_assignment"),         // 角色分配
    ROLE_CONFIRM("role_confirm"),               // 角色确认
    
    // 游戏进程相关
    GAME_START("game_start"),                   // 游戏开始
    GAME_PHASE_CHANGE("game_phase_change"),     // 游戏阶段变更
    TIMER_UPDATE("timer_update"),               // 计时器更新
    VOTING_START("voting_start"),               // 投票开始
    VOTING_END("voting_end"),                   // 投票结束
    
    // 聊天相关
    CHAT_MESSAGE("chat_message"),               // 聊天消息
    EMOJI_REACTION("emoji_reaction"),           // 表情反应
    
    // 心跳相关
    PING("ping"),                               // 心跳检测
    PONG("pong");                               // 心跳响应
}
```

### 消息数据结构

```java
/**
 * WebSocket消息基类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WebSocketMessage {
    private String messageId;           // 消息唯一ID
    private MessageType type;           // 消息类型
    private Object content;             // 消息内容
    private Long roomId;                // 房间ID（可选）
    private Long fromUserId;            // 发送者用户ID（可选）
    private Long timestamp;             // 时间戳
    private Map<String, Object> extra;  // 扩展字段
}

/**
 * 房间状态变更消息
 */
@Data
@Builder
public class RoomStateChangeMessage {
    private Long roomId;
    private RoomStatus fromStatus;
    private RoomStatus toStatus;
    private String reason;
    private Long timestamp;
}

/**
 * 成员变动消息
 */
@Data
@Builder
public class MemberChangeMessage {
    private Long roomId;
    private Long userId;
    private String username;
    private String avatar;
    private MemberChangeType changeType;  // JOIN, LEAVE, KICK
    private Long timestamp;
}

/**
 * 聊天消息
 */
@Data
@Builder
public class ChatMessage {
    private Long roomId;
    private Long fromUserId;
    private String fromUsername;
    private String content;
    private ChatMessageType messageType;  // TEXT, EMOJI, IMAGE
    private Long timestamp;
}
```

## 🔄 Redis Pub/Sub集群通信

### 集群消息处理

```java
/**
 * Redis Pub/Sub集群通信处理器
 */
@Component
public class WebSocketClusterHandler {
    
    @Autowired
    private RoomWebSocketManager webSocketManager;
    
    /**
     * 监听集群消息
     */
    @RedisListener(channel = "#{redisConstants.WEBSOCKET_CLUSTER_CHANNEL}")
    public void handleClusterMessage(String channel, ClusterMessage message) {
        // 忽略自己发送的消息
        if (getServerId().equals(message.getFromServerId())) {
            return;
        }
        
        try {
            switch (message.getType()) {
                case SEND_TO_USER:
                    handleSendToUser(message);
                    break;
                case BROADCAST_TO_ROOM:
                    handleBroadcastToRoom(message);
                    break;
                case USER_DISCONNECT:
                    handleUserDisconnect(message);
                    break;
                default:
                    log.warn("未知的集群消息类型: {}", message.getType());
            }
        } catch (Exception e) {
            log.error("处理集群消息失败: message={}", message, e);
        }
    }
    
    /**
     * 处理发送给指定用户的消息
     */
    private void handleSendToUser(ClusterMessage clusterMessage) {
        Long userId = clusterMessage.getTargetUserId();
        WebSocketMessage message = (WebSocketMessage) clusterMessage.getPayload();
        
        // 检查该用户是否在本节点有连接
        if (webSocketManager.hasLocalConnection(userId)) {
            webSocketManager.sendToUser(userId, message);
        }
    }
    
    /**
     * 处理房间广播消息
     */
    private void handleBroadcastToRoom(ClusterMessage clusterMessage) {
        Long roomId = clusterMessage.getTargetRoomId();
        WebSocketMessage message = (WebSocketMessage) clusterMessage.getPayload();
        
        // 获取本节点该房间的用户列表
        Set<Long> localUsers = webSocketManager.getLocalRoomUsers(roomId);
        if (localUsers != null && !localUsers.isEmpty()) {
            for (Long userId : localUsers) {
                webSocketManager.sendToUser(userId, message);
            }
        }
    }
}

/**
 * Redis Pub/Sub配置
 */
@Configuration
public class RedisWebSocketConfig {
    
    @Bean
    public RedisMessageListenerContainer redisMessageListenerContainer(
            RedisConnectionFactory connectionFactory,
            WebSocketClusterHandler clusterHandler) {
        
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        
        // 监听WebSocket集群消息频道
        container.addMessageListener(
            new MessageListenerAdapter(clusterHandler, "handleClusterMessage"),
            new PatternTopic(RedisConstants.WEBSOCKET_CLUSTER_CHANNEL)
        );
        
        return container;
    }
}
```

## 💓 心跳检测与连接保活

### 心跳机制实现

```java
/**
 * WebSocket心跳检测服务
 */
@Service
public class WebSocketHeartbeatService {
    
    @Autowired
    private RoomWebSocketManager webSocketManager;
    
    // 心跳间隔时间（30秒）
    private static final long HEARTBEAT_INTERVAL = 30000;
    
    // 连接超时时间（90秒）
    private static final long CONNECTION_TIMEOUT = 90000;
    
    // 存储最后心跳时间
    private final Map<Long, Long> lastHeartbeatMap = new ConcurrentHashMap<>();
    
    /**
     * 定时发送心跳
     */
    @Scheduled(fixedDelay = HEARTBEAT_INTERVAL)
    public void sendHeartbeat() {
        WebSocketMessage pingMessage = WebSocketMessage.builder()
            .type(MessageType.PING)
            .content("ping")
            .timestamp(System.currentTimeMillis())
            .build();
        
        // 向所有在线用户发送心跳
        Set<Long> onlineUsers = webSocketManager.getAllOnlineUsers();
        for (Long userId : onlineUsers) {
            if (webSocketManager.sendToUser(userId, pingMessage)) {
                lastHeartbeatMap.put(userId, System.currentTimeMillis());
            }
        }
        
        log.debug("心跳发送完成: 在线用户数={}", onlineUsers.size());
    }
    
    /**
     * 处理客户端心跳响应
     */
    public void handlePongMessage(Long userId) {
        lastHeartbeatMap.put(userId, System.currentTimeMillis());
        log.debug("收到心跳响应: userId={}", userId);
    }
    
    /**
     * 检查连接超时
     */
    @Scheduled(fixedDelay = 60000) // 每分钟检查一次
    public void checkConnectionTimeout() {
        long currentTime = System.currentTimeMillis();
        List<Long> timeoutUsers = new ArrayList<>();
        
        lastHeartbeatMap.entrySet().removeIf(entry -> {
            Long userId = entry.getKey();
            Long lastHeartbeat = entry.getValue();
            
            if (currentTime - lastHeartbeat > CONNECTION_TIMEOUT) {
                timeoutUsers.add(userId);
                return true;
            }
            return false;
        });
        
        // 清理超时连接
        for (Long userId : timeoutUsers) {
            webSocketManager.forceDisconnectUser(userId, "心跳超时");
            log.info("清理超时连接: userId={}", userId);
        }
        
        if (!timeoutUsers.isEmpty()) {
            log.info("连接超时检查完成: 清理连接数={}", timeoutUsers.size());
        }
    }
}
```

## 📱 前端WebSocket客户端

### Vue3 WebSocket组合式函数

```typescript
// composables/useWebSocket.ts
import { ref, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/modules/auth'

export interface WebSocketMessage {
  messageId?: string
  type: string
  content: any
  roomId?: number
  fromUserId?: number
  timestamp: number
  extra?: Record<string, any>
}

export const useWebSocket = () => {
  const authStore = useAuthStore()
  const isConnected = ref(false)
  const isReconnecting = ref(false)
  const ws = ref<WebSocket | null>(null)
  
  // 消息处理器映射
  const messageHandlers = new Map<string, Function[]>()
  
  // 重连配置
  const reconnectConfig = {
    maxAttempts: 5,
    currentAttempt: 0,
    delay: 1000,
    maxDelay: 30000
  }
  
  /**
   * 建立WebSocket连接
   */
  const connect = () => {
    if (!authStore.token) {
      console.warn('未登录，无法建立WebSocket连接')
      return
    }
    
    try {
      const wsUrl = `${import.meta.env.VITE_WS_BASE_URL}/ws/room/${authStore.token}`
      ws.value = new WebSocket(wsUrl)
      
      ws.value.onopen = handleOpen
      ws.value.onmessage = handleMessage
      ws.value.onclose = handleClose
      ws.value.onerror = handleError
      
    } catch (error) {
      console.error('WebSocket连接失败:', error)
      scheduleReconnect()
    }
  }
  
  /**
   * 连接打开事件
   */
  const handleOpen = () => {
    console.log('WebSocket连接已建立')
    isConnected.value = true
    isReconnecting.value = false
    reconnectConfig.currentAttempt = 0
    
    // 开始心跳
    startHeartbeat()
  }
  
  /**
   * 消息接收事件
   */
  const handleMessage = (event: MessageEvent) => {
    try {
      const message: WebSocketMessage = JSON.parse(event.data)
      
      // 处理心跳消息
      if (message.type === 'ping') {
        sendPong()
        return
      }
      
      // 调用注册的消息处理器
      const handlers = messageHandlers.get(message.type) || []
      handlers.forEach(handler => {
        try {
          handler(message)
        } catch (error) {
          console.error('消息处理器执行失败:', error)
        }
      })
      
    } catch (error) {
      console.error('解析WebSocket消息失败:', event.data, error)
    }
  }
  
  /**
   * 连接关闭事件
   */
  const handleClose = (event: CloseEvent) => {
    console.log('WebSocket连接已关闭:', event.code, event.reason)
    isConnected.value = false
    stopHeartbeat()
    
    // 如果不是正常关闭，尝试重连
    if (event.code !== 1000 && event.code !== 1001) {
      scheduleReconnect()
    }
  }
  
  /**
   * 连接错误事件
   */
  const handleError = (error: Event) => {
    console.error('WebSocket连接错误:', error)
    isConnected.value = false
  }
  
  /**
   * 发送消息
   */
  const sendMessage = (message: Partial<WebSocketMessage>) => {
    if (!isConnected.value || !ws.value) {
      console.warn('WebSocket未连接，无法发送消息')
      return false
    }
    
    try {
      const fullMessage: WebSocketMessage = {
        messageId: generateMessageId(),
        timestamp: Date.now(),
        ...message
      }
      
      ws.value.send(JSON.stringify(fullMessage))
      return true
    } catch (error) {
      console.error('发送WebSocket消息失败:', error)
      return false
    }
  }
  
  /**
   * 注册消息处理器
   */
  const onMessage = (type: string, handler: Function) => {
    if (!messageHandlers.has(type)) {
      messageHandlers.set(type, [])
    }
    messageHandlers.get(type)!.push(handler)
    
    // 返回取消注册函数
    return () => {
      const handlers = messageHandlers.get(type)
      if (handlers) {
        const index = handlers.indexOf(handler)
        if (index > -1) {
          handlers.splice(index, 1)
        }
      }
    }
  }
  
  // 心跳相关
  let heartbeatTimer: number | null = null
  
  const startHeartbeat = () => {
    stopHeartbeat()
    heartbeatTimer = window.setInterval(() => {
      if (isConnected.value) {
        // 心跳由服务端主动发送，客户端只需响应
      }
    }, 30000)
  }
  
  const stopHeartbeat = () => {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer)
      heartbeatTimer = null
    }
  }
  
  const sendPong = () => {
    sendMessage({
      type: 'pong',
      content: 'pong'
    })
  }
  
  // 重连逻辑
  const scheduleReconnect = () => {
    if (reconnectConfig.currentAttempt >= reconnectConfig.maxAttempts) {
      console.error('WebSocket重连次数已达上限')
      return
    }
    
    isReconnecting.value = true
    reconnectConfig.currentAttempt++
    
    const delay = Math.min(
      reconnectConfig.delay * Math.pow(2, reconnectConfig.currentAttempt - 1),
      reconnectConfig.maxDelay
    )
    
    console.log(`${delay}ms后尝试第${reconnectConfig.currentAttempt}次重连`)
    
    setTimeout(() => {
      if (isReconnecting.value) {
        connect()
      }
    }, delay)
  }
  
  /**
   * 手动断开连接
   */
  const disconnect = () => {
    if (ws.value) {
      ws.value.close(1000, '用户主动断开')
      ws.value = null
    }
    isConnected.value = false
    isReconnecting.value = false
    stopHeartbeat()
  }
  
  /**
   * 生成消息ID
   */
  const generateMessageId = (): string => {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
  
  // 生命周期
  onMounted(() => {
    connect()
  })
  
  onUnmounted(() => {
    disconnect()
  })
  
  return {
    isConnected: readonly(isConnected),
    isReconnecting: readonly(isReconnecting),
    connect,
    disconnect,
    sendMessage,
    onMessage
  }
}
```

## 📊 性能监控

### 实时通信性能指标

```java
@Component
public class WebSocketMetricsCollector {
    
    private final MeterRegistry meterRegistry;
    
    public WebSocketMetricsCollector(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        
        // 注册自定义指标
        Gauge.builder("websocket.connections.active")
            .description("活跃WebSocket连接数")
            .register(meterRegistry, this, WebSocketMetricsCollector::getActiveConnections);
    }
    
    /**
     * 记录消息发送指标
     */
    public void recordMessageSent(String messageType, boolean success) {
        Counter.builder("websocket.messages.sent")
            .tag("type", messageType)
            .tag("success", String.valueOf(success))
            .register(meterRegistry)
            .increment();
    }
    
    /**
     * 记录消息延迟
     */
    public void recordMessageLatency(String messageType, long latencyMs) {
        Timer.builder("websocket.message.latency")
            .tag("type", messageType)
            .register(meterRegistry)
            .record(latencyMs, TimeUnit.MILLISECONDS);
    }
}
```

---

**相关文档**: [异常处理策略](./04_room_system_exception_handling.md)  
**下一步**: 完成数据库设计文档