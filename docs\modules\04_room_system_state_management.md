# 房间状态管理与分布式锁设计

## 📋 文档信息

**模块**: 房间系统 - 状态管理  
**版本**: v1.0  
**日期**: 2025-08-03  
**作者**: an  

## 🎯 设计目标

确保在高并发、分布式环境下，房间状态变更的原子性、一致性和可靠性，防止出现状态冲突和数据不一致问题。

## 🔒 分布式锁设计

### 锁策略总览

```java
// 锁的层级设计
房间级锁     → "room:lock:{roomId}"           // 房间整体操作锁
成员级锁     → "room:member:lock:{roomId}"    // 成员变更操作锁
状态级锁     → "room:state:lock:{roomId}"     // 状态变更操作锁
全局计数锁   → "room:counter:lock"            // 全局房间计数锁
```

### Redis分布式锁实现

```java
@Component
public class RoomLockService {
    
    @Autowired
    private RedissonClient redissonClient;
    
    private static final long LOCK_WAIT_TIME = 3L;      // 获取锁等待时间
    private static final long LOCK_LEASE_TIME = 10L;    // 锁持有时间
    
    /**
     * 房间操作锁 - 用于房间创建、解散等整体操作
     */
    public <T> T executeWithRoomLock(Long roomId, Supplier<T> action) {
        String lockKey = RedisConstants.ROOM_LOCK_KEY + roomId;
        RLock lock = redissonClient.getLock(lockKey);
        
        try {
            if (lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS)) {
                return action.get();
            } else {
                throw new BusinessException("房间操作繁忙，请稍后重试");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BusinessException("房间操作被中断");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
    
    /**
     * 成员操作锁 - 用于加入、离开房间等成员变更操作
     */
    public <T> T executeWithMemberLock(Long roomId, Supplier<T> action) {
        String lockKey = RedisConstants.ROOM_MEMBER_LOCK_KEY + roomId;
        RLock lock = redissonClient.getLock(lockKey);
        
        try {
            if (lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS)) {
                log.info("获取房间成员锁成功: roomId={}", roomId);
                return action.get();
            } else {
                throw new BusinessException("房间人员变动频繁，请稍后重试");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BusinessException("成员操作被中断");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.info("释放房间成员锁: roomId={}", roomId);
            }
        }
    }
    
    /**
     * 状态变更锁 - 用于房间状态转换
     */
    public <T> T executeWithStateLock(Long roomId, Supplier<T> action) {
        String lockKey = RedisConstants.ROOM_STATE_LOCK_KEY + roomId;
        RLock lock = redissonClient.getLock(lockKey);
        
        try {
            if (lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS)) {
                return action.get();
            } else {
                throw new BusinessException("房间状态变更冲突，请稍后重试");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BusinessException("状态变更被中断");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
```

### 锁粒度控制策略

| 操作类型 | 锁级别 | 锁粒度 | 并发策略 |
|----------|--------|--------|----------|
| 创建房间 | 全局 | 用户级 | 限制单用户并发创建 |
| 加入房间 | 房间级 | 成员锁 | 串行处理成员变更 |
| 离开房间 | 房间级 | 成员锁 | 串行处理成员变更 |
| 状态变更 | 房间级 | 状态锁 | 串行处理状态转换 |
| 角色分配 | 房间级 | 房间锁 | 整体原子操作 |
| 解散房间 | 房间级 | 房间锁 | 整体原子操作 |

## 🔄 状态管理机制

### 状态转换控制器

```java
@Service
public class RoomStateManager {
    
    @Autowired
    private RoomLockService roomLockService;
    
    @Autowired
    private RoomRepository roomRepository;
    
    @Autowired
    private RoomEventPublisher eventPublisher;
    
    /**
     * 安全的状态转换方法
     */
    @Transactional
    public boolean changeRoomState(Long roomId, RoomStatus fromStatus, 
                                 RoomStatus toStatus, String reason) {
        return roomLockService.executeWithStateLock(roomId, () -> {
            // 1. 获取当前房间状态
            Room room = roomRepository.findById(roomId)
                .orElseThrow(() -> new BusinessException("房间不存在"));
            
            // 2. 验证状态转换合法性
            if (!isValidStateTransition(room.getStatus(), fromStatus, toStatus)) {
                throw new BusinessException("非法的状态转换: " + 
                    room.getStatus() + " -> " + toStatus);
            }
            
            // 3. 验证状态转换条件
            if (!checkTransitionConditions(room, toStatus)) {
                return false;
            }
            
            // 4. 执行状态转换
            RoomStatus oldStatus = room.getStatus();
            room.setStatus(toStatus);
            room.setUpdateTime(LocalDateTime.now());
            roomRepository.save(room);
            
            // 5. 发布状态变更事件
            eventPublisher.publishStateChangeEvent(roomId, oldStatus, toStatus, reason);
            
            log.info("房间状态变更成功: roomId={}, {} -> {}, reason={}", 
                roomId, oldStatus, toStatus, reason);
            
            return true;
        });
    }
    
    /**
     * 验证状态转换合法性
     */
    private boolean isValidStateTransition(RoomStatus current, RoomStatus from, RoomStatus to) {
        // 当前状态必须匹配预期的起始状态
        if (current != from) {
            log.warn("状态转换失败: 当前状态{}与预期状态{}不匹配", current, from);
            return false;
        }
        
        // 定义合法的状态转换
        Map<RoomStatus, Set<RoomStatus>> validTransitions = Map.of(
            RECRUITING, Set.of(PREPARING, CANCELLED),
            PREPARING, Set.of(PLAYING, RECRUITING, CANCELLED),
            PLAYING, Set.of(FINISHED, SUSPENDED, CANCELLED),
            SUSPENDED, Set.of(PLAYING, CANCELLED),
            FINISHED, Set.of(),  // 终态
            CANCELLED, Set.of()  // 终态
        );
        
        return validTransitions.get(from).contains(to);
    }
    
    /**
     * 检查状态转换条件
     */
    private boolean checkTransitionConditions(Room room, RoomStatus toStatus) {
        switch (toStatus) {
            case PREPARING:
                // 转入准备状态：检查人数是否足够
                return room.getCurrentPlayers() >= room.getMinPlayers();
                
            case PLAYING:
                // 转入游戏状态：检查角色分配是否完成
                return checkRoleAssignmentComplete(room.getId());
                
            case FINISHED:
                // 转入结束状态：检查游戏是否真正完成
                return checkGameCompleted(room.getId());
                
            case CANCELLED:
                // 取消状态：总是允许
                return true;
                
            default:
                return true;
        }
    }
}
```

### 状态一致性保障

```java
@Component
public class RoomStateConsistencyChecker {
    
    @Scheduled(fixedDelay = 30000) // 30秒检查一次
    public void checkRoomStateConsistency() {
        try {
            // 检查超时的房间
            checkTimeoutRooms();
            
            // 检查异常状态的房间
            checkAbnormalRooms();
            
            // 检查人数与状态不一致的房间
            checkPlayerCountConsistency();
            
        } catch (Exception e) {
            log.error("房间状态一致性检查异常", e);
        }
    }
    
    /**
     * 检查超时房间
     */
    private void checkTimeoutRooms() {
        LocalDateTime timeoutThreshold = LocalDateTime.now().minusMinutes(30);
        
        // 查找招募超时的房间
        List<Room> timeoutRecruitingRooms = roomRepository
            .findByStatusAndCreateTimeBefore(RECRUITING, timeoutThreshold);
        
        timeoutRecruitingRooms.forEach(room -> {
            roomStateManager.changeRoomState(room.getId(), RECRUITING, CANCELLED, "招募超时");
        });
        
        // 查找准备超时的房间
        LocalDateTime prepareTimeoutThreshold = LocalDateTime.now().minusMinutes(15);
        List<Room> timeoutPreparingRooms = roomRepository
            .findByStatusAndUpdateTimeBefore(PREPARING, prepareTimeoutThreshold);
        
        timeoutPreparingRooms.forEach(room -> {
            if (room.getCurrentPlayers() >= room.getMinPlayers()) {
                // 人数够，强制开始游戏
                roomStateManager.changeRoomState(room.getId(), PREPARING, PLAYING, "准备超时自动开始");
            } else {
                // 人数不够，取消房间
                roomStateManager.changeRoomState(room.getId(), PREPARING, CANCELLED, "准备超时人数不足");
            }
        });
    }
}
```

## 📊 性能监控与优化

### 锁性能监控

```java
@Component
public class LockPerformanceMonitor {
    
    private final MeterRegistry meterRegistry;
    private final Timer.Sample lockWaitTime;
    private final Counter lockFailureCounter;
    
    public LockPerformanceMonitor(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.lockFailureCounter = Counter.builder("room.lock.failures")
            .description("房间锁获取失败次数")
            .register(meterRegistry);
    }
    
    /**
     * 监控锁的获取时间和成功率
     */
    public <T> T monitorLockExecution(String lockType, Long roomId, Supplier<T> action) {
        Timer.Sample sample = Timer.start(meterRegistry);
        try {
            T result = action.get();
            sample.stop(Timer.builder("room.lock.duration")
                .tag("type", lockType)
                .tag("success", "true")
                .register(meterRegistry));
            return result;
        } catch (Exception e) {
            lockFailureCounter.increment(
                Tags.of("type", lockType, "room", String.valueOf(roomId))
            );
            sample.stop(Timer.builder("room.lock.duration")
                .tag("type", lockType)
                .tag("success", "false")
                .register(meterRegistry));
            throw e;
        }
    }
}
```

### 锁优化策略

1. **锁粒度优化**
   - 读多写少场景使用读写锁
   - 不同类型操作使用不同级别的锁
   - 避免长时间持有锁

2. **锁超时优化**
   - 根据业务场景调整锁超时时间
   - 实现锁续期机制防止业务执行超时
   - 提供锁等待队列优先级

3. **性能优化**
   - 使用本地缓存减少Redis访问
   - 实现锁池化减少锁对象创建
   - 批量操作时使用批量锁

## 🚨 异常处理策略

### 锁异常处理

```java
@Component
public class LockExceptionHandler {
    
    /**
     * 锁获取失败的降级策略
     */
    public <T> T handleLockFailure(String operation, Long roomId, Supplier<T> fallback) {
        log.warn("房间操作锁获取失败: operation={}, roomId={}", operation, roomId);
        
        // 记录失败指标
        meterRegistry.counter("room.lock.fallback", "operation", operation).increment();
        
        // 根据操作类型选择降级策略
        switch (operation) {
            case "join_room":
                // 加入房间失败 - 提示用户重试
                throw new BusinessException("房间人员变动频繁，请稍后重试");
                
            case "leave_room":
                // 离开房间失败 - 异步处理
                return (T) asyncProcessLeaveRoom(roomId);
                
            case "state_change":
                // 状态变更失败 - 使用最终一致性
                return (T) scheduleStateChangeRetry(roomId);
                
            default:
                // 其他操作 - 执行降级逻辑
                return fallback.get();
        }
    }
}
```

## 📈 监控指标

| 指标名称 | 类型 | 描述 |
|----------|------|------|
| room.lock.duration | Timer | 锁持有时间 |
| room.lock.failures | Counter | 锁获取失败次数 |
| room.lock.contention | Gauge | 锁竞争程度 |
| room.state.transitions | Counter | 状态转换次数 |
| room.state.errors | Counter | 状态转换错误次数 |
| room.consistency.checks | Counter | 一致性检查次数 |

---

**相关文档**: [数据一致性解决方案](./04_room_system_consistency.md)  
**下一步**: 实现WebSocket实时通信机制