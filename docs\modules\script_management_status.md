# 剧本管理状态模块开发文档

## 📋 文档信息

**模块**: 剧本管理系统 - 状态管理  
**版本**: v1.0  
**日期**: 2025-08-04  
**作者**: an  

## 🎯 模块概述

剧本管理状态模块负责管理剧本的生命周期状态，包括剧本的发布、审核、上下架等状态转换，以及相关的业务逻辑处理。

## 📊 剧本状态设计

### 状态定义

| 状态码 | 状态名称 | 中文描述 | 说明 |
|--------|----------|----------|------|
| 1 | PUBLISHED | 已上架 | 剧本已通过审核，可以被用户浏览和使用 |
| 2 | UNPUBLISHED | 已下架 | 剧本被下架，用户无法访问 |
| 3 | REVIEWING | 审核中 | 剧本提交审核，等待管理员审核 |
| 4 | REJECTED | 审核拒绝 | 剧本审核未通过，需要修改后重新提交 |
| 5 | DRAFT | 草稿 | 剧本尚未完成，保存为草稿状态 |

### 状态流转规则

```
DRAFT → REVIEWING      (提交审核)
DRAFT → DRAFT          (保存草稿)

REVIEWING → PUBLISHED  (审核通过)
REVIEWING → REJECTED   (审核拒绝)

REJECTED → REVIEWING   (修改后重新提交)
REJECTED → DRAFT       (退回草稿)

PUBLISHED → UNPUBLISHED (管理员下架)
UNPUBLISHED → PUBLISHED (重新上架)
UNPUBLISHED → REVIEWING (修改后重新审核)
```

## 🗄️ 数据库表结构

### 主表 - tb_script

```sql
CREATE TABLE `tb_script` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` varchar(255) NOT NULL COMMENT '剧本标题',
  `description` text NOT NULL COMMENT '剧本描述',
  `cover_image` varchar(512) DEFAULT NULL COMMENT '封面图片',
  `images` varchar(2048) DEFAULT NULL COMMENT '剧本图片，多张以","隔开',
  `category` varchar(64) NOT NULL COMMENT '剧本类型：推理/恐怖/情感/欢乐/古风/现代',
  `player_count_min` int UNSIGNED NOT NULL COMMENT '最少玩家数',
  `player_count_max` int UNSIGNED NOT NULL COMMENT '最多玩家数',
  `duration` varchar(32) NOT NULL COMMENT '游戏时长',
  `difficulty` int UNSIGNED NOT NULL DEFAULT 1 COMMENT '难度等级 1-5',
  `age_range` varchar(32) DEFAULT NULL COMMENT '推荐年龄',
  `tags` varchar(512) DEFAULT NULL COMMENT '标签，以","隔开',
  `highlights` text DEFAULT NULL COMMENT '剧本亮点，JSON格式',
  `warnings` varchar(512) DEFAULT NULL COMMENT '内容警告，以","隔开',
  `price` decimal(10, 2) UNSIGNED DEFAULT 0.00 COMMENT '剧本价格',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 5 COMMENT '状态：1-上架，2-下架，3-审核中，4-审核拒绝，5-草稿',
  `average_rating` decimal(3, 2) UNSIGNED DEFAULT 0.00 COMMENT '平均评分',
  `review_count` int UNSIGNED DEFAULT 0 COMMENT '评价数量',
  `play_count` int UNSIGNED DEFAULT 0 COMMENT '游玩次数',
  `creator_id` bigint UNSIGNED DEFAULT NULL COMMENT '创建者ID',
  `reviewer_id` bigint UNSIGNED DEFAULT NULL COMMENT '审核者ID',
  `review_time` timestamp NULL DEFAULT NULL COMMENT '审核时间',
  `review_comment` text DEFAULT NULL COMMENT '审核意见',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`),
  KEY `idx_player_count` (`player_count_min`, `player_count_max`),
  KEY `idx_status` (`status`),
  KEY `idx_creator` (`creator_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='剧本表';
```

### 状态历史表 - tb_script_status_history

```sql
CREATE TABLE `tb_script_status_history` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `script_id` bigint UNSIGNED NOT NULL COMMENT '剧本ID',
  `from_status` tinyint UNSIGNED NOT NULL COMMENT '原状态',
  `to_status` tinyint UNSIGNED NOT NULL COMMENT '新状态',
  `operator_id` bigint UNSIGNED DEFAULT NULL COMMENT '操作者ID',
  `operator_type` varchar(20) DEFAULT 'USER' COMMENT '操作者类型：USER-用户，ADMIN-管理员，SYSTEM-系统',
  `reason` varchar(200) DEFAULT NULL COMMENT '状态变更原因',
  `comment` text DEFAULT NULL COMMENT '备注信息',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_script_id` (`script_id`),
  KEY `idx_operator` (`operator_id`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_script_status_script` FOREIGN KEY (`script_id`) REFERENCES `tb_script` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='剧本状态变更历史表';
```

## 🔧 核心业务逻辑

### 状态管理服务

```java
@Service
@Slf4j
public class ScriptStatusService {
    
    @Autowired
    private ScriptMapper scriptMapper;
    
    @Autowired
    private ScriptStatusHistoryMapper statusHistoryMapper;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 提交剧本审核
     */
    @Transactional
    public boolean submitForReview(Long scriptId, Long userId) {
        Script script = scriptMapper.selectById(scriptId);
        if (script == null) {
            throw new BusinessException("剧本不存在");
        }
        
        // 验证状态转换合法性
        if (!canTransitionTo(script.getStatus(), ScriptStatus.REVIEWING)) {
            throw new BusinessException("当前状态不允许提交审核");
        }
        
        // 验证剧本信息完整性
        validateScriptForReview(script);
        
        // 更新状态
        script.setStatus(ScriptStatus.REVIEWING.getCode());
        script.setUpdateTime(LocalDateTime.now());
        scriptMapper.updateById(script);
        
        // 记录状态变更历史
        recordStatusChange(scriptId, script.getStatus(), ScriptStatus.REVIEWING.getCode(), 
                          userId, "USER", "提交审核", null);
        
        // 发送审核通知
        sendReviewNotification(scriptId);
        
        log.info("剧本提交审核成功: scriptId={}, userId={}", scriptId, userId);
        return true;
    }
    
    /**
     * 审核剧本
     */
    @Transactional
    public boolean reviewScript(Long scriptId, Long reviewerId, boolean approved, String comment) {
        Script script = scriptMapper.selectById(scriptId);
        if (script == null) {
            throw new BusinessException("剧本不存在");
        }
        
        if (script.getStatus() != ScriptStatus.REVIEWING.getCode()) {
            throw new BusinessException("剧本不在审核状态");
        }
        
        ScriptStatus newStatus = approved ? ScriptStatus.PUBLISHED : ScriptStatus.REJECTED;
        
        // 更新剧本状态
        script.setStatus(newStatus.getCode());
        script.setReviewerId(reviewerId);
        script.setReviewTime(LocalDateTime.now());
        script.setReviewComment(comment);
        script.setUpdateTime(LocalDateTime.now());
        scriptMapper.updateById(script);
        
        // 记录状态变更历史
        recordStatusChange(scriptId, ScriptStatus.REVIEWING.getCode(), newStatus.getCode(),
                          reviewerId, "ADMIN", approved ? "审核通过" : "审核拒绝", comment);
        
        // 发送审核结果通知
        sendReviewResultNotification(scriptId, script.getCreatorId(), approved, comment);
        
        // 如果审核通过，更新搜索索引
        if (approved) {
            updateSearchIndex(script);
        }
        
        log.info("剧本审核完成: scriptId={}, reviewerId={}, approved={}", scriptId, reviewerId, approved);
        return true;
    }
    
    /**
     * 上架/下架剧本
     */
    @Transactional
    public boolean togglePublishStatus(Long scriptId, Long operatorId, boolean publish, String reason) {
        Script script = scriptMapper.selectById(scriptId);
        if (script == null) {
            throw new BusinessException("剧本不存在");
        }
        
        ScriptStatus currentStatus = ScriptStatus.fromCode(script.getStatus());
        ScriptStatus targetStatus = publish ? ScriptStatus.PUBLISHED : ScriptStatus.UNPUBLISHED;
        
        if (!canTransitionTo(currentStatus, targetStatus)) {
            throw new BusinessException("当前状态不允许此操作");
        }
        
        // 更新状态
        script.setStatus(targetStatus.getCode());
        script.setUpdateTime(LocalDateTime.now());
        scriptMapper.updateById(script);
        
        // 记录状态变更历史
        recordStatusChange(scriptId, currentStatus.getCode(), targetStatus.getCode(),
                          operatorId, "ADMIN", publish ? "上架" : "下架", reason);
        
        // 更新搜索索引
        if (publish) {
            updateSearchIndex(script);
        } else {
            removeFromSearchIndex(scriptId);
        }
        
        log.info("剧本状态变更成功: scriptId={}, operatorId={}, publish={}", scriptId, operatorId, publish);
        return true;
    }
    
    /**
     * 验证状态转换合法性
     */
    private boolean canTransitionTo(ScriptStatus from, ScriptStatus to) {
        Map<ScriptStatus, Set<ScriptStatus>> validTransitions = Map.of(
            ScriptStatus.DRAFT, Set.of(ScriptStatus.REVIEWING),
            ScriptStatus.REVIEWING, Set.of(ScriptStatus.PUBLISHED, ScriptStatus.REJECTED),
            ScriptStatus.REJECTED, Set.of(ScriptStatus.REVIEWING, ScriptStatus.DRAFT),
            ScriptStatus.PUBLISHED, Set.of(ScriptStatus.UNPUBLISHED),
            ScriptStatus.UNPUBLISHED, Set.of(ScriptStatus.PUBLISHED, ScriptStatus.REVIEWING)
        );
        
        return validTransitions.getOrDefault(from, Set.of()).contains(to);
    }
    
    /**
     * 验证剧本信息完整性
     */
    private void validateScriptForReview(Script script) {
        List<String> errors = new ArrayList<>();
        
        if (StringUtils.isBlank(script.getTitle())) {
            errors.add("剧本标题不能为空");
        }
        if (StringUtils.isBlank(script.getDescription())) {
            errors.add("剧本描述不能为空");
        }
        if (StringUtils.isBlank(script.getCategory())) {
            errors.add("剧本分类不能为空");
        }
        if (script.getPlayerCountMin() == null || script.getPlayerCountMax() == null) {
            errors.add("玩家人数范围不能为空");
        }
        if (script.getPlayerCountMin() > script.getPlayerCountMax()) {
            errors.add("最小玩家数不能大于最大玩家数");
        }
        
        if (!errors.isEmpty()) {
            throw new BusinessException("剧本信息不完整：" + String.join(", ", errors));
        }
    }
    
    /**
     * 记录状态变更历史
     */
    private void recordStatusChange(Long scriptId, Integer fromStatus, Integer toStatus,
                                   Long operatorId, String operatorType, String reason, String comment) {
        ScriptStatusHistory history = new ScriptStatusHistory();
        history.setScriptId(scriptId);
        history.setFromStatus(fromStatus);
        history.setToStatus(toStatus);
        history.setOperatorId(operatorId);
        history.setOperatorType(operatorType);
        history.setReason(reason);
        history.setComment(comment);
        history.setCreateTime(LocalDateTime.now());
        
        statusHistoryMapper.insert(history);
    }
}
```

## 📊 状态统计与监控

### 状态统计查询

```sql
-- 按状态统计剧本数量
SELECT 
    status,
    CASE status
        WHEN 1 THEN '已上架'
        WHEN 2 THEN '已下架'
        WHEN 3 THEN '审核中'
        WHEN 4 THEN '审核拒绝'
        WHEN 5 THEN '草稿'
    END as status_name,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM tb_script), 2) as percentage
FROM tb_script 
GROUP BY status 
ORDER BY status;

-- 审核效率统计
SELECT 
    DATE(create_time) as date,
    COUNT(*) as submitted_count,
    COUNT(CASE WHEN status IN (1, 4) THEN 1 END) as reviewed_count,
    ROUND(AVG(CASE 
        WHEN review_time IS NOT NULL 
        THEN TIMESTAMPDIFF(HOUR, create_time, review_time) 
    END), 2) as avg_review_hours
FROM tb_script 
WHERE create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(create_time)
ORDER BY date DESC;
```

## 🚀 下一步开发计划

1. **实现状态管理API接口**
2. **开发前端状态管理组件**
3. **集成消息通知系统**
4. **完善审核工作流**
5. **添加批量操作功能**

---

**相关文档**: 
- [剧本管理系统总览](../DOCUMENTATION.md)
- [房间状态管理](./04_room_system_state_management.md)
