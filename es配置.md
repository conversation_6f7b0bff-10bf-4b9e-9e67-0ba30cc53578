# ElasticSearch 安装配置文档

## 概述

本文档基于项目后端代码分析，提供ElasticSearch 7.x/8.x的完整安装配置指南。项目使用ElasticSearch进行剧本搜索功能，支持中文分词和复杂查询。

## 系统要求

- Java 8+ (推荐Java 11+)
- 内存: 至少2GB，推荐4GB+
- 磁盘空间: 至少10GB可用空间
- 操作系统: Linux/Windows/macOS

## 1. ElasticSearch 安装

### 1.1 Linux 安装 (推荐)

```bash
# 下载 ElasticSearch 7.17.15 (与项目兼容版本)
wget https://artifacts.elastic.co/downloads/elasticsearch/elasticsearch-7.17.15-linux-x86_64.tar.gz

# 解压
tar -xzf elasticsearch-7.17.15-linux-x86_64.tar.gz
cd elasticsearch-7.17.15/

# 创建elasticsearch用户 (不能用root运行)
sudo useradd elasticsearch
sudo chown -R elasticsearch:elasticsearch /path/to/elasticsearch-7.17.15/

# 切换到elasticsearch用户
sudo su elasticsearch
```

### 1.2 Windows 安装

```powershell
# 下载 ElasticSearch 7.17.15
# 访问: https://artifacts.elastic.co/downloads/elasticsearch/elasticsearch-7.17.15-windows-x86_64.zip

# 解压到目标目录，例如: C:\elasticsearch-7.17.15\
```

### 1.3 Docker 安装 (推荐用于开发环境)

```bash
# 拉取镜像
docker pull elasticsearch:7.17.15

# 运行容器
docker run -d \
  --name elasticsearch \
  -p 9200:9200 \
  -p 9300:9300 \
  -e "discovery.type=single-node" \
  -e "ES_JAVA_OPTS=-Xms1g -Xmx1g" \
  elasticsearch:7.17.15
```

## 2. ElasticSearch 配置

### 2.1 基础配置文件 (config/elasticsearch.yml)

```yaml
# 集群名称
cluster.name: script-murder-cluster

# 节点名称
node.name: script-murder-node-1

# 数据和日志路径
path.data: /var/lib/elasticsearch
path.logs: /var/log/elasticsearch

# 网络配置
network.host: 0.0.0.0
http.port: 9200

# 发现配置 (单节点)
discovery.type: single-node

# 内存锁定 (生产环境推荐)
bootstrap.memory_lock: true

# 安全配置 (ES 8.x 默认启用，7.x 需要手动配置)
xpack.security.enabled: false

# 分片设置
index.number_of_shards: 1
index.number_of_replicas: 0

# 搜索线程池
thread_pool.search.size: 4
thread_pool.search.queue_size: 1000

# 批量操作设置
indices.memory.index_buffer_size: 20%
```

### 2.2 JVM 配置 (config/jvm.options)

```bash
# 堆内存设置 (根据服务器内存调整)
-Xms2g
-Xmx2g

# GC 配置
-XX:+UseG1GC
-XX:G1HeapRegionSize=16m
-XX:+UseG1GC
-XX:+UnlockExperimentalVMOptions
-XX:+UseG1GC
```

## 3. 中文分词插件安装

项目使用IK分词器进行中文分词，需要安装对应插件：

```bash
# 进入elasticsearch目录
cd elasticsearch-7.17.15/

# 安装IK分词器插件
./bin/elasticsearch-plugin install https://github.com/medcl/elasticsearch-analysis-ik/releases/download/v7.17.15/elasticsearch-analysis-ik-7.17.15.zip

# 验证插件安装
./bin/elasticsearch-plugin list
```

## 4. 启动 ElasticSearch

### 4.1 Linux 启动

```bash
# 前台启动 (用于测试)
./bin/elasticsearch

# 后台启动
./bin/elasticsearch -d

# 使用systemd管理 (推荐生产环境)
sudo systemctl enable elasticsearch
sudo systemctl start elasticsearch
sudo systemctl status elasticsearch
```

### 4.2 Windows 启动

```powershell
# 进入elasticsearch目录
cd C:\elasticsearch-7.17.15\

# 启动
.\bin\elasticsearch.bat
```

### 4.3 验证启动

```bash
# 检查ES状态
curl -X GET "localhost:9200/"

# 检查集群健康状态
curl -X GET "localhost:9200/_cluster/health"

# 检查IK分词器
curl -X POST "localhost:9200/_analyze" -H 'Content-Type: application/json' -d'
{
  "analyzer": "ik_max_word",
  "text": "剧本杀推理游戏"
}'
```

## 5. 项目配置文件修改

### 5.1 application.yaml 配置

根据你的ES安装情况，修改 `backend/src/main/resources/application.yaml`：

```yaml
spring:
  # Elasticsearch 配置
  elasticsearch:
    uris: http://localhost:9200  # 修改为你的ES地址
    username:                    # 如果启用了安全认证，填写用户名
    password:                    # 如果启用了安全认证，填写密码
    socket-timeout: 30s
    connection-timeout: 5s

script-murder:
  elasticsearch:
    index-prefix: script_murder  # 索引前缀
    batch-size: 1000            # 批量操作大小
    request-timeout: 10s
    max-connections: 100
    max-connections-per-route: 10
```

### 5.2 生产环境配置建议

```yaml
spring:
  elasticsearch:
    uris: http://your-es-server:9200
    username: elastic
    password: your-secure-password
    socket-timeout: 60s
    connection-timeout: 10s

script-murder:
  elasticsearch:
    index-prefix: script_murder_prod
    batch-size: 500
    request-timeout: 30s
    max-connections: 50
    max-connections-per-route: 5
```

## 6. 索引初始化

项目启动后，会自动创建索引。你也可以手动初始化：

```bash
# 创建索引模板
curl -X PUT "localhost:9200/_template/script_murder_template" -H 'Content-Type: application/json' -d'
{
  "index_patterns": ["script_murder_*"],
  "settings": {
    "analysis": {
      "analyzer": {
        "ik_max_word": {
          "type": "ik_max_word"
        },
        "ik_smart": {
          "type": "ik_smart"
        }
      }
    },
    "number_of_shards": 1,
    "number_of_replicas": 0
  }
}'
```

## 7. 监控和维护

### 7.1 常用监控命令

```bash
# 查看索引状态
curl -X GET "localhost:9200/_cat/indices?v"

# 查看集群状态
curl -X GET "localhost:9200/_cluster/stats?pretty"

# 查看节点信息
curl -X GET "localhost:9200/_cat/nodes?v"
```

### 7.2 日志位置

- Linux: `/var/log/elasticsearch/`
- Windows: `elasticsearch安装目录/logs/`
- Docker: `docker logs elasticsearch`

## 8. 故障排除

### 8.1 常见问题

1. **内存不足**: 调整JVM堆内存设置
2. **端口被占用**: 修改http.port配置
3. **权限问题**: 确保elasticsearch用户有足够权限
4. **中文分词不生效**: 检查IK插件是否正确安装

### 8.2 性能优化

1. **调整刷新间隔**: `index.refresh_interval: 30s`
2. **禁用副本**: `number_of_replicas: 0` (单节点环境)
3. **调整批量大小**: 根据数据量调整batch-size
4. **内存映射**: 启用`bootstrap.memory_lock`

## 9. 安全配置 (生产环境)

如果需要启用安全认证：

```yaml
# elasticsearch.yml
xpack.security.enabled: true
xpack.security.transport.ssl.enabled: true
```

```bash
# 设置密码
./bin/elasticsearch-setup-passwords auto
```

## 总结

按照以上步骤完成ElasticSearch的安装配置后，项目的搜索功能就可以正常使用了。记得根据实际环境调整配置参数，特别是内存和网络设置。

## 当前状态

✅ **ElasticSearch 7.17.15 已安装并运行**
✅ **IK中文分词器已成功安装** (版本 7.17.15)
✅ **IK分词器功能验证通过** - 可以正常分析中文文本
✅ **Spring Boot ElasticSearch配置已完成**
✅ **项目配置文件已正确设置**

### 已解决的问题
1. **Bean定义冲突** - 修复了ElasticsearchConfig中的Bean配置冲突
2. **IK分词器缺失** - 成功安装了IK中文分词器插件
3. **配置文件错误** - 修复了application.yaml中的path-prefix配置问题

### 验证步骤
1. **验证ES服务**: `http://localhost:9200`
2. **验证IK分词器**:
   ```powershell
   $headers = @{"Content-Type" = "application/json"}
   $body = '{"analyzer": "ik_max_word", "text": "剧本杀推理游戏"}'
   Invoke-RestMethod -Uri "http://localhost:9200/_analyze" -Method Post -Headers $headers -Body $body
   ```

### 下一步
- 启动ElasticSearch服务: `D:\Developer\elasticsearch-7.17.15-windows-x86_64\elasticsearch-7.17.15\bin\elasticsearch.bat`
- 启动Spring Boot应用
- 测试搜索功能
