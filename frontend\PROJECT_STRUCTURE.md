# 迷雾拼本 (Misty Labyrinth) 前端项目结构

## 目录结构

```
frontend/
├── public/                          # 静态资源
│   ├── favicon.ico
│   ├── logo.png
│   └── index.html
├── src/
│   ├── api/                         # API 接口层
│   │   ├── index.ts                 # Axios 实例配置
│   │   ├── interceptors.ts          # 请求/响应拦截器
│   │   ├── types.ts                 # API 响应类型定义
│   │   └── modules/                 # 按模块分组的 API
│   │       ├── auth.ts              # 认证相关 API
│   │       ├── script.ts            # 剧本相关 API
│   │       ├── lobby.ts             # 车队相关 API
│   │       ├── user.ts              # 用户相关 API
│   │       └── feed.ts              # 动态相关 API
│   ├── assets/                      # 静态资源
│   │   ├── images/                  # 图片资源
│   │   ├── icons/                   # 图标资源
│   │   └── fonts/                   # 字体文件
│   ├── components/                  # 可复用组件
│   │   ├── common/                  # 通用组件
│   │   │   ├── AppHeader.vue        # 顶部导航
│   │   │   ├── AppFooter.vue        # 页脚
│   │   │   ├── LoadingSpinner.vue   # 加载动画
│   │   │   ├── EmptyState.vue       # 空状态
│   │   │   └── ConfirmDialog.vue    # 确认对话框
│   │   ├── ui/                      # UI 基础组件
│   │   │   ├── Button.vue           # 自定义按钮
│   │   │   ├── Card.vue             # 毛玻璃卡片
│   │   │   ├── Modal.vue            # 模态框
│   │   │   ├── Input.vue            # 输入框
│   │   │   └── Avatar.vue           # 头像组件
│   │   ├── business/                # 业务组件
│   │   │   ├── LobbyCard.vue        # 车队卡片
│   │   │   ├── ScriptCard.vue       # 剧本卡片
│   │   │   ├── UserCard.vue         # 用户卡片
│   │   │   ├── ReviewItem.vue       # 评论项
│   │   │   └── FeedItem.vue         # 动态项
│   │   └── layout/                  # 布局组件
│   │       ├── DefaultLayout.vue    # 默认布局
│   │       ├── AuthLayout.vue       # 认证页面布局
│   │       └── FullscreenLayout.vue # 全屏布局
│   ├── composables/                 # 组合式函数
│   │   ├── useAuth.ts               # 认证逻辑
│   │   ├── useWebSocket.ts          # WebSocket 连接
│   │   ├── useCountdown.ts          # 倒计时逻辑
│   │   ├── useInfiniteScroll.ts     # 无限滚动
│   │   ├── useLocalStorage.ts       # 本地存储
│   │   └── useTheme.ts              # 主题管理
│   ├── directives/                  # 自定义指令
│   │   ├── loading.ts               # 加载指令
│   │   ├── lazy.ts                  # 懒加载指令
│   │   └── ripple.ts                # 波纹效果指令
│   ├── plugins/                     # 插件配置
│   │   ├── element-plus.ts          # Element Plus 配置
│   │   ├── router.ts                # 路由配置
│   │   └── pinia.ts                 # 状态管理配置
│   ├── router/                      # 路由配置
│   │   ├── index.ts                 # 主路由文件
│   │   ├── guards.ts                # 路由守卫
│   │   └── routes/                  # 路由模块
│   │       ├── home.ts              # 首页路由
│   │       ├── lobby.ts             # 拼车大厅路由
│   │       ├── script.ts            # 剧本相关路由
│   │       ├── user.ts              # 用户相关路由
│   │       └── feed.ts              # 动态相关路由
│   ├── stores/                      # Pinia 状态管理
│   │   ├── index.ts                 # Store 入口
│   │   ├── auth.ts                  # 认证状态
│   │   ├── user.ts                  # 用户信息状态
│   │   ├── lobby.ts                 # 车队状态
│   │   ├── script.ts                # 剧本状态
│   │   └── app.ts                   # 应用全局状态
│   ├── styles/                      # 样式文件
│   │   ├── index.scss               # 样式入口
│   │   ├── variables.scss           # SCSS 变量
│   │   ├── mixins.scss              # SCSS 混入
│   │   ├── reset.scss               # 样式重置
│   │   ├── themes/                  # 主题样式
│   │   │   └── dark.scss            # 暗黑主题
│   │   ├── components/              # 组件样式
│   │   │   ├── glassmorphism.scss   # 毛玻璃效果
│   │   │   ├── neon-effects.scss    # 霓虹效果
│   │   │   └── animations.scss      # 动画效果
│   │   └── utilities/               # 工具类样式
│   │       ├── spacing.scss         # 间距工具类
│   │       ├── typography.scss      # 字体工具类
│   │       └── colors.scss          # 颜色工具类
│   ├── types/                       # TypeScript 类型定义
│   │   ├── index.ts                 # 类型入口
│   │   ├── api.ts                   # API 相关类型
│   │   ├── user.ts                  # 用户相关类型
│   │   ├── script.ts                # 剧本相关类型
│   │   ├── lobby.ts                 # 车队相关类型
│   │   └── common.ts                # 通用类型
│   ├── utils/                       # 工具函数
│   │   ├── index.ts                 # 工具函数入口
│   │   ├── format.ts                # 格式化函数
│   │   ├── validation.ts            # 验证函数
│   │   ├── storage.ts               # 存储工具
│   │   ├── date.ts                  # 日期处理
│   │   └── constants.ts             # 常量定义
│   ├── views/                       # 页面组件
│   │   ├── Home/                    # 首页
│   │   │   ├── index.vue            # 首页主组件
│   │   │   ├── components/          # 首页专用组件
│   │   │   │   ├── HeroSection.vue  # 英雄区域
│   │   │   │   ├── FeaturedScripts.vue # 热门剧本
│   │   │   │   ├── LiveLobbyFeed.vue   # 实时车队
│   │   │   │   └── CommunityPreview.vue # 社区预览
│   │   │   └── composables/         # 首页专用逻辑
│   │   │       └── useHomeData.ts   # 首页数据逻辑
│   │   ├── Lobby/                   # 拼车大厅
│   │   │   ├── index.vue            # 大厅主页
│   │   │   ├── LobbyDetail.vue      # 车队详情
│   │   │   ├── CreateLobby.vue      # 创建车队
│   │   │   └── components/          # 大厅专用组件
│   │   │       ├── LobbyFilter.vue  # 筛选器
│   │   │       ├── LobbyList.vue    # 车队列表
│   │   │       └── QuickJoin.vue    # 快速加入
│   │   ├── Script/                  # 剧本相关
│   │   │   ├── ScriptList.vue       # 剧本列表
│   │   │   ├── ScriptDetail.vue     # 剧本详情
│   │   │   └── components/          # 剧本专用组件
│   │   │       ├── ScriptHero.vue   # 剧本英雄区
│   │   │       ├── ScriptInfo.vue   # 剧本信息
│   │   │       ├── ReviewSection.vue # 评论区
│   │   │       └── AvailableLobbies.vue # 可用车队
│   │   ├── Feed/                    # 动态广场
│   │   │   ├── index.vue            # 动态主页
│   │   │   ├── CreatePost.vue       # 发布动态
│   │   │   └── components/          # 动态专用组件
│   │   │       ├── FeedList.vue     # 动态列表
│   │   │       ├── PostEditor.vue   # 动态编辑器
│   │   │       └── MediaUpload.vue  # 媒体上传
│   │   ├── User/                    # 用户相关
│   │   │   ├── Profile.vue          # 个人资料
│   │   │   ├── Settings.vue         # 设置页面
│   │   │   ├── MyLobbies.vue        # 我的车队
│   │   │   └── Favorites.vue        # 我的收藏
│   │   └── Auth/                    # 认证相关
│   │       ├── Login.vue            # 登录页面
│   │       ├── Register.vue         # 注册页面
│   │       └── ForgotPassword.vue   # 忘记密码
│   ├── App.vue                      # 根组件
│   └── main.ts                      # 应用入口
├── tests/                           # 测试文件
│   ├── unit/                        # 单元测试
│   ├── integration/                 # 集成测试
│   └── e2e/                         # 端到端测试
├── .env                             # 环境变量
├── .env.development                 # 开发环境变量
├── .env.production                  # 生产环境变量
├── .gitignore                       # Git 忽略文件
├── index.html                       # HTML 模板
├── package.json                     # 项目配置
├── tsconfig.json                    # TypeScript 配置
├── vite.config.ts                   # Vite 配置
└── README.md                        # 项目说明
```

## 核心设计原则

### 1. 模块化设计
- 按功能模块组织代码，每个模块职责单一
- 组件按复用性分层：通用组件 → UI组件 → 业务组件
- API 按业务模块分组，便于维护

### 2. 类型安全
- 全面使用 TypeScript，确保类型安全
- 统一的类型定义，避免重复声明
- API 响应类型与后端保持同步

### 3. 可维护性
- 清晰的文件命名规范
- 组合式函数封装复用逻辑
- 样式模块化，主题可配置

### 4. 性能优化
- 路由懒加载
- 组件按需导入
- 图片懒加载
- 无限滚动优化

### 5. 开发体验
- 完善的开发工具配置
- 统一的代码规范
- 自动化测试覆盖
- 热更新开发环境
