# 迷雾拼本 - 剧本杀拼车平台前端

## 项目简介

迷雾拼本是一个现代化的剧本杀拼车平台，为剧本杀爱好者提供便捷的组局和社交功能。

## 技术栈

- **框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **样式**: SCSS
- **HTTP客户端**: Axios
- **工具库**: VueUse

## 功能特色

### 🏠 首页
- 英雄区域展示
- 热门剧本推荐
- 实时拼车信息流
- 社区动态预览

### 🚗 拼车大厅
- 高级筛选功能
- 实时车队信息
- 快速创建车队
- 网格/列表视图切换

### 📚 剧本库
- 剧本详情展示
- 角色信息介绍
- 用户评价系统
- 可用车队查看

### 💬 动态广场
- 用户动态分享
- 多媒体内容支持
- 互动功能（点赞、评论、分享）
- 热门话题推荐

## 快速开始

### 环境要求

- Node.js >= 18.0.0
- npm >= 8.0.0

### 安装依赖

```bash
cd frontend
npm install
```

### 启动开发服务器

```bash
npm run dev
```

项目将在 http://localhost:3000 启动

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 项目结构

```
frontend/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API 接口
│   ├── components/        # 组件
│   │   ├── common/        # 通用组件
│   │   └── layout/        # 布局组件
│   ├── router/            # 路由配置
│   ├── stores/            # 状态管理
│   ├── styles/            # 样式文件
│   ├── types/             # 类型定义
│   ├── views/             # 页面组件
│   ├── App.vue            # 根组件
│   └── main.ts            # 入口文件
├── index.html             # HTML 模板
├── package.json           # 项目配置
├── tsconfig.json          # TypeScript 配置
└── vite.config.ts         # Vite 配置
```

## 开发指南

### 代码规范

项目使用 ESLint + Prettier 进行代码规范检查：

```bash
# 检查代码规范
npm run lint

# 格式化代码
npm run format

# 类型检查
npm run type-check
```

### 组件开发

- 使用 Vue 3 Composition API
- 支持 TypeScript 类型检查
- 遵循单一职责原则
- 组件名使用 PascalCase

### 样式开发

- 使用 SCSS 预处理器
- 支持 CSS 变量和混合
- 响应式设计优先
- 遵循 BEM 命名规范

### 状态管理

- 使用 Pinia 进行状态管理
- 模块化 store 设计
- 支持数据持久化
- TypeScript 类型安全

## 部署

### 环境变量

创建 `.env.production` 文件：

```env
VITE_APP_API_BASE_URL=https://api.misty-labyrinth.com
VITE_APP_WS_URL=wss://ws.misty-labyrinth.com
```

### 构建部署

```bash
# 构建生产版本
npm run build

# 部署到服务器
# 将 dist 目录上传到 Web 服务器
```

## 浏览器支持

- Chrome >= 80
- Firefox >= 75
- Safari >= 13
- Edge >= 80

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系我们

- 官网: https://misty-labyrinth.com
- 邮箱: <EMAIL>
- QQ群: 123456789
