<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="迷雾拼本 - 探索无限可能的剧本世界，与志同道合的伙伴一起，开启沉浸式剧本杀之旅" />
    <meta name="keywords" content="剧本杀,拼车,剧本,推理,悬疑,角色扮演" />
    <meta name="author" content="迷雾拼本团队" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://misty-labyrinth.com/" />
    <meta property="og:title" content="迷雾拼本 - 剧本杀拼车平台" />
    <meta property="og:description" content="探索无限可能的剧本世界，与志同道合的伙伴一起，开启沉浸式剧本杀之旅" />
    <meta property="og:image" content="/og-image.jpg" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://misty-labyrinth.com/" />
    <meta property="twitter:title" content="迷雾拼本 - 剧本杀拼车平台" />
    <meta property="twitter:description" content="探索无限可能的剧本世界，与志同道合的伙伴一起，开启沉浸式剧本杀之旅" />
    <meta property="twitter:image" content="/og-image.jpg" />

    <!-- 主题色 -->
    <meta name="theme-color" content="#00F5D4" />
    <meta name="msapplication-TileColor" content="#00F5D4" />
    
    <!-- PWA 配置 -->
    <link rel="manifest" href="/manifest.json" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="迷雾拼本" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    
    <title>迷雾拼本 - 探索无限可能的剧本世界</title>
    
    <!-- 加载动画样式 -->
    <style>
      #initial-loader {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(180deg, #1A1A2E 0%, #16213E 50%, #0F0F1E 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: opacity 0.5s ease;
      }
      
      .loader-logo {
        font-size: 3rem;
        margin-bottom: 2rem;
        filter: drop-shadow(0 0 20px rgba(0, 245, 212, 0.5));
        animation: float 3s ease-in-out infinite;
      }
      
      .loader-text {
        font-size: 1.5rem;
        font-weight: 700;
        color: #fff;
        background: linear-gradient(135deg, #00F5D4, #FF00E4);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 2rem;
      }
      
      .loader-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(0, 245, 212, 0.1);
        border-top: 3px solid #00F5D4;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      .loader-subtitle {
        margin-top: 1rem;
        color: #B0B0B0;
        font-size: 0.9rem;
      }
      
      @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .fade-out {
        opacity: 0;
        pointer-events: none;
      }
    </style>
  </head>
  <body>
    <!-- 初始加载动画 -->
    <div id="initial-loader">
      <div class="loader-logo">🌫️</div>
      <div class="loader-text">迷雾拼本</div>
      <div class="loader-spinner"></div>
      <div class="loader-subtitle">正在加载精彩内容...</div>
    </div>
    
    <!-- Vue 应用挂载点 -->
    <div id="app"></div>
    
    <!-- 应用脚本 -->
    <script type="module" src="/src/main.ts"></script>
    
    <!-- 移除加载动画 -->
    <script>
      window.addEventListener('load', () => {
        setTimeout(() => {
          const loader = document.getElementById('initial-loader');
          if (loader) {
            loader.classList.add('fade-out');
            setTimeout(() => {
              loader.remove();
            }, 500);
          }
        }, 1000);
      });
    </script>
    
    <!-- 浏览器兼容性检查 -->
    <script>
      // 检查是否支持 ES6
      try {
        new Function('(a = 0) => a');
      } catch (e) {
        document.body.innerHTML = `
          <div style="
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            background: #1A1A2E;
            color: #fff;
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 20px;
          ">
            <h1 style="color: #00F5D4; margin-bottom: 20px;">浏览器版本过低</h1>
            <p style="margin-bottom: 20px;">您的浏览器版本过低，无法正常使用本网站。</p>
            <p>请升级到以下浏览器的最新版本：</p>
            <ul style="list-style: none; padding: 0; margin: 20px 0;">
              <li>Chrome 80+</li>
              <li>Firefox 75+</li>
              <li>Safari 13+</li>
              <li>Edge 80+</li>
            </ul>
          </div>
        `;
      }
    </script>
  </body>
</html>
