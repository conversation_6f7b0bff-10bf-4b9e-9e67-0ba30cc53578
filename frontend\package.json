{"name": "misty-labyrinth-frontend", "version": "1.0.0", "description": "迷雾拼本 - 剧本杀拼车平台前端", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "axios": "^1.6.2", "@vueuse/core": "^10.7.0", "@vueuse/components": "^10.7.0", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "@vue/tsconfig": "^0.5.1", "typescript": "~5.3.0", "vue-tsc": "^1.8.25", "vite": "^5.0.10", "@types/node": "^20.10.6", "sass": "^1.70.0", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.1.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.2", "@vue/eslint-config-typescript": "^12.0.0", "@vue/eslint-config-prettier": "^8.0.0", "@rushstack/eslint-patch": "^1.6.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}