<template>
  <div id="app" class="app-container">
    <!-- 全局加载遮罩 -->
    <GlobalLoading v-if="appStore.isLoading" :text="appStore.loadingText" />
    
    <!-- 路由视图 -->
    <router-view v-slot="{ Component, route }">
      <transition
        :name="getTransitionName(route)"
        mode="out-in"
        @before-enter="onBeforeEnter"
        @after-enter="onAfterEnter"
      >
        <component :is="Component" :key="route.path" />
      </transition>
    </router-view>
    
    <!-- 全局通知 -->
    <NotificationContainer />
    
    <!-- 网络状态提示 -->
    <NetworkStatus v-if="!appStore.isOnline" />
    
    <!-- 返回顶部按钮 -->
    <BackToTop />
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { useAppStore } from '@/stores'
import GlobalLoading from '@/components/common/GlobalLoading.vue'
import NotificationContainer from '@/components/common/NotificationContainer.vue'
import NetworkStatus from '@/components/common/NetworkStatus.vue'
import BackToTop from '@/components/common/BackToTop.vue'

// 路由
const route = useRoute()

// 状态管理
const appStore = useAppStore()

// 页面转场动画名称
const getTransitionName = (route: any) => {
  // 根据路由层级决定转场动画
  const depth = route.path.split('/').length
  
  if (route.meta?.transition) {
    return route.meta.transition
  }
  
  // 默认转场动画
  if (depth <= 2) {
    return 'fade'
  } else {
    return 'slide-left'
  }
}

// 转场动画钩子
const onBeforeEnter = () => {
  // 页面切换开始时的处理
  document.body.style.overflow = 'hidden'
}

const onAfterEnter = () => {
  // 页面切换完成后的处理
  document.body.style.overflow = ''
  
  // 滚动到顶部
  window.scrollTo(0, 0)
}

// 键盘快捷键
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl/Cmd + K 打开搜索
  if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
    event.preventDefault()
    // 触发搜索功能
    console.log('打开搜索')
  }
  
  // ESC 关闭模态框
  if (event.key === 'Escape') {
    // 可以在这里处理全局 ESC 逻辑
    console.log('ESC 键被按下')
  }
}

// 生命周期
onMounted(() => {
  // 初始化应用
  appStore.initializeApp()
  
  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)
  
  // 添加页面可见性监听
  document.addEventListener('visibilitychange', handleVisibilityChange)
  
  // 添加网络状态监听
  window.addEventListener('online', handleOnline)
  window.addEventListener('offline', handleOffline)
  
  // 添加未处理的 Promise 错误监听
  window.addEventListener('unhandledrejection', handleUnhandledRejection)
  
  // 页面加载完成提示
  appStore.showSuccess('欢迎来到迷雾拼本', '探索无限可能的剧本世界')
})

onUnmounted(() => {
  // 清理事件监听
  document.removeEventListener('keydown', handleKeydown)
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  window.removeEventListener('online', handleOnline)
  window.removeEventListener('offline', handleOffline)
  window.removeEventListener('unhandledrejection', handleUnhandledRejection)
})

// 事件处理函数
const handleVisibilityChange = () => {
  if (document.visibilityState === 'visible') {
    console.log('页面变为可见')
    // 可以在这里刷新数据或重新连接 WebSocket
  } else {
    console.log('页面变为隐藏')
    // 可以在这里暂停一些操作
  }
}

const handleOnline = () => {
  appStore.setOnlineStatus(true)
  appStore.showSuccess('网络已连接', '您现在可以正常使用所有功能')
}

const handleOffline = () => {
  appStore.setOnlineStatus(false)
  appStore.showWarning('网络已断开', '请检查您的网络连接')
}

const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
  console.error('未处理的 Promise 错误:', event.reason)
  appStore.handleError(new Error(event.reason), 'unhandledrejection')
  event.preventDefault()
}
</script>

<style lang="scss">
// 全局样式
.app-container {
  min-height: 100vh;
  background: var(--color-background, #1A1A2E);
  color: var(--color-text, #FFFFFF);
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
  font-size: var(--font-size-base, 16px);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 页面转场动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.3s ease;
}

.slide-left-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.slide-left-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.3s ease;
}

.slide-right-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.slide-right-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(30px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 245, 212, 0.3);
  border-radius: 4px;
  
  &:hover {
    background: rgba(0, 245, 212, 0.5);
  }
}

// 选择文本样式
::selection {
  background: rgba(0, 245, 212, 0.3);
  color: #fff;
}

::-moz-selection {
  background: rgba(0, 245, 212, 0.3);
  color: #fff;
}

// 焦点样式
:focus-visible {
  outline: 2px solid var(--color-primary, #00F5D4);
  outline-offset: 2px;
}

// 禁用状态
[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

// 响应式断点
@media (max-width: 768px) {
  .app-container {
    font-size: 14px;
  }
}

// 打印样式
@media print {
  .app-container {
    background: white !important;
    color: black !important;
  }
  
  // 隐藏不需要打印的元素
  nav,
  .floating-actions,
  .notification-container,
  .back-to-top {
    display: none !important;
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .app-container {
    --color-primary: #00FFFF;
    --color-secondary: #FF00FF;
  }
}

// 减少动画模式
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// 暗色模式
@media (prefers-color-scheme: dark) {
  .theme-auto {
    --color-background: #1A1A2E;
    --color-surface: #16213E;
    --color-text: #FFFFFF;
  }
}

// 亮色模式
@media (prefers-color-scheme: light) {
  .theme-auto {
    --color-background: #FFFFFF;
    --color-surface: #F5F5F5;
    --color-text: #333333;
  }
}
</style>
