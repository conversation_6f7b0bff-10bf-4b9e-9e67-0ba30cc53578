import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { useAppStore, useUserStore } from '@/stores'
import { useAuthStore } from '@/stores/modules/auth'

// 创建 axios 实例
const http: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    // 添加认证 token
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 添加请求 ID 用于追踪
    config.headers['X-Request-ID'] = generateRequestId()

    // 显示加载状态
    const appStore = useAppStore()
    appStore.setLoading(true)

    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
http.interceptors.response.use(
  (response: AxiosResponse) => {
    // 隐藏加载状态
    const appStore = useAppStore()
    appStore.setLoading(false)

    // 统一处理响应数据
    const { data } = response

    // 如果后端返回的是新的 ApiResponse 格式 (code, message, data)
    if (data && typeof data === 'object' && 'code' in data) {
      if (data.code !== 200) {
        // 业务错误
        handleBusinessError(data)
        return Promise.reject(new Error(data.message || '请求失败'))
      }
      return data
    }

    // 如果后端返回的是旧的格式 (success, errorMsg, data)
    if (data && typeof data === 'object' && 'success' in data) {
      if (!data.success) {
        // 业务错误
        handleBusinessError(data)
        return Promise.reject(new Error(data.errorMsg || '请求失败'))
      }
      return data
    }

    // 直接返回数据
    return {
      code: 200,
      message: '操作成功',
      data
    }
  },
  (error) => {
    // 隐藏加载状态
    const appStore = useAppStore()
    appStore.setLoading(false)

    // 处理网络错误
    handleNetworkError(error)
    return Promise.reject(error)
  }
)

// 处理业务错误
function handleBusinessError(data: any) {
  const appStore = useAppStore()
  const userStore = useUserStore()

  // 获取错误消息，兼容新旧格式
  const errorMessage = data.message || data.errorMsg || '服务器返回错误'
  const errorCode = data.code || data.errorCode

  // 根据错误码处理
  switch (errorCode) {
    case 401:
    case 'UNAUTHORIZED':
      // 未授权，清除所有用户相关数据并跳转到登录页
      handleUnauthorized(appStore, userStore)
      break
    case 403:
    case 'FORBIDDEN':
      appStore.showError('权限不足', '您没有权限执行此操作')
      break
    case 404:
    case 'NOT_FOUND':
      appStore.showError('资源不存在', '请求的资源不存在或已被删除')
      break
    case 400:
    case 'VALIDATION_ERROR':
      appStore.showError('参数错误', errorMessage)
      break
    default:
      appStore.showError('请求失败', errorMessage)
  }
}

// 处理网络错误
function handleNetworkError(error: any) {
  const appStore = useAppStore()
  const userStore = useUserStore()

  // 检查是否是认证相关的错误
  const isAuthError = checkIfAuthError(error)

  if (isAuthError) {
    // 认证相关错误，统一处理为401
    handleUnauthorized(appStore, userStore)
    return
  }

  if (error.code === 'ECONNABORTED') {
    appStore.showError('请求超时', '网络连接超时，请稍后重试')
  } else if (error.response) {
    // 服务器返回错误状态码
    const { status, statusText } = error.response

    switch (status) {
      case 400:
        appStore.showError('请求错误', '请求参数有误')
        break
      case 401:
        // 未授权，清除所有用户相关数据并跳转到登录页
        handleUnauthorized(appStore, userStore)
        break
      case 403:
        appStore.showError('权限不足', '您没有权限访问此资源')
        break
      case 404:
        appStore.showError('资源不存在', '请求的资源不存在')
        break
      case 429:
        appStore.showError('请求过于频繁', '请稍后再试')
        break
      case 500:
        appStore.showError('服务器错误', '服务器内部错误，请稍后重试')
        break
      case 502:
        appStore.showError('网关错误', '服务暂时不可用，请稍后重试')
        break
      case 503:
        appStore.showError('服务不可用', '服务正在维护，请稍后重试')
        break
      default:
        appStore.showError('网络错误', `${status} ${statusText}`)
    }
  } else if (error.request) {
    // 网络连接错误
    appStore.showError('网络连接失败', '请检查您的网络连接')
  } else {
    // 其他错误
    appStore.showError('请求失败', error.message || '未知错误')
  }
}

// 检查是否是认证相关的错误
function checkIfAuthError(error: any): boolean {
  // 检查错误消息中是否包含认证相关的关键词
  const authErrorKeywords = [
    'unauthorized', 'authentication', 'token', 'login', 'auth',
    '401', 'invalid token', 'expired token', 'access denied'
  ]

  const errorMessage = (error.message || '').toLowerCase()
  const hasAuthKeyword = authErrorKeywords.some(keyword =>
    errorMessage.includes(keyword)
  )

  // 检查是否是发送到认证相关端点的请求
  const authEndpoints = ['/user/me', '/user/profile', '/user/logout']
  const requestUrl = error.config?.url || ''
  const isAuthEndpoint = authEndpoints.some(endpoint =>
    requestUrl.includes(endpoint)
  )

  // 检查是否有Authorization头但请求失败
  const hasAuthHeader = error.config?.headers?.Authorization
  const isNetworkError = !error.response && error.request

  // 如果是发送到认证端点的请求失败，或者有认证头但网络错误，很可能是认证问题
  return (isAuthEndpoint && isNetworkError) ||
         (hasAuthHeader && isNetworkError && hasAuthKeyword)
}

// 统一处理401未授权错误
function handleUnauthorized(appStore: any, userStore: any) {
  // 显示错误提示
  appStore.showError('认证失败', '登录已过期，请重新登录')

  // 清除localStorage中的token
  localStorage.removeItem('auth_token')

  // 清除Pinia store中的用户数据
  userStore.clearUserData()

  // 清除认证store状态
  try {
    const authStore = useAuthStore()
    authStore.logout()
  } catch (error) {
    console.warn('清除认证状态失败:', error)
  }

  // 延迟跳转，确保用户能看到错误提示
  setTimeout(() => {
    window.location.href = '/auth/login'
  }, 1500)
}

// 生成请求 ID
function generateRequestId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 请求重试机制
export function createRetryableRequest<T>(
  requestFn: () => Promise<T>,
  maxRetries = 3,
  delay = 1000
): Promise<T> {
  return new Promise((resolve, reject) => {
    let retries = 0

    const attempt = async () => {
      try {
        const result = await requestFn()
        resolve(result)
      } catch (error) {
        retries++
        
        if (retries <= maxRetries) {
          console.log(`请求失败，${delay}ms 后进行第 ${retries} 次重试`)
          setTimeout(attempt, delay * retries)
        } else {
          reject(error)
        }
      }
    }

    attempt()
  })
}

// 并发请求控制
export class RequestQueue {
  private queue: Array<() => Promise<any>> = []
  private running = 0
  private maxConcurrent = 5

  add<T>(requestFn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push(async () => {
        try {
          this.running++
          const result = await requestFn()
          resolve(result)
        } catch (error) {
          reject(error)
        } finally {
          this.running--
          this.processQueue()
        }
      })

      this.processQueue()
    })
  }

  private processQueue() {
    if (this.running >= this.maxConcurrent || this.queue.length === 0) {
      return
    }

    const request = this.queue.shift()
    if (request) {
      request()
    }
  }
}

// 创建全局请求队列实例
export const requestQueue = new RequestQueue()

export { http }
