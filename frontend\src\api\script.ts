import { http } from '@/api/http'
import type { ApiResponse, PageResponse } from '@/types/api'
import type { 
  Script, 
  ScriptDetail, 
  ScriptSearchParams, 
  CategoryStats 
} from '@/types/script'

export interface ScriptAPI {
  // 获取剧本列表
  getScriptList(params: ScriptSearchParams): Promise<ApiResponse<PageResponse<Script>>>
  
  // 搜索剧本
  searchScripts(params: ScriptSearchParams): Promise<ApiResponse<PageResponse<Script>>>
  
  // 获取剧本详情
  getScriptDetail(id: number): Promise<ApiResponse<ScriptDetail>>
  
  // 获取热门剧本
  getPopularScripts(limit?: number): Promise<ApiResponse<Script[]>>
  
  // 获取推荐剧本
  getRecommendedScripts(limit?: number): Promise<ApiResponse<Script[]>>
  
  // 获取剧本分类统计
  getCategoryStats(): Promise<ApiResponse<CategoryStats[]>>
  
  // 获取搜索建议
  getSearchSuggestions(keyword: string): Promise<ApiResponse<string[]>>
  
  // 获取热门搜索
  getHotSearchKeywords(): Promise<ApiResponse<string[]>>
  
  // 重建搜索索引（管理员功能）
  rebuildSearchIndex(): Promise<ApiResponse<void>>

  // 切换收藏状态
  toggleFavorite(scriptId: number): Promise<ApiResponse<boolean>>
}

export const scriptApi: ScriptAPI = {
  getScriptList(params: ScriptSearchParams) {
    return http.get('/scripts/list', { params })
  },
  
  searchScripts(params: ScriptSearchParams) {
    return http.get('/scripts/search', { params })
  },
  
  getScriptDetail(id: number) {
    return http.get(`/scripts/${id}`)
  },
  
  getPopularScripts(limit = 10) {
    return http.get('/scripts/popular', { params: { limit } })
  },
  
  getRecommendedScripts(limit = 10) {
    return http.get('/scripts/recommendations', { params: { limit } })
  },
  
  getCategoryStats() {
    return http.get('/scripts/categories')
  },
  
  getSearchSuggestions(keyword: string) {
    return http.get('/scripts/search/suggestions', { params: { keyword } })
  },
  
  getHotSearchKeywords() {
    return http.get('/scripts/search/hot')
  },
  
  rebuildSearchIndex() {
    return http.post('/scripts/search/index/rebuild')
  },

  toggleFavorite(scriptId: number) {
    return http.post('/scripts/favorites/toggle', null, {
      params: { scriptId }
    })
  }
}