import request from '@/utils/request'

/**
 * 用户设置相关API
 */

// ==================== 用户设置接口 ====================

/**
 * 获取用户设置
 */
export function getUserSettings() {
  return request.get('/user/settings')
}

/**
 * 更新用户设置
 * @param {Object} settings 设置数据
 */
export function updateUserSettings(settings) {
  return request.put('/user/settings', settings)
}

/**
 * 重置为默认设置
 */
export function resetToDefaults() {
  return request.post('/user/settings/reset')
}

/**
 * 切换主题
 */
export function toggleTheme() {
  return request.post('/user/settings/toggle-theme')
}

/**
 * 切换通知设置
 * @param {string} notificationType 通知类型
 */
export function toggleNotification(notificationType) {
  return request.post('/user/settings/toggle-notification', {}, {
    params: { notificationType }
  })
}

/**
 * 更新单个设置项
 * @param {string} fieldName 字段名
 * @param {string} fieldValue 字段值
 */
export function updateSingleSetting(fieldName, fieldValue) {
  return request.put('/user/settings/field', {}, {
    params: { fieldName, fieldValue }
  })
}

/**
 * 获取用户设置摘要
 */
export function getUserSettingsSummary() {
  return request.get('/user/settings/summary')
}

/**
 * 导出用户设置
 */
export function exportUserSettings() {
  return request.get('/user/settings/export')
}

/**
 * 导入用户设置
 * @param {string} settingsJson 设置JSON字符串
 */
export function importUserSettings(settingsJson) {
  return request.post('/user/settings/import', settingsJson, {
    headers: {
      'Content-Type': 'text/plain'
    }
  })
}

// ==================== 登录历史接口 ====================

/**
 * 获取登录历史
 * @param {number} page 页码
 * @param {number} size 每页大小
 */
export function getLoginHistory(page = 1, size = 10) {
  return request.get('/user/login-history', { page, size })
}

/**
 * 获取最近登录记录
 * @param {number} limit 限制数量
 */
export function getRecentLogins(limit = 5) {
  return request.get('/user/login-history/recent', { limit })
}

/**
 * 获取在线会话
 */
export function getOnlineSessions() {
  return request.get('/user/login-history/online')
}

/**
 * 清除登录历史
 */
export function clearLoginHistory() {
  return request.delete('/user/login-history')
}

/**
 * 获取登录统计
 */
export function getLoginStatistics() {
  return request.get('/user/login-history/statistics')
}

/**
 * 检测可疑登录
 */
export function detectSuspiciousLogins() {
  return request.get('/user/login-history/suspicious')
}

/**
 * 生成安全报告
 * @param {number} days 统计天数
 */
export function generateSecurityReport(days = 30) {
  return request.get('/user/login-history/security-report', { days })
}

/**
 * 登出所有会话
 */
export function logoutAllSessions() {
  return request.post('/user/login-history/logout-all')
}

/**
 * 记录登出
 */
export function recordLogout() {
  return request.post('/user/logout-record')
}

// ==================== 设置统计接口（管理员用） ====================

/**
 * 获取主题使用统计
 */
export function getThemeStatistics() {
  return request({
    url: '/admin/settings/theme-statistics',
    method: 'get'
  })
}

/**
 * 获取隐私级别统计
 */
export function getPrivacyLevelStatistics() {
  return request({
    url: '/admin/settings/privacy-statistics',
    method: 'get'
  })
}

/**
 * 获取通知设置统计
 */
export function getNotificationStatistics() {
  return request({
    url: '/admin/settings/notification-statistics',
    method: 'get'
  })
}

/**
 * 获取设备类型统计
 */
export function getDeviceTypeStatistics() {
  return request({
    url: '/admin/login-history/device-statistics',
    method: 'get'
  })
}

/**
 * 获取浏览器统计
 * @param {number} limit 限制数量
 */
export function getBrowserStatistics(limit = 10) {
  return request({
    url: '/admin/login-history/browser-statistics',
    method: 'get',
    params: { limit }
  })
}

/**
 * 获取最活跃用户
 * @param {number} days 统计天数
 * @param {number} limit 限制数量
 */
export function getMostActiveUsers(days = 7, limit = 10) {
  return request({
    url: '/admin/login-history/active-users',
    method: 'get',
    params: { days, limit }
  })
}
