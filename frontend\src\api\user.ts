import { http } from './http'
import type { ApiResponse, User, UserProfile, UserStats, FollowUser } from '@/types/user'

export const userApi = {
  // 获取用户信息 - 对接后端 /api/user/{id}
  getUserInfo(userId: number): Promise<ApiResponse<User>> {
    return http.get(`/user/${userId}`)
  },

  // 获取当前用户信息 - 对接后端 /api/user/me
  getCurrentUser(): Promise<ApiResponse<User>> {
    return http.get('/user/me')
  },

  // 发送验证码 - 对接后端 /api/user/code
  sendCode(phone: string): Promise<ApiResponse<string>> {
    return http.post('/user/code', null, {
      params: { phone }
    })
  },

  // 用户登录 - 对接后端 /api/user/login
  login(credentials: { phone: string; code: string }): Promise<ApiResponse<string>> {
    return http.post('/user/login', credentials)
  },

  // 用户登出 - 对接后端 /api/user/logout
  logout(): Promise<ApiResponse<string>> {
    return http.post('/user/logout')
  },

  // 获取用户详细资料 (暂时使用基础用户信息)
  getUserProfile(userId: number): Promise<ApiResponse<UserProfile>> {
    return http.get(`/user/${userId}`)
  },

  // 获取用户统计数据 (模拟数据)
  getUserStats(userId: number): Promise<ApiResponse<UserStats>> {
    // 暂时返回模拟数据，后续可以对接实际统计接口
    return Promise.resolve({
      code: 200,
      message: '获取成功',
      data: {
        gamesPlayed: 0,
        gamesWon: 0,
        totalPlayTime: 0,
        favoriteGenres: [],
        achievements: [],
        joinDate: new Date().toISOString()
      }
    })
  },

  // 更新用户资料
  updateProfile(data: Partial<UserProfile>): Promise<ApiResponse<boolean>> {
    return http.put('/user/profile', data)
  },

  // 上传头像 - 修复路径，对接后端 /api/upload/avatar
  uploadAvatar(file: File): Promise<ApiResponse<{ url: string }>> {
    const formData = new FormData()
    formData.append('file', file)
    return http.post('/upload/avatar', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  // 修改密码
  changePassword(data: {
    oldPassword: string
    newPassword: string
  }): Promise<ApiResponse<boolean>> {
    return http.put('/user/password', data)
  },

  // 获取我的收藏
  getMyFavorites(params: {
    page: number
    size: number
    type?: string
  }): Promise<ApiResponse<any>> {
    return http.get('/user/favorites', { params })
  },

  // 添加收藏
  addFavorite(data: {
    targetId: number
    targetType: string
  }): Promise<ApiResponse<boolean>> {
    return http.post('/user/favorites', data)
  },

  // 取消收藏
  removeFavorite(id: number): Promise<ApiResponse<boolean>> {
    return http.delete(`/user/favorites/${id}`)
  },

  // 检查是否已收藏
  checkFavorite(targetId: number, targetType: string): Promise<ApiResponse<boolean>> {
    return http.get('/user/favorites/check', {
      params: { targetId, targetType }
    })
  },

  // 关注/取消关注用户 (暂时不支持)
  toggleFollow(targetUserId: number): Promise<ApiResponse<{ isFollowing: boolean }>> {
    return Promise.reject(new Error('暂不支持关注功能'))
  },

  // 获取关注列表 (暂时不支持)
  getFollowList(userId: number, type: 'following' | 'followers'): Promise<ApiResponse<FollowUser[]>> {
    return Promise.reject(new Error('暂不支持关注列表'))
  },

  // 搜索用户 (暂时不支持)
  searchUsers(keyword: string, page = 1, pageSize = 20): Promise<ApiResponse<User[]>> {
    return Promise.reject(new Error('暂不支持用户搜索'))
  }
}
