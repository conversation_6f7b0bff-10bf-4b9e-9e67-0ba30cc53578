<template>
  <div 
    class="lobby-card"
    :class="{ 'lobby-card--disabled': isDisabled }"
    @click="handleCardClick"
  >
    <!-- 剧本封面 -->
    <div class="lobby-card__cover">
      <img 
        :src="lobby.script.coverImage" 
        :alt="lobby.script.title"
        class="lobby-card__cover-image"
      />
      <div class="lobby-card__status-badge" :class="`lobby-card__status-badge--${lobby.status}`">
        {{ statusText }}
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="lobby-card__content">
      <!-- 剧本标题 -->
      <h3 class="lobby-card__title">{{ lobby.script.title }}</h3>
      
      <!-- 房主信息 -->
      <div class="lobby-card__host">
        <img :src="lobby.host.avatar" :alt="lobby.host.nickname" class="lobby-card__host-avatar" />
        <span class="lobby-card__host-name">{{ lobby.host.nickname }}</span>
      </div>

      <!-- 人数进度条 -->
      <div class="lobby-card__progress">
        <div class="lobby-card__progress-info">
          <span class="lobby-card__progress-text">
            {{ lobby.currentPlayers }}/{{ lobby.maxPlayers }}人
          </span>
          <span class="lobby-card__progress-percentage">
            {{ progressPercentage }}%
          </span>
        </div>
        <div class="lobby-card__progress-bar">
          <div 
            class="lobby-card__progress-fill"
            :style="{ width: `${progressPercentage}%` }"
          ></div>
        </div>
      </div>

      <!-- 倒计时 -->
      <div class="lobby-card__countdown" v-if="lobby.status === 'waiting'">
        <i class="icon-clock"></i>
        <span>{{ formattedCountdown }}</span>
      </div>

      <!-- 状态按钮 -->
      <button 
        class="lobby-card__action-btn"
        :class="buttonClass"
        :disabled="isButtonDisabled"
        @click.stop="handleButtonClick"
      >
        <span v-if="!isLoading">{{ buttonText }}</span>
        <div v-else class="lobby-card__loading">
          <div class="lobby-card__spinner"></div>
          <span>加入中...</span>
        </div>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'

// 类型定义
interface Script {
  id: string
  title: string
  coverImage: string
}

interface Host {
  id: string
  nickname: string
  avatar: string
}

interface Lobby {
  id: string
  script: Script
  host: Host
  currentPlayers: number
  maxPlayers: number
  status: 'waiting' | 'full' | 'in_progress'
  startTime: string
  endTime: string
}

// Props
interface Props {
  lobby: Lobby
  viewMode?: 'grid' | 'list'
}

const props = withDefaults(defineProps<Props>(), {
  viewMode: 'grid'
})

// Emits
interface Emits {
  (e: 'join-lobby', lobbyId: string): void
  (e: 'view-details', lobbyId: string): void
  (e: 'favorite', lobbyId: string): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const isLoading = ref(false)
const countdown = ref(0)
const countdownTimer = ref<NodeJS.Timeout | null>(null)

// 计算属性
const progressPercentage = computed(() => {
  return Math.round((props.lobby.currentPlayers / props.lobby.maxPlayers) * 100)
})

const statusText = computed(() => {
  const statusMap = {
    waiting: '等待中',
    full: '已满员',
    in_progress: '游戏中'
  }
  return statusMap[props.lobby.status]
})

const buttonText = computed(() => {
  const textMap = {
    waiting: '一键上车',
    full: '人员已满',
    in_progress: '游戏中'
  }
  return textMap[props.lobby.status]
})

const buttonClass = computed(() => {
  return `lobby-card__action-btn--${props.lobby.status}`
})

const isButtonDisabled = computed(() => {
  return props.lobby.status !== 'waiting' || isLoading.value
})

const isDisabled = computed(() => {
  return props.lobby.status !== 'waiting'
})

const formattedCountdown = computed(() => {
  const hours = Math.floor(countdown.value / 3600)
  const minutes = Math.floor((countdown.value % 3600) / 60)
  const seconds = countdown.value % 60
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
})

// 方法
const calculateCountdown = () => {
  const now = new Date().getTime()
  const startTime = new Date(props.lobby.startTime).getTime()
  const diff = Math.max(0, Math.floor((startTime - now) / 1000))
  countdown.value = diff
  
  if (diff <= 0 && countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
}

const startCountdown = () => {
  if (props.lobby.status === 'waiting') {
    calculateCountdown()
    countdownTimer.value = setInterval(calculateCountdown, 1000)
  }
}

const handleCardClick = () => {
  if (!isDisabled.value) {
    emit('view-details', props.lobby.id)
  }
}

const handleButtonClick = async () => {
  if (props.lobby.status === 'waiting' && !isLoading.value) {
    isLoading.value = true
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1500))
      emit('join-lobby', props.lobby.id)
    } catch (error) {
      console.error('加入车队失败:', error)
    } finally {
      isLoading.value = false
    }
  }
}

// 生命周期
onMounted(() => {
  startCountdown()
})

onUnmounted(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
  }
})
</script>

<style lang="scss" scoped>
.lobby-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 245, 212, 0.1), rgba(255, 0, 228, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }

  &:hover:not(&--disabled) {
    transform: translateY(-8px);
    border-color: rgba(0, 245, 212, 0.6);
    box-shadow: 
      0 20px 40px rgba(0, 0, 0, 0.3),
      0 0 20px rgba(0, 245, 212, 0.3);

    &::before {
      opacity: 1;
    }
  }

  &--disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &__cover {
    position: relative;
    margin-bottom: 16px;

    &-image {
      width: 100%;
      height: 120px;
      object-fit: cover;
      border-radius: 12px;
    }
  }

  &__status-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    backdrop-filter: blur(10px);

    &--waiting {
      background: rgba(0, 245, 212, 0.2);
      color: #00F5D4;
      border: 1px solid rgba(0, 245, 212, 0.4);
    }

    &--full {
      background: rgba(255, 255, 255, 0.1);
      color: #999;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    &--in_progress {
      background: rgba(255, 0, 228, 0.2);
      color: #FF00E4;
      border: 1px solid rgba(255, 0, 228, 0.4);
    }
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  &__title {
    font-size: 18px;
    font-weight: 600;
    color: #fff;
    margin: 0;
    line-height: 1.3;
  }

  &__host {
    display: flex;
    align-items: center;
    gap: 8px;

    &-avatar {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      object-fit: cover;
    }

    &-name {
      font-size: 14px;
      color: #ccc;
    }
  }

  &__progress {
    &-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      font-size: 14px;
    }

    &-text {
      color: #fff;
      font-weight: 500;
    }

    &-percentage {
      color: #00F5D4;
    }

    &-bar {
      height: 6px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
      overflow: hidden;
    }

    &-fill {
      height: 100%;
      background: linear-gradient(90deg, #00F5D4, #FF00E4);
      border-radius: 3px;
      transition: width 0.3s ease;
    }
  }

  &__countdown {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    color: #00F5D4;

    .icon-clock::before {
      content: '⏰';
    }
  }

  &__action-btn {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &--waiting {
      background: linear-gradient(135deg, #00F5D4, #00C9A7);
      color: #1A1A2E;

      &:hover:not(:disabled) {
        transform: scale(1.02);
        box-shadow: 0 0 20px rgba(0, 245, 212, 0.5);
      }

      &:active {
        transform: scale(0.98);
      }
    }

    &--full,
    &--in_progress {
      background: rgba(255, 255, 255, 0.1);
      color: #999;
      cursor: not-allowed;
    }

    &:disabled {
      cursor: not-allowed;
    }
  }

  &__loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  &__spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(26, 26, 46, 0.3);
    border-top: 2px solid #1A1A2E;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
