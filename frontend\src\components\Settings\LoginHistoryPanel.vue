<template>
  <div class="login-history-panel">
    <!-- 安全概览 -->
    <div class="security-overview">
      <h3 class="overview-title">🛡️ 安全概览</h3>
      
      <div class="security-stats">
        <div class="stat-card">
          <div class="stat-icon">📊</div>
          <div class="stat-content">
            <div class="stat-value">{{ statistics.totalLogins }}</div>
            <div class="stat-label">总登录次数</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">✅</div>
          <div class="stat-content">
            <div class="stat-value">{{ successRate }}%</div>
            <div class="stat-label">成功率</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">⏱️</div>
          <div class="stat-content">
            <div class="stat-value">{{ avgSessionDurationText }}</div>
            <div class="stat-label">平均在线时长</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon" :class="{ online: statistics.isOnline }">
            {{ statistics.isOnline ? '🟢' : '⚫' }}
          </div>
          <div class="stat-content">
            <div class="stat-value" :class="{ online: statistics.isOnline }">
              {{ statistics.isOnline ? '在线' : '离线' }}
            </div>
            <div class="stat-label">当前状态</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 可疑登录警告 -->
    <div v-if="hasSuspiciousLogins" class="suspicious-alert">
      <el-alert
        title="⚠️ 检测到可疑登录活动"
        type="warning"
        :description="`发现 ${suspiciousLogins.length} 条可疑登录记录，建议立即检查`"
        show-icon
        :closable="false"
      />
      
      <div class="suspicious-actions">
        <el-button @click="showSuspiciousDetails = !showSuspiciousDetails" text>
          {{ showSuspiciousDetails ? '隐藏详情' : '查看详情' }}
        </el-button>
        <el-button @click="logoutAll" type="danger" size="small">
          登出所有设备
        </el-button>
      </div>
      
      <div v-if="showSuspiciousDetails" class="suspicious-details">
        <div 
          v-for="login in suspiciousLogins.slice(0, 3)" 
          :key="login.id"
          class="suspicious-item"
        >
          <div class="suspicious-info">
            <div class="suspicious-device">
              {{ getDeviceIcon(login.deviceType) }} {{ login.browser }}
            </div>
            <div class="suspicious-location">
              📍 {{ login.loginLocation }} · {{ login.loginIp }}
            </div>
            <div class="suspicious-time">
              🕒 {{ formatLoginTime(login.loginTime) }}
            </div>
          </div>
          <el-tag type="warning" size="small">可疑</el-tag>
        </div>
      </div>
    </div>

    <!-- 在线会话 -->
    <div v-if="hasOnlineSessions" class="online-sessions">
      <div class="section-header">
        <h4 class="section-title">🟢 在线会话 ({{ onlineSessions.length }})</h4>
        <el-button @click="logoutAll" type="danger" size="small" plain>
          登出所有设备
        </el-button>
      </div>
      
      <div class="session-list">
        <div 
          v-for="session in onlineSessions" 
          :key="session.id"
          class="session-item"
        >
          <div class="session-icon">{{ getDeviceIcon(session.deviceType) }}</div>
          <div class="session-info">
            <div class="session-device">{{ session.browser }}</div>
            <div class="session-location">{{ session.loginLocation }}</div>
            <div class="session-time">登录于 {{ formatLoginTime(session.loginTime) }}</div>
          </div>
          <div class="session-status">
            <span class="online-indicator">🟢 在线</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近登录记录 -->
    <div class="recent-logins">
      <div class="section-header">
        <h4 class="section-title">📝 最近登录记录</h4>
        <div class="section-actions">
          <el-button @click="fetchRecentLogins" :loading="loading" size="small" text>
            刷新
          </el-button>
          <el-button @click="$emit('show-full-history')" size="small" text>
            查看全部
          </el-button>
        </div>
      </div>
      
      <div class="login-list">
        <div 
          v-for="login in recentLogins" 
          :key="login.id"
          class="login-item"
          :class="{ failed: login.status === 0 }"
        >
          <div class="login-icon">{{ getDeviceIcon(login.deviceType) }}</div>
          <div class="login-info">
            <div class="login-device">
              {{ login.browser }} · {{ login.deviceTypeDesc }}
            </div>
            <div class="login-location">
              📍 {{ login.loginLocation }} · {{ login.loginIp }}
            </div>
            <div class="login-time">
              🕒 {{ formatLoginTime(login.loginTime) }}
            </div>
          </div>
          <div class="login-status">
            <el-tag :type="login.status === 1 ? 'success' : 'danger'" size="small">
              {{ getStatusIcon(login.status) }} {{ login.statusDesc }}
            </el-tag>
            <span v-if="login.isOnline" class="online-badge">🟢 在线</span>
          </div>
        </div>
        
        <div v-if="recentLogins.length === 0" class="empty-state">
          <el-empty description="暂无登录记录" />
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="panel-actions">
      <el-button @click="exportHistory" icon="Download">
        导出历史
      </el-button>
      <el-button @click="generateReport" icon="Document">
        安全报告
      </el-button>
      <el-button @click="clearHistory" type="danger" plain>
        清除历史
      </el-button>
    </div>

    <!-- 安全报告对话框 -->
    <el-dialog
      v-model="showReport"
      title="安全报告"
      width="600px"
      :before-close="handleCloseReport"
    >
      <div v-if="securityReport" class="security-report">
        <div class="report-summary">
          <h4>报告摘要</h4>
          <p>统计周期：最近 {{ reportDays }} 天</p>
          <p>风险等级：<el-tag :type="riskLevel.color">{{ riskLevel.text }}</el-tag></p>
        </div>
        
        <div class="report-details">
          <h4>详细信息</h4>
          <!-- 这里可以添加更多报告详情 -->
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showReport = false">关闭</el-button>
        <el-button type="primary" @click="downloadReport">下载报告</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useLoginHistory } from '@/composables/useLoginHistory'

// Emits
const emit = defineEmits(['show-full-history'])

// 使用登录历史 Composable
const {
  recentLogins,
  onlineSessions,
  statistics,
  suspiciousLogins,
  hasOnlineSessions,
  hasSuspiciousLogins,
  successRate,
  avgSessionDurationText,
  fetchRecentLogins,
  logoutAll,
  clearHistory,
  exportHistory,
  generateSecurityReport,
  formatLoginTime,
  getDeviceIcon,
  getStatusIcon,
  getRiskLevel
} = useLoginHistory()

// 本地状态
const loading = ref(false)
const showSuspiciousDetails = ref(false)
const showReport = ref(false)
const securityReport = ref(null)
const reportDays = ref(30)

// 计算属性
const riskLevel = computed(() => {
  return getRiskLevel(suspiciousLogins.value.length)
})

// 方法
const generateReport = async () => {
  try {
    loading.value = true
    const report = await generateSecurityReport(reportDays.value)
    securityReport.value = report
    showReport.value = true
  } finally {
    loading.value = false
  }
}

const downloadReport = () => {
  if (!securityReport.value) return
  
  const data = JSON.stringify(securityReport.value, null, 2)
  const blob = new Blob([data], { type: 'application/json' })
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `security-report-${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

const handleCloseReport = () => {
  showReport.value = false
  securityReport.value = null
}
</script>

<style lang="scss" scoped>
.login-history-panel {
  padding: 20px;
}

.security-overview {
  margin-bottom: 30px;
}

.overview-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--theme-text, #ffffff);
  margin: 0 0 20px 0;
}

.security-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: var(--theme-surface, #2d2d2d);
  border: 1px solid var(--theme-border, #333333);
  border-radius: 12px;
  transition: all 0.3s ease;

  &:hover {
    border-color: #00F5D4;
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 245, 212, 0.2);
  }
}

.stat-icon {
  font-size: 2rem;
  margin-right: 15px;

  &.online {
    animation: pulse 2s infinite;
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #00F5D4;
  margin-bottom: 4px;

  &.online {
    color: #67C23A;
  }
}

.stat-label {
  font-size: 0.9rem;
  color: var(--theme-text-secondary, #b3b3b3);
}

.suspicious-alert {
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(230, 162, 60, 0.1);
  border: 1px solid #E6A23C;
  border-radius: 12px;
}

.suspicious-actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.suspicious-details {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid rgba(230, 162, 60, 0.3);
}

.suspicious-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  background: rgba(230, 162, 60, 0.05);
  border-radius: 8px;
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }
}

.suspicious-info {
  flex: 1;
}

.suspicious-device,
.suspicious-location,
.suspicious-time {
  font-size: 0.9rem;
  color: var(--theme-text-secondary, #b3b3b3);
  margin-bottom: 4px;

  &:last-child {
    margin-bottom: 0;
  }
}

.online-sessions,
.recent-logins {
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.section-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--theme-text, #ffffff);
  margin: 0;
}

.section-actions {
  display: flex;
  gap: 10px;
}

.session-list,
.login-list {
  max-height: 400px;
  overflow-y: auto;
}

.session-item,
.login-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 1px solid var(--theme-border, #333333);
  border-radius: 8px;
  margin-bottom: 10px;
  background: var(--theme-surface, #2d2d2d);
  transition: all 0.3s ease;

  &:hover {
    border-color: #00F5D4;
    transform: translateX(5px);
  }

  &.failed {
    border-color: #F56C6C;
    background: rgba(245, 108, 108, 0.05);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.session-icon,
.login-icon {
  font-size: 1.5rem;
  margin-right: 15px;
}

.session-info,
.login-info {
  flex: 1;
}

.session-device,
.login-device {
  font-weight: 600;
  color: var(--theme-text, #ffffff);
  margin-bottom: 4px;
}

.session-location,
.login-location,
.session-time,
.login-time {
  font-size: 0.9rem;
  color: var(--theme-text-secondary, #b3b3b3);
  margin-bottom: 4px;

  &:last-child {
    margin-bottom: 0;
  }
}

.session-status,
.login-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
}

.online-indicator,
.online-badge {
  font-size: 0.8rem;
  color: #67C23A;
  font-weight: 600;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--theme-text-secondary, #b3b3b3);
}

.panel-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  padding-top: 20px;
  border-top: 1px solid var(--theme-border, #333333);

  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.security-report {
  .report-summary,
  .report-details {
    margin-bottom: 20px;

    h4 {
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 10px;
    }

    p {
      margin: 5px 0;
    }
  }
}
</style>
