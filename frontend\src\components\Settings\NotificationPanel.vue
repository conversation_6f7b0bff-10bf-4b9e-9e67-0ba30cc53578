<template>
  <div class="notification-panel">
    <!-- 通知概览 -->
    <div class="notification-overview">
      <div class="overview-stats">
        <div class="stat-circle">
          <el-progress 
            type="circle" 
            :percentage="(enabledCount / 4) * 100"
            :width="80"
            :stroke-width="8"
            :color="progressColor"
          >
            <span class="progress-text">{{ enabledCount }}/4</span>
          </el-progress>
        </div>
        
        <div class="stat-info">
          <h3 class="stat-title">通知设置</h3>
          <p class="stat-description">{{ notificationSummary }}</p>
          <div class="stat-actions">
            <el-button @click="enableAllNotifications" size="small" type="primary">
              全部开启
            </el-button>
            <el-button @click="disableAllNotifications" size="small">
              全部关闭
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <el-divider />

    <!-- 通知类型列表 -->
    <div class="notification-types">
      <div 
        v-for="(config, type) in notificationTypes" 
        :key="type"
        class="notification-type"
      >
        <div class="type-header">
          <div class="type-icon">{{ config.icon }}</div>
          <div class="type-info">
            <h4 class="type-name">{{ config.name }}</h4>
            <p class="type-description">{{ config.description }}</p>
          </div>
          <div class="type-controls">
            <el-switch 
              :model-value="getNotificationStatus(type)"
              @change="handleToggle(type)"
              size="large"
              :loading="toggleLoading[type]"
            />
          </div>
        </div>
        
        <!-- 示例标签 -->
        <div class="type-examples">
          <span 
            v-for="example in config.examples" 
            :key="example" 
            class="example-tag"
          >
            {{ example }}
          </span>
        </div>
        
        <!-- 测试按钮 -->
        <div class="type-actions">
          <el-button 
            @click="sendTestNotification(type)"
            size="small"
            text
            :disabled="!getNotificationStatus(type)"
            :loading="testLoading[type]"
          >
            <el-icon><Bell /></el-icon>
            发送测试通知
          </el-button>
        </div>
      </div>
    </div>

    <el-divider />

    <!-- 快速预设 -->
    <div class="notification-presets">
      <h4 class="presets-title">快速设置</h4>
      <p class="presets-description">根据使用场景快速配置通知偏好</p>
      
      <div class="presets-grid">
        <div 
          v-for="(preset, key) in presets" 
          :key="key"
          class="preset-card"
          @click="applyPreset(key)"
        >
          <div class="preset-icon">{{ getPresetIcon(key) }}</div>
          <div class="preset-info">
            <h5 class="preset-name">{{ preset.name }}</h5>
            <p class="preset-description">{{ preset.description }}</p>
          </div>
          <div class="preset-preview">
            <div class="preview-dots">
              <span 
                v-for="(enabled, notifType) in preset.settings" 
                :key="notifType"
                class="preview-dot"
                :class="{ active: enabled }"
              ></span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-divider />

    <!-- 浏览器通知设置 -->
    <div class="browser-notifications">
      <div class="browser-header">
        <div class="browser-info">
          <h4 class="browser-title">🌐 浏览器通知</h4>
          <p class="browser-description">允许网站发送桌面通知</p>
        </div>
        <div class="browser-status">
          <el-tag :type="browserPermissionType" size="small">
            {{ browserPermissionText }}
          </el-tag>
        </div>
      </div>
      
      <div v-if="browserPermission !== 'granted'" class="browser-actions">
        <el-button 
          @click="requestBrowserPermission"
          :loading="permissionLoading"
          size="small"
          type="primary"
        >
          开启浏览器通知
        </el-button>
      </div>
      
      <div v-else class="browser-test">
        <el-button 
          @click="testBrowserNotification"
          size="small"
        >
          测试桌面通知
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { Bell } from '@element-plus/icons-vue'
import { useNotifications } from '@/composables/useNotifications'

// 使用通知 Composable
const {
  notificationTypes,
  enabledCount,
  notificationSummary,
  getNotificationStatus,
  toggleSingleNotification,
  enableAllNotifications,
  disableAllNotifications,
  sendTestNotification,
  getRecommendedSettings,
  applyRecommendedSettings,
  checkBrowserNotificationPermission,
  requestBrowserNotificationPermission,
  sendBrowserNotification
} = useNotifications()

// 本地状态
const toggleLoading = reactive({})
const testLoading = reactive({})
const permissionLoading = ref(false)
const browserPermission = ref('default')

// 预设配置
const presets = computed(() => ({
  work: getRecommendedSettings('work'),
  gaming: getRecommendedSettings('gaming'),
  minimal: getRecommendedSettings('minimal'),
  full: getRecommendedSettings('full')
}))

// 进度条颜色
const progressColor = computed(() => {
  const percentage = (enabledCount.value / 4) * 100
  if (percentage === 100) return '#67C23A'
  if (percentage >= 50) return '#00F5D4'
  if (percentage > 0) return '#E6A23C'
  return '#F56C6C'
})

// 浏览器权限状态
const browserPermissionType = computed(() => {
  switch (browserPermission.value) {
    case 'granted': return 'success'
    case 'denied': return 'danger'
    default: return 'warning'
  }
})

const browserPermissionText = computed(() => {
  switch (browserPermission.value) {
    case 'granted': return '已授权'
    case 'denied': return '已拒绝'
    default: return '未设置'
  }
})

// 方法
const handleToggle = async (type) => {
  toggleLoading[type] = true
  try {
    await toggleSingleNotification(type)
  } finally {
    toggleLoading[type] = false
  }
}

const handleTestNotification = async (type) => {
  testLoading[type] = true
  try {
    await sendTestNotification(type)
  } finally {
    testLoading[type] = false
  }
}

const applyPreset = async (presetKey) => {
  await applyRecommendedSettings(presetKey)
}

const getPresetIcon = (key) => {
  const icons = {
    work: '💼',
    gaming: '🎮',
    minimal: '🔕',
    full: '🔔'
  }
  return icons[key] || '⚙️'
}

const requestBrowserPermission = async () => {
  permissionLoading.value = true
  try {
    const permission = await requestBrowserNotificationPermission()
    browserPermission.value = permission
  } finally {
    permissionLoading.value = false
  }
}

const testBrowserNotification = () => {
  sendBrowserNotification('测试通知', {
    body: '这是一条来自剧本杀平台的测试通知',
    icon: '/favicon.ico'
  })
}

// 生命周期
onMounted(() => {
  browserPermission.value = checkBrowserNotificationPermission()
})
</script>

<style lang="scss" scoped>
.notification-panel {
  padding: 20px;
}

.notification-overview {
  margin-bottom: 30px;
}

.overview-stats {
  display: flex;
  align-items: center;
  gap: 30px;

  @media (max-width: 768px) {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
}

.stat-circle {
  .progress-text {
    font-size: 1rem;
    font-weight: 600;
    color: var(--theme-text, #ffffff);
  }
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--theme-text, #ffffff);
  margin: 0 0 8px 0;
}

.stat-description {
  font-size: 1rem;
  color: var(--theme-text-secondary, #b3b3b3);
  margin: 0 0 15px 0;
}

.stat-actions {
  display: flex;
  gap: 10px;
}

.notification-types {
  margin: 30px 0;
}

.notification-type {
  padding: 20px;
  border: 1px solid var(--theme-border, #333333);
  border-radius: 12px;
  margin-bottom: 20px;
  background: var(--theme-background, #1a1a1a);
  transition: all 0.3s ease;

  &:hover {
    border-color: #00F5D4;
    box-shadow: 0 4px 16px rgba(0, 245, 212, 0.1);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.type-header {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 15px;
}

.type-icon {
  font-size: 1.8rem;
  margin-top: 2px;
}

.type-info {
  flex: 1;
}

.type-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--theme-text, #ffffff);
  margin: 0 0 8px 0;
}

.type-description {
  font-size: 1rem;
  color: var(--theme-text-secondary, #b3b3b3);
  margin: 0;
  line-height: 1.5;
}

.type-controls {
  margin-left: 20px;
}

.type-examples {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 15px;
}

.example-tag {
  font-size: 0.85rem;
  background: rgba(0, 245, 212, 0.1);
  color: #00F5D4;
  padding: 4px 10px;
  border-radius: 8px;
  border: 1px solid rgba(0, 245, 212, 0.3);
}

.type-actions {
  text-align: right;
}

.notification-presets {
  margin: 30px 0;
}

.presets-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--theme-text, #ffffff);
  margin: 0 0 8px 0;
}

.presets-description {
  font-size: 1rem;
  color: var(--theme-text-secondary, #b3b3b3);
  margin: 0 0 20px 0;
}

.presets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.preset-card {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 1px solid var(--theme-border, #333333);
  border-radius: 12px;
  background: var(--theme-background, #1a1a1a);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #00F5D4;
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 245, 212, 0.2);
  }
}

.preset-icon {
  font-size: 1.5rem;
  margin-right: 15px;
}

.preset-info {
  flex: 1;
}

.preset-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--theme-text, #ffffff);
  margin: 0 0 4px 0;
}

.preset-description {
  font-size: 0.9rem;
  color: var(--theme-text-secondary, #b3b3b3);
  margin: 0;
}

.preset-preview {
  margin-left: 15px;
}

.preview-dots {
  display: flex;
  gap: 4px;
}

.preview-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--theme-border, #333333);
  transition: all 0.3s ease;

  &.active {
    background: #00F5D4;
    box-shadow: 0 0 8px rgba(0, 245, 212, 0.5);
  }
}

.browser-notifications {
  margin: 30px 0;
}

.browser-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}

.browser-info {
  flex: 1;
}

.browser-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--theme-text, #ffffff);
  margin: 0 0 4px 0;
}

.browser-description {
  font-size: 0.9rem;
  color: var(--theme-text-secondary, #b3b3b3);
  margin: 0;
}

.browser-status {
  margin-left: 20px;
}

.browser-actions,
.browser-test {
  text-align: center;
}
</style>
