# 用户设置模块

## 📋 概述

用户设置模块提供了完整的个人偏好管理功能，包括主题切换、通知管理、登录历史查看等功能。

## 🏗️ 架构设计

### Composables（状态管理）

#### `useSettings.js`
- **功能**：用户设置的核心状态管理
- **特性**：
  - 设置的获取、更新、重置
  - Redis缓存支持
  - 自动保存功能
  - 导入导出功能

#### `useTheme.js`
- **功能**：主题管理
- **特性**：
  - 深色/浅色主题切换
  - 自动跟随系统主题
  - 主题预览功能
  - CSS变量动态更新

#### `useNotifications.js`
- **功能**：通知设置管理
- **特性**：
  - 四种通知类型管理
  - 快速预设配置
  - 浏览器通知权限管理
  - 测试通知功能

#### `useLoginHistory.js`
- **功能**：登录历史管理
- **特性**：
  - 登录记录查询
  - 在线会话管理
  - 可疑登录检测
  - 安全报告生成

### 组件结构

```
Settings/
├── Settings.vue           # 主设置页面
├── ThemeToggle.vue        # 主题切换组件
├── NotificationPanel.vue  # 通知设置面板
├── LoginHistoryPanel.vue  # 登录历史面板
└── README.md             # 文档说明
```

## 🎨 主题系统

### 主题配置

支持两种主题模式：
- **深色主题**：适合夜间使用，减少眼部疲劳
- **浅色主题**：适合白天使用，清晰明亮

### CSS变量

主题系统使用CSS变量实现，支持动态切换：

```scss
:root {
  --theme-primary: #00F5D4;
  --theme-background: #1a1a1a;
  --theme-surface: #2d2d2d;
  --theme-text: #ffffff;
  --theme-border: #333333;
}
```

### 使用方法

```vue
<template>
  <div class="my-component">
    <ThemeToggle />
  </div>
</template>

<script setup>
import { useTheme } from '@/composables/useTheme'

const { currentTheme, toggleTheme, isDark } = useTheme()
</script>

<style scoped>
.my-component {
  background: var(--theme-surface);
  color: var(--theme-text);
  border: 1px solid var(--theme-border);
}
</style>
```

## 🔔 通知系统

### 通知类型

1. **邮件通知**：重要信息和更新
2. **系统通知**：系统消息和状态更新
3. **活动通知**：游戏活动和事件提醒
4. **社交通知**：社交互动和关注提醒

### 快速预设

- **工作模式**：适合工作时间，减少干扰
- **游戏模式**：专注游戏体验
- **极简模式**：只保留必要通知
- **完整模式**：接收所有通知

### 使用示例

```vue
<script setup>
import { useNotifications } from '@/composables/useNotifications'

const {
  toggleSingleNotification,
  enableAllNotifications,
  sendTestNotification
} = useNotifications()

// 切换邮件通知
const toggleEmail = () => toggleSingleNotification('email')

// 发送测试通知
const testNotification = () => sendTestNotification('system')
</script>
```

## 🔒 安全功能

### 登录历史

- **记录内容**：IP地址、设备信息、登录时间、会话时长
- **安全分析**：可疑登录检测、异常行为识别
- **会话管理**：在线会话查看、批量登出功能

### 隐私保护

- **IP脱敏**：显示时隐藏IP最后一段
- **数据清理**：支持清除历史记录
- **权限控制**：三级隐私设置

## 📱 响应式设计

### 断点设置

- **桌面端**：> 768px
- **移动端**：≤ 768px

### 适配特性

- 侧边栏在移动端自动折叠
- 表格在小屏幕上优化显示
- 触摸友好的交互设计

## 🚀 性能优化

### 缓存策略

- **Redis缓存**：用户设置缓存24小时
- **本地缓存**：主题设置本地存储
- **懒加载**：组件按需加载

### 网络优化

- **防抖处理**：设置更新防抖1秒
- **批量操作**：支持批量更新设置
- **错误重试**：网络请求自动重试

## 🔧 开发指南

### 添加新的设置项

1. **更新后端实体**：在`UserSettings`实体中添加字段
2. **更新DTO**：在`UserSettingsDTO`中添加对应字段
3. **更新前端状态**：在`useSettings.js`中添加状态管理
4. **更新UI组件**：在设置页面中添加对应的表单控件

### 添加新的通知类型

1. **更新配置**：在`useNotifications.js`中添加通知类型配置
2. **更新后端**：在数据库和服务层添加对应字段
3. **更新UI**：在通知面板中添加对应的开关

### 自定义主题

1. **定义CSS变量**：在`theme.scss`中添加新的颜色变量
2. **更新主题配置**：在`useTheme.js`中添加主题配置
3. **应用样式**：在组件中使用新的CSS变量

## 🧪 测试

### 单元测试

```bash
# 运行设置模块测试
npm run test:unit -- --grep "Settings"

# 运行主题测试
npm run test:unit -- --grep "Theme"

# 运行通知测试
npm run test:unit -- --grep "Notifications"
```

### E2E测试

```bash
# 运行设置页面E2E测试
npm run test:e2e -- --spec "settings.spec.ts"
```

## 📚 API文档

### 设置相关接口

- `GET /api/user/settings` - 获取用户设置
- `PUT /api/user/settings` - 更新用户设置
- `POST /api/user/settings/reset` - 重置为默认设置
- `POST /api/user/settings/toggle-theme` - 切换主题

### 登录历史接口

- `GET /api/user/login-history` - 获取登录历史
- `GET /api/user/login-history/recent` - 获取最近登录
- `GET /api/user/login-history/statistics` - 获取登录统计
- `DELETE /api/user/login-history` - 清除登录历史

## 🐛 常见问题

### 主题切换不生效

**问题**：点击主题切换按钮后页面样式没有变化
**解决**：检查CSS变量是否正确定义，确保`theme.scss`已正确导入

### 设置保存失败

**问题**：修改设置后提示保存失败
**解决**：检查网络连接和后端服务状态，查看浏览器控制台错误信息

### 通知权限被拒绝

**问题**：浏览器通知权限被拒绝
**解决**：引导用户在浏览器设置中手动开启通知权限

## 🔄 更新日志

### v1.1.0 (2025-01-31)
- ✨ 新增用户设置模块
- ✨ 新增主题切换功能
- ✨ 新增通知管理功能
- ✨ 新增登录历史查看
- ✨ 新增安全报告生成
- 🎨 优化响应式设计
- 🚀 性能优化和缓存策略

## 📞 技术支持

如有问题，请查看：
1. 本文档的常见问题部分
2. 项目的Issue页面
3. 开发团队联系方式

---

**维护者**: 开发团队  
**最后更新**: 2025-01-31
