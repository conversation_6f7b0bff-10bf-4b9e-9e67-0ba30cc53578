<template>
  <div class="theme-toggle">
    <el-button
      :icon="isDark ? 'Sunny' : 'Moon'"
      :loading="loading"
      @click="handleToggle"
      circle
      :title="isDark ? '切换到浅色主题' : '切换到深色主题'"
      class="theme-button"
    />
    
    <!-- 主题选择器（展开版） -->
    <div v-if="expanded" class="theme-selector">
      <div class="selector-header">
        <h3 class="selector-title">选择主题</h3>
        <el-button @click="expanded = false" text icon="Close" />
      </div>
      
      <div class="theme-options">
        <div 
          v-for="(config, theme) in themeConfig" 
          :key="theme"
          class="theme-option"
          :class="{ active: currentTheme === theme }"
          @click="setTheme(theme)"
        >
          <div class="theme-preview" :class="`${theme}-preview`">
            <div class="preview-header"></div>
            <div class="preview-content">
              <div class="preview-sidebar"></div>
              <div class="preview-main"></div>
            </div>
          </div>
          
          <div class="theme-info">
            <div class="theme-name">
              <span class="theme-icon">{{ config.icon }}</span>
              {{ config.name }}
            </div>
            <div class="theme-description">{{ config.description }}</div>
          </div>
          
          <div class="theme-check">
            <el-icon v-if="currentTheme === theme" class="check-icon">
              <Check />
            </el-icon>
          </div>
        </div>
      </div>
      
      <div class="theme-actions">
        <el-button @click="enableAutoFollow" :disabled="autoFollowSystem" size="small">
          跟随系统
        </el-button>
        <el-button @click="disableAutoFollow" :disabled="!autoFollowSystem" size="small">
          手动选择
        </el-button>
      </div>
      
      <div v-if="autoFollowSystem" class="auto-follow-info">
        <el-alert
          title="已启用自动跟随系统主题"
          type="info"
          :closable="false"
          show-icon
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Check } from '@element-plus/icons-vue'
import { useTheme } from '@/composables/useTheme'

// Props
const props = defineProps({
  expanded: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:expanded'])

// 使用主题 Composable
const {
  currentTheme,
  isDark,
  themeConfig,
  toggleTheme,
  setTheme,
  autoFollowSystem,
  enableAutoFollowSystem,
  disableAutoFollowSystem
} = useTheme()

// 本地状态
const loading = ref(false)
const expanded = ref(props.expanded)

// 方法
const handleToggle = async () => {
  if (props.expanded) {
    expanded.value = !expanded.value
    emit('update:expanded', expanded.value)
  } else {
    loading.value = true
    try {
      await toggleTheme()
    } finally {
      loading.value = false
    }
  }
}

const enableAutoFollow = () => {
  enableAutoFollowSystem()
}

const disableAutoFollow = () => {
  disableAutoFollowSystem()
}
</script>

<style lang="scss" scoped>
.theme-toggle {
  position: relative;
}

.theme-button {
  background: var(--theme-surface, #2d2d2d);
  border: 1px solid var(--theme-border, #333333);
  color: var(--theme-text, #ffffff);
  transition: all 0.3s ease;

  &:hover {
    border-color: #00F5D4;
    color: #00F5D4;
    transform: scale(1.1);
  }
}

.theme-selector {
  position: absolute;
  top: 100%;
  right: 0;
  width: 400px;
  background: var(--theme-surface, #2d2d2d);
  border: 1px solid var(--theme-border, #333333);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.4);
  z-index: 1000;
  margin-top: 10px;

  @media (max-width: 768px) {
    width: 320px;
    right: -50px;
  }
}

.selector-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.selector-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--theme-text, #ffffff);
  margin: 0;
}

.theme-options {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.theme-option {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 2px solid var(--theme-border, #333333);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--theme-background, #1a1a1a);

  &:hover {
    border-color: #00F5D4;
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 245, 212, 0.2);
  }

  &.active {
    border-color: #00F5D4;
    background: rgba(0, 245, 212, 0.1);
    box-shadow: 0 8px 24px rgba(0, 245, 212, 0.3);
  }
}

.theme-preview {
  width: 60px;
  height: 40px;
  border-radius: 6px;
  margin-right: 15px;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--theme-border, #333333);

  .preview-header {
    height: 12px;
    width: 100%;
  }

  .preview-content {
    height: 28px;
    width: 100%;
    display: flex;
  }

  .preview-sidebar {
    width: 20px;
    height: 100%;
  }

  .preview-main {
    flex: 1;
    height: 100%;
  }

  &.dark-preview {
    .preview-header {
      background: #2d2d2d;
    }
    .preview-sidebar {
      background: #1a1a1a;
    }
    .preview-main {
      background: #0f0f0f;
    }
  }

  &.light-preview {
    .preview-header {
      background: #f5f5f5;
    }
    .preview-sidebar {
      background: #ffffff;
    }
    .preview-main {
      background: #fafafa;
    }
  }
}

.theme-info {
  flex: 1;
}

.theme-name {
  font-weight: 600;
  color: var(--theme-text, #ffffff);
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.theme-icon {
  font-size: 1.1rem;
}

.theme-description {
  font-size: 0.9rem;
  color: var(--theme-text-secondary, #b3b3b3);
}

.theme-check {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon {
  color: #00F5D4;
  font-size: 1.2rem;
}

.theme-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.auto-follow-info {
  margin-top: 15px;
}
</style>
