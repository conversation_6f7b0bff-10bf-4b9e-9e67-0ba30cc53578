<template>
  <footer class="app-footer">
    <div class="footer-container">
      <!-- 主要内容区 -->
      <div class="footer-content">
        <!-- 品牌信息 -->
        <div class="footer-brand">
          <div class="brand-logo">
            <div class="logo-icon">🌫️</div>
            <div class="logo-text">
              <span class="logo-main">迷雾拼本</span>
              <span class="logo-sub">Misty Labyrinth</span>
            </div>
          </div>
          <p class="brand-description">
            探索无限可能的剧本世界，与志同道合的伙伴一起，开启沉浸式剧本杀之旅。
          </p>
          <div class="social-links">
            <a href="#" class="social-link" title="微信公众号">
              <span class="social-icon">💬</span>
            </a>
            <a href="#" class="social-link" title="微博">
              <span class="social-icon">📱</span>
            </a>
            <a href="#" class="social-link" title="QQ群">
              <span class="social-icon">👥</span>
            </a>
            <a href="#" class="social-link" title="抖音">
              <span class="social-icon">🎵</span>
            </a>
          </div>
        </div>

        <!-- 快速链接 -->
        <div class="footer-links">
          <div class="link-group">
            <h4 class="group-title">探索</h4>
            <ul class="link-list">
              <li><router-link to="/scripts">剧本库</router-link></li>
              <li><router-link to="/lobby">拼车大厅</router-link></li>
              <li><router-link to="/feed">动态广场</router-link></li>
              <li><router-link to="/rankings">排行榜</router-link></li>
            </ul>
          </div>
          
          <div class="link-group">
            <h4 class="group-title">服务</h4>
            <ul class="link-list">
              <li><router-link to="/help">帮助中心</router-link></li>
              <li><router-link to="/feedback">意见反馈</router-link></li>
              <li><router-link to="/contact">联系我们</router-link></li>
              <li><router-link to="/business">商务合作</router-link></li>
            </ul>
          </div>
          
          <div class="link-group">
            <h4 class="group-title">关于</h4>
            <ul class="link-list">
              <li><router-link to="/about">关于我们</router-link></li>
              <li><router-link to="/privacy">隐私政策</router-link></li>
              <li><router-link to="/terms">服务条款</router-link></li>
              <li><router-link to="/careers">加入我们</router-link></li>
            </ul>
          </div>
        </div>

        <!-- 统计信息 -->
        <div class="footer-stats">
          <h4 class="stats-title">平台数据</h4>
          <div class="stats-grid">
            <div class="stat-item">
              <span class="stat-number">{{ formatNumber(stats.totalUsers) }}</span>
              <span class="stat-label">注册用户</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ formatNumber(stats.totalScripts) }}</span>
              <span class="stat-label">精品剧本</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ formatNumber(stats.totalGames) }}</span>
              <span class="stat-label">完成游戏</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ formatNumber(stats.onlineUsers) }}</span>
              <span class="stat-label">在线用户</span>
            </div>
          </div>
          
          <!-- 下载应用 -->
          <div class="app-download">
            <h5 class="download-title">下载APP</h5>
            <div class="download-buttons">
              <a href="#" class="download-btn">
                <span class="download-icon">📱</span>
                <div class="download-text">
                  <span class="download-platform">iOS</span>
                  <span class="download-desc">App Store</span>
                </div>
              </a>
              <a href="#" class="download-btn">
                <span class="download-icon">🤖</span>
                <div class="download-text">
                  <span class="download-platform">Android</span>
                  <span class="download-desc">应用商店</span>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部信息 -->
      <div class="footer-bottom">
        <div class="bottom-left">
          <p class="copyright">
            © 2024 迷雾拼本 Misty Labyrinth. All rights reserved.
          </p>
          <div class="legal-links">
            <a href="/privacy">隐私政策</a>
            <span class="separator">|</span>
            <a href="/terms">服务条款</a>
            <span class="separator">|</span>
            <a href="/license">许可协议</a>
          </div>
        </div>
        
        <div class="bottom-right">
          <div class="tech-info">
            <span class="tech-item">
              <span class="tech-icon">⚡</span>
              <span class="tech-text">Vue 3 + TypeScript</span>
            </span>
            <span class="tech-item">
              <span class="tech-icon">🚀</span>
              <span class="tech-text">Vite + Pinia</span>
            </span>
          </div>
          
          <!-- 备案信息 -->
          <div class="icp-info">
            <a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener">
              京ICP备12345678号-1
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- 装饰元素 -->
    <div class="footer-decorations">
      <div class="decoration-line line-1"></div>
      <div class="decoration-line line-2"></div>
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { reactive } from 'vue'

// 统计数据
const stats = reactive({
  totalUsers: 125000,
  totalScripts: 3500,
  totalGames: 89000,
  onlineUsers: 2340
})

// 方法
const formatNumber = (num: number): string => {
  if (num >= 100000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}
</script>

<style lang="scss" scoped>
.app-footer {
  position: relative;
  background: linear-gradient(180deg, #16213E 0%, #0F0F1E 100%);
  border-top: 1px solid rgba(0, 245, 212, 0.1);
  overflow: hidden;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 60px 20px 20px;
  position: relative;
  z-index: 2;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 2fr 1fr;
  gap: 60px;
  margin-bottom: 40px;
}

.footer-brand {
  .brand-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    
    .logo-icon {
      font-size: 2.5rem;
      filter: drop-shadow(0 0 10px rgba(0, 245, 212, 0.5));
    }
    
    .logo-text {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }
    
    .logo-main {
      font-size: 1.5rem;
      font-weight: 700;
      color: #fff;
      background: linear-gradient(135deg, #00F5D4, #FF00E4);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .logo-sub {
      font-size: 0.8rem;
      color: #888;
      letter-spacing: 1px;
      text-transform: uppercase;
    }
  }
  
  .brand-description {
    color: #B0B0B0;
    line-height: 1.6;
    margin-bottom: 24px;
    font-size: 0.95rem;
  }
  
  .social-links {
    display: flex;
    gap: 12px;
  }
  
  .social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(0, 245, 212, 0.2);
    border-radius: 50%;
    color: #B0B0B0;
    text-decoration: none;
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(0, 245, 212, 0.1);
      border-color: #00F5D4;
      color: #00F5D4;
      transform: translateY(-2px);
    }
    
    .social-icon {
      font-size: 1.1rem;
    }
  }
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
}

.link-group {
  .group-title {
    font-size: 1.1rem;
    color: #fff;
    font-weight: 600;
    margin-bottom: 16px;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -4px;
      left: 0;
      width: 30px;
      height: 2px;
      background: linear-gradient(90deg, #00F5D4, #FF00E4);
      border-radius: 1px;
    }
  }
  
  .link-list {
    list-style: none;
    padding: 0;
    margin: 0;
    
    li {
      margin-bottom: 8px;
      
      a {
        color: #B0B0B0;
        text-decoration: none;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        
        &:hover {
          color: #00F5D4;
          padding-left: 4px;
        }
      }
    }
  }
}

.footer-stats {
  .stats-title {
    font-size: 1.1rem;
    color: #fff;
    font-weight: 600;
    margin-bottom: 20px;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -4px;
      left: 0;
      width: 30px;
      height: 2px;
      background: linear-gradient(90deg, #00F5D4, #FF00E4);
      border-radius: 1px;
    }
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    margin-bottom: 32px;
  }
  
  .stat-item {
    text-align: center;
    padding: 16px 12px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(0, 245, 212, 0.1);
    border-radius: 8px;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: rgba(0, 245, 212, 0.3);
      transform: translateY(-2px);
    }
    
    .stat-number {
      display: block;
      font-size: 1.5rem;
      font-weight: 700;
      color: #00F5D4;
      line-height: 1;
      margin-bottom: 4px;
    }
    
    .stat-label {
      font-size: 0.75rem;
      color: #888;
      text-transform: uppercase;
      letter-spacing: 1px;
    }
  }
}

.app-download {
  .download-title {
    font-size: 1rem;
    color: #fff;
    font-weight: 600;
    margin-bottom: 16px;
  }
  
  .download-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .download-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(0, 245, 212, 0.2);
    border-radius: 8px;
    color: #B0B0B0;
    text-decoration: none;
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(0, 245, 212, 0.1);
      border-color: #00F5D4;
      color: #00F5D4;
    }
    
    .download-icon {
      font-size: 1.2rem;
    }
    
    .download-text {
      display: flex;
      flex-direction: column;
      gap: 2px;
    }
    
    .download-platform {
      font-size: 0.85rem;
      font-weight: 500;
    }
    
    .download-desc {
      font-size: 0.7rem;
      opacity: 0.7;
    }
  }
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.bottom-left {
  .copyright {
    color: #888;
    font-size: 0.8rem;
    margin-bottom: 8px;
  }
  
  .legal-links {
    display: flex;
    align-items: center;
    gap: 8px;
    
    a {
      color: #888;
      text-decoration: none;
      font-size: 0.75rem;
      
      &:hover {
        color: #00F5D4;
      }
    }
    
    .separator {
      color: #666;
      font-size: 0.75rem;
    }
  }
}

.bottom-right {
  text-align: right;
  
  .tech-info {
    display: flex;
    gap: 16px;
    margin-bottom: 8px;
  }
  
  .tech-item {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #888;
    font-size: 0.75rem;
    
    .tech-icon {
      font-size: 0.8rem;
    }
  }
  
  .icp-info {
    a {
      color: #888;
      text-decoration: none;
      font-size: 0.75rem;
      
      &:hover {
        color: #00F5D4;
      }
    }
  }
}

.footer-decorations {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.decoration-line {
  position: absolute;
  background: linear-gradient(90deg, transparent, rgba(0, 245, 212, 0.2), transparent);
  height: 1px;
  animation: slide 12s linear infinite;
  
  &.line-1 {
    width: 200px;
    top: 20%;
    left: 0;
    animation-delay: 0s;
  }
  
  &.line-2 {
    width: 150px;
    bottom: 30%;
    right: 0;
    animation-delay: 6s;
  }
}

.decoration-circle {
  position: absolute;
  border: 1px solid rgba(255, 0, 228, 0.1);
  border-radius: 50%;
  animation: float 8s ease-in-out infinite;
  
  &.circle-1 {
    width: 100px;
    height: 100px;
    top: 10%;
    right: 10%;
    animation-delay: 0s;
  }
  
  &.circle-2 {
    width: 60px;
    height: 60px;
    bottom: 20%;
    left: 15%;
    animation-delay: 4s;
  }
}

@keyframes slide {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100vw); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(180deg); }
}

@media (max-width: 768px) {
  .footer-container {
    padding: 40px 15px 20px;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }
  
  .footer-links {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .footer-stats {
    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
    }
    
    .stat-item {
      padding: 12px 8px;
      
      .stat-number {
        font-size: 1.2rem;
      }
    }
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .bottom-right {
    text-align: center;
    
    .tech-info {
      justify-content: center;
      gap: 12px;
    }
  }
  
  .decoration-circle {
    &.circle-1 {
      width: 60px;
      height: 60px;
    }
    
    &.circle-2 {
      width: 40px;
      height: 40px;
    }
  }
}
</style>
