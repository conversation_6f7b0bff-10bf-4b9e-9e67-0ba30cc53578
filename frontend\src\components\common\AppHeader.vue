<template>
  <header class="app-header" :class="{ 'header-scrolled': isScrolled }">
    <div class="header-container">
      <!-- Logo区域 -->
      <div class="header-logo">
        <router-link to="/" class="logo-link">
          <div class="logo-icon">🌫️</div>
          <div class="logo-text">
            <span class="logo-main">迷雾拼本</span>
            <span class="logo-sub">Misty Labyrinth</span>
          </div>
        </router-link>
      </div>

      <!-- 导航菜单 -->
      <nav class="header-nav">
        <router-link 
          v-for="navItem in navItems" 
          :key="navItem.path"
          :to="navItem.path"
          class="nav-item"
          :class="{ active: isActiveRoute(navItem.path) }"
        >
          <span class="nav-icon">{{ navItem.icon }}</span>
          <span class="nav-text">{{ navItem.label }}</span>
          <span v-if="navItem.badge" class="nav-badge">{{ navItem.badge }}</span>
        </router-link>
      </nav>

      <!-- 搜索框 -->
      <div class="header-search">
        <div class="search-wrapper" :class="{ expanded: isSearchExpanded }">
          <i class="search-icon">🔍</i>
          <input 
            ref="searchInput"
            v-model="searchKeyword"
            type="text" 
            placeholder="搜索剧本、用户、动态..."
            class="search-input"
            @focus="expandSearch"
            @blur="collapseSearch"
            @keyup.enter="handleSearch"
          />
          <button 
            v-if="searchKeyword" 
            class="search-clear"
            @click="clearSearch"
          >
            ✕
          </button>
        </div>
        
        <!-- 搜索建议 -->
        <div v-if="isSearchExpanded && searchSuggestions.length > 0" class="search-suggestions">
          <div 
            v-for="suggestion in searchSuggestions" 
            :key="suggestion.id"
            class="suggestion-item"
            @click="selectSuggestion(suggestion)"
          >
            <span class="suggestion-icon">{{ suggestion.type === 'script' ? '📚' : suggestion.type === 'user' ? '👤' : '💬' }}</span>
            <span class="suggestion-text">{{ suggestion.title }}</span>
            <span class="suggestion-type">{{ getSuggestionTypeText(suggestion.type) }}</span>
          </div>
        </div>
      </div>

      <!-- 用户区域 -->
      <div class="header-user">
        <!-- 未登录状态 -->
        <div v-if="!userStore.isLoggedIn" class="auth-buttons">
          <router-link to="/auth/login" class="auth-btn login-btn">
            <span class="btn-text">登录</span>
          </router-link>
          <router-link to="/auth/register" class="auth-btn register-btn">
            <span class="btn-text">注册</span>
          </router-link>
        </div>

        <!-- 已登录状态 -->
        <template v-else>
          <!-- 通知 -->
          <div class="notification-wrapper">
            <button class="notification-btn" @click="toggleNotifications">
              <span class="notification-icon">🔔</span>
              <span v-if="unreadCount > 0" class="notification-badge">{{ formatBadgeCount(unreadCount) }}</span>
            </button>

            <!-- 通知下拉菜单 -->
            <div v-if="showNotifications" class="notification-dropdown">
              <div class="dropdown-header">
                <span class="dropdown-title">通知</span>
                <button class="mark-all-read" @click="markAllAsRead">全部已读</button>
              </div>
              <div class="notifications-list">
                <div
                  v-for="notification in notifications"
                  :key="notification.id"
                  class="notification-item"
                  :class="{ unread: !notification.isRead }"
                  @click="handleNotificationClick(notification)"
                >
                  <div class="notification-avatar">
                    <img :src="notification.avatar" :alt="notification.title" />
                  </div>
                  <div class="notification-content">
                    <div class="notification-title">{{ notification.title }}</div>
                    <div class="notification-desc">{{ notification.description }}</div>
                    <div class="notification-time">{{ formatTime(notification.createdAt) }}</div>
                  </div>
                </div>
              </div>
              <div class="dropdown-footer">
                <router-link to="/notifications" class="view-all-link">查看全部通知</router-link>
              </div>
            </div>
          </div>

          <!-- 用户菜单 -->
          <div class="user-menu-wrapper">
            <button class="user-menu-btn" @click="toggleUserMenu">
              <img
                :src="userStore.userAvatar || 'https://picsum.photos/32/32?random=99'"
                :alt="userStore.userNickname"
                class="user-avatar"
              />
              <span class="user-name">{{ userStore.userNickname }}</span>
              <span class="dropdown-arrow">▼</span>
            </button>

            <!-- 用户下拉菜单 -->
            <div v-if="showUserMenu" class="user-dropdown">
              <div class="dropdown-user-info">
                <img
                  :src="userStore.userAvatar || 'https://picsum.photos/48/48?random=99'"
                  :alt="userStore.userNickname"
                  class="dropdown-avatar"
                />
                <div class="dropdown-user-text">
                  <div class="dropdown-user-name">{{ userStore.userNickname }}</div>
                  <div class="dropdown-user-level">Lv.{{ userStore.userLevel }}</div>
                </div>
              </div>
              <div class="dropdown-menu">
                <router-link to="/profile" class="menu-item">
                  <span class="menu-icon">👤</span>
                  <span class="menu-text">个人资料</span>
                </router-link>
                <router-link to="/settings" class="menu-item">
                  <span class="menu-icon">⚙️</span>
                  <span class="menu-text">设置</span>
                </router-link>
                <router-link to="/my-lobbies" class="menu-item">
                  <span class="menu-icon">🚗</span>
                  <span class="menu-text">我的车队</span>
                </router-link>
                <router-link to="/favorites" class="menu-item">
                  <span class="menu-icon">❤️</span>
                  <span class="menu-text">我的收藏</span>
                </router-link>
                <div class="menu-divider"></div>
                <button class="menu-item logout-btn" @click="handleLogout">
                  <span class="menu-icon">🚪</span>
                  <span class="menu-text">退出登录</span>
                </button>
              </div>
            </div>
          </div>
        </template>
      </div>

      <!-- 移动端菜单按钮 -->
      <button class="mobile-menu-btn" @click="toggleMobileMenu">
        <span class="hamburger-line"></span>
        <span class="hamburger-line"></span>
        <span class="hamburger-line"></span>
      </button>
    </div>

    <!-- 移动端导航菜单 -->
    <div v-if="showMobileMenu" class="mobile-nav">
      <router-link 
        v-for="navItem in navItems" 
        :key="navItem.path"
        :to="navItem.path"
        class="mobile-nav-item"
        @click="closeMobileMenu"
      >
        <span class="nav-icon">{{ navItem.icon }}</span>
        <span class="nav-text">{{ navItem.label }}</span>
        <span v-if="navItem.badge" class="nav-badge">{{ navItem.badge }}</span>
      </router-link>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import { useAuthStore } from '@/stores/modules/auth'
import { userApi } from '@/api/user'

// 类型定义
interface NavItem {
  path: string
  label: string
  icon: string
  badge?: string | number
}

interface User {
  id: number
  nickname: string
  avatar: string
  level: number
}

interface Notification {
  id: number
  title: string
  description: string
  avatar: string
  createdAt: string
  isRead: boolean
  type: string
  targetId?: number
}

interface SearchSuggestion {
  id: number
  title: string
  type: 'script' | 'user' | 'post'
  targetId: number
}

// 路由
const route = useRoute()
const router = useRouter()

// 状态管理
const userStore = useUserStore()
const authStore = useAuthStore()

// 响应式数据
const isScrolled = ref(false)
const isSearchExpanded = ref(false)
const searchKeyword = ref('')
const showNotifications = ref(false)
const showUserMenu = ref(false)
const showMobileMenu = ref(false)
const searchInput = ref<HTMLInputElement>()
const unreadCount = ref(5)

// 导航菜单项
const navItems: NavItem[] = [
  { path: '/', label: '首页', icon: '🏠' },
  { path: '/lobby', label: '拼车大厅', icon: '🚗', badge: 'HOT' },
  { path: '/scripts', label: '剧本库', icon: '📚' },
  { path: '/feed', label: '动态广场', icon: '💬' }
]

// 通知数据
const notifications = ref<Notification[]>([
  {
    id: 1,
    title: '有人加入了你的车队',
    description: '推理达人加入了《迷雾庄园》车队',
    avatar: 'https://picsum.photos/40/40?random=21',
    createdAt: '2024-07-29T14:30:00',
    isRead: false,
    type: 'lobby_join'
  },
  {
    id: 2,
    title: '你关注的用户发布了新动态',
    description: '剧本杀导师分享了新手攻略',
    avatar: 'https://picsum.photos/40/40?random=22',
    createdAt: '2024-07-29T13:15:00',
    isRead: false,
    type: 'user_post'
  }
])

// 搜索建议
const searchSuggestions = ref<SearchSuggestion[]>([])

// 计算属性
const isActiveRoute = (path: string): boolean => {
  if (path === '/') {
    return route.path === '/'
  }
  return route.path.startsWith(path)
}

// 方法
const handleScroll = () => {
  isScrolled.value = window.scrollY > 50
}

const expandSearch = () => {
  isSearchExpanded.value = true
  // 模拟搜索建议
  if (searchKeyword.value) {
    loadSearchSuggestions()
  }
}

const collapseSearch = () => {
  setTimeout(() => {
    isSearchExpanded.value = false
    searchSuggestions.value = []
  }, 200)
}

const loadSearchSuggestions = async () => {
  // 模拟搜索建议API
  searchSuggestions.value = [
    { id: 1, title: '迷雾庄园', type: 'script', targetId: 1 },
    { id: 2, title: '推理达人', type: 'user', targetId: 2 },
    { id: 3, title: '新手攻略分享', type: 'post', targetId: 3 }
  ]
}

const handleSearch = () => {
  if (searchKeyword.value.trim()) {
    router.push(`/search?q=${encodeURIComponent(searchKeyword.value)}`)
    collapseSearch()
  }
}

const clearSearch = () => {
  searchKeyword.value = ''
  searchSuggestions.value = []
  searchInput.value?.focus()
}

const selectSuggestion = (suggestion: SearchSuggestion) => {
  const routeMap = {
    script: `/scripts/${suggestion.targetId}`,
    user: `/user/${suggestion.targetId}`,
    post: `/feed/${suggestion.targetId}`
  }
  router.push(routeMap[suggestion.type])
  collapseSearch()
}

const getSuggestionTypeText = (type: string): string => {
  const typeMap = {
    script: '剧本',
    user: '用户',
    post: '动态'
  }
  return typeMap[type as keyof typeof typeMap] || ''
}

const toggleNotifications = () => {
  showNotifications.value = !showNotifications.value
  showUserMenu.value = false
}

const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value
  showNotifications.value = false
}

const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

const closeMobileMenu = () => {
  showMobileMenu.value = false
}

const formatBadgeCount = (count: number): string => {
  return count > 99 ? '99+' : count.toString()
}

const formatTime = (timeString: string): string => {
  const now = new Date()
  const time = new Date(timeString)
  const diff = now.getTime() - time.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days > 0) return `${days}天前`
  if (hours > 0) return `${hours}小时前`
  if (minutes > 0) return `${minutes}分钟前`
  return '刚刚'
}

const markAllAsRead = () => {
  notifications.value.forEach(n => n.isRead = true)
  unreadCount.value = 0
}

const handleNotificationClick = (notification: Notification) => {
  notification.isRead = true
  unreadCount.value = Math.max(0, unreadCount.value - 1)
  
  // 根据通知类型跳转
  const routeMap = {
    lobby_join: '/my-lobbies',
    user_post: '/feed',
    system: '/notifications'
  }
  
  const targetRoute = routeMap[notification.type as keyof typeof routeMap]
  if (targetRoute) {
    router.push(targetRoute)
  }
  
  showNotifications.value = false
}

const handleLogout = async () => {
  try {
    // 调用登出API
    await userApi.logout()

    // 清除本地状态
    authStore.logout()
    userStore.clearUserData()

    // 关闭下拉菜单
    showUserMenu.value = false

    // 刷新页面到首页
    router.push('/').then(() => {
      window.location.reload()
    })
  } catch (error) {
    console.error('登出失败:', error)

    // 即使API调用失败，也要清除本地状态
    authStore.logout()
    userStore.clearUserData()

    // 刷新页面
    window.location.reload()
  }
}

// 点击外部关闭下拉菜单
const handleClickOutside = (event: Event) => {
  const target = event.target as Element
  if (!target.closest('.notification-wrapper')) {
    showNotifications.value = false
  }
  if (!target.closest('.user-menu-wrapper')) {
    showUserMenu.value = false
  }
}

// 初始化用户状态
const initializeUserState = async () => {
  const token = localStorage.getItem('auth_token')
  if (token && !userStore.currentUser) {
    try {
      const response = await userApi.getCurrentUser()
      if (response.code === 200) {
        userStore.setCurrentUser({
          id: response.data.id,
          nickname: response.data.nickName,
          avatar: response.data.icon || '',
          level: 1, // 默认等级
          experience: 0
        })
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果获取失败，清除无效token
      localStorage.removeItem('auth_token')
    }
  }
}

// 生命周期
onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  document.addEventListener('click', handleClickOutside)

  // 初始化用户状态
  initializeUserState()
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style lang="scss" scoped>
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(26, 26, 46, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 245, 212, 0.1);
  transition: all 0.3s ease;
  
  &.header-scrolled {
    background: rgba(26, 26, 46, 0.95);
    border-bottom-color: rgba(0, 245, 212, 0.2);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  height: 70px;
  display: flex;
  align-items: center;
  gap: 32px;
}

.header-logo {
  flex-shrink: 0;
}

.logo-link {
  display: flex;
  align-items: center;
  gap: 12px;
  text-decoration: none;
  transition: transform 0.3s ease;
  
  &:hover {
    transform: scale(1.05);
  }
}

.logo-icon {
  font-size: 2rem;
  filter: drop-shadow(0 0 10px rgba(0, 245, 212, 0.5));
}

.logo-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.logo-main {
  font-size: 1.2rem;
  font-weight: 700;
  color: #fff;
  background: linear-gradient(135deg, #00F5D4, #FF00E4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo-sub {
  font-size: 0.7rem;
  color: #888;
  letter-spacing: 1px;
  text-transform: uppercase;
}

.header-nav {
  display: flex;
  gap: 8px;
  flex: 1;
}

.nav-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 8px;
  text-decoration: none;
  color: #B0B0B0;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.05);
    color: #fff;
  }
  
  &.active {
    background: rgba(0, 245, 212, 0.1);
    color: #00F5D4;
    border: 1px solid rgba(0, 245, 212, 0.3);
    
    &::after {
      content: '';
      position: absolute;
      bottom: -1px;
      left: 50%;
      transform: translateX(-50%);
      width: 20px;
      height: 2px;
      background: #00F5D4;
      border-radius: 1px;
    }
  }
}

.nav-icon {
  font-size: 1rem;
}

.nav-badge {
  padding: 2px 6px;
  background: #FF4444;
  color: #fff;
  border-radius: 8px;
  font-size: 0.6rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.header-search {
  position: relative;
  flex-shrink: 0;
}

.search-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  width: 280px;
  transition: all 0.3s ease;
  
  &.expanded {
    width: 320px;
  }
}

.search-icon {
  position: absolute;
  left: 12px;
  color: #888;
  font-size: 0.9rem;
  z-index: 2;
}

.search-input {
  width: 100%;
  padding: 10px 16px 10px 36px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 20px;
  color: #fff;
  font-size: 0.85rem;
  
  &::placeholder {
    color: #666;
  }
  
  &:focus {
    outline: none;
    border-color: #00F5D4;
    box-shadow: 0 0 0 2px rgba(0, 245, 212, 0.1);
  }
}

.search-clear {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  font-size: 0.8rem;
  
  &:hover {
    color: #fff;
  }
}

.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(26, 26, 46, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 8px;
  margin-top: 4px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 10;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  transition: background 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.05);
  }
}

.suggestion-icon {
  font-size: 0.9rem;
}

.suggestion-text {
  flex: 1;
  color: #fff;
  font-size: 0.85rem;
}

.suggestion-type {
  color: #888;
  font-size: 0.75rem;
}

.header-user {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-shrink: 0;
}

.auth-buttons {
  display: flex;
  gap: 12px;
}

.auth-btn {
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;

  &.login-btn {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: 1px solid rgba(0, 245, 212, 0.3);

    &:hover {
      background: rgba(255, 255, 255, 0.15);
      border-color: rgba(0, 245, 212, 0.6);
      transform: translateY(-2px);
    }
  }

  &.register-btn {
    background: linear-gradient(135deg, #00F5D4, #00C9A7);
    color: #1A1A2E;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 245, 212, 0.3);
    }
  }
}

.notification-wrapper, .user-menu-wrapper {
  position: relative;
}

.notification-btn, .user-menu-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #B0B0B0;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(0, 245, 212, 0.3);
    color: #fff;
  }
}

.notification-btn {
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  justify-content: center;
}

.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #FF4444;
  color: #fff;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 0.6rem;
  font-weight: 600;
  min-width: 16px;
  text-align: center;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.user-name {
  font-size: 0.85rem;
  font-weight: 500;
}

.dropdown-arrow {
  font-size: 0.7rem;
  transition: transform 0.3s ease;
}

.notification-dropdown, .user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: rgba(26, 26, 46, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 12px;
  margin-top: 8px;
  min-width: 300px;
  max-height: 400px;
  overflow: hidden;
  z-index: 10;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dropdown-title {
  font-size: 1rem;
  font-weight: 600;
  color: #fff;
}

.mark-all-read {
  background: none;
  border: none;
  color: #00F5D4;
  font-size: 0.8rem;
  cursor: pointer;
  
  &:hover {
    color: #FF00E4;
  }
}

.notifications-list {
  max-height: 250px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: background 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.05);
  }
  
  &.unread {
    background: rgba(0, 245, 212, 0.05);
    border-left: 3px solid #00F5D4;
  }
}

.notification-avatar img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-size: 0.85rem;
  color: #fff;
  font-weight: 500;
  margin-bottom: 4px;
}

.notification-desc {
  font-size: 0.75rem;
  color: #B0B0B0;
  margin-bottom: 4px;
}

.notification-time {
  font-size: 0.7rem;
  color: #888;
}

.dropdown-footer {
  padding: 12px 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.view-all-link {
  color: #00F5D4;
  text-decoration: none;
  font-size: 0.85rem;
  
  &:hover {
    color: #FF00E4;
  }
}

.dropdown-user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dropdown-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.dropdown-user-name {
  font-size: 1rem;
  color: #fff;
  font-weight: 600;
}

.dropdown-user-level {
  font-size: 0.8rem;
  color: #00F5D4;
}

.dropdown-menu {
  padding: 8px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 10px 16px;
  background: none;
  border: none;
  color: #B0B0B0;
  text-decoration: none;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.05);
    color: #fff;
  }
  
  &.logout-btn:hover {
    background: rgba(255, 68, 68, 0.1);
    color: #FF4444;
  }
}

.menu-divider {
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
  margin: 8px 0;
}

.mobile-menu-btn {
  display: none;
  flex-direction: column;
  gap: 4px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
}

.hamburger-line {
  width: 20px;
  height: 2px;
  background: #B0B0B0;
  border-radius: 1px;
  transition: all 0.3s ease;
}

.mobile-nav {
  display: none;
  background: rgba(26, 26, 46, 0.95);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 16px 20px;
}

.mobile-nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  text-decoration: none;
  color: #B0B0B0;
  font-size: 0.9rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  
  &:last-child {
    border-bottom: none;
  }
  
  &:hover {
    color: #00F5D4;
  }
}

@media (max-width: 768px) {
  .header-container {
    padding: 0 15px;
    gap: 16px;
  }
  
  .header-nav {
    display: none;
  }
  
  .header-search {
    flex: 1;
    
    .search-wrapper {
      width: 100%;
      
      &.expanded {
        width: 100%;
      }
    }
  }
  
  .user-name {
    display: none;
  }
  
  .mobile-menu-btn {
    display: flex;
  }
  
  .mobile-nav {
    display: block;
  }
}
</style>
