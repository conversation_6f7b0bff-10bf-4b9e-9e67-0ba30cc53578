<template>
  <div class="network-status">
    <div class="status-icon">📡</div>
    <div class="status-text">网络连接已断开</div>
    <div class="status-subtitle">请检查您的网络连接</div>
  </div>
</template>

<style lang="scss" scoped>
.network-status {
  position: fixed;
  top: 70px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1070;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: rgba(244, 67, 54, 0.9);
  color: #fff;
  border-radius: 8px;
  font-size: 0.85rem;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
  animation: slideDown 0.3s ease;
}

.status-icon {
  font-size: 1rem;
}

.status-subtitle {
  font-size: 0.75rem;
  opacity: 0.8;
  margin-left: 8px;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@media (max-width: 768px) {
  .network-status {
    left: 15px;
    right: 15px;
    transform: none;
    justify-content: center;
  }
}
</style>
