<template>
  <teleport to="body">
    <div class="notification-container">
      <transition-group name="notification" tag="div">
        <div
          v-for="notification in notifications"
          :key="notification.id"
          class="notification-item"
          :class="`notification-${notification.type}`"
        >
          <div class="notification-icon">
            {{ getNotificationIcon(notification.type) }}
          </div>
          <div class="notification-content">
            <div class="notification-title">{{ notification.title }}</div>
            <div class="notification-message">{{ notification.message }}</div>
          </div>
          <button
            class="notification-close"
            @click="removeNotification(notification.id)"
          >
            ✕
          </button>
        </div>
      </transition-group>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAppStore } from '@/stores'

const appStore = useAppStore()

const notifications = computed(() => appStore.unreadNotifications)

const getNotificationIcon = (type: string) => {
  const icons = {
    success: '✅',
    error: '❌',
    warning: '⚠️',
    info: 'ℹ️'
  }
  return icons[type as keyof typeof icons] || 'ℹ️'
}

const removeNotification = (id: string) => {
  appStore.removeNotification(id)
}
</script>

<style lang="scss" scoped>
.notification-container {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 1080;
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 400px;
  width: 100%;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: rgba(26, 26, 46, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  border-left: 4px solid;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  
  &.notification-success {
    border-left-color: #4CAF50;
  }
  
  &.notification-error {
    border-left-color: #F44336;
  }
  
  &.notification-warning {
    border-left-color: #FFC107;
  }
  
  &.notification-info {
    border-left-color: #2196F3;
  }
}

.notification-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
  margin-top: 2px;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #fff;
  margin-bottom: 4px;
}

.notification-message {
  font-size: 0.8rem;
  color: #B0B0B0;
  line-height: 1.4;
}

.notification-close {
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  font-size: 0.9rem;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
  }
}

// 动画
.notification-enter-active {
  transition: all 0.3s ease;
}

.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.notification-move {
  transition: transform 0.3s ease;
}

@media (max-width: 768px) {
  .notification-container {
    top: 70px;
    right: 15px;
    left: 15px;
    max-width: none;
  }
}
</style>
