<template>
  <div class="auth-layout">
    <!-- 背景装饰 -->
    <div class="auth-background">
      <div class="background-gradient"></div>
      <div class="background-pattern"></div>
      <div class="floating-elements">
        <div class="floating-circle circle-1"></div>
        <div class="floating-circle circle-2"></div>
        <div class="floating-circle circle-3"></div>
      </div>
    </div>
    
    <!-- 主要内容 -->
    <div class="auth-container">
      <!-- 左侧品牌区域 -->
      <div class="brand-section">
        <div class="brand-content">
          <div class="brand-logo">
            <div class="logo-icon">🌫️</div>
            <div class="logo-text">
              <h1 class="logo-title">迷雾拼本</h1>
              <p class="logo-subtitle">Misty Labyrinth</p>
            </div>
          </div>
          
          <div class="brand-description">
            <h2 class="description-title">探索无限可能的剧本世界</h2>
            <p class="description-text">
              与志同道合的伙伴一起，开启沉浸式剧本杀之旅。
              在这里，每一个故事都等待着你来演绎。
            </p>
          </div>
          
          <div class="brand-features">
            <div class="feature-item">
              <span class="feature-icon">🎭</span>
              <span class="feature-text">精选优质剧本</span>
            </div>
            <div class="feature-item">
              <span class="feature-icon">👥</span>
              <span class="feature-text">智能拼车匹配</span>
            </div>
            <div class="feature-item">
              <span class="feature-icon">💬</span>
              <span class="feature-text">活跃社区交流</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧表单区域 -->
      <div class="form-section">
        <div class="form-container">
          <router-view />
        </div>
        
        <!-- 返回首页链接 -->
        <div class="back-home">
          <router-link to="/" class="back-link">
            <span class="back-icon">←</span>
            <span class="back-text">返回首页</span>
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.auth-layout {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.auth-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.background-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    #1A1A2E 0%,
    #16213E 25%,
    #0F0F1E 50%,
    #1A1A2E 75%,
    #16213E 100%
  );
}

.background-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(0, 245, 212, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 0, 228, 0.1) 0%, transparent 50%);
  background-size: 400px 400px;
  animation: patternMove 20s ease-in-out infinite;
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.floating-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(0, 245, 212, 0.1), rgba(255, 0, 228, 0.1));
  animation: float 6s ease-in-out infinite;
  
  &.circle-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
  }
  
  &.circle-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
  }
  
  &.circle-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
  }
}

.auth-container {
  position: relative;
  z-index: 2;
  min-height: 100vh;
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
}

.brand-section {
  padding: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand-content {
  max-width: 500px;
}

.brand-logo {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 40px;
}

.logo-icon {
  font-size: 4rem;
  filter: drop-shadow(0 0 20px rgba(0, 245, 212, 0.5));
}

.logo-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #fff;
  background: linear-gradient(135deg, #00F5D4, #FF00E4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 8px;
}

.logo-subtitle {
  font-size: 1rem;
  color: #888;
  letter-spacing: 2px;
  text-transform: uppercase;
  margin: 0;
}

.brand-description {
  margin-bottom: 40px;
}

.description-title {
  font-size: 1.8rem;
  color: #fff;
  font-weight: 600;
  margin-bottom: 16px;
  line-height: 1.3;
}

.description-text {
  font-size: 1.1rem;
  color: #B0B0B0;
  line-height: 1.6;
  margin: 0;
}

.brand-features {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
}

.feature-icon {
  font-size: 1.5rem;
}

.feature-text {
  font-size: 1rem;
  color: #E0E0E0;
  font-weight: 500;
}

.form-section {
  padding: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.form-container {
  width: 100%;
  max-width: 380px;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 245, 212, 0.3);
  border-radius: 20px;
  padding: 24px 28px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
      rgba(0, 245, 212, 0.05) 0%,
      rgba(255, 0, 228, 0.05) 100%);
    border-radius: 20px;
    z-index: -1;
  }
}

.back-home {
  position: absolute;
  top: 30px;
  right: 30px;
}

.back-link {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #B0B0B0;
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  
  &:hover {
    color: #00F5D4;
    transform: translateX(-4px);
  }
}

.back-icon {
  font-size: 1.2rem;
}

// 动画
@keyframes patternMove {
  0%, 100% { transform: translate(0, 0); }
  25% { transform: translate(20px, -20px); }
  50% { transform: translate(-20px, 20px); }
  75% { transform: translate(20px, 20px); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(120deg); }
  66% { transform: translateY(10px) rotate(240deg); }
}

// 响应式设计
@media (max-width: 1024px) {
  .auth-container {
    grid-template-columns: 1fr;
  }

  .brand-section {
    display: none;
  }

  .form-section {
    padding: 40px 20px;
    min-height: 100vh;
  }

  .form-container {
    max-width: 420px;
    padding: 24px 24px;
    max-height: 90vh;
  }

  .back-home {
    top: 20px;
    right: 20px;
  }
}

@media (max-width: 480px) {
  .form-section {
    padding: 20px 16px;
  }

  .form-container {
    padding: 20px 18px;
    border-radius: 16px;
    max-width: 100%;
    max-height: 95vh;
  }
}
</style>
