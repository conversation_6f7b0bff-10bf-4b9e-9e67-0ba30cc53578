import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores'
import axios from 'axios'

// 配置后端API地址
const API_BASE_URL = 'http://localhost:8081'

/**
 * 认证相关组合式函数
 */
export function useAuth() {
  const router = useRouter()
  const userStore = useUserStore()
  
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  
  // 计算属性
  const isLoggedIn = computed(() => userStore.isLoggedIn)
  const currentUser = computed(() => userStore.currentUser)
  
  // 发送邮箱验证码
  const sendEmailCode = async (email: string) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/user/email/code`, null, {
        params: { email }
      })

      if (response.data.code === 200) {
        return true
      } else {
        throw new Error(response.data.message || '发送验证码失败')
      }
    } catch (err: any) {
      error.value = err.response?.data?.message || err.message || '发送验证码失败'
      throw err
    }
  }

  // 登录
  const login = async (credentials: {
    email: string;
    password: string
  }) => {
    try {
      isLoading.value = true
      error.value = null

      // 调用登录API
      const response = await axios.post(`${API_BASE_URL}/api/user/login`, credentials)

      if (response.data.code === 200) {
        // 登录成功，获取token
        const token = response.data.data
        localStorage.setItem('auth_token', token)

        // 获取用户信息
        const userResponse = await axios.get(`${API_BASE_URL}/api/user/me`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })

        if (userResponse.data.code === 200) {
          const userData = userResponse.data.data
          const user = {
            id: userData.id,
            nickname: userData.nickName,
            avatar: userData.icon || 'https://picsum.photos/48/48?random=99',
            email: credentials.email || userData.email || '',
            level: 1,
            experience: 0,
            createdAt: new Date().toISOString(),
            status: 'active' as const
          }

          // 保存用户信息
          userStore.setCurrentUser(user)
        }

        return true
      } else {
        throw new Error(response.data.message || '登录失败')
      }
    } catch (err: any) {
      error.value = err.response?.data?.message || err.message || '登录失败'
      return false
    } finally {
      isLoading.value = false
    }
  }
  
  // 注册
  const register = async (userData: {
    email: string
    password: string
    confirmPassword: string
    code: string
    nickName?: string
  }) => {
    try {
      isLoading.value = true
      error.value = null

      // 调用注册API
      const response = await axios.post(`${API_BASE_URL}/api/user/register`, userData)

      if (response.data.code === 200) {
        return true
      } else {
        throw new Error(response.data.message || '注册失败')
      }
    } catch (err: any) {
      error.value = err.response?.data?.message || err.message || '注册失败'
      return false
    } finally {
      isLoading.value = false
    }
  }
  
  // 退出登录
  const logout = async () => {
    try {
      // 调用退出API
      const token = localStorage.getItem('auth_token')
      if (token) {
        try {
          await axios.post(`${API_BASE_URL}/api/user/logout`, {}, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          })
        } catch (err) {
          // 即使后端退出失败，也要清除本地数据
          console.warn('后端退出失败，但继续清除本地数据:', err)
        }
      }

      // 清除本地数据
      userStore.clearUserData()
      localStorage.removeItem('auth_token')

      // 跳转到首页
      router.push('/')

      return true
    } catch (err: any) {
      error.value = err.message || '退出失败'
      return false
    }
  }
  
  // 检查认证状态
  const checkAuth = async () => {
    const token = localStorage.getItem('auth_token')
    if (!token) {
      return false
    }

    try {
      // 验证token有效性
      const response = await axios.get(`${API_BASE_URL}/api/user/me`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.data.code === 200) {
        // token有效，更新用户信息
        const userData = response.data.data
        const user = {
          id: userData.id,
          nickname: userData.nickName,
          avatar: userData.icon || 'https://picsum.photos/48/48?random=99',
          email: userData.email || '',
          level: 1,
          experience: 0,
          createdAt: new Date().toISOString(),
          status: 'active' as const
        }

        userStore.setCurrentUser(user)
        return true
      } else {
        // token无效
        return false
      }
    } catch (err) {
      // token无效，清除本地数据
      logout()
      return false
    }
  }
  
  // 清除错误
  const clearError = () => {
    error.value = null
  }
  
  return {
    // 状态
    isLoading,
    error,
    isLoggedIn,
    currentUser,

    // 方法
    sendEmailCode,
    login,
    register,
    logout,
    checkAuth,
    clearError
  }
}
