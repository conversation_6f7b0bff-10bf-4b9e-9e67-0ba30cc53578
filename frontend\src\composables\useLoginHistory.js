import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getLoginHistory,
  getRecentLogins,
  getOnlineSessions,
  clearLoginHistory,
  getLoginStatistics,
  detectSuspiciousLogins,
  generateSecurityReport,
  logoutAllSessions
} from '@/api/settings'

/**
 * 登录历史管理 Composable
 */
export function useLoginHistory() {
  // 响应式状态
  const loading = ref(false)
  const historyLoading = ref(false)
  const statisticsLoading = ref(false)

  // 登录历史数据
  const historyData = reactive({
    records: [],
    total: 0,
    current: 1,
    size: 10
  })

  // 最近登录记录
  const recentLogins = ref([])
  
  // 在线会话
  const onlineSessions = ref([])
  
  // 登录统计
  const statistics = reactive({
    totalLogins: 0,
    failedLogins: 0,
    sessionCount: 0,
    avgSessionDuration: 0,
    totalSessionDuration: 0,
    lastLoginTime: null,
    isOnline: false
  })

  // 可疑登录
  const suspiciousLogins = ref([])
  
  // 安全报告
  const securityReport = ref({})

  // 计算属性
  const hasHistory = computed(() => historyData.records.length > 0)
  const hasOnlineSessions = computed(() => onlineSessions.value.length > 0)
  const hasSuspiciousLogins = computed(() => suspiciousLogins.value.length > 0)
  
  const successRate = computed(() => {
    const total = statistics.totalLogins + statistics.failedLogins
    if (total === 0) return 100
    return Math.round((statistics.totalLogins / total) * 100)
  })

  const avgSessionDurationText = computed(() => {
    const duration = statistics.avgSessionDuration
    if (!duration) return '未知'
    
    const hours = Math.floor(duration / 3600)
    const minutes = Math.floor((duration % 3600) / 60)
    
    if (hours > 0) {
      return `${hours}小时${minutes}分钟`
    } else if (minutes > 0) {
      return `${minutes}分钟`
    } else {
      return `${duration}秒`
    }
  })

  // 获取登录历史
  const fetchHistory = async (page = 1, size = 10) => {
    try {
      historyLoading.value = true
      const response = await getLoginHistory(page, size)
      
      if (response.code === 200) {
        historyData.records = response.data.records || []
        historyData.total = response.data.total || 0
        historyData.current = response.data.current || 1
        historyData.size = response.data.size || 10
      } else {
        ElMessage.error(response.message || '获取登录历史失败')
      }
    } catch (error) {
      console.error('获取登录历史失败:', error)
      ElMessage.error('获取登录历史失败')
    } finally {
      historyLoading.value = false
    }
  }

  // 获取最近登录记录
  const fetchRecentLogins = async (limit = 5) => {
    try {
      const response = await getRecentLogins(limit)
      
      if (response.code === 200) {
        recentLogins.value = response.data || []
      } else {
        ElMessage.error(response.message || '获取最近登录记录失败')
      }
    } catch (error) {
      console.error('获取最近登录记录失败:', error)
      ElMessage.error('获取最近登录记录失败')
    }
  }

  // 获取在线会话
  const fetchOnlineSessions = async () => {
    try {
      const response = await getOnlineSessions()
      
      if (response.code === 200) {
        onlineSessions.value = response.data || []
      } else {
        ElMessage.error(response.message || '获取在线会话失败')
      }
    } catch (error) {
      console.error('获取在线会话失败:', error)
      ElMessage.error('获取在线会话失败')
    }
  }

  // 获取登录统计
  const fetchStatistics = async () => {
    try {
      statisticsLoading.value = true
      const response = await getLoginStatistics()
      
      if (response.code === 200) {
        Object.assign(statistics, response.data)
      } else {
        ElMessage.error(response.message || '获取登录统计失败')
      }
    } catch (error) {
      console.error('获取登录统计失败:', error)
      ElMessage.error('获取登录统计失败')
    } finally {
      statisticsLoading.value = false
    }
  }

  // 检测可疑登录
  const fetchSuspiciousLogins = async () => {
    try {
      const response = await detectSuspiciousLogins()
      
      if (response.code === 200) {
        suspiciousLogins.value = response.data || []
      } else {
        ElMessage.error(response.message || '检测可疑登录失败')
      }
    } catch (error) {
      console.error('检测可疑登录失败:', error)
      ElMessage.error('检测可疑登录失败')
    }
  }

  // 生成安全报告
  const generateSecurityReport = async (days = 30) => {
    try {
      loading.value = true
      const response = await generateSecurityReport(days)
      
      if (response.code === 200) {
        securityReport.value = response.data || {}
        return response.data
      } else {
        ElMessage.error(response.message || '生成安全报告失败')
        return null
      }
    } catch (error) {
      console.error('生成安全报告失败:', error)
      ElMessage.error('生成安全报告失败')
      return null
    } finally {
      loading.value = false
    }
  }

  // 清除登录历史
  const clearHistory = async () => {
    try {
      await ElMessageBox.confirm(
        '确定要清除所有登录历史吗？此操作不可撤销。',
        '确认清除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      loading.value = true
      const response = await clearLoginHistory()
      
      if (response.code === 200) {
        // 清空本地数据
        historyData.records = []
        historyData.total = 0
        recentLogins.value = []
        
        ElMessage.success('登录历史已清除')
        return true
      } else {
        ElMessage.error(response.message || '清除失败')
        return false
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('清除登录历史失败:', error)
        ElMessage.error('清除失败')
      }
      return false
    } finally {
      loading.value = false
    }
  }

  // 登出所有会话
  const logoutAll = async () => {
    try {
      await ElMessageBox.confirm(
        '确定要登出所有设备上的会话吗？您需要重新登录。',
        '确认登出',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      loading.value = true
      const response = await logoutAllSessions()
      
      if (response.code === 200) {
        // 刷新在线会话
        await fetchOnlineSessions()
        
        ElMessage.success(`已登出 ${response.data} 个会话`)
        return true
      } else {
        ElMessage.error(response.message || '登出失败')
        return false
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('登出所有会话失败:', error)
        ElMessage.error('登出失败')
      }
      return false
    } finally {
      loading.value = false
    }
  }

  // 刷新所有数据
  const refreshAll = async () => {
    await Promise.all([
      fetchHistory(historyData.current, historyData.size),
      fetchRecentLogins(),
      fetchOnlineSessions(),
      fetchStatistics(),
      fetchSuspiciousLogins()
    ])
  }

  // 格式化登录时间
  const formatLoginTime = (time) => {
    if (!time) return '未知'
    
    const now = new Date()
    const loginTime = new Date(time)
    const diff = now - loginTime
    
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    
    if (days > 0) {
      return `${days}天前`
    } else if (hours > 0) {
      return `${hours}小时前`
    } else if (minutes > 0) {
      return `${minutes}分钟前`
    } else {
      return '刚刚'
    }
  }

  // 获取设备图标
  const getDeviceIcon = (deviceType) => {
    const icons = {
      Mobile: '📱',
      Tablet: '📱',
      Desktop: '💻'
    }
    return icons[deviceType] || '❓'
  }

  // 获取状态图标
  const getStatusIcon = (status) => {
    return status === 1 ? '✅' : '❌'
  }

  // 获取在线状态图标
  const getOnlineIcon = (isOnline) => {
    return isOnline ? '🟢' : '⚫'
  }

  // 获取风险等级
  const getRiskLevel = (suspiciousCount) => {
    if (suspiciousCount === 0) return { level: 'low', text: '低风险', color: '#67C23A' }
    if (suspiciousCount <= 2) return { level: 'medium', text: '中风险', color: '#E6A23C' }
    return { level: 'high', text: '高风险', color: '#F56C6C' }
  }

  // 导出登录历史
  const exportHistory = () => {
    if (!hasHistory.value) {
      ElMessage.warning('没有可导出的数据')
      return
    }

    try {
      const data = {
        exportTime: new Date().toISOString(),
        statistics: statistics,
        history: historyData.records,
        suspiciousLogins: suspiciousLogins.value
      }

      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `login-history-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      ElMessage.success('登录历史导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败')
    }
  }

  return {
    // 状态
    loading,
    historyLoading,
    statisticsLoading,
    
    // 数据
    historyData,
    recentLogins,
    onlineSessions,
    statistics,
    suspiciousLogins,
    securityReport,
    
    // 计算属性
    hasHistory,
    hasOnlineSessions,
    hasSuspiciousLogins,
    successRate,
    avgSessionDurationText,
    
    // 方法
    fetchHistory,
    fetchRecentLogins,
    fetchOnlineSessions,
    fetchStatistics,
    fetchSuspiciousLogins,
    generateSecurityReport,
    clearHistory,
    logoutAll,
    refreshAll,
    exportHistory,
    
    // 工具方法
    formatLoginTime,
    getDeviceIcon,
    getStatusIcon,
    getOnlineIcon,
    getRiskLevel
  }
}
