import { computed } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { useGlobalSettings } from './useSettings'

/**
 * 通知管理 Composable
 */
export function useNotifications() {
  const { settings, toggleNotification, updateSingleField } = useGlobalSettings()

  // 通知类型配置
  const notificationTypes = {
    email: {
      name: '邮件通知',
      icon: '📧',
      description: '重要信息和更新通过邮件发送',
      examples: ['账户安全提醒', '密码修改通知', '重要系统公告']
    },
    system: {
      name: '系统通知',
      icon: '🔔',
      description: '系统消息和状态更新',
      examples: ['登录提醒', '系统维护通知', '功能更新提示']
    },
    activity: {
      name: '活动通知',
      icon: '🎯',
      description: '游戏活动和事件提醒',
      examples: ['新活动开始', '活动即将结束', '奖励发放通知']
    },
    social: {
      name: '社交通知',
      icon: '👥',
      description: '社交互动和关注提醒',
      examples: ['新增关注者', '收到点赞', '评论回复']
    }
  }

  // 计算属性
  const emailEnabled = computed(() => settings.emailNotifications)
  const systemEnabled = computed(() => settings.systemNotifications)
  const activityEnabled = computed(() => settings.activityNotifications)
  const socialEnabled = computed(() => settings.socialNotifications)

  const enabledCount = computed(() => {
    let count = 0
    if (emailEnabled.value) count++
    if (systemEnabled.value) count++
    if (activityEnabled.value) count++
    if (socialEnabled.value) count++
    return count
  })

  const allEnabled = computed(() => enabledCount.value === 4)
  const noneEnabled = computed(() => enabledCount.value === 0)

  const notificationSummary = computed(() => {
    return `已启用 ${enabledCount.value}/4 项通知`
  })

  // 获取通知状态
  const getNotificationStatus = (type) => {
    const fieldName = `${type}Notifications`
    return settings[fieldName] || false
  }

  // 切换单个通知
  const toggleSingleNotification = async (type) => {
    try {
      const newStatus = await toggleNotification(type)
      return newStatus
    } catch (error) {
      console.error(`切换${type}通知失败:`, error)
      return getNotificationStatus(type)
    }
  }

  // 启用所有通知
  const enableAllNotifications = async () => {
    try {
      const updates = {
        emailNotifications: true,
        systemNotifications: true,
        activityNotifications: true,
        socialNotifications: true
      }

      const promises = Object.entries(updates).map(([field, value]) =>
        updateSingleField(field, value)
      )

      await Promise.all(promises)
      ElMessage.success('已启用所有通知')
      return true
    } catch (error) {
      console.error('启用所有通知失败:', error)
      ElMessage.error('操作失败')
      return false
    }
  }

  // 禁用所有通知
  const disableAllNotifications = async () => {
    try {
      const updates = {
        emailNotifications: false,
        systemNotifications: false,
        activityNotifications: false,
        socialNotifications: false
      }

      const promises = Object.entries(updates).map(([field, value]) =>
        updateSingleField(field, value)
      )

      await Promise.all(promises)
      ElMessage.success('已禁用所有通知')
      return true
    } catch (error) {
      console.error('禁用所有通知失败:', error)
      ElMessage.error('操作失败')
      return false
    }
  }

  // 获取通知配置
  const getNotificationConfig = (type) => {
    return notificationTypes[type] || {
      name: '未知通知',
      icon: '❓',
      description: '未知的通知类型',
      examples: []
    }
  }

  // 发送测试通知
  const sendTestNotification = (type) => {
    const config = getNotificationConfig(type)
    
    if (!getNotificationStatus(type)) {
      ElMessage.warning(`${config.name}已关闭，无法发送测试通知`)
      return
    }

    ElNotification({
      title: `${config.icon} ${config.name}测试`,
      message: `这是一条${config.name}的测试消息`,
      type: 'info',
      duration: 3000
    })
  }

  // 批量设置通知
  const batchSetNotifications = async (settings) => {
    try {
      const promises = Object.entries(settings).map(([type, enabled]) => {
        const fieldName = `${type}Notifications`
        return updateSingleField(fieldName, enabled)
      })

      await Promise.all(promises)
      ElMessage.success('通知设置已更新')
      return true
    } catch (error) {
      console.error('批量设置通知失败:', error)
      ElMessage.error('设置失败')
      return false
    }
  }

  // 获取推荐设置
  const getRecommendedSettings = (scenario) => {
    const scenarios = {
      work: {
        name: '工作模式',
        description: '适合工作时间，减少干扰',
        settings: {
          email: true,
          system: true,
          activity: false,
          social: false
        }
      },
      gaming: {
        name: '游戏模式',
        description: '专注游戏体验',
        settings: {
          email: false,
          system: true,
          activity: true,
          social: true
        }
      },
      minimal: {
        name: '极简模式',
        description: '只保留必要通知',
        settings: {
          email: true,
          system: true,
          activity: false,
          social: false
        }
      },
      full: {
        name: '完整模式',
        description: '接收所有通知',
        settings: {
          email: true,
          system: true,
          activity: true,
          social: true
        }
      }
    }

    return scenarios[scenario] || scenarios.minimal
  }

  // 应用推荐设置
  const applyRecommendedSettings = async (scenario) => {
    const recommended = getRecommendedSettings(scenario)
    const success = await batchSetNotifications(recommended.settings)
    
    if (success) {
      ElMessage.success(`已应用${recommended.name}设置`)
    }
    
    return success
  }

  // 获取通知统计
  const getNotificationStats = () => {
    return {
      total: 4,
      enabled: enabledCount.value,
      disabled: 4 - enabledCount.value,
      percentage: Math.round((enabledCount.value / 4) * 100)
    }
  }

  // 检查通知权限（浏览器通知）
  const checkBrowserNotificationPermission = () => {
    if (!('Notification' in window)) {
      return 'not-supported'
    }
    return Notification.permission
  }

  // 请求浏览器通知权限
  const requestBrowserNotificationPermission = async () => {
    if (!('Notification' in window)) {
      ElMessage.warning('您的浏览器不支持桌面通知')
      return 'not-supported'
    }

    if (Notification.permission === 'granted') {
      return 'granted'
    }

    if (Notification.permission === 'denied') {
      ElMessage.warning('桌面通知权限已被拒绝，请在浏览器设置中手动开启')
      return 'denied'
    }

    try {
      const permission = await Notification.requestPermission()
      if (permission === 'granted') {
        ElMessage.success('桌面通知权限已开启')
      } else {
        ElMessage.warning('桌面通知权限被拒绝')
      }
      return permission
    } catch (error) {
      console.error('请求通知权限失败:', error)
      return 'denied'
    }
  }

  // 发送浏览器通知
  const sendBrowserNotification = (title, options = {}) => {
    if (checkBrowserNotificationPermission() !== 'granted') {
      return false
    }

    try {
      const notification = new Notification(title, {
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        ...options
      })

      // 自动关闭
      setTimeout(() => {
        notification.close()
      }, options.duration || 5000)

      return true
    } catch (error) {
      console.error('发送浏览器通知失败:', error)
      return false
    }
  }

  return {
    // 状态
    emailEnabled,
    systemEnabled,
    activityEnabled,
    socialEnabled,
    enabledCount,
    allEnabled,
    noneEnabled,
    notificationSummary,

    // 配置
    notificationTypes,

    // 方法
    getNotificationStatus,
    toggleSingleNotification,
    enableAllNotifications,
    disableAllNotifications,
    getNotificationConfig,
    sendTestNotification,
    batchSetNotifications,
    getRecommendedSettings,
    applyRecommendedSettings,
    getNotificationStats,

    // 浏览器通知
    checkBrowserNotificationPermission,
    requestBrowserNotificationPermission,
    sendBrowserNotification
  }
}
