import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getUserSettings, 
  updateUserSettings, 
  resetToDefaults,
  toggleTheme as apiToggleTheme,
  toggleNotification as apiToggleNotification,
  updateSingleSetting,
  exportUserSettings,
  importUserSettings
} from '@/api/settings'

/**
 * 用户设置状态管理
 */
export function useSettings() {
  // 响应式状态
  const loading = ref(false)
  const settings = reactive({
    theme: 'dark',
    language: 'zh-CN',
    timezone: 'Asia/Shanghai',
    emailNotifications: true,
    systemNotifications: true,
    activityNotifications: true,
    socialNotifications: false,
    privacyLevel: 1,
    autoSave: true
  })

  // 计算属性
  const isDarkTheme = computed(() => settings.theme === 'dark')
  const isLightTheme = computed(() => settings.theme === 'light')
  
  const privacyLevelText = computed(() => {
    const levels = { 1: '公开', 2: '好友', 3: '私密' }
    return levels[settings.privacyLevel] || '未知'
  })

  const themeText = computed(() => {
    return settings.theme === 'dark' ? '深色' : '浅色'
  })

  const notificationCount = computed(() => {
    let count = 0
    if (settings.emailNotifications) count++
    if (settings.systemNotifications) count++
    if (settings.activityNotifications) count++
    if (settings.socialNotifications) count++
    return count
  })

  const notificationSummary = computed(() => {
    return `已启用 ${notificationCount.value}/4 项通知`
  })

  // 获取用户设置
  const fetchSettings = async () => {
    try {
      loading.value = true
      const response = await getUserSettings()
      
      if (response.code === 200) {
        Object.assign(settings, response.data)
        // 应用主题到页面
        applyTheme(settings.theme)
      } else {
        ElMessage.error(response.message || '获取设置失败')
      }
    } catch (error) {
      console.error('获取设置失败:', error)
      ElMessage.error('获取设置失败')
    } finally {
      loading.value = false
    }
  }

  // 更新设置
  const updateSettings = async (newSettings) => {
    try {
      loading.value = true
      const response = await updateUserSettings(newSettings)
      
      if (response.code === 200) {
        Object.assign(settings, newSettings)
        ElMessage.success('设置更新成功')
        
        // 如果更新了主题，应用到页面
        if (newSettings.theme) {
          applyTheme(newSettings.theme)
        }
        
        return true
      } else {
        ElMessage.error(response.message || '设置更新失败')
        return false
      }
    } catch (error) {
      console.error('更新设置失败:', error)
      ElMessage.error('设置更新失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 更新单个设置项
  const updateSingleField = async (fieldName, fieldValue) => {
    try {
      const response = await updateSingleSetting(fieldName, fieldValue)
      
      if (response.code === 200) {
        settings[fieldName] = fieldValue
        
        // 如果是主题设置，应用到页面
        if (fieldName === 'theme') {
          applyTheme(fieldValue)
        }
        
        return true
      } else {
        ElMessage.error(response.message || '设置更新失败')
        return false
      }
    } catch (error) {
      console.error('更新设置失败:', error)
      ElMessage.error('设置更新失败')
      return false
    }
  }

  // 切换主题
  const toggleTheme = async () => {
    try {
      const response = await apiToggleTheme()
      
      if (response.code === 200) {
        settings.theme = response.data
        applyTheme(settings.theme)
        ElMessage.success(`已切换到${settings.theme === 'dark' ? '深色' : '浅色'}主题`)
        return settings.theme
      } else {
        ElMessage.error(response.message || '主题切换失败')
        return settings.theme
      }
    } catch (error) {
      console.error('主题切换失败:', error)
      ElMessage.error('主题切换失败')
      return settings.theme
    }
  }

  // 切换通知设置
  const toggleNotification = async (notificationType) => {
    try {
      const response = await apiToggleNotification(notificationType)
      
      if (response.code === 200) {
        const fieldName = `${notificationType}Notifications`
        settings[fieldName] = response.data
        ElMessage.success(`${getNotificationTypeName(notificationType)}通知已${response.data ? '开启' : '关闭'}`)
        return response.data
      } else {
        ElMessage.error(response.message || '通知设置更新失败')
        return settings[`${notificationType}Notifications`]
      }
    } catch (error) {
      console.error('通知设置更新失败:', error)
      ElMessage.error('通知设置更新失败')
      return settings[`${notificationType}Notifications`]
    }
  }

  // 重置为默认设置
  const resetSettings = async () => {
    try {
      await ElMessageBox.confirm(
        '确定要重置为默认设置吗？此操作不可撤销。',
        '确认重置',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      loading.value = true
      const response = await resetToDefaults()
      
      if (response.code === 200) {
        // 重新获取设置
        await fetchSettings()
        ElMessage.success('已重置为默认设置')
        return true
      } else {
        ElMessage.error(response.message || '重置失败')
        return false
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('重置设置失败:', error)
        ElMessage.error('重置失败')
      }
      return false
    } finally {
      loading.value = false
    }
  }

  // 导出设置
  const exportSettings = async () => {
    try {
      const response = await exportUserSettings()
      
      if (response.code === 200) {
        // 创建下载链接
        const blob = new Blob([response.data], { type: 'application/json' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `user-settings-${new Date().toISOString().split('T')[0]}.json`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
        
        ElMessage.success('设置导出成功')
        return true
      } else {
        ElMessage.error(response.message || '导出失败')
        return false
      }
    } catch (error) {
      console.error('导出设置失败:', error)
      ElMessage.error('导出失败')
      return false
    }
  }

  // 导入设置
  const importSettings = async (file) => {
    try {
      const text = await file.text()
      const response = await importUserSettings(text)
      
      if (response.code === 200) {
        // 重新获取设置
        await fetchSettings()
        ElMessage.success('设置导入成功')
        return true
      } else {
        ElMessage.error(response.message || '导入失败')
        return false
      }
    } catch (error) {
      console.error('导入设置失败:', error)
      ElMessage.error('导入失败，请检查文件格式')
      return false
    }
  }

  // 应用主题到页面
  const applyTheme = (theme) => {
    const html = document.documentElement
    if (theme === 'dark') {
      html.classList.add('dark')
      html.classList.remove('light')
    } else {
      html.classList.add('light')
      html.classList.remove('dark')
    }
    
    // 更新CSS变量
    const root = document.documentElement
    if (theme === 'dark') {
      root.style.setProperty('--el-bg-color', '#1a1a1a')
      root.style.setProperty('--el-text-color-primary', '#ffffff')
      root.style.setProperty('--el-border-color', '#333333')
    } else {
      root.style.setProperty('--el-bg-color', '#ffffff')
      root.style.setProperty('--el-text-color-primary', '#303133')
      root.style.setProperty('--el-border-color', '#dcdfe6')
    }
  }

  // 获取通知类型名称
  const getNotificationTypeName = (type) => {
    const names = {
      email: '邮件',
      system: '系统',
      activity: '活动',
      social: '社交'
    }
    return names[type] || type
  }

  // 监听设置变化，自动保存（如果开启了自动保存）
  watch(
    () => ({ ...settings }),
    (newSettings, oldSettings) => {
      if (settings.autoSave && oldSettings && Object.keys(oldSettings).length > 0) {
        // 防抖处理，避免频繁保存
        clearTimeout(autoSaveTimer)
        autoSaveTimer = setTimeout(() => {
          updateSettings(newSettings)
        }, 1000)
      }
    },
    { deep: true }
  )

  let autoSaveTimer = null

  // 初始化时获取设置
  fetchSettings()

  return {
    // 状态
    loading,
    settings,
    
    // 计算属性
    isDarkTheme,
    isLightTheme,
    privacyLevelText,
    themeText,
    notificationCount,
    notificationSummary,
    
    // 方法
    fetchSettings,
    updateSettings,
    updateSingleField,
    toggleTheme,
    toggleNotification,
    resetSettings,
    exportSettings,
    importSettings,
    applyTheme
  }
}

// 全局设置实例（单例模式）
let globalSettingsInstance = null

export function useGlobalSettings() {
  if (!globalSettingsInstance) {
    globalSettingsInstance = useSettings()
  }
  return globalSettingsInstance
}
