import { ref, computed, watch, onMounted } from 'vue'
import { useGlobalSettings } from './useSettings'

/**
 * 主题管理 Composable
 */
export function useTheme() {
  const { settings, toggleTheme: apiToggleTheme, applyTheme } = useGlobalSettings()
  
  // 当前主题
  const currentTheme = computed(() => settings.theme)
  const isDark = computed(() => settings.theme === 'dark')
  const isLight = computed(() => settings.theme === 'light')

  // 主题配置
  const themeConfig = {
    dark: {
      name: '深色主题',
      icon: '🌙',
      description: '适合夜间使用，减少眼部疲劳',
      colors: {
        primary: '#00F5D4',
        background: '#1a1a1a',
        surface: '#2d2d2d',
        text: '#ffffff',
        textSecondary: '#b3b3b3',
        border: '#333333',
        shadow: 'rgba(0, 0, 0, 0.3)'
      }
    },
    light: {
      name: '浅色主题',
      icon: '☀️',
      description: '适合白天使用，清晰明亮',
      colors: {
        primary: '#00F5D4',
        background: '#ffffff',
        surface: '#f5f5f5',
        text: '#303133',
        textSecondary: '#606266',
        border: '#dcdfe6',
        shadow: 'rgba(0, 0, 0, 0.1)'
      }
    }
  }

  // 获取当前主题配置
  const currentThemeConfig = computed(() => themeConfig[currentTheme.value])

  // 切换主题
  const toggleTheme = async () => {
    try {
      const newTheme = await apiToggleTheme()
      return newTheme
    } catch (error) {
      console.error('主题切换失败:', error)
      return currentTheme.value
    }
  }

  // 设置特定主题
  const setTheme = async (theme) => {
    if (theme !== currentTheme.value) {
      return await toggleTheme()
    }
    return theme
  }

  // 应用主题样式
  const applyThemeStyles = (theme = currentTheme.value) => {
    const config = themeConfig[theme]
    if (!config) return

    const root = document.documentElement
    
    // 设置CSS变量
    Object.entries(config.colors).forEach(([key, value]) => {
      root.style.setProperty(`--theme-${key}`, value)
    })

    // 设置Element Plus主题变量
    root.style.setProperty('--el-color-primary', config.colors.primary)
    root.style.setProperty('--el-bg-color', config.colors.background)
    root.style.setProperty('--el-bg-color-page', config.colors.surface)
    root.style.setProperty('--el-text-color-primary', config.colors.text)
    root.style.setProperty('--el-text-color-regular', config.colors.textSecondary)
    root.style.setProperty('--el-border-color', config.colors.border)
    root.style.setProperty('--el-box-shadow', `0 2px 12px 0 ${config.colors.shadow}`)

    // 设置body背景色
    document.body.style.backgroundColor = config.colors.background
    document.body.style.color = config.colors.text

    // 添加主题类名
    document.documentElement.className = document.documentElement.className
      .replace(/theme-\w+/g, '')
      .trim()
    document.documentElement.classList.add(`theme-${theme}`)
  }

  // 获取主题图标
  const getThemeIcon = (theme = currentTheme.value) => {
    return themeConfig[theme]?.icon || '🎨'
  }

  // 获取主题名称
  const getThemeName = (theme = currentTheme.value) => {
    return themeConfig[theme]?.name || '未知主题'
  }

  // 获取主题描述
  const getThemeDescription = (theme = currentTheme.value) => {
    return themeConfig[theme]?.description || ''
  }

  // 检测系统主题偏好
  const detectSystemTheme = () => {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'dark'
    }
    return 'light'
  }

  // 监听系统主题变化
  const watchSystemTheme = (callback) => {
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      const handler = (e) => {
        callback(e.matches ? 'dark' : 'light')
      }
      
      mediaQuery.addEventListener('change', handler)
      
      // 返回清理函数
      return () => {
        mediaQuery.removeEventListener('change', handler)
      }
    }
    return () => {}
  }

  // 自动跟随系统主题
  const autoFollowSystem = ref(false)
  let systemThemeCleanup = null

  const enableAutoFollowSystem = () => {
    autoFollowSystem.value = true
    systemThemeCleanup = watchSystemTheme((systemTheme) => {
      if (autoFollowSystem.value && systemTheme !== currentTheme.value) {
        setTheme(systemTheme)
      }
    })
  }

  const disableAutoFollowSystem = () => {
    autoFollowSystem.value = false
    if (systemThemeCleanup) {
      systemThemeCleanup()
      systemThemeCleanup = null
    }
  }

  // 获取主题预览色彩
  const getThemePreview = (theme) => {
    const config = themeConfig[theme]
    if (!config) return {}

    return {
      background: config.colors.background,
      surface: config.colors.surface,
      primary: config.colors.primary,
      text: config.colors.text
    }
  }

  // 生成主题CSS
  const generateThemeCSS = (theme = currentTheme.value) => {
    const config = themeConfig[theme]
    if (!config) return ''

    return `
      :root.theme-${theme} {
        ${Object.entries(config.colors).map(([key, value]) => 
          `--theme-${key}: ${value};`
        ).join('\n        ')}
        
        --el-color-primary: ${config.colors.primary};
        --el-bg-color: ${config.colors.background};
        --el-bg-color-page: ${config.colors.surface};
        --el-text-color-primary: ${config.colors.text};
        --el-text-color-regular: ${config.colors.textSecondary};
        --el-border-color: ${config.colors.border};
        --el-box-shadow: 0 2px 12px 0 ${config.colors.shadow};
      }
      
      body.theme-${theme} {
        background-color: ${config.colors.background};
        color: ${config.colors.text};
        transition: background-color 0.3s ease, color 0.3s ease;
      }
    `
  }

  // 监听主题变化，自动应用样式
  watch(currentTheme, (newTheme) => {
    applyThemeStyles(newTheme)
  }, { immediate: true })

  // 组件挂载时应用主题
  onMounted(() => {
    applyThemeStyles()
  })

  return {
    // 状态
    currentTheme,
    isDark,
    isLight,
    autoFollowSystem,
    
    // 配置
    themeConfig,
    currentThemeConfig,
    
    // 方法
    toggleTheme,
    setTheme,
    applyThemeStyles,
    getThemeIcon,
    getThemeName,
    getThemeDescription,
    detectSystemTheme,
    watchSystemTheme,
    enableAutoFollowSystem,
    disableAutoFollowSystem,
    getThemePreview,
    generateThemeCSS
  }
}

// 全局主题实例
let globalThemeInstance = null

export function useGlobalTheme() {
  if (!globalThemeInstance) {
    globalThemeInstance = useTheme()
  }
  return globalThemeInstance
}
