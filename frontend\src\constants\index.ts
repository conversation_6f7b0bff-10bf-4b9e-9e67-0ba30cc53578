// 应用常量定义

// 应用信息
export const APP_INFO = {
  NAME: '迷雾拼本',
  VERSION: '1.0.0',
  DESCRIPTION: '探索无限可能的剧本世界'
}

// 存储键名
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  USER_INFO: 'user_info',
  THEME: 'app_theme',
  LANGUAGE: 'app_language',
  LOBBY_VIEW_MODE: 'lobby_view_mode'
}

// API 端点
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh'
  },
  USER: {
    PROFILE: '/users/profile',
    AVATAR: '/users/avatar',
    FOLLOW: '/users/follow'
  },
  SCRIPT: {
    LIST: '/scripts',
    DETAIL: '/scripts/:id',
    SEARCH: '/scripts/search'
  },
  LOBBY: {
    LIST: '/lobbies',
    CREATE: '/lobbies',
    JOIN: '/lobbies/:id/join',
    LEAVE: '/lobbies/:id/leave'
  },
  FEED: {
    LIST: '/feed',
    CREATE: '/feed',
    LIKE: '/feed/:id/like',
    COMMENT: '/feed/:id/comments'
  }
}

// 剧本类型
export const SCRIPT_GENRES = [
  '推理',
  '悬疑',
  '恐怖',
  '情感',
  '欢乐',
  '古风',
  '现代',
  '科幻',
  '奇幻',
  '历史'
]

// 游戏人数选项
export const PLAYER_COUNT_OPTIONS = [4, 5, 6, 7, 8, 9, 10, 11, 12]

// 游戏时长选项（分钟）
export const DURATION_OPTIONS = [
  { label: '2小时', value: 120 },
  { label: '3小时', value: 180 },
  { label: '4小时', value: 240 },
  { label: '5小时', value: 300 },
  { label: '6小时', value: 360 },
  { label: '7小时', value: 420 },
  { label: '8小时', value: 480 }
]

// 地点选项
export const LOCATION_OPTIONS = ['线上', '线下', '混合']

// 难度等级
export const DIFFICULTY_LEVELS = [
  { value: 1, label: '新手', color: '#4CAF50' },
  { value: 2, label: '简单', color: '#8BC34A' },
  { value: 3, label: '中等', color: '#FFC107' },
  { value: 4, label: '困难', color: '#FF9800' },
  { value: 5, label: '专家', color: '#F44336' }
]

// 车队状态
export const LOBBY_STATUS = {
  WAITING: 'waiting',
  FULL: 'full',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
}

// 动态类型
export const POST_TYPES = {
  REVIEW: 'review',
  SHARE: 'share',
  GUIDE: 'guide',
  DISCUSSION: 'discussion'
}

// 通知类型
export const NOTIFICATION_TYPES = {
  LOBBY_JOIN: 'lobby_join',
  LOBBY_START: 'lobby_start',
  USER_FOLLOW: 'user_follow',
  POST_LIKE: 'post_like',
  POST_COMMENT: 'post_comment',
  SYSTEM: 'system'
}

// 分页配置
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100
}

// 文件上传配置
export const UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  ALLOWED_VIDEO_TYPES: ['video/mp4', 'video/webm', 'video/ogg']
}

// 正则表达式
export const REGEX = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^1[3-9]\d{9}$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
  NICKNAME: /^[\u4e00-\u9fa5a-zA-Z0-9_]{2,20}$/
}
