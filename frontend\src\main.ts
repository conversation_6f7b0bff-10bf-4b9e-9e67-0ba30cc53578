import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import pinia from './stores'

// Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 样式文件
import './styles/main.scss'
import './styles/theme.scss'

// 创建应用实例
const app = createApp(App)

// 使用插件
app.use(pinia)
app.use(router)
app.use(ElementPlus)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 全局错误处理
app.config.errorHandler = (err, instance, info) => {
  console.error('全局错误:', err, info)
  
  // 可以在这里添加错误上报逻辑
  // errorReporting.report(err, { instance, info })
}

// 全局警告处理
app.config.warnHandler = (msg, instance, trace) => {
  console.warn('全局警告:', msg, trace)
}

// 初始化应用状态
const initApp = async () => {
  // 检查登录状态
  const token = localStorage.getItem('auth_token')
  if (token) {
    try {
      // 验证token并获取用户信息
      const response = await fetch('http://localhost:8081/api/user/me', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const result = await response.json()
        if (result.code === 200) {
          // 恢复用户登录状态
          const { useUserStore } = await import('@/stores')
          const userStore = useUserStore()

          const userData = result.data
          const user = {
            id: userData.id,
            nickname: userData.nickName,
            avatar: userData.icon || 'https://picsum.photos/48/48?random=99',
            phone: '', // 后端没有返回手机号
            level: 1,
            experience: 0,
            createdAt: new Date().toISOString(),
            status: 'active' as const
          }

          userStore.setCurrentUser(user)
          console.log('用户登录状态已恢复:', user)
        } else {
          // token无效，清除本地存储
          localStorage.removeItem('auth_token')
        }
      } else {
        // 请求失败，清除本地存储
        localStorage.removeItem('auth_token')
      }
    } catch (error) {
      console.error('检查登录状态失败:', error)
      localStorage.removeItem('auth_token')
    }
  }
}

// 挂载应用
app.mount('#app')

// 初始化应用状态
initApp()

// 开发环境下的调试工具
if (import.meta.env.DEV) {
  // 暴露应用实例到全局，方便调试
  window.__app__ = app
  
  // 性能监控
  if ('performance' in window) {
    window.addEventListener('load', () => {
      const perfData = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      console.log('页面加载性能:', {
        DNS解析: perfData.domainLookupEnd - perfData.domainLookupStart,
        TCP连接: perfData.connectEnd - perfData.connectStart,
        请求响应: perfData.responseEnd - perfData.requestStart,
        DOM解析: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
        页面加载: perfData.loadEventEnd - perfData.loadEventStart,
        总时间: perfData.loadEventEnd - perfData.navigationStart
      })
    })
  }
}

// 注册 Service Worker（生产环境）
if (import.meta.env.PROD && 'serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then(registration => {
        console.log('SW registered: ', registration)
      })
      .catch(registrationError => {
        console.log('SW registration failed: ', registrationError)
      })
  })
}
