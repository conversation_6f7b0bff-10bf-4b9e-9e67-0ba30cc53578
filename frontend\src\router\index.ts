import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 布局组件
const DefaultLayout = () => import('@/components/layout/DefaultLayout.vue')
const AuthLayout = () => import('@/components/layout/AuthLayout.vue')

// 页面组件 - 懒加载
const HomePage = () => import('@/views/Home/index.vue')
const LobbyPage = () => import('@/views/Lobby/index.vue')
const LobbyDetailPage = () => import('@/views/Lobby/LobbyDetail.vue')
const CreateLobbyPage = () => import('@/views/Lobby/CreateLobby.vue')
const ScriptListPage = () => import('@/views/Script/ScriptList.vue')
const ScriptDetailPage = () => import('@/views/Script/ScriptDetail.vue')
const FeedPage = () => import('@/views/Feed/index.vue')
const CreatePostPage = () => import('@/views/Feed/CreatePost.vue')
const UserProfilePage = () => import('@/views/User/Profile.vue')
const UserSettingsPage = () => import('@/views/User/Settings.vue')
const MyLobbiesPage = () => import('@/views/User/MyLobbies.vue')
const FavoritesPage = () => import('@/views/User/Favorites.vue')
const LoginPage = () => import('@/views/Auth/Login.vue')
const RegisterPage = () => import('@/views/Auth/Register.vue')
const ForgotPasswordPage = () => import('@/views/Auth/ForgotPassword.vue')

// 测试页面
const AuthTestPage = () => import('@/views/Test/AuthTest.vue')
const LoginTestPage = () => import('@/views/Test/LoginTest.vue')
const UserManagementTestPage = () => import('@/views/Test/UserManagement.vue')

// 错误页面
const NotFoundPage = () => import('@/views/Error/NotFound.vue')
const ServerErrorPage = () => import('@/views/Error/ServerError.vue')

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: DefaultLayout,
    children: [
      {
        path: '',
        name: 'Home',
        component: HomePage,
        meta: {
          title: '首页 - 迷雾拼本',
          description: '探索无限可能的剧本世界',
          requiresAuth: false
        }
      },
      {
        path: '/lobby',
        name: 'Lobby',
        component: LobbyPage,
        meta: {
          title: '拼车大厅 - 迷雾拼本',
          description: '实时更新的车队信息，快速找到心仪的伙伴',
          requiresAuth: false
        }
      },
      {
        path: '/lobby/create',
        name: 'CreateLobby',
        component: CreateLobbyPage,
        meta: {
          title: '创建车队 - 迷雾拼本',
          description: '创建你的专属车队',
          requiresAuth: true
        }
      },
      {
        path: '/lobby/:id',
        name: 'LobbyDetail',
        component: LobbyDetailPage,
        meta: {
          title: '车队详情 - 迷雾拼本',
          description: '查看车队详细信息',
          requiresAuth: false
        }
      },
      {
        path: '/scripts',
        name: 'ScriptList',
        component: ScriptListPage,
        meta: {
          title: '剧本库 - 迷雾拼本',
          description: '精选高质量剧本，带你体验不同的故事世界',
          requiresAuth: false
        }
      },
      {
        path: '/scripts/:id',
        name: 'ScriptDetail',
        component: ScriptDetailPage,
        meta: {
          title: '剧本详情 - 迷雾拼本',
          description: '查看剧本详细信息',
          requiresAuth: false
        }
      },
      {
        path: '/feed',
        name: 'Feed',
        component: FeedPage,
        meta: {
          title: '动态广场 - 迷雾拼本',
          description: '分享你的游戏心得，发现更多精彩内容',
          requiresAuth: false
        }
      },
      {
        path: '/feed/create',
        name: 'CreatePost',
        component: CreatePostPage,
        meta: {
          title: '发布动态 - 迷雾拼本',
          description: '分享你的游戏心得',
          requiresAuth: true
        }
      },
      {
        path: '/user/:id',
        name: 'UserProfile',
        component: UserProfilePage,
        meta: {
          title: '用户资料 - 迷雾拼本',
          description: '查看用户详细信息',
          requiresAuth: false
        }
      },
      {
        path: '/profile',
        name: 'MyProfile',
        component: UserProfilePage,
        meta: {
          title: '个人资料 - 迷雾拼本',
          description: '管理你的个人信息',
          requiresAuth: true
        }
      },
      {
        path: '/settings',
        name: 'Settings',
        component: UserSettingsPage,
        meta: {
          title: '设置 - 迷雾拼本',
          description: '个性化设置',
          requiresAuth: true
        }
      },
      {
        path: '/my-lobbies',
        name: 'MyLobbies',
        component: MyLobbiesPage,
        meta: {
          title: '我的车队 - 迷雾拼本',
          description: '管理你的车队',
          requiresAuth: true
        }
      },
      {
        path: '/favorites',
        name: 'Favorites',
        component: FavoritesPage,
        meta: {
          title: '我的收藏 - 迷雾拼本',
          description: '查看收藏的剧本和动态',
          requiresAuth: true
        }
      },
      {
        path: '/test/auth',
        name: 'AuthTest',
        component: AuthTestPage,
        meta: {
          title: '认证接口测试 - 迷雾拼本',
          description: '测试用户认证相关接口',
          requiresAuth: false
        }
      },
      {
        path: '/test/login',
        name: 'LoginTest',
        component: LoginTestPage,
        meta: {
          title: '登录功能测试 - 迷雾拼本',
          description: '测试登录功能和API对接',
          requiresAuth: false
        }
      },
      {
        path: '/test/user-management',
        name: 'UserManagementTest',
        component: UserManagementTestPage,
        meta: {
          title: '用户管理模块测试 - 迷雾拼本',
          description: '测试用户管理相关功能',
          requiresAuth: true
        }
      },
      {
        path: '/test/script-api',
        name: 'ScriptApiTest',
        component: () => import('@/views/Test/ScriptApiTest.vue'),
        meta: {
          title: '剧本API测试 - 迷雾拼本',
          description: '测试剧本API接口',
          requiresAuth: false
        }
      }
    ]
  },
  {
    path: '/auth',
    component: AuthLayout,
    children: [
      {
        path: 'login',
        name: 'Login',
        component: LoginPage,
        meta: {
          title: '登录 - 迷雾拼本',
          description: '登录你的账户',
          requiresAuth: false,
          hideForAuth: true
        }
      },
      {
        path: 'register',
        name: 'Register',
        component: RegisterPage,
        meta: {
          title: '注册 - 迷雾拼本',
          description: '创建新账户',
          requiresAuth: false,
          hideForAuth: true
        }
      },
      {
        path: 'forgot-password',
        name: 'ForgotPassword',
        component: ForgotPasswordPage,
        meta: {
          title: '忘记密码 - 迷雾拼本',
          description: '重置你的密码',
          requiresAuth: false,
          hideForAuth: true
        }
      }
    ]
  },
  // 重定向路由
  {
    path: '/login',
    redirect: '/auth/login'
  },
  {
    path: '/register',
    redirect: '/auth/register'
  },
  // 错误页面
  {
    path: '/500',
    name: 'ServerError',
    component: ServerErrorPage,
    meta: {
      title: '服务器错误 - 迷雾拼本',
      description: '服务器遇到了问题',
      requiresAuth: false
    }
  },
  // 404页面 - 必须放在最后
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFoundPage,
    meta: {
      title: '页面未找到 - 迷雾拼本',
      description: '你访问的页面不存在',
      requiresAuth: false
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 如果有保存的位置（浏览器前进/后退）
    if (savedPosition) {
      return savedPosition
    }
    // 如果有锚点
    if (to.hash) {
      return {
        el: to.hash,
        behavior: 'smooth'
      }
    }
    // 默认滚动到顶部
    return { top: 0, behavior: 'smooth' }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title as string
  }

  // 设置页面描述
  if (to.meta.description) {
    const metaDescription = document.querySelector('meta[name="description"]')
    if (metaDescription) {
      metaDescription.setAttribute('content', to.meta.description as string)
    }
  }

  // 检查认证状态
  const isAuthenticated = await checkAuthStatus()

  // 需要认证的页面
  if (to.meta.requiresAuth && !isAuthenticated) {
    next({
      name: 'Login',
      query: { redirect: to.fullPath }
    })
    return
  }

  // 已登录用户访问认证页面时重定向
  if (to.meta.hideForAuth && isAuthenticated) {
    const redirect = from.query.redirect as string
    next(redirect || '/')
    return
  }

  next()
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
  
  // 根据错误类型进行处理
  if (error.message.includes('Loading chunk')) {
    // 代码分割加载失败，刷新页面
    window.location.reload()
  } else {
    // 其他错误，跳转到错误页面
    router.push('/500')
  }
})

// 检查认证状态的辅助函数
async function checkAuthStatus(): Promise<boolean> {
  const token = localStorage.getItem('auth_token')
  if (!token) {
    return false
  }

  try {
    // 验证token有效性
    const response = await fetch('http://localhost:8081/api/user/me', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })

    if (response.ok) {
      const result = await response.json()
      return result.code === 200
    } else {
      // token无效，清除本地存储
      localStorage.removeItem('auth_token')
      return false
    }
  } catch (error) {
    // 网络错误或其他问题，清除token
    localStorage.removeItem('auth_token')
    return false
  }
}

export default router
