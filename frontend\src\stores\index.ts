import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-plugin-persistedstate'

// 创建 Pinia 实例
const pinia = createPinia()

// 添加持久化插件
pinia.use(
  createPersistedState({
    // 默认使用 localStorage
    storage: localStorage,
    // 可以自定义序列化方法
    serializer: {
      serialize: JSON.stringify,
      deserialize: JSON.parse
    }
  })
)

export default pinia

// 导出所有 store
export { useUserStore } from './modules/user'
export { useAuthStore } from './modules/auth'
export { useScriptStore } from './modules/script'
export { useLobbyStore } from './modules/lobby'
export { useFeedStore } from './modules/feed'
export { useAppStore } from './modules/app'
