import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface AppTheme {
  name: string
  primary: string
  secondary: string
  background: string
  surface: string
  text: string
}

export interface AppSettings {
  theme: 'dark' | 'light' | 'auto'
  language: 'zh-CN' | 'en-US'
  fontSize: 'small' | 'medium' | 'large'
  autoPlay: boolean
  notifications: {
    lobby: boolean
    follow: boolean
    system: boolean
    sound: boolean
  }
}

export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  duration?: number
  persistent?: boolean
  actions?: Array<{
    label: string
    action: () => void
  }>
  createdAt: number
}

export const useAppStore = defineStore('app', () => {
  // 状态
  const isLoading = ref(false)
  const loadingText = ref('')
  const isOnline = ref(navigator.onLine)
  const notifications = ref<Notification[]>([])
  const settings = ref<AppSettings>({
    theme: 'dark',
    language: 'zh-CN',
    fontSize: 'medium',
    autoPlay: true,
    notifications: {
      lobby: true,
      follow: true,
      system: true,
      sound: true
    }
  })

  // 主题配置
  const themes: Record<string, AppTheme> = {
    dark: {
      name: 'dark',
      primary: '#00F5D4',
      secondary: '#FF00E4',
      background: '#1A1A2E',
      surface: '#16213E',
      text: '#FFFFFF'
    },
    light: {
      name: 'light',
      primary: '#00C9A7',
      secondary: '#E91E63',
      background: '#FFFFFF',
      surface: '#F5F5F5',
      text: '#333333'
    }
  }

  // 计算属性
  const currentTheme = computed(() => {
    if (settings.value.theme === 'auto') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? themes.dark : themes.light
    }
    return themes[settings.value.theme]
  })

  const unreadNotifications = computed(() => 
    notifications.value.filter(n => !n.persistent)
  )

  const persistentNotifications = computed(() => 
    notifications.value.filter(n => n.persistent)
  )

  // 全局加载状态
  const setLoading = (loading: boolean, text = '') => {
    isLoading.value = loading
    loadingText.value = text
  }

  // 网络状态管理
  const setOnlineStatus = (online: boolean) => {
    isOnline.value = online
  }

  // 通知管理
  const addNotification = (notification: Omit<Notification, 'id' | 'createdAt'>) => {
    const id = Date.now().toString() + Math.random().toString(36).substr(2, 9)
    const newNotification: Notification = {
      ...notification,
      id,
      createdAt: Date.now(),
      duration: notification.duration ?? 5000
    }

    notifications.value.push(newNotification)

    // 自动移除非持久化通知
    if (!newNotification.persistent && newNotification.duration > 0) {
      setTimeout(() => {
        removeNotification(id)
      }, newNotification.duration)
    }

    return id
  }

  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  const clearAllNotifications = () => {
    notifications.value = []
  }

  // 便捷的通知方法
  const showSuccess = (title: string, message: string, duration = 3000) => {
    return addNotification({
      type: 'success',
      title,
      message,
      duration
    })
  }

  const showError = (title: string, message: string, duration = 5000) => {
    return addNotification({
      type: 'error',
      title,
      message,
      duration
    })
  }

  const showWarning = (title: string, message: string, duration = 4000) => {
    return addNotification({
      type: 'warning',
      title,
      message,
      duration
    })
  }

  const showInfo = (title: string, message: string, duration = 3000) => {
    return addNotification({
      type: 'info',
      title,
      message,
      duration
    })
  }

  // 设置管理
  const updateSettings = (newSettings: Partial<AppSettings>) => {
    settings.value = { ...settings.value, ...newSettings }
    applyTheme()
  }

  const resetSettings = () => {
    settings.value = {
      theme: 'dark',
      language: 'zh-CN',
      fontSize: 'medium',
      autoPlay: true,
      notifications: {
        lobby: true,
        follow: true,
        system: true,
        sound: true
      }
    }
    applyTheme()
  }

  // 应用主题
  const applyTheme = () => {
    const theme = currentTheme.value
    const root = document.documentElement

    // 设置CSS变量
    root.style.setProperty('--color-primary', theme.primary)
    root.style.setProperty('--color-secondary', theme.secondary)
    root.style.setProperty('--color-background', theme.background)
    root.style.setProperty('--color-surface', theme.surface)
    root.style.setProperty('--color-text', theme.text)

    // 设置主题类名
    root.className = root.className.replace(/theme-\w+/g, '')
    root.classList.add(`theme-${theme.name}`)

    // 设置字体大小
    const fontSizeMap = {
      small: '14px',
      medium: '16px',
      large: '18px'
    }
    root.style.setProperty('--font-size-base', fontSizeMap[settings.value.fontSize])
  }

  // 初始化应用
  const initializeApp = () => {
    // 应用主题
    applyTheme()

    // 监听网络状态
    window.addEventListener('online', () => setOnlineStatus(true))
    window.addEventListener('offline', () => setOnlineStatus(false))

    // 监听系统主题变化
    if (settings.value.theme === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      mediaQuery.addEventListener('change', applyTheme)
    }

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible') {
        // 页面变为可见时的处理
        console.log('页面变为可见')
      }
    })
  }

  // 错误处理
  const handleError = (error: Error, context?: string) => {
    console.error('应用错误:', error, context)
    
    showError(
      '系统错误',
      error.message || '发生了未知错误，请稍后重试'
    )

    // 可以在这里添加错误上报逻辑
    // errorReporting.report(error, context)
  }

  // 性能监控
  const trackPerformance = (name: string, startTime: number) => {
    const endTime = performance.now()
    const duration = endTime - startTime
    
    console.log(`性能监控 - ${name}: ${duration.toFixed(2)}ms`)
    
    // 可以在这里添加性能数据上报
    // analytics.track('performance', { name, duration })
  }

  return {
    // 状态
    isLoading,
    loadingText,
    isOnline,
    notifications,
    settings,
    
    // 计算属性
    currentTheme,
    unreadNotifications,
    persistentNotifications,
    
    // 方法
    setLoading,
    setOnlineStatus,
    addNotification,
    removeNotification,
    clearAllNotifications,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    updateSettings,
    resetSettings,
    applyTheme,
    initializeApp,
    handleError,
    trackPerformance
  }
}, {
  persist: {
    // 持久化设置
    paths: ['settings']
  }
})
