import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User } from '@/types/user'

export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterData {
  email: string
  password: string
  nickname: string
  confirmPassword: string
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const token = ref<string | null>(localStorage.getItem('auth_token'))

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)

  // 登录
  const login = async (credentials: LoginCredentials) => {
    try {
      isLoading.value = true
      error.value = null

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 模拟登录成功
      const mockToken = 'mock_token_' + Date.now()
      token.value = mockToken
      localStorage.setItem('auth_token', mockToken)

      return { success: true }
    } catch (err: any) {
      error.value = err.message || '登录失败'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  // 注册
  const register = async (userData: RegisterData) => {
    try {
      isLoading.value = true
      error.value = null

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 模拟注册成功
      const mockToken = 'mock_token_' + Date.now()
      token.value = mockToken
      localStorage.setItem('auth_token', mockToken)

      return { success: true }
    } catch (err: any) {
      error.value = err.message || '注册失败'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  // 退出登录
  const logout = () => {
    token.value = null
    localStorage.removeItem('auth_token')
    error.value = null
  }

  // 清除错误
  const clearError = () => {
    error.value = null
  }

  return {
    // 状态
    isLoading,
    error,
    token,
    
    // 计算属性
    isAuthenticated,
    
    // 方法
    login,
    register,
    logout,
    clearError
  }
}, {
  persist: {
    paths: ['token']
  }
})
