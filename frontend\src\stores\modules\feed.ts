import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface Post {
  id: number
  title?: string
  content: string
  type: 'review' | 'share' | 'guide' | 'discussion'
  author: {
    id: number
    nickname: string
    avatar: string
    level: number
  }
  script?: {
    id: number
    title: string
    coverImage: string
    genre: string
  }
  media?: Array<{
    type: 'image' | 'video'
    url: string
    thumbnail?: string
  }>
  location?: string
  likeCount: number
  commentCount: number
  shareCount: number
  isLiked: boolean
  isBookmarked: boolean
  isHot: boolean
  isPinned: boolean
  createdAt: string
}

export const useFeedStore = defineStore('feed', () => {
  // 状态
  const posts = ref<Post[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // 获取动态列表
  const fetchPosts = async (params?: any) => {
    try {
      isLoading.value = true
      error.value = null

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))

      // 模拟数据
      posts.value = [
        {
          id: 1,
          title: "《迷雾庄园》通关心得分享",
          content: "昨晚和朋友们一起玩了这个剧本，真的太精彩了！",
          type: 'review',
          author: {
            id: 2,
            nickname: "推理达人",
            avatar: "https://picsum.photos/48/48?random=21",
            level: 8
          },
          script: {
            id: 1,
            title: "迷雾庄园",
            coverImage: "https://picsum.photos/40/40?random=1",
            genre: "推理"
          },
          likeCount: 156,
          commentCount: 23,
          shareCount: 12,
          isLiked: false,
          isBookmarked: false,
          isHot: true,
          isPinned: false,
          createdAt: "2024-07-29T14:30:00"
        }
      ]
    } catch (err: any) {
      error.value = err.message || '获取动态列表失败'
    } finally {
      isLoading.value = false
    }
  }

  // 创建动态
  const createPost = async (postData: any) => {
    try {
      isLoading.value = true
      error.value = null

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      const newPost: Post = {
        id: Date.now(),
        ...postData,
        likeCount: 0,
        commentCount: 0,
        shareCount: 0,
        isLiked: false,
        isBookmarked: false,
        isHot: false,
        isPinned: false,
        createdAt: new Date().toISOString()
      }

      posts.value.unshift(newPost)
      return { success: true, data: newPost }
    } catch (err: any) {
      error.value = err.message || '发布动态失败'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  return {
    // 状态
    posts,
    isLoading,
    error,
    
    // 方法
    fetchPosts,
    createPost
  }
})
