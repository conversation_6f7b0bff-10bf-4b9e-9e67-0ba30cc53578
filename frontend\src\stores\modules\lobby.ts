import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface Lobby {
  id: number
  script: {
    id: number
    title: string
    coverImage: string
    genre: string
  }
  host: {
    id: number
    nickname: string
    avatar: string
    level: number
  }
  currentPlayers: number
  maxPlayers: number
  status: 'waiting' | 'full' | 'in_progress' | 'completed' | 'cancelled'
  startTime: string
  endTime?: string
  location: string
  price: number
  description: string
  requirements?: string
  createdAt: string
}

export const useLobbyStore = defineStore('lobby', () => {
  // 状态
  const lobbies = ref<Lobby[]>([])
  const currentLobby = ref<Lobby | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // 获取车队列表
  const fetchLobbies = async (params?: any) => {
    try {
      isLoading.value = true
      error.value = null

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))

      // 模拟数据
      lobbies.value = [
        {
          id: 1,
          script: {
            id: 1,
            title: "迷雾庄园",
            coverImage: "https://picsum.photos/300/400?random=1",
            genre: "推理"
          },
          host: {
            id: 1,
            nickname: "推理大师",
            avatar: "https://picsum.photos/40/40?random=1",
            level: 10
          },
          currentPlayers: 4,
          maxPlayers: 6,
          status: 'waiting',
          startTime: '2024-07-30T19:00:00',
          location: '线上',
          price: 68,
          description: '欢迎新手，氛围轻松',
          createdAt: '2024-07-29T14:30:00'
        }
      ]
    } catch (err: any) {
      error.value = err.message || '获取车队列表失败'
    } finally {
      isLoading.value = false
    }
  }

  // 创建车队
  const createLobby = async (lobbyData: any) => {
    try {
      isLoading.value = true
      error.value = null

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      const newLobby: Lobby = {
        id: Date.now(),
        ...lobbyData,
        currentPlayers: 1,
        status: 'waiting' as const,
        createdAt: new Date().toISOString()
      }

      lobbies.value.unshift(newLobby)
      return { success: true, data: newLobby }
    } catch (err: any) {
      error.value = err.message || '创建车队失败'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  return {
    // 状态
    lobbies,
    currentLobby,
    isLoading,
    error,
    
    // 方法
    fetchLobbies,
    createLobby
  }
})
