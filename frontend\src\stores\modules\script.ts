import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { scriptApi } from '@/api/script'
import type { 
  <PERSON>ript, 
  ScriptDetail, 
  ScriptSearchParams, 
  CategoryStats 
} from '@/types/script'
import type { PageResponse } from '@/types/api'

export const useScriptStore = defineStore('script', () => {
  // 状态
  const scripts = ref<Script[]>([])
  const currentScript = ref<ScriptDetail | null>(null)
  const searchResults = ref<Script[]>([])
  const popularScripts = ref<Script[]>([])
  const recommendedScripts = ref<Script[]>([])
  const categoryStats = ref<CategoryStats[]>([])
  const hotSearchKeywords = ref<string[]>([])
  const searchSuggestions = ref<string[]>([])
  
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const pagination = ref({
    current: 1,
    size: 20,
    total: 0,
    pages: 0
  })
  
  // 计算属性
  const hasMoreScripts = computed(() => {
    return pagination.value.current < pagination.value.pages
  })
  
  const isLastPage = computed(() => {
    return pagination.value.current >= pagination.value.pages
  })
  
  // 获取剧本列表
  const fetchScripts = async (params: ScriptSearchParams = {}) => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await scriptApi.getScriptList({
        page: 1,
        size: 20,
        ...params
      })
      
      if (response.success) {
        scripts.value = response.data.records
        pagination.value = {
          current: response.data.current,
          size: response.data.size,
          total: response.data.total,
          pages: response.data.pages
        }
        return response.data
      } else {
        throw new Error(response.message || '获取剧本列表失败')
      }
    } catch (err: any) {
      error.value = err.message || '获取剧本列表失败'
      console.error('获取剧本列表失败:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }
  
  // 搜索剧本
  const searchScripts = async (params: ScriptSearchParams) => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await scriptApi.searchScripts(params)
      
      if (response.success) {
        if (params.page === 1) {
          searchResults.value = response.data.records
        } else {
          searchResults.value.push(...response.data.records)
        }
        
        pagination.value = {
          current: response.data.current,
          size: response.data.size,
          total: response.data.total,
          pages: response.data.pages
        }
        
        return response.data
      } else {
        throw new Error(response.message || '搜索失败')
      }
    } catch (err: any) {
      error.value = err.message || '搜索失败'
      console.error('搜索剧本失败:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }
  
  // 获取剧本详情
  const fetchScriptDetail = async (id: number) => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await scriptApi.getScriptDetail(id)
      
      if (response.success) {
        currentScript.value = response.data
        return response.data
      } else {
        throw new Error(response.message || '获取剧本详情失败')
      }
    } catch (err: any) {
      error.value = err.message || '获取剧本详情失败'
      console.error('获取剧本详情失败:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }
  
  // 获取热门剧本
  const fetchPopularScripts = async (limit = 10) => {
    try {
      const response = await scriptApi.getPopularScripts(limit)
      if (response.success) {
        popularScripts.value = response.data
        return response.data
      } else {
        console.error('获取热门剧本失败:', response.message)
      }
    } catch (err: any) {
      console.error('获取热门剧本失败:', err)
    }
  }
  
  // 获取推荐剧本
  const fetchRecommendedScripts = async (limit = 10) => {
    try {
      const response = await scriptApi.getRecommendedScripts(limit)
      if (response.success) {
        recommendedScripts.value = response.data
        return response.data
      } else {
        console.error('获取推荐剧本失败:', response.message)
      }
    } catch (err: any) {
      console.error('获取推荐剧本失败:', err)
    }
  }
  
  // 获取分类统计
  const fetchCategoryStats = async () => {
    try {
      const response = await scriptApi.getCategoryStats()
      if (response.success) {
        categoryStats.value = response.data
        return response.data
      } else {
        console.error('获取分类统计失败:', response.message)
      }
    } catch (err: any) {
      console.error('获取分类统计失败:', err)
    }
  }
  
  // 获取搜索建议
  const fetchSearchSuggestions = async (keyword: string) => {
    if (!keyword.trim()) {
      searchSuggestions.value = []
      return []
    }
    
    try {
      const response = await scriptApi.getSearchSuggestions(keyword)
      if (response.success) {
        searchSuggestions.value = response.data
        return response.data
      } else {
        console.error('获取搜索建议失败:', response.message)
        return []
      }
    } catch (err: any) {
      console.error('获取搜索建议失败:', err)
      return []
    }
  }
  
  // 获取热门搜索
  const fetchHotSearchKeywords = async () => {
    try {
      const response = await scriptApi.getHotSearchKeywords()
      if (response.success) {
        hotSearchKeywords.value = response.data
        return response.data
      } else {
        console.error('获取热门搜索失败:', response.message)
      }
    } catch (err: any) {
      console.error('获取热门搜索失败:', err)
    }
  }
  
  // 加载更多搜索结果
  const loadMoreSearchResults = async (params: ScriptSearchParams) => {
    if (isLastPage.value || isLoading.value) {
      return
    }
    
    const nextPage = pagination.value.current + 1
    await searchScripts({ ...params, page: nextPage })
  }
  
  // 重置搜索状态
  const resetSearch = () => {
    searchResults.value = []
    searchSuggestions.value = []
    error.value = null
    pagination.value = {
      current: 1,
      size: 20,
      total: 0,
      pages: 0
    }
  }
  
  // 清空当前剧本详情
  const clearCurrentScript = () => {
    currentScript.value = null
  }
  
  // 更新剧本收藏状态
  const updateScriptFavoriteStatus = (scriptId: number, isFavorite: boolean) => {
    // 更新列表中的剧本
    const scriptInList = scripts.value.find(s => s.id === scriptId)
    if (scriptInList) {
      scriptInList.isFavorite = isFavorite
    }
    
    // 更新搜索结果中的剧本
    const scriptInSearch = searchResults.value.find(s => s.id === scriptId)
    if (scriptInSearch) {
      scriptInSearch.isFavorite = isFavorite
    }
    
    // 更新热门剧本中的剧本
    const scriptInPopular = popularScripts.value.find(s => s.id === scriptId)
    if (scriptInPopular) {
      scriptInPopular.isFavorite = isFavorite
    }
    
    // 更新推荐剧本中的剧本
    const scriptInRecommended = recommendedScripts.value.find(s => s.id === scriptId)
    if (scriptInRecommended) {
      scriptInRecommended.isFavorite = isFavorite
    }
    
    // 更新当前剧本详情
    if (currentScript.value && currentScript.value.id === scriptId) {
      currentScript.value.isFavorite = isFavorite
    }
  }
  
  return {
    // 状态
    scripts,
    currentScript,
    searchResults,
    popularScripts,
    recommendedScripts,
    categoryStats,
    hotSearchKeywords,
    searchSuggestions,
    isLoading,
    error,
    pagination,
    
    // 计算属性
    hasMoreScripts,
    isLastPage,
    
    // 方法
    fetchScripts,
    searchScripts,
    fetchScriptDetail,
    fetchPopularScripts,
    fetchRecommendedScripts,
    fetchCategoryStats,
    fetchSearchSuggestions,
    fetchHotSearchKeywords,
    loadMoreSearchResults,
    resetSearch,
    clearCurrentScript,
    updateScriptFavoriteStatus
  }
})
