import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, UserProfile, UserStats } from '@/types/user'
import { userApi } from '@/api/user'

export const useUserStore = defineStore('user', () => {
  // 状态
  const currentUser = ref<User | null>(null)
  const userProfile = ref<UserProfile | null>(null)
  const userStats = ref<UserStats | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const isLoggedIn = computed(() => !!currentUser.value)
  const userId = computed(() => currentUser.value?.id)
  const userLevel = computed(() => currentUser.value?.level || 1)
  const userExperience = computed(() => currentUser.value?.experience || 0)
  const userAvatar = computed(() => currentUser.value?.avatar || '')
  const userNickname = computed(() => currentUser.value?.nickname || '未登录')

  // 获取用户信息
  const fetchUserInfo = async () => {
    if (!currentUser.value?.id) return

    try {
      isLoading.value = true
      error.value = null
      
      const response = await userApi.getUserInfo(currentUser.value.id)
      if (response.success) {
        currentUser.value = { ...currentUser.value, ...response.data }
      } else {
        error.value = response.errorMsg || '获取用户信息失败'
      }
    } catch (err) {
      error.value = '网络错误，请稍后重试'
      console.error('获取用户信息失败:', err)
    } finally {
      isLoading.value = false
    }
  }

  // 获取用户详细资料
  const fetchUserProfile = async (userId: number) => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await userApi.getUserProfile(userId)
      if (response.success) {
        userProfile.value = response.data
      } else {
        error.value = response.errorMsg || '获取用户资料失败'
      }
    } catch (err) {
      error.value = '网络错误，请稍后重试'
      console.error('获取用户资料失败:', err)
    } finally {
      isLoading.value = false
    }
  }

  // 获取用户统计数据
  const fetchUserStats = async (userId: number) => {
    try {
      const response = await userApi.getUserStats(userId)
      if (response.success) {
        userStats.value = response.data
      }
    } catch (err) {
      console.error('获取用户统计失败:', err)
    }
  }

  // 更新用户资料
  const updateProfile = async (profileData: Partial<UserProfile>) => {
    if (!currentUser.value?.id) return false

    try {
      isLoading.value = true
      error.value = null
      
      const response = await userApi.updateProfile(currentUser.value.id, profileData)
      if (response.success) {
        // 更新本地用户信息
        if (currentUser.value) {
          currentUser.value = { ...currentUser.value, ...response.data }
        }
        if (userProfile.value) {
          userProfile.value = { ...userProfile.value, ...profileData }
        }
        return true
      } else {
        error.value = response.errorMsg || '更新资料失败'
        return false
      }
    } catch (err) {
      error.value = '网络错误，请稍后重试'
      console.error('更新用户资料失败:', err)
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 上传头像
  const uploadAvatar = async (file: File) => {
    if (!currentUser.value?.id) return null

    try {
      isLoading.value = true
      error.value = null
      
      const response = await userApi.uploadAvatar(file)
      if (response.success) {
        // 更新本地头像
        if (currentUser.value) {
          currentUser.value.avatar = response.data.url
        }
        return response.data.url
      } else {
        error.value = response.errorMsg || '上传头像失败'
        return null
      }
    } catch (err) {
      error.value = '网络错误，请稍后重试'
      console.error('上传头像失败:', err)
      return null
    } finally {
      isLoading.value = false
    }
  }

  // 关注/取消关注用户
  const toggleFollow = async (targetUserId: number) => {
    if (!currentUser.value?.id) return false

    try {
      const response = await userApi.toggleFollow(targetUserId)
      if (response.success) {
        return response.data.isFollowing
      } else {
        error.value = response.errorMsg || '操作失败'
        return false
      }
    } catch (err) {
      error.value = '网络错误，请稍后重试'
      console.error('关注操作失败:', err)
      return false
    }
  }

  // 获取关注列表
  const fetchFollowList = async (userId: number, type: 'following' | 'followers') => {
    try {
      const response = await userApi.getFollowList(userId, type)
      if (response.success) {
        return response.data
      } else {
        error.value = response.errorMsg || '获取关注列表失败'
        return []
      }
    } catch (err) {
      error.value = '网络错误，请稍后重试'
      console.error('获取关注列表失败:', err)
      return []
    }
  }

  // 设置当前用户（登录时调用）
  const setCurrentUser = (user: User) => {
    currentUser.value = user
  }

  // 清除用户数据（退出登录时调用）
  const clearUserData = () => {
    currentUser.value = null
    userProfile.value = null
    userStats.value = null
    error.value = null
  }

  // 重置错误状态
  const clearError = () => {
    error.value = null
  }

  return {
    // 状态
    currentUser,
    userProfile,
    userStats,
    isLoading,
    error,
    
    // 计算属性
    isLoggedIn,
    userId,
    userLevel,
    userExperience,
    userAvatar,
    userNickname,
    
    // 方法
    fetchUserInfo,
    fetchUserProfile,
    fetchUserStats,
    updateProfile,
    uploadAvatar,
    toggleFollow,
    fetchFollowList,
    setCurrentUser,
    clearUserData,
    clearError
  }
}, {
  persist: {
    // 只持久化必要的用户信息
    paths: ['currentUser']
  }
})
