// 导入变量和混合
@use './variables.scss' as *;
@use './mixins.scss' as *;

// 重置样式
@use './reset.scss';

// 全局样式
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
  background: $color-background;
  color: $color-text;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 容器样式
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  
  @media (max-width: 768px) {
    padding: 0 15px;
  }
}

// 按钮基础样式
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  &.btn-primary {
    background: linear-gradient(135deg, $color-primary, $color-primary-dark);
    color: $color-background;
    
    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba($color-primary, 0.4);
    }
  }
  
  &.btn-secondary {
    background: rgba($color-primary, 0.1);
    color: $color-primary;
    border: 1px solid rgba($color-primary, 0.3);
    
    &:hover:not(:disabled) {
      background: rgba($color-primary, 0.2);
      border-color: $color-primary;
    }
  }
  
  &.btn-ghost {
    background: transparent;
    color: $color-text-secondary;
    border: 1px solid rgba($color-text, 0.2);
    
    &:hover:not(:disabled) {
      background: rgba($color-text, 0.05);
      color: $color-text;
    }
  }
}

// 输入框基础样式
.input {
  width: 100%;
  padding: 12px 16px;
  background: rgba($color-text, 0.05);
  border: 1px solid rgba($color-primary, 0.2);
  border-radius: 8px;
  color: $color-text;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  
  &::placeholder {
    color: $color-text-muted;
  }
  
  &:focus {
    outline: none;
    border-color: $color-primary;
    box-shadow: 0 0 0 2px rgba($color-primary, 0.1);
  }
}

// 卡片样式
.card {
  background: rgba($color-text, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba($color-primary, 0.1);
  border-radius: 16px;
  padding: 20px;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: rgba($color-primary, 0.3);
    transform: translateY(-2px);
  }
}

// 工具类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-5 { margin-bottom: 1.25rem; }
.mb-6 { margin-bottom: 1.5rem; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-5 { margin-top: 1.25rem; }
.mt-6 { margin-top: 1.5rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-5 { padding: 1.25rem; }
.p-6 { padding: 1.5rem; }

// 响应式工具类
@media (max-width: 768px) {
  .hidden-mobile { display: none !important; }
  .text-center-mobile { text-align: center; }
}

@media (min-width: 769px) {
  .hidden-desktop { display: none !important; }
}

// 动画类
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba($color-text, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba($color-primary, 0.3);
  border-radius: 4px;
  
  &:hover {
    background: rgba($color-primary, 0.5);
  }
}

// 选择文本样式
::selection {
  background: rgba($color-primary, 0.3);
  color: $color-text;
}

::-moz-selection {
  background: rgba($color-primary, 0.3);
  color: $color-text;
}
