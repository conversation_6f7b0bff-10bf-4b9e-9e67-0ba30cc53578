// 响应式断点混合
@mixin mobile {
  @media (max-width: #{$breakpoint-md - 1px}) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: #{$breakpoint-md}) and (max-width: #{$breakpoint-lg - 1px}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: #{$breakpoint-lg}) {
    @content;
  }
}

@mixin large-desktop {
  @media (min-width: #{$breakpoint-xl}) {
    @content;
  }
}

// 文本省略
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin text-ellipsis-multiline($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 居中对齐
@mixin center-absolute {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@mixin center-flex {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 毛玻璃效果
@mixin glass-effect($opacity: 0.05) {
  background: rgba($color-text, $opacity);
  backdrop-filter: $backdrop-blur;
  border: 1px solid rgba($color-primary, 0.1);
}

// 渐变边框
@mixin gradient-border($width: 1px, $radius: 8px) {
  position: relative;
  border-radius: $radius;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: $radius;
    padding: $width;
    background: $gradient-primary;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
  }
}

// 悬停效果
@mixin hover-lift {
  transition: transform $transition-normal;
  
  &:hover {
    transform: translateY(-2px);
  }
}

@mixin hover-glow($color: $color-primary) {
  transition: box-shadow $transition-normal;
  
  &:hover {
    box-shadow: 0 4px 20px rgba($color, 0.3);
  }
}

// 按钮样式混合
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: $border-radius-md;
  font-size: $font-size-sm;
  font-weight: $font-weight-semibold;
  text-decoration: none;
  cursor: pointer;
  transition: all $transition-normal;
  
  &:disabled {
    opacity: $opacity-disabled;
    cursor: not-allowed;
  }
}

@mixin button-primary {
  @include button-base;
  background: $gradient-primary;
  color: $color-background;
  
  &:hover:not(:disabled) {
    @include hover-lift;
    @include hover-glow($color-primary);
  }
}

@mixin button-secondary {
  @include button-base;
  background: rgba($color-primary, 0.1);
  color: $color-primary;
  border: 1px solid rgba($color-primary, 0.3);
  
  &:hover:not(:disabled) {
    background: rgba($color-primary, 0.2);
    border-color: $color-primary;
  }
}

// 输入框样式混合
@mixin input-base {
  width: 100%;
  padding: 12px 16px;
  background: rgba($color-text, 0.05);
  border: 1px solid rgba($color-primary, 0.2);
  border-radius: $border-radius-md;
  color: $color-text;
  font-size: $font-size-sm;
  transition: all $transition-normal;
  
  &::placeholder {
    color: $color-text-muted;
  }
  
  &:focus {
    outline: none;
    border-color: $color-primary;
    box-shadow: 0 0 0 2px rgba($color-primary, 0.1);
  }
}

// 卡片样式混合
@mixin card-base {
  @include glass-effect;
  border-radius: $border-radius-xl;
  padding: $spacing-3xl;
  transition: all $transition-normal;
  
  &:hover {
    border-color: rgba($color-primary, 0.3);
    @include hover-lift;
  }
}

// 加载动画
@mixin loading-spinner($size: 40px, $color: $color-primary) {
  width: $size;
  height: $size;
  border: 3px solid rgba($color, 0.1);
  border-top: 3px solid $color;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 脉冲动画
@mixin pulse-animation($color: $color-primary) {
  animation: pulse 2s ease-in-out infinite;
  
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.05);
    }
  }
}

// 淡入动画
@mixin fade-in-animation($duration: 0.3s) {
  animation: fadeIn $duration ease-in-out;
  
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
}

// 滑入动画
@mixin slide-in-animation($direction: up, $distance: 20px, $duration: 0.3s) {
  animation: slideIn#{capitalize($direction)} $duration ease-out;
  
  @if $direction == up {
    @keyframes slideInUp {
      from {
        opacity: 0;
        transform: translateY($distance);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  } @else if $direction == down {
    @keyframes slideInDown {
      from {
        opacity: 0;
        transform: translateY(-$distance);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  } @else if $direction == left {
    @keyframes slideInLeft {
      from {
        opacity: 0;
        transform: translateX(-$distance);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }
  } @else if $direction == right {
    @keyframes slideInRight {
      from {
        opacity: 0;
        transform: translateX($distance);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }
  }
}

// 工具函数
@function capitalize($string) {
  @return to-upper-case(str-slice($string, 1, 1)) + str-slice($string, 2);
}
