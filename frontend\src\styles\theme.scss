// 主题样式文件
// 定义深色和浅色主题的CSS变量

:root {
  // 默认深色主题
  --theme-primary: #00F5D4;
  --theme-primary-light: #33F7DD;
  --theme-primary-dark: #00D4AA;
  
  --theme-background: #1a1a1a;
  --theme-surface: #2d2d2d;
  --theme-surface-light: #3a3a3a;
  
  --theme-text: #ffffff;
  --theme-text-secondary: #b3b3b3;
  --theme-text-muted: #808080;
  
  --theme-border: #333333;
  --theme-border-light: #404040;
  
  --theme-shadow: rgba(0, 0, 0, 0.3);
  --theme-shadow-light: rgba(0, 0, 0, 0.1);
  
  // 状态颜色
  --theme-success: #67C23A;
  --theme-warning: #E6A23C;
  --theme-danger: #F56C6C;
  --theme-info: #409EFF;
  
  // 渐变
  --theme-gradient-primary: linear-gradient(135deg, #00F5D4, #00D4AA);
  --theme-gradient-surface: linear-gradient(135deg, #2d2d2d, #1a1a1a);
  --theme-gradient-background: linear-gradient(180deg, #1A1A2E 0%, #16213E 50%, #0F0F1E 100%);
}

// 浅色主题
:root.theme-light {
  --theme-primary: #00F5D4;
  --theme-primary-light: #33F7DD;
  --theme-primary-dark: #00D4AA;
  
  --theme-background: #ffffff;
  --theme-surface: #f5f5f5;
  --theme-surface-light: #fafafa;
  
  --theme-text: #303133;
  --theme-text-secondary: #606266;
  --theme-text-muted: #909399;
  
  --theme-border: #dcdfe6;
  --theme-border-light: #e4e7ed;
  
  --theme-shadow: rgba(0, 0, 0, 0.1);
  --theme-shadow-light: rgba(0, 0, 0, 0.05);
  
  // 渐变
  --theme-gradient-surface: linear-gradient(135deg, #f5f5f5, #ffffff);
  --theme-gradient-background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
}

// 深色主题
:root.theme-dark {
  --theme-primary: #00F5D4;
  --theme-primary-light: #33F7DD;
  --theme-primary-dark: #00D4AA;
  
  --theme-background: #1a1a1a;
  --theme-surface: #2d2d2d;
  --theme-surface-light: #3a3a3a;
  
  --theme-text: #ffffff;
  --theme-text-secondary: #b3b3b3;
  --theme-text-muted: #808080;
  
  --theme-border: #333333;
  --theme-border-light: #404040;
  
  --theme-shadow: rgba(0, 0, 0, 0.3);
  --theme-shadow-light: rgba(0, 0, 0, 0.1);
  
  // 渐变
  --theme-gradient-surface: linear-gradient(135deg, #2d2d2d, #1a1a1a);
  --theme-gradient-background: linear-gradient(180deg, #1A1A2E 0%, #16213E 50%, #0F0F1E 100%);
}

// 全局样式应用
body {
  background-color: var(--theme-background);
  color: var(--theme-text);
  transition: background-color 0.3s ease, color 0.3s ease;
}

// Element Plus 主题变量覆盖
:root {
  --el-color-primary: var(--theme-primary);
  --el-color-primary-light-3: var(--theme-primary-light);
  --el-color-primary-dark-2: var(--theme-primary-dark);
  
  --el-bg-color: var(--theme-background);
  --el-bg-color-page: var(--theme-surface);
  --el-bg-color-overlay: var(--theme-surface);
  
  --el-text-color-primary: var(--theme-text);
  --el-text-color-regular: var(--theme-text-secondary);
  --el-text-color-secondary: var(--theme-text-muted);
  
  --el-border-color: var(--theme-border);
  --el-border-color-light: var(--theme-border-light);
  --el-border-color-lighter: var(--theme-border-light);
  
  --el-box-shadow: 0 2px 12px 0 var(--theme-shadow);
  --el-box-shadow-light: 0 2px 8px 0 var(--theme-shadow-light);
  
  // 成功、警告、危险、信息颜色
  --el-color-success: var(--theme-success);
  --el-color-warning: var(--theme-warning);
  --el-color-danger: var(--theme-danger);
  --el-color-info: var(--theme-info);
}

// 主题切换动画
* {
  transition: background-color 0.3s ease, 
              border-color 0.3s ease, 
              color 0.3s ease,
              box-shadow 0.3s ease;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--theme-surface);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--theme-border);
  border-radius: 4px;
  
  &:hover {
    background: var(--theme-primary);
  }
}

// 选择文本样式
::selection {
  background: var(--theme-primary);
  color: var(--theme-background);
}

::-moz-selection {
  background: var(--theme-primary);
  color: var(--theme-background);
}

// 焦点样式
:focus-visible {
  outline: 2px solid var(--theme-primary);
  outline-offset: 2px;
}

// 卡片样式
.theme-card {
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: 12px;
  box-shadow: 0 4px 16px var(--theme-shadow);
  transition: all 0.3s ease;
  
  &:hover {
    border-color: var(--theme-primary);
    box-shadow: 0 8px 32px rgba(0, 245, 212, 0.2);
    transform: translateY(-2px);
  }
}

// 按钮样式
.theme-button {
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  color: var(--theme-text);
  border-radius: 8px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: var(--theme-primary);
    color: var(--theme-primary);
    transform: translateY(-1px);
  }
  
  &.primary {
    background: var(--theme-gradient-primary);
    border-color: var(--theme-primary);
    color: var(--theme-background);
    
    &:hover {
      box-shadow: 0 4px 16px rgba(0, 245, 212, 0.3);
    }
  }
}

// 输入框样式
.theme-input {
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  color: var(--theme-text);
  border-radius: 8px;
  padding: 8px 12px;
  transition: all 0.3s ease;
  
  &:focus {
    border-color: var(--theme-primary);
    box-shadow: 0 0 0 2px rgba(0, 245, 212, 0.2);
  }
  
  &::placeholder {
    color: var(--theme-text-muted);
  }
}

// 标签样式
.theme-tag {
  background: rgba(0, 245, 212, 0.1);
  border: 1px solid rgba(0, 245, 212, 0.3);
  color: var(--theme-primary);
  border-radius: 6px;
  padding: 2px 8px;
  font-size: 0.8rem;
  font-weight: 500;
}

// 徽章样式
.theme-badge {
  background: var(--theme-primary);
  color: var(--theme-background);
  border-radius: 10px;
  padding: 2px 8px;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

// 分割线样式
.theme-divider {
  border: none;
  height: 1px;
  background: var(--theme-border);
  margin: 20px 0;
}

// 响应式断点
@media (max-width: 768px) {
  :root {
    --theme-shadow: rgba(0, 0, 0, 0.2);
    --theme-shadow-light: rgba(0, 0, 0, 0.05);
  }
}

// 打印样式
@media print {
  :root {
    --theme-background: #ffffff;
    --theme-surface: #ffffff;
    --theme-text: #000000;
    --theme-text-secondary: #333333;
    --theme-border: #cccccc;
    --theme-shadow: none;
  }
  
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  :root {
    --theme-border: #ffffff;
    --theme-text-secondary: #ffffff;
  }
  
  :root.theme-light {
    --theme-border: #000000;
    --theme-text-secondary: #000000;
  }
}

// 减少动画模式
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
}
