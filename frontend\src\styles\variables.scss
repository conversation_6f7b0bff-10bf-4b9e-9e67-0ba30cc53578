// 颜色变量
$color-primary: #00F5D4;
$color-primary-dark: #00C9A7;
$color-secondary: #FF00E4;
$color-secondary-dark: #E91E63;

$color-background: #1A1A2E;
$color-surface: #16213E;
$color-surface-light: #0F0F1E;

$color-text: #FFFFFF;
$color-text-secondary: #B0B0B0;
$color-text-muted: #888888;
$color-text-disabled: #666666;

$color-success: #4CAF50;
$color-warning: #FFC107;
$color-error: #F44336;
$color-info: #2196F3;

// 渐变色
$gradient-primary: linear-gradient(135deg, $color-primary, $color-primary-dark);
$gradient-secondary: linear-gradient(135deg, $color-secondary, $color-secondary-dark);
$gradient-background: linear-gradient(180deg, $color-background 0%, $color-surface 50%, $color-surface-light 100%);

// 字体
$font-family-base: 'Inter', 'Helvetica Neue', Arial, sans-serif;
$font-family-mono: 'Fira Code', 'Monaco', 'Consolas', monospace;

// 字体大小
$font-size-xs: 0.75rem;    // 12px
$font-size-sm: 0.875rem;   // 14px
$font-size-base: 1rem;     // 16px
$font-size-lg: 1.125rem;   // 18px
$font-size-xl: 1.25rem;    // 20px
$font-size-2xl: 1.5rem;    // 24px
$font-size-3xl: 1.875rem;  // 30px
$font-size-4xl: 2.25rem;   // 36px
$font-size-5xl: 3rem;      // 48px

// 字重
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 行高
$line-height-tight: 1.25;
$line-height-normal: 1.5;
$line-height-relaxed: 1.75;

// 间距
$spacing-xs: 0.25rem;   // 4px
$spacing-sm: 0.5rem;    // 8px
$spacing-md: 0.75rem;   // 12px
$spacing-lg: 1rem;      // 16px
$spacing-xl: 1.25rem;   // 20px
$spacing-2xl: 1.5rem;   // 24px
$spacing-3xl: 2rem;     // 32px
$spacing-4xl: 2.5rem;   // 40px
$spacing-5xl: 3rem;     // 48px
$spacing-6xl: 4rem;     // 64px

// 圆角
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;
$border-radius-xl: 16px;
$border-radius-2xl: 20px;
$border-radius-full: 50%;

// 阴影
$shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
$shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);
$shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);

// 特殊阴影
$shadow-glow-primary: 0 0 20px rgba($color-primary, 0.3);
$shadow-glow-secondary: 0 0 20px rgba($color-secondary, 0.3);

// 过渡动画
$transition-fast: 0.15s ease;
$transition-normal: 0.3s ease;
$transition-slow: 0.5s ease;

// 缓动函数
$ease-in-out-cubic: cubic-bezier(0.4, 0, 0.2, 1);
$ease-out-cubic: cubic-bezier(0.33, 1, 0.68, 1);
$ease-in-cubic: cubic-bezier(0.32, 0, 0.67, 0);

// 断点
$breakpoint-xs: 480px;
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;
$breakpoint-2xl: 1536px;

// 容器最大宽度
$container-sm: 640px;
$container-md: 768px;
$container-lg: 1024px;
$container-xl: 1280px;
$container-2xl: 1536px;

// Z-index 层级
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;
$z-index-toast: 1080;

// 透明度
$opacity-disabled: 0.5;
$opacity-hover: 0.8;
$opacity-active: 0.9;

// 边框
$border-width: 1px;
$border-width-thick: 2px;
$border-color: rgba($color-text, 0.1);
$border-color-light: rgba($color-text, 0.05);
$border-color-dark: rgba($color-text, 0.2);

// 特殊效果
$backdrop-blur: blur(20px);
$backdrop-blur-light: blur(10px);
$backdrop-blur-heavy: blur(40px);
