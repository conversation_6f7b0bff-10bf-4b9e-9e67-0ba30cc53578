// 测试环境设置
import { config } from '@vue/test-utils'

// 全局组件注册
config.global.components = {
  // 在这里注册全局组件
}

// 全局插件
config.global.plugins = [
  // 在这里注册全局插件
]

// Mock 全局对象
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})
