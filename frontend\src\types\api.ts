// 通用API响应接口
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
}

// 分页响应接口
export interface PageResponse<T = any> {
  records: T[];
  total: number;
  current: number;
  size: number;
  pages: number;
}

// HTTP请求方法类型
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

// 请求配置接口
export interface RequestConfig {
  url: string;
  method?: HttpMethod;
  params?: Record<string, any>;
  data?: any;
  headers?: Record<string, string>;
  timeout?: number;
}

// 错误响应接口
export interface ErrorResponse {
  code: number;
  message: string;
  timestamp: number;
  path?: string;
}

// 业务错误码
export enum BusinessErrorCode {
  SUCCESS = 200,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  INTERNAL_ERROR = 500,
  
  // 业务相关错误码
  USER_NOT_FOUND = 1001,
  USER_ALREADY_EXISTS = 1002,
  INVALID_PASSWORD = 1003,
  INVALID_TOKEN = 1004,
  TOKEN_EXPIRED = 1005,
  
  SCRIPT_NOT_FOUND = 2001,
  SCRIPT_ALREADY_FAVORITED = 2002,
  
  LOBBY_NOT_FOUND = 3001,
  LOBBY_FULL = 3002,
  LOBBY_ALREADY_JOINED = 3003,
}