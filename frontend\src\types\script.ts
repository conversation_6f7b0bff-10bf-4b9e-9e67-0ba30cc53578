// 剧本搜索参数接口
export interface ScriptSearchParams {
  keyword?: string;
  category?: string;
  playerCountMin?: number;
  playerCountMax?: number;
  difficulties?: number[];
  priceMin?: number;
  priceMax?: number;
  tags?: string[];
  sortBy?: 'createTime' | 'averageRating' | 'playCount' | 'reviewCount' | 'price';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  size?: number;
}

// 剧本基础信息接口
export interface Script {
  id: number;
  title: string;
  description: string;
  coverImage: string;
  category: string;
  playerCountRange: string;
  playerCountMin: number;
  playerCountMax: number;
  duration: string;
  difficulty: number;
  ageRange?: string;
  tagList: string[];
  highlights: string[];
  warnings: string[];
  price: number;
  averageRating: number;
  reviewCount: number;
  playCount: number;
  isFavorite: boolean;
  createTime: string;
  updateTime: string;
}

// 剧本详情接口
export interface ScriptDetail extends Script {
  images: string[];
  characters: ScriptCharacter[];
  rules: ScriptRule[];
  recentReviews: ScriptReview[];
}

// 剧本角色接口
export interface ScriptCharacter {
  id: number;
  name: string;
  gender: string;
  age: string;
  occupation: string;
  personality: string;
  background: string;
  avatar?: string;
  difficulty: number;
  isCore: boolean;
  specialAbilities?: string;
}

// 剧本规则接口
export interface ScriptRule {
  id: number;
  ruleType: string;
  title: string;
  content: string;
  sortOrder: number;
  isImportant: boolean;
}

// 剧本评价接口
export interface ScriptReview {
  id: number;
  userId: number;
  userNickname: string;
  userAvatar?: string;
  rating: number;
  title?: string;
  content: string;
  images: string[];
  likedCount: number;
  helpfulCount: number;
  isAnonymous: boolean;
  tags: string[];
  isLiked?: boolean;
  isHelpful?: boolean;
  createTime: string;
}

// 分类统计接口
export interface CategoryStats {
  category: string;
  displayName: string;
  count: number;
}

// 通用API响应接口
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
}

// 分页响应接口
export interface PageResponse<T = any> {
  records: T[];
  total: number;
  current: number;
  size: number;
  pages: number;
}