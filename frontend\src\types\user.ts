// 用户相关类型定义

export interface User {
  id: number
  nickname: string
  avatar: string
  email?: string
  phone?: string
  level: number
  experience: number
  createdAt: string
  lastLoginAt?: string
  status: 'active' | 'inactive' | 'banned'
}

export interface UserProfile extends User {
  bio?: string
  gender?: 'male' | 'female' | 'other'
  birthday?: string
  location?: string
  website?: string
  socialLinks?: {
    wechat?: string
    qq?: string
    weibo?: string
  }
  preferences: {
    favoriteGenres: string[]
    playStyle: 'casual' | 'serious' | 'competitive'
    preferredPlayerCount: number[]
    preferredDuration: number[]
  }
  privacy: {
    showEmail: boolean
    showPhone: boolean
    showLocation: boolean
    allowFollow: boolean
    allowMessage: boolean
  }
}

export interface UserStats {
  totalGames: number
  totalHours: number
  favoriteCount: number
  followingCount: number
  followersCount: number
  postsCount: number
  likesReceived: number
  averageRating: number
  achievements: Achievement[]
  recentGames: GameRecord[]
}

export interface Achievement {
  id: number
  name: string
  description: string
  icon: string
  unlockedAt: string
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
}

export interface GameRecord {
  id: number
  scriptId: number
  scriptTitle: string
  scriptCover: string
  playedAt: string
  duration: number
  rating?: number
  review?: string
  role?: string
}

export interface FollowUser {
  id: number
  nickname: string
  avatar: string
  bio?: string
  level: number
  isFollowing: boolean
  followedAt?: string
}

// API 响应类型 - 匹配后端 ApiResponse 格式
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 兼容旧格式的响应类型
export interface LegacyApiResponse<T = any> {
  success: boolean
  data: T
  errorCode?: string
  errorMsg?: string
  timestamp: number
}

// 分页响应类型
export interface PaginatedResponse<T = any> {
  items: T[]
  total: number
  page: number
  pageSize: number
  hasMore: boolean
}
