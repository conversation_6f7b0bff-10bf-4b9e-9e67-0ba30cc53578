/**
 * HTTP请求工具
 * 基于fetch API的简单封装
 */

// 基础配置
const BASE_URL = 'http://localhost:8081/api'
const TIMEOUT = 10000

/**
 * 请求拦截器
 */
function requestInterceptor(config) {
  // 添加认证token
  const token = localStorage.getItem('auth_token')
  if (token) {
    config.headers = {
      ...config.headers,
      'Authorization': `Bearer ${token}`
    }
  }
  
  // 添加默认headers
  config.headers = {
    'Content-Type': 'application/json',
    ...config.headers
  }
  
  return config
}

/**
 * 响应拦截器
 */
function responseInterceptor(response, data) {
  // 处理认证失败
  if (response.status === 401) {
    localStorage.removeItem('auth_token')
    window.location.href = '/login'
    return Promise.reject(new Error('认证失败'))
  }
  
  // 处理业务错误
  if (!response.ok) {
    const error = new Error(data.message || `HTTP ${response.status}`)
    error.status = response.status
    error.data = data
    return Promise.reject(error)
  }
  
  return data
}

/**
 * 创建请求实例
 */
async function request(url, options = {}) {
  // 处理URL
  const fullUrl = url.startsWith('http') ? url : `${BASE_URL}${url}`
  
  // 处理配置
  const config = requestInterceptor({
    method: 'GET',
    headers: {},
    ...options
  })
  
  // 处理请求体
  if (config.body && typeof config.body === 'object') {
    config.body = JSON.stringify(config.body)
  }
  
  try {
    // 创建超时控制
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), TIMEOUT)
    
    // 发送请求
    const response = await fetch(fullUrl, {
      ...config,
      signal: controller.signal
    })
    
    clearTimeout(timeoutId)
    
    // 解析响应
    let data
    const contentType = response.headers.get('content-type')
    if (contentType && contentType.includes('application/json')) {
      data = await response.json()
    } else {
      data = await response.text()
    }
    
    // 响应拦截
    return responseInterceptor(response, data)
    
  } catch (error) {
    if (error.name === 'AbortError') {
      throw new Error('请求超时')
    }
    throw error
  }
}

/**
 * GET请求
 */
export function get(url, params = {}, options = {}) {
  // 处理查询参数
  if (Object.keys(params).length > 0) {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        searchParams.append(key, value)
      }
    })
    url += (url.includes('?') ? '&' : '?') + searchParams.toString()
  }
  
  return request(url, {
    method: 'GET',
    ...options
  })
}

/**
 * POST请求
 */
export function post(url, data = {}, options = {}) {
  return request(url, {
    method: 'POST',
    body: data,
    ...options
  })
}

/**
 * PUT请求
 */
export function put(url, data = {}, options = {}) {
  return request(url, {
    method: 'PUT',
    body: data,
    ...options
  })
}

/**
 * DELETE请求
 */
export function del(url, options = {}) {
  return request(url, {
    method: 'DELETE',
    ...options
  })
}

/**
 * PATCH请求
 */
export function patch(url, data = {}, options = {}) {
  return request(url, {
    method: 'PATCH',
    body: data,
    ...options
  })
}

// 默认导出
export default {
  get,
  post,
  put,
  delete: del,
  patch,
  request
}

/**
 * 文件上传
 */
export function upload(url, file, options = {}) {
  const formData = new FormData()
  formData.append('file', file)
  
  // 添加额外的字段
  if (options.data) {
    Object.entries(options.data).forEach(([key, value]) => {
      formData.append(key, value)
    })
  }
  
  return request(url, {
    method: 'POST',
    body: formData,
    headers: {
      // 不设置Content-Type，让浏览器自动设置
    },
    ...options
  })
}

/**
 * 下载文件
 */
export async function download(url, filename, options = {}) {
  try {
    const response = await fetch(url.startsWith('http') ? url : `${BASE_URL}${url}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      },
      ...options
    })
    
    if (!response.ok) {
      throw new Error(`下载失败: ${response.status}`)
    }
    
    const blob = await response.blob()
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
    
    return true
  } catch (error) {
    console.error('下载失败:', error)
    throw error
  }
}
