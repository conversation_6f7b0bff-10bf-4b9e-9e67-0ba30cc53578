<template>
  <div class="forgot-password-form">
    <div class="form-header">
      <h2 class="form-title">找回密码</h2>
      <p class="form-subtitle">输入你的邮箱地址，我们将发送重置链接给你</p>
    </div>
    
    <form @submit.prevent="handleSubmit" class="forgot-password-content">
      <div class="form-group">
        <label class="form-label">邮箱地址</label>
        <input 
          v-model="email"
          type="email" 
          class="form-input"
          placeholder="请输入注册时使用的邮箱"
          required
        />
      </div>
      
      <button 
        type="submit" 
        class="submit-button"
        :disabled="!email || isLoading"
      >
        <span v-if="isLoading" class="loading-spinner"></span>
        <span>{{ isLoading ? '发送中...' : '发送重置链接' }}</span>
      </button>
      
      <div class="form-footer">
        <span class="footer-text">想起密码了？</span>
        <router-link to="/auth/login" class="footer-link">
          返回登录
        </router-link>
      </div>
    </form>
    
    <!-- 成功提示 -->
    <div v-if="emailSent" class="success-message">
      <div class="success-icon">✅</div>
      <h3 class="success-title">邮件已发送</h3>
      <p class="success-text">
        我们已向 <strong>{{ email }}</strong> 发送了密码重置链接，
        请查收邮件并按照说明操作。
      </p>
      <p class="success-note">
        没有收到邮件？请检查垃圾邮件文件夹，或者
        <button class="resend-btn" @click="handleSubmit">重新发送</button>
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const email = ref('')
const isLoading = ref(false)
const emailSent = ref(false)

const handleSubmit = async () => {
  if (!email.value || isLoading.value) return
  
  try {
    isLoading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    emailSent.value = true
    console.log('发送重置邮件到:', email.value)
  } catch (error) {
    console.error('发送邮件失败:', error)
  } finally {
    isLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
.forgot-password-form {
  width: 100%;
}

.form-header {
  text-align: center;
  margin-bottom: 32px;
}

.form-title {
  font-size: 1.8rem;
  color: #fff;
  font-weight: 700;
  margin-bottom: 8px;
}

.form-subtitle {
  color: #B0B0B0;
  font-size: 0.9rem;
  margin: 0;
  line-height: 1.4;
}

.forgot-password-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 0.9rem;
  color: #E0E0E0;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 14px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 10px;
  color: #fff;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  
  &::placeholder {
    color: #666;
  }
  
  &:focus {
    outline: none;
    border-color: #00F5D4;
    box-shadow: 0 0 0 3px rgba(0, 245, 212, 0.1);
    background: rgba(255, 255, 255, 0.08);
  }
}

.submit-button {
  width: 100%;
  padding: 14px;
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 245, 212, 0.4);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
  }
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(26, 26, 46, 0.3);
  border-top: 2px solid #1A1A2E;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.form-footer {
  text-align: center;
  margin-top: 24px;
}

.footer-text {
  color: #B0B0B0;
  font-size: 0.9rem;
  margin-right: 8px;
}

.footer-link {
  color: #00F5D4;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
  
  &:hover {
    color: #FF00E4;
  }
}

.success-message {
  text-align: center;
  padding: 32px;
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  border-radius: 12px;
  margin-top: 24px;
}

.success-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.success-title {
  font-size: 1.3rem;
  color: #4CAF50;
  font-weight: 600;
  margin-bottom: 16px;
}

.success-text {
  color: #E0E0E0;
  line-height: 1.5;
  margin-bottom: 16px;
  
  strong {
    color: #00F5D4;
  }
}

.success-note {
  color: #B0B0B0;
  font-size: 0.85rem;
  line-height: 1.4;
}

.resend-btn {
  background: none;
  border: none;
  color: #00F5D4;
  cursor: pointer;
  text-decoration: underline;
  font-size: inherit;
  
  &:hover {
    color: #FF00E4;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
