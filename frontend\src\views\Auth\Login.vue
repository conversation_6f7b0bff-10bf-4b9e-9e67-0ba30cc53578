<template>
  <div class="login-form">
    <div class="form-header">
      <h2 class="form-title">欢迎回来</h2>
      <p class="form-subtitle">登录你的账户，继续精彩旅程</p>
    </div>

    <form @submit.prevent="handleLogin" class="login-form-content">
      <!-- 邮箱输入框 -->
      <div class="form-group">
        <label class="form-label">邮箱</label>
        <input
          v-model="formData.email"
          type="email"
          class="form-input"
          placeholder="请输入邮箱地址"
          required
        />
      </div>

      <!-- 密码输入框 -->
      <div class="form-group">
        <label class="form-label">密码</label>
        <div class="password-input-wrapper">
          <input
            v-model="formData.password"
            :type="showPassword ? 'text' : 'password'"
            class="form-input"
            placeholder="请输入密码"
            required
          />
          <button
            type="button"
            class="password-toggle"
            @click="togglePassword"
          >
            <i :class="showPassword ? 'icon-eye-off' : 'icon-eye'"></i>
          </button>
        </div>
      </div>

      <div class="form-options">
        <label class="checkbox-wrapper">
          <input type="checkbox" v-model="rememberMe" />
          <span class="checkbox-text">记住我</span>
        </label>
        <router-link to="/auth/forgot-password" class="forgot-link">
          忘记密码？
        </router-link>
      </div>

      <button
        type="submit"
        class="submit-button"
        :disabled="isLoading"
      >
        <span v-if="isLoading" class="loading-spinner"></span>
        <span>{{ isLoading ? '登录中...' : '登录' }}</span>
      </button>

      <div class="form-footer">
        <span class="footer-text">还没有账户？</span>
        <router-link to="/auth/register" class="footer-link">
          立即注册
        </router-link>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuth } from '@/composables/useAuth'

const router = useRouter()
const { login, isLoading, error } = useAuth()

const formData = ref({
  email: '',
  password: ''
})

const showPassword = ref(false)
const rememberMe = ref(false)

// 切换密码显示/隐藏
const togglePassword = () => {
  showPassword.value = !showPassword.value
}

// 验证邮箱格式
const isValidEmail = (email: string) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

const handleLogin = async () => {
  // 验证表单
  if (!formData.value.email) {
    ElMessage.error('请输入邮箱地址')
    return
  }

  if (!formData.value.password) {
    ElMessage.error('请输入密码')
    return
  }

  // 验证邮箱格式
  if (!isValidEmail(formData.value.email)) {
    ElMessage.error('请输入正确的邮箱地址')
    return
  }

  try {
    const success = await login({
      email: formData.value.email,
      password: formData.value.password
    })

    if (success) {
      ElMessage.success('登录成功！')
      router.push('/')
    } else {
      // 登录失败，显示错误信息
      ElMessage.error(error.value || '登录失败，请检查邮箱和密码')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '登录失败，请检查邮箱和密码')
  }
}
</script>

<style lang="scss" scoped>
.login-form {
  width: 100%;
}

.form-header {
  text-align: center;
  margin-bottom: 32px;
}

.form-title {
  font-size: 1.8rem;
  color: #fff;
  font-weight: 700;
  margin-bottom: 8px;
}

.form-subtitle {
  color: #B0B0B0;
  font-size: 0.9rem;
  margin: 0;
}

.login-form-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 0.9rem;
  color: #E0E0E0;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 14px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 10px;
  color: #fff;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  
  &::placeholder {
    color: #666;
  }
  
  &:focus {
    outline: none;
    border-color: #00F5D4;
    box-shadow: 0 0 0 3px rgba(0, 245, 212, 0.1);
    background: rgba(255, 255, 255, 0.08);
  }
}

.password-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.password-toggle {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  color: #B0B0B0;
  cursor: pointer;
  padding: 4px;
  transition: color 0.3s ease;

  &:hover {
    color: #00F5D4;
  }

  i {
    font-size: 1rem;
  }
}



.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  
  input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #00F5D4;
  }
}

.checkbox-text {
  font-size: 0.85rem;
  color: #B0B0B0;
}

.forgot-link {
  font-size: 0.85rem;
  color: #00F5D4;
  text-decoration: none;
  transition: color 0.3s ease;
  
  &:hover {
    color: #FF00E4;
  }
}

.submit-button {
  width: 100%;
  padding: 14px;
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 245, 212, 0.4);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
  }
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(26, 26, 46, 0.3);
  border-top: 2px solid #1A1A2E;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.form-footer {
  text-align: center;
  margin-top: 24px;
}

.footer-text {
  color: #B0B0B0;
  font-size: 0.9rem;
  margin-right: 8px;
}

.footer-link {
  color: #00F5D4;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
  
  &:hover {
    color: #FF00E4;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 简单的眼睛图标样式
.icon-eye::before {
  content: '👁';
}

.icon-eye-off::before {
  content: '🙈';
}
</style>
