<template>
  <div class="register-form">
    <div class="form-header">
      <h2 class="form-title">加入我们</h2>
      <p class="form-subtitle">创建账户，开启你的剧本杀之旅</p>
    </div>
    
    <form @submit.prevent="handleRegister" class="register-form-content">
      <div class="form-group">
        <label class="form-label">昵称</label>
        <input
          v-model="formData.nickName"
          type="text"
          class="form-input"
          placeholder="请输入昵称"
        />
      </div>
      
      <div class="form-group">
        <label class="form-label">邮箱</label>
        <input
          v-model="formData.email"
          type="email"
          class="form-input"
          placeholder="请输入邮箱地址"
        />
      </div>

      <div class="form-group">
        <label class="form-label">邮箱验证码</label>
        <div class="code-input-wrapper">
          <input
            v-model="formData.code"
            type="text"
            class="form-input"
            placeholder="请输入验证码"
          />
          <button
            type="button"
            class="code-button"
            @click="sendCode"
            :disabled="codeDisabled || isLoading || !isEmailValid"
          >
            {{ codeButtonText }}
          </button>
        </div>
      </div>

      <div class="form-group">
        <label class="form-label">密码</label>
        <div class="password-input-wrapper">
          <input
            v-model="formData.password"
            :type="showPassword ? 'text' : 'password'"
            class="form-input"
            placeholder="请输入密码（至少6位）"
          />
          <button
            type="button"
            class="password-toggle"
            @click="showPassword = !showPassword"
          >
            <i :class="showPassword ? 'icon-eye-off' : 'icon-eye'"></i>
          </button>
        </div>
      </div>

      <div class="form-group">
        <label class="form-label">确认密码</label>
        <input
          v-model="formData.confirmPassword"
          type="password"
          class="form-input"
          placeholder="请再次输入密码"
        />
      </div>
      
      <div class="form-options">
        <label class="checkbox-wrapper">
          <input type="checkbox" v-model="agreeTerms" />
          <span class="checkbox-text">
            我已阅读并同意
            <a href="#" class="terms-link">用户协议</a>
            和
            <a href="#" class="terms-link">隐私政策</a>
          </span>
        </label>
      </div>
      
      <button
        type="submit"
        class="submit-button"
        :disabled="isLoading"
      >
        <span v-if="isLoading" class="loading-spinner"></span>
        <span>{{ isLoading ? '注册中...' : '注册' }}</span>
      </button>
      
      <div class="form-footer">
        <span class="footer-text">已有账户？</span>
        <router-link to="/auth/login" class="footer-link">
          立即登录
        </router-link>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuth } from '@/composables/useAuth'

const router = useRouter()
const { register, sendEmailCode, isLoading, error } = useAuth()

const formData = ref({
  nickName: '',
  email: '',
  code: '',
  password: '',
  confirmPassword: ''
})

const showPassword = ref(false)
const agreeTerms = ref(false)
const codeDisabled = ref(false)
const countdown = ref(0)
const codeButtonText = ref('发送验证码')

// 验证邮箱格式
const isEmailValid = computed(() => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(formData.value.email)
})

const isFormValid = computed(() => {
  return formData.value.nickName.trim() &&
         formData.value.email.trim() &&
         isEmailValid.value &&
         formData.value.code.trim() &&
         formData.value.password &&
         formData.value.confirmPassword &&
         formData.value.password === formData.value.confirmPassword &&
         formData.value.password.length >= 6 &&
         agreeTerms.value
})

// 发送邮箱验证码
const sendCode = async () => {
  if (!isEmailValid.value) {
    ElMessage.error('请输入正确的邮箱地址')
    return
  }

  try {
    codeDisabled.value = true
    await sendEmailCode(formData.value.email)

    ElMessage.success('验证码发送成功，请查收邮件')

    // 开始倒计时
    countdown.value = 60
    const timer = setInterval(() => {
      countdown.value--
      codeButtonText.value = `${countdown.value}秒后重发`

      if (countdown.value <= 0) {
        clearInterval(timer)
        codeDisabled.value = false
        codeButtonText.value = '发送验证码'
      }
    }, 1000)
  } catch (error: any) {
    codeDisabled.value = false
    ElMessage.error(error.message || '发送验证码失败，请稍后重试')
  }
}

const handleRegister = async () => {
  // 验证昵称
  if (!formData.value.nickName.trim()) {
    ElMessage.error('请输入昵称')
    return
  }

  // 验证邮箱
  if (!formData.value.email.trim()) {
    ElMessage.error('请输入邮箱地址')
    return
  }

  if (!isEmailValid.value) {
    ElMessage.error('请输入正确的邮箱地址')
    return
  }

  // 验证验证码
  if (!formData.value.code.trim()) {
    ElMessage.error('请输入邮箱验证码')
    return
  }

  // 验证密码
  if (!formData.value.password) {
    ElMessage.error('请输入密码')
    return
  }

  if (formData.value.password.length < 6) {
    ElMessage.error('密码长度不能少于6位')
    return
  }

  if (!formData.value.confirmPassword) {
    ElMessage.error('请确认密码')
    return
  }

  if (formData.value.password !== formData.value.confirmPassword) {
    ElMessage.error('两次输入的密码不一致')
    return
  }

  // 验证协议同意
  if (!agreeTerms.value) {
    ElMessage.error('请阅读并同意用户协议和隐私政策')
    return
  }

  try {
    const success = await register(formData.value)
    if (success) {
      ElMessage.success('注册成功！请登录')
      router.push('/auth/login')
    } else {
      // 注册失败，显示错误信息
      ElMessage.error(error.value || '注册失败，请稍后重试')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '注册失败，请稍后重试')
  }
}
</script>

<style lang="scss" scoped>
.register-form {
  width: 100%;
}

.form-header {
  text-align: center;
  margin-bottom: 14px;
}

.form-title {
  font-size: 1.4rem;
  color: #fff;
  font-weight: 700;
  margin-bottom: 6px;
  background: linear-gradient(135deg, #00F5D4, #FF00E4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.form-subtitle {
  color: #B0B0B0;
  font-size: 0.8rem;
  margin: 0;
  opacity: 0.9;
}

.register-form-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-label {
  font-size: 0.8rem;
  color: #E0E0E0;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 12px 14px;
  background: rgba(255, 255, 255, 0.06);
  border: 1px solid rgba(0, 245, 212, 0.25);
  border-radius: 10px;
  color: #fff;
  font-size: 0.9rem;
  transition: all 0.3s ease;

  &::placeholder {
    color: #888;
  }

  &:focus {
    outline: none;
    border-color: #00F5D4;
    box-shadow: 0 0 0 3px rgba(0, 245, 212, 0.15);
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
  }

  &:hover:not(:focus) {
    border-color: rgba(0, 245, 212, 0.4);
    background: rgba(255, 255, 255, 0.08);
  }
}

.code-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12px;
}

.code-button {
  background: linear-gradient(135deg, #00F5D4 0%, #00D4AA 100%);
  border: none;
  border-radius: 8px;
  color: #1a1a1a;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 600;
  padding: 12px 16px;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: 100px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(0, 245, 212, 0.4);
    background: linear-gradient(135deg, #00F5D4 0%, #00E4BB 100%);
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
    background: rgba(0, 245, 212, 0.3);
  }
}

.password-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.password-toggle {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  color: #B0B0B0;
  cursor: pointer;
  padding: 4px;
  transition: color 0.3s ease;

  &:hover {
    color: #00F5D4;
  }

  i {
    font-size: 1rem;
  }
}

.form-options {
  margin: 4px 0;
}

.checkbox-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 6px;
  cursor: pointer;

  input[type="checkbox"] {
    width: 14px;
    height: 14px;
    accent-color: #00F5D4;
    margin-top: 2px;
  }
}

.checkbox-text {
  font-size: 0.8rem;
  color: #B0B0B0;
  line-height: 1.3;
}

.terms-link {
  color: #00F5D4;
  text-decoration: none;
  
  &:hover {
    color: #FF00E4;
  }
}

.submit-button {
  width: 100%;
  padding: 14px;
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  border: none;
  border-radius: 10px;
  font-size: 0.95rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 6px;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 245, 212, 0.5);
    background: linear-gradient(135deg, #00F5D4, #00E4BB);

    &::before {
      left: 100%;
    }
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    background: rgba(0, 245, 212, 0.3);
  }
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(26, 26, 46, 0.3);
  border-top: 2px solid #1A1A2E;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.form-footer {
  text-align: center;
  margin-top: 12px;
}

.footer-text {
  color: #B0B0B0;
  font-size: 0.85rem;
  margin-right: 6px;
}

.footer-link {
  color: #00F5D4;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;

  &:hover {
    color: #FF00E4;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 简单的眼睛图标样式
.icon-eye::before {
  content: '👁';
}

.icon-eye-off::before {
  content: '🙈';
}
</style>
