<template>
  <div class="debug-user-state">
    <div class="debug-container">
      <h2>🔍 用户状态调试面板</h2>
      
      <!-- 当前状态显示 -->
      <div class="state-section">
        <h3>📊 当前状态</h3>
        <div class="state-grid">
          <div class="state-item">
            <label>认证状态:</label>
            <span :class="authStore.isAuthenticated ? 'status-success' : 'status-error'">
              {{ authStore.isAuthenticated ? '已认证' : '未认证' }}
            </span>
          </div>
          
          <div class="state-item">
            <label>用户登录状态:</label>
            <span :class="userStore.isLoggedIn ? 'status-success' : 'status-error'">
              {{ userStore.isLoggedIn ? '已登录' : '未登录' }}
            </span>
          </div>
          
          <div class="state-item">
            <label>Token存在:</label>
            <span :class="hasToken ? 'status-success' : 'status-error'">
              {{ hasToken ? '存在' : '不存在' }}
            </span>
          </div>
          
          <div class="state-item">
            <label>用户昵称:</label>
            <span>{{ userStore.userNickname }}</span>
          </div>
          
          <div class="state-item">
            <label>用户头像:</label>
            <div class="avatar-info">
              <img :src="getUserAvatarUrl()" alt="头像" class="debug-avatar" @error="handleAvatarError" />
              <span class="avatar-url">{{ userStore.userAvatar || '无头像' }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="actions-section">
        <h3>🛠️ 调试操作</h3>
        <div class="actions-grid">
          <button @click="testGetUserInfo" class="action-btn primary">
            测试获取用户信息
          </button>
          
          <button @click="clearAllUserData" class="action-btn danger">
            清除所有用户数据
          </button>
          
          <button @click="simulateTokenExpiry" class="action-btn warning">
            模拟Token过期
          </button>
          
          <button @click="refreshUserState" class="action-btn success">
            刷新用户状态
          </button>
        </div>
      </div>

      <!-- 详细信息 -->
      <div class="details-section">
        <h3>📋 详细信息</h3>
        <div class="details-content">
          <div class="detail-block">
            <h4>Auth Store</h4>
            <pre>{{ JSON.stringify(authStoreData, null, 2) }}</pre>
          </div>
          
          <div class="detail-block">
            <h4>User Store</h4>
            <pre>{{ JSON.stringify(userStoreData, null, 2) }}</pre>
          </div>
          
          <div class="detail-block">
            <h4>LocalStorage</h4>
            <pre>{{ JSON.stringify(localStorageData, null, 2) }}</pre>
          </div>
        </div>
      </div>

      <!-- 日志 -->
      <div class="logs-section">
        <h3>📝 操作日志</h3>
        <div class="logs-content">
          <div v-for="(log, index) in logs" :key="index" class="log-item" :class="log.type">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
        <button @click="clearLogs" class="clear-logs-btn">清除日志</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/modules/auth'
import { useUserStore } from '@/stores/modules/user'
import { userApi } from '@/api/user'

// 状态管理
const authStore = useAuthStore()
const userStore = useUserStore()

// 响应式数据
const logs = ref<Array<{time: string, message: string, type: string}>>([])

// 计算属性
const hasToken = computed(() => !!localStorage.getItem('auth_token'))

const authStoreData = computed(() => ({
  isAuthenticated: authStore.isAuthenticated,
  token: authStore.token,
  isLoading: authStore.isLoading,
  error: authStore.error
}))

const userStoreData = computed(() => ({
  isLoggedIn: userStore.isLoggedIn,
  userId: userStore.userId,
  userNickname: userStore.userNickname,
  userAvatar: userStore.userAvatar,
  userLevel: userStore.userLevel,
  currentUser: userStore.currentUser
}))

const localStorageData = computed(() => ({
  auth_token: localStorage.getItem('auth_token'),
  'pinia-user': localStorage.getItem('pinia-user'),
  'pinia-auth': localStorage.getItem('pinia-auth')
}))

// 方法
const addLog = (message: string, type: string = 'info') => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message,
    type
  })
  
  // 限制日志数量
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
}

const getUserAvatarUrl = (size: number = 32): string => {
  const avatar = userStore.userAvatar
  
  if (avatar && avatar.trim() && !avatar.includes('picsum.photos')) {
    return avatar
  }
  
  const userId = userStore.userId || 0
  const nickname = userStore.userNickname || '用户'
  const seed = userId > 0 ? userId : nickname.charCodeAt(0) + nickname.length
  
  return `https://api.dicebear.com/7.x/avataaars/svg?seed=${seed}&size=${size}&backgroundColor=00f5d4,ff00e4,1a1a2e`
}

const handleAvatarError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = getUserAvatarUrl(32)
  addLog('头像加载失败，使用默认头像', 'warning')
}

const testGetUserInfo = async () => {
  addLog('开始测试获取用户信息...', 'info')
  
  try {
    const response = await userApi.getCurrentUser()
    addLog(`API响应: ${JSON.stringify(response)}`, 'success')
  } catch (error) {
    addLog(`API错误: ${error}`, 'error')
  }
}

const clearAllUserData = () => {
  addLog('清除所有用户数据...', 'warning')
  
  // 清除localStorage
  localStorage.removeItem('auth_token')
  localStorage.removeItem('pinia-user')
  localStorage.removeItem('pinia-auth')
  
  // 清除store状态
  authStore.logout()
  userStore.clearUserData()
  
  addLog('用户数据已清除', 'success')
}

const simulateTokenExpiry = () => {
  addLog('模拟Token过期...', 'warning')
  
  // 设置一个无效的token
  localStorage.setItem('auth_token', 'expired_token_' + Date.now())
  
  addLog('已设置无效Token，下次API调用将触发认证失败', 'info')
}

const refreshUserState = () => {
  addLog('刷新用户状态...', 'info')
  
  // 重新加载页面来触发用户状态初始化
  window.location.reload()
}

const clearLogs = () => {
  logs.value = []
}

// 生命周期
onMounted(() => {
  addLog('用户状态调试面板已加载', 'info')
})
</script>

<style lang="scss" scoped>
.debug-user-state {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  padding: 20px;
}

.debug-container {
  max-width: 1200px;
  margin: 0 auto;
  color: #fff;
}

h2, h3, h4 {
  color: #00f5d4;
  margin-bottom: 16px;
}

.state-section, .actions-section, .details-section, .logs-section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
}

.state-grid, .actions-grid {
  display: grid;
  gap: 16px;
}

.state-grid {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.actions-grid {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.state-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  
  label {
    font-weight: 600;
    color: #b0b0b0;
    min-width: 120px;
  }
}

.status-success {
  color: #00f5d4;
  font-weight: 600;
}

.status-error {
  color: #ff4444;
  font-weight: 600;
}

.avatar-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.debug-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-url {
  font-size: 0.8rem;
  color: #888;
  word-break: break-all;
}

.action-btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &.primary {
    background: #00f5d4;
    color: #1a1a2e;
    
    &:hover {
      background: #00c9a7;
    }
  }
  
  &.danger {
    background: #ff4444;
    color: #fff;
    
    &:hover {
      background: #cc3333;
    }
  }
  
  &.warning {
    background: #ffaa00;
    color: #1a1a2e;
    
    &:hover {
      background: #cc8800;
    }
  }
  
  &.success {
    background: #44ff44;
    color: #1a1a2e;
    
    &:hover {
      background: #33cc33;
    }
  }
}

.details-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.detail-block {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  padding: 16px;
  
  h4 {
    margin-bottom: 12px;
    font-size: 1rem;
  }
  
  pre {
    font-size: 0.8rem;
    color: #b0b0b0;
    white-space: pre-wrap;
    word-break: break-word;
  }
}

.logs-content {
  max-height: 300px;
  overflow-y: auto;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
}

.log-item {
  display: flex;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.85rem;
  
  &:last-child {
    border-bottom: none;
  }
  
  &.info {
    color: #b0b0b0;
  }
  
  &.success {
    color: #00f5d4;
  }
  
  &.warning {
    color: #ffaa00;
  }
  
  &.error {
    color: #ff4444;
  }
}

.log-time {
  color: #888;
  min-width: 80px;
  font-family: monospace;
}

.clear-logs-btn {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: #b0b0b0;
  cursor: pointer;
  
  &:hover {
    background: rgba(255, 255, 255, 0.15);
    color: #fff;
  }
}
</style>
