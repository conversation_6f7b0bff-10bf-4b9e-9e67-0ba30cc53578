<template>
  <div class="not-found">
    <div class="container">
      <div class="error-content">
        <!-- 动画背景 -->
        <div class="error-background">
          <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
          </div>
        </div>
        
        <!-- 错误信息 -->
        <div class="error-info">
          <div class="error-code">404</div>
          <div class="error-title">页面走丢了</div>
          <div class="error-subtitle">
            抱歉，你访问的页面不存在或已被移除
          </div>
          
          <div class="error-illustration">
            <div class="maze-icon">🌫️</div>
            <div class="maze-text">迷失在迷雾中...</div>
          </div>
          
          <div class="error-actions">
            <button class="back-btn" @click="goBack">
              <span class="btn-icon">←</span>
              <span class="btn-text">返回上页</span>
            </button>
            
            <router-link to="/" class="home-btn">
              <span class="btn-icon">🏠</span>
              <span class="btn-text">回到首页</span>
            </router-link>
          </div>
          
          <div class="helpful-links">
            <h4 class="links-title">你可能在寻找：</h4>
            <div class="links-grid">
              <router-link to="/lobby" class="help-link">
                <span class="link-icon">🚗</span>
                <span class="link-text">拼车大厅</span>
              </router-link>
              
              <router-link to="/scripts" class="help-link">
                <span class="link-icon">📚</span>
                <span class="link-text">剧本库</span>
              </router-link>
              
              <router-link to="/feed" class="help-link">
                <span class="link-icon">💬</span>
                <span class="link-text">动态广场</span>
              </router-link>
              
              <router-link to="/user/profile" class="help-link">
                <span class="link-icon">👤</span>
                <span class="link-text">个人中心</span>
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}
</script>

<style lang="scss" scoped>
.not-found {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(180deg, #1A1A2E 0%, #16213E 50%, #0F0F1E 100%);
  position: relative;
  overflow: hidden;
}

.error-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 600px;
  padding: 40px 20px;
}

.error-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.floating-shapes {
  position: relative;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(0, 245, 212, 0.1), rgba(255, 0, 228, 0.1));
  animation: float 6s ease-in-out infinite;
  
  &.shape-1 {
    width: 200px;
    height: 200px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
  }
  
  &.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 20%;
    animation-delay: 2s;
  }
  
  &.shape-3 {
    width: 100px;
    height: 100px;
    bottom: 30%;
    left: 30%;
    animation-delay: 4s;
  }
}

.error-info {
  position: relative;
  z-index: 3;
}

.error-code {
  font-size: 8rem;
  font-weight: 900;
  background: linear-gradient(135deg, #00F5D4, #FF00E4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 16px;
  text-shadow: 0 0 30px rgba(0, 245, 212, 0.3);
  animation: glow 2s ease-in-out infinite alternate;
}

.error-title {
  font-size: 2.5rem;
  color: #fff;
  font-weight: 700;
  margin-bottom: 16px;
}

.error-subtitle {
  font-size: 1.2rem;
  color: #B0B0B0;
  margin-bottom: 40px;
  line-height: 1.5;
}

.error-illustration {
  margin-bottom: 40px;
}

.maze-icon {
  font-size: 4rem;
  margin-bottom: 12px;
  filter: drop-shadow(0 0 20px rgba(0, 245, 212, 0.5));
  animation: float 3s ease-in-out infinite;
}

.maze-text {
  font-size: 1rem;
  color: #888;
  font-style: italic;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 48px;
  flex-wrap: wrap;
}

.back-btn, .home-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.back-btn {
  background: rgba(255, 255, 255, 0.1);
  color: #E0E0E0;
  border: 1px solid rgba(255, 255, 255, 0.2);
  
  &:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
  }
}

.home-btn {
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 245, 212, 0.4);
  }
}

.btn-icon {
  font-size: 1.2rem;
}

.helpful-links {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.1);
  border-radius: 16px;
  padding: 32px;
  backdrop-filter: blur(10px);
}

.links-title {
  font-size: 1.2rem;
  color: #fff;
  font-weight: 600;
  margin-bottom: 24px;
}

.links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 16px;
}

.help-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  text-decoration: none;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(0, 245, 212, 0.1);
    border-color: rgba(0, 245, 212, 0.3);
    transform: translateY(-2px);
  }
}

.link-icon {
  font-size: 2rem;
  margin-bottom: 4px;
}

.link-text {
  font-size: 0.9rem;
  color: #E0E0E0;
  font-weight: 500;
}

// 动画
@keyframes float {
  0%, 100% { 
    transform: translateY(0px) rotate(0deg); 
  }
  33% { 
    transform: translateY(-20px) rotate(120deg); 
  }
  66% { 
    transform: translateY(10px) rotate(240deg); 
  }
}

@keyframes glow {
  0% { 
    text-shadow: 0 0 30px rgba(0, 245, 212, 0.3); 
  }
  100% { 
    text-shadow: 0 0 50px rgba(0, 245, 212, 0.6), 0 0 70px rgba(255, 0, 228, 0.3); 
  }
}

// 响应式设计
@media (max-width: 768px) {
  .error-code {
    font-size: 6rem;
  }
  
  .error-title {
    font-size: 2rem;
  }
  
  .error-subtitle {
    font-size: 1rem;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .back-btn, .home-btn {
    width: 200px;
    justify-content: center;
  }
  
  .links-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .helpful-links {
    padding: 24px;
  }
}

@media (max-width: 480px) {
  .error-content {
    padding: 20px 15px;
  }
  
  .error-code {
    font-size: 4rem;
  }
  
  .links-grid {
    grid-template-columns: 1fr;
  }
}
</style>
