<template>
  <div class="server-error">
    <div class="container">
      <div class="error-content">
        <!-- 动画背景 -->
        <div class="error-background">
          <div class="glitch-lines">
            <div class="line line-1"></div>
            <div class="line line-2"></div>
            <div class="line line-3"></div>
          </div>
        </div>
        
        <!-- 错误信息 -->
        <div class="error-info">
          <div class="error-code glitch" data-text="500">500</div>
          <div class="error-title">服务器开小差了</div>
          <div class="error-subtitle">
            服务器遇到了一些问题，我们正在努力修复中...
          </div>
          
          <div class="error-illustration">
            <div class="server-icon">🔧</div>
            <div class="status-text">系统维护中</div>
            <div class="loading-dots">
              <span class="dot"></span>
              <span class="dot"></span>
              <span class="dot"></span>
            </div>
          </div>
          
          <div class="error-details">
            <div class="detail-item">
              <span class="detail-label">错误时间：</span>
              <span class="detail-value">{{ currentTime }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">错误代码：</span>
              <span class="detail-value">INTERNAL_SERVER_ERROR</span>
            </div>
          </div>
          
          <div class="error-actions">
            <button class="retry-btn" @click="retryRequest" :disabled="isRetrying">
              <span v-if="isRetrying" class="loading-spinner"></span>
              <span class="btn-icon">🔄</span>
              <span class="btn-text">{{ isRetrying ? '重试中...' : '重新尝试' }}</span>
            </button>
            
            <router-link to="/" class="home-btn">
              <span class="btn-icon">🏠</span>
              <span class="btn-text">回到首页</span>
            </router-link>
          </div>
          
          <div class="help-section">
            <h4 class="help-title">如果问题持续存在：</h4>
            <div class="help-options">
              <div class="help-option">
                <span class="option-icon">📧</span>
                <span class="option-text">发送邮件至 <EMAIL></span>
              </div>
              <div class="help-option">
                <span class="option-icon">💬</span>
                <span class="option-text">联系在线客服</span>
              </div>
              <div class="help-option">
                <span class="option-icon">🐛</span>
                <span class="option-text">报告此问题</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const currentTime = ref('')
const isRetrying = ref(false)

const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleString('zh-CN')
}

const retryRequest = async () => {
  isRetrying.value = true
  
  try {
    // 模拟重试请求
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 刷新页面或重新加载
    window.location.reload()
  } catch (error) {
    console.error('重试失败:', error)
  } finally {
    isRetrying.value = false
  }
}

onMounted(() => {
  updateTime()
  // 每秒更新时间
  setInterval(updateTime, 1000)
})
</script>

<style lang="scss" scoped>
.server-error {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(180deg, #1A1A2E 0%, #16213E 50%, #0F0F1E 100%);
  position: relative;
  overflow: hidden;
}

.error-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 600px;
  padding: 40px 20px;
}

.error-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.glitch-lines {
  position: relative;
  width: 100%;
  height: 100%;
}

.line {
  position: absolute;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #FF00E4, transparent);
  animation: glitch-move 3s linear infinite;
  
  &.line-1 {
    top: 20%;
    animation-delay: 0s;
  }
  
  &.line-2 {
    top: 50%;
    animation-delay: 1s;
  }
  
  &.line-3 {
    top: 80%;
    animation-delay: 2s;
  }
}

.error-info {
  position: relative;
  z-index: 3;
}

.error-code {
  font-size: 8rem;
  font-weight: 900;
  color: #FF4444;
  margin-bottom: 16px;
  text-shadow: 0 0 30px rgba(255, 68, 68, 0.5);
  position: relative;
}

.glitch {
  position: relative;
  
  &::before,
  &::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
  
  &::before {
    animation: glitch-1 0.5s infinite;
    color: #00F5D4;
    z-index: -1;
  }
  
  &::after {
    animation: glitch-2 0.5s infinite;
    color: #FF00E4;
    z-index: -2;
  }
}

.error-title {
  font-size: 2.5rem;
  color: #fff;
  font-weight: 700;
  margin-bottom: 16px;
}

.error-subtitle {
  font-size: 1.2rem;
  color: #B0B0B0;
  margin-bottom: 40px;
  line-height: 1.5;
}

.error-illustration {
  margin-bottom: 32px;
}

.server-icon {
  font-size: 4rem;
  margin-bottom: 12px;
  filter: drop-shadow(0 0 20px rgba(255, 68, 68, 0.5));
  animation: pulse 2s ease-in-out infinite;
}

.status-text {
  font-size: 1.1rem;
  color: #FF4444;
  font-weight: 600;
  margin-bottom: 16px;
}

.loading-dots {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.dot {
  width: 8px;
  height: 8px;
  background: #FF4444;
  border-radius: 50%;
  animation: dot-bounce 1.4s ease-in-out infinite both;
  
  &:nth-child(1) { animation-delay: -0.32s; }
  &:nth-child(2) { animation-delay: -0.16s; }
  &:nth-child(3) { animation-delay: 0s; }
}

.error-details {
  background: rgba(255, 68, 68, 0.1);
  border: 1px solid rgba(255, 68, 68, 0.2);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 32px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.detail-label {
  color: #B0B0B0;
  font-size: 0.9rem;
}

.detail-value {
  color: #fff;
  font-weight: 500;
  font-size: 0.9rem;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 48px;
  flex-wrap: wrap;
}

.retry-btn, .home-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.retry-btn {
  background: rgba(255, 68, 68, 0.2);
  color: #FF4444;
  border: 1px solid rgba(255, 68, 68, 0.3);
  
  &:hover:not(:disabled) {
    background: rgba(255, 68, 68, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 68, 68, 0.3);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.home-btn {
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 245, 212, 0.4);
  }
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 68, 68, 0.3);
  border-top: 2px solid #FF4444;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.help-section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 32px;
  backdrop-filter: blur(10px);
}

.help-title {
  font-size: 1.2rem;
  color: #fff;
  font-weight: 600;
  margin-bottom: 24px;
}

.help-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.help-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(0, 245, 212, 0.1);
    border-color: rgba(0, 245, 212, 0.3);
  }
}

.option-icon {
  font-size: 1.5rem;
}

.option-text {
  color: #E0E0E0;
  font-size: 0.95rem;
}

// 动画
@keyframes glitch-move {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes glitch-1 {
  0%, 14%, 15%, 49%, 50%, 99%, 100% { 
    transform: translate(0); 
  }
  15%, 49% { 
    transform: translate(-2px, -1px); 
  }
}

@keyframes glitch-2 {
  0%, 20%, 21%, 62%, 63%, 99%, 100% { 
    transform: translate(0); 
  }
  21%, 62% { 
    transform: translate(2px, 1px); 
  }
}

@keyframes pulse {
  0%, 100% { 
    transform: scale(1); 
  }
  50% { 
    transform: scale(1.1); 
  }
}

@keyframes dot-bounce {
  0%, 80%, 100% { 
    transform: scale(0); 
  } 
  40% { 
    transform: scale(1); 
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 响应式设计
@media (max-width: 768px) {
  .error-code {
    font-size: 6rem;
  }
  
  .error-title {
    font-size: 2rem;
  }
  
  .error-subtitle {
    font-size: 1rem;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .retry-btn, .home-btn {
    width: 200px;
    justify-content: center;
  }
  
  .help-section {
    padding: 24px;
  }
}

@media (max-width: 480px) {
  .error-content {
    padding: 20px 15px;
  }
  
  .error-code {
    font-size: 4rem;
  }
  
  .detail-item {
    flex-direction: column;
    gap: 4px;
    text-align: left;
  }
}
</style>
