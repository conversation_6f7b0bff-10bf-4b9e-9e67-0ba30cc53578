<template>
  <div class="create-post">
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">发布动态</h1>
        <p class="page-subtitle">分享你的游戏心得和精彩瞬间</p>
      </div>

      <div class="create-form">
        <form @submit.prevent="handleSubmit">
          <div class="form-group">
            <label class="form-label">动态类型</label>
            <select v-model="formData.type" class="form-select" required>
              <option value="">请选择类型</option>
              <option value="review">测评</option>
              <option value="share">分享</option>
              <option value="guide">攻略</option>
              <option value="discussion">讨论</option>
            </select>
          </div>
          
          <div class="form-group">
            <label class="form-label">标题（可选）</label>
            <input 
              v-model="formData.title"
              type="text" 
              class="form-input"
              placeholder="给你的动态起个标题..."
              maxlength="100"
            />
          </div>
          
          <div class="form-group">
            <label class="form-label">内容 *</label>
            <textarea 
              v-model="formData.content"
              class="form-textarea"
              placeholder="分享你的想法..."
              rows="8"
              maxlength="1000"
              required
            ></textarea>
            <div class="char-count">{{ formData.content.length }}/1000</div>
          </div>
          
          <div class="form-actions">
            <button type="button" class="cancel-btn" @click="$router.go(-1)">
              取消
            </button>
            <button 
              type="submit" 
              class="submit-btn"
              :disabled="!isFormValid || isSubmitting"
            >
              <span v-if="isSubmitting" class="loading-spinner"></span>
              <span>{{ isSubmitting ? '发布中...' : '发布动态' }}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const formData = ref({
  type: '',
  title: '',
  content: ''
})

const isSubmitting = ref(false)

const isFormValid = computed(() => {
  return formData.value.type && formData.value.content.trim()
})

const handleSubmit = async () => {
  if (!isFormValid.value || isSubmitting.value) return
  
  try {
    isSubmitting.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    console.log('发布动态:', formData.value)
    router.push('/feed')
  } catch (error) {
    console.error('发布动态失败:', error)
  } finally {
    isSubmitting.value = false
  }
}
</script>

<style lang="scss" scoped>
.create-post {
  min-height: 100vh;
  padding: 40px 0;
  background: linear-gradient(180deg, #1A1A2E 0%, #16213E 50%, #0F0F1E 100%);
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 2.5rem;
  color: #fff;
  font-weight: 700;
  margin-bottom: 12px;
}

.page-subtitle {
  color: #B0B0B0;
  font-size: 1.1rem;
}

.create-form {
  max-width: 600px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 245, 212, 0.1);
  border-radius: 16px;
  padding: 32px;
}

.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  font-size: 0.9rem;
  color: #E0E0E0;
  font-weight: 500;
  margin-bottom: 8px;
}

.form-input, .form-select, .form-textarea {
  width: 100%;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 8px;
  color: #fff;
  font-size: 0.9rem;
  
  &::placeholder {
    color: #666;
  }
  
  &:focus {
    outline: none;
    border-color: #00F5D4;
    box-shadow: 0 0 0 2px rgba(0, 245, 212, 0.1);
  }
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.char-count {
  text-align: right;
  font-size: 0.75rem;
  color: #666;
  margin-top: 4px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 32px;
}

.cancel-btn, .submit-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: rgba(255, 255, 255, 0.05);
  color: #B0B0B0;
  border: 1px solid rgba(255, 255, 255, 0.1);
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
  }
}

.submit-btn {
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  display: flex;
  align-items: center;
  gap: 8px;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 245, 212, 0.4);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(26, 26, 46, 0.3);
  border-top: 2px solid #1A1A2E;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
