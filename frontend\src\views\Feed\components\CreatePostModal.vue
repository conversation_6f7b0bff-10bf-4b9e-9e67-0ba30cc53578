<template>
  <div class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h2 class="modal-title">发布动态</h2>
        <button class="close-button" @click="$emit('close')">✕</button>
      </div>
      
      <div class="modal-content">
        <form @submit.prevent="handleSubmit">
          <div class="form-group">
            <label class="form-label">动态类型</label>
            <select v-model="formData.type" class="form-select" required>
              <option value="">请选择类型</option>
              <option value="review">测评</option>
              <option value="share">分享</option>
              <option value="guide">攻略</option>
              <option value="discussion">讨论</option>
            </select>
          </div>
          
          <div class="form-group">
            <label class="form-label">标题（可选）</label>
            <input 
              v-model="formData.title"
              type="text" 
              class="form-input"
              placeholder="给你的动态起个标题..."
              maxlength="100"
            />
          </div>
          
          <div class="form-group">
            <label class="form-label">内容 *</label>
            <textarea 
              v-model="formData.content"
              class="form-textarea"
              placeholder="分享你的想法..."
              rows="6"
              maxlength="1000"
              required
            ></textarea>
            <div class="char-count">{{ formData.content.length }}/1000</div>
          </div>
        </form>
      </div>
      
      <div class="modal-footer">
        <button type="button" class="cancel-button" @click="$emit('close')">
          取消
        </button>
        <button 
          type="submit" 
          class="publish-button"
          :disabled="!isFormValid || isPublishing"
          @click="handleSubmit"
        >
          <span v-if="isPublishing" class="loading-spinner"></span>
          <span>{{ isPublishing ? '发布中...' : '发布' }}</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Emits {
  (e: 'close'): void
  (e: 'created', post: any): void
}

const emit = defineEmits<Emits>()

const isPublishing = ref(false)
const formData = ref({
  type: '',
  title: '',
  content: ''
})

const isFormValid = computed(() => {
  return formData.value.type && formData.value.content.trim()
})

const handleOverlayClick = () => {
  emit('close')
}

const handleSubmit = async () => {
  if (!isFormValid.value || isPublishing.value) return
  
  try {
    isPublishing.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const newPost = {
      id: Date.now(),
      ...formData.value,
      author: {
        id: 1,
        nickname: '当前用户',
        avatar: 'https://picsum.photos/48/48?random=99',
        level: 10
      },
      likeCount: 0,
      commentCount: 0,
      shareCount: 0,
      isLiked: false,
      isBookmarked: false,
      isHot: false,
      isPinned: false,
      createdAt: new Date().toISOString()
    }
    
    emit('created', newPost)
  } catch (error) {
    console.error('发布动态失败:', error)
  } finally {
    isPublishing.value = false
  }
}
</script>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-container {
  background: rgba(26, 26, 46, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 16px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #fff;
}

.close-button {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: #888;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.2);
    color: #fff;
  }
}

.modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 0.9rem;
  color: #B0B0B0;
  font-weight: 500;
  margin-bottom: 8px;
}

.form-input, .form-select, .form-textarea {
  width: 100%;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 8px;
  color: #fff;
  font-size: 0.9rem;
  
  &::placeholder {
    color: #666;
  }
  
  &:focus {
    outline: none;
    border-color: #00F5D4;
    box-shadow: 0 0 0 2px rgba(0, 245, 212, 0.1);
  }
}

.char-count {
  text-align: right;
  font-size: 0.75rem;
  color: #666;
  margin-top: 4px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.cancel-button, .publish-button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-button {
  background: rgba(255, 255, 255, 0.05);
  color: #B0B0B0;
  border: 1px solid rgba(255, 255, 255, 0.1);
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
  }
}

.publish-button {
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  display: flex;
  align-items: center;
  gap: 8px;
  
  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 245, 212, 0.3);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(26, 26, 46, 0.3);
  border-top: 2px solid #1A1A2E;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
