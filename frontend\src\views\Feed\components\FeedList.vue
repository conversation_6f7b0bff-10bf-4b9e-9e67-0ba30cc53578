<template>
  <div class="feed-list">
    <!-- 筛选栏 -->
    <div class="feed-filters">
      <div class="filter-tabs">
        <button 
          v-for="filter in filters" 
          :key="filter.key"
          class="filter-tab"
          :class="{ active: activeFilter === filter.key }"
          @click="setActiveFilter(filter.key)"
        >
          <span class="tab-icon">{{ filter.icon }}</span>
          <span class="tab-text">{{ filter.label }}</span>
        </button>
      </div>
      
      <div class="filter-actions">
        <button class="refresh-btn" @click="refreshFeed" title="刷新">
          <span class="refresh-icon" :class="{ spinning: isRefreshing }">🔄</span>
        </button>
        <button class="create-btn" @click="$emit('create-post')">
          <span class="btn-icon">✏️</span>
          <span class="btn-text">发布动态</span>
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading && posts.length === 0" class="loading-container">
      <div class="loading-spinner"></div>
      <span class="loading-text">正在加载动态...</span>
    </div>

    <!-- 空状态 -->
    <div v-else-if="posts.length === 0" class="empty-state">
      <div class="empty-icon">📝</div>
      <h3 class="empty-title">暂无动态</h3>
      <p class="empty-description">
        还没有人发布动态，
        <button class="create-link" @click="$emit('create-post')">
          成为第一个分享者
        </button>
      </p>
    </div>

    <!-- 动态列表 -->
    <div v-else class="posts-container">
      <div 
        v-for="post in posts" 
        :key="post.id"
        class="post-card"
        :class="{ 'post-hot': post.isHot, 'post-pinned': post.isPinned }"
      >
        <!-- 置顶标识 -->
        <div v-if="post.isPinned" class="pinned-badge">
          📌 置顶
        </div>
        
        <!-- 热门标识 -->
        <div v-if="post.isHot" class="hot-badge">
          🔥 热门
        </div>

        <!-- 用户信息 -->
        <div class="post-header">
          <img 
            :src="post.author.avatar" 
            :alt="post.author.nickname"
            class="author-avatar"
            @click="viewUserProfile(post.author.id)"
          />
          <div class="author-info">
            <div class="author-name" @click="viewUserProfile(post.author.id)">
              {{ post.author.nickname }}
            </div>
            <div class="post-meta">
              <span class="post-time">{{ formatTime(post.createdAt) }}</span>
              <span class="post-type" :class="`type-${post.type}`">
                {{ getPostTypeText(post.type) }}
              </span>
              <span v-if="post.location" class="post-location">
                📍 {{ post.location }}
              </span>
            </div>
          </div>
          <div class="post-actions">
            <button class="action-btn" @click="togglePostMenu(post.id)">
              ⋯
            </button>
            <!-- 下拉菜单 -->
            <div v-if="showMenuId === post.id" class="post-menu">
              <button @click="sharePost(post)">分享</button>
              <button @click="reportPost(post)">举报</button>
              <button v-if="isMyPost(post)" @click="editPost(post)">编辑</button>
              <button v-if="isMyPost(post)" @click="deletePost(post)">删除</button>
            </div>
          </div>
        </div>

        <!-- 关联剧本 -->
        <div v-if="post.script" class="related-script" @click="viewScript(post.script.id)">
          <img 
            :src="post.script.coverImage" 
            :alt="post.script.title"
            class="script-thumb"
          />
          <div class="script-info">
            <span class="script-name">{{ post.script.title }}</span>
            <span class="script-genre">{{ post.script.genre }}</span>
          </div>
        </div>

        <!-- 内容 -->
        <div class="post-content">
          <h3 v-if="post.title" class="post-title">{{ post.title }}</h3>
          <div class="post-text" :class="{ expanded: expandedPosts.includes(post.id) }">
            {{ post.content }}
          </div>
          <button 
            v-if="post.content.length > 200 && !expandedPosts.includes(post.id)"
            class="expand-btn"
            @click="expandPost(post.id)"
          >
            展开全文
          </button>
        </div>

        <!-- 图片/视频 -->
        <div v-if="post.media && post.media.length > 0" class="post-media">
          <div 
            class="media-grid"
            :class="`grid-${Math.min(post.media.length, 9)}`"
          >
            <div 
              v-for="(media, index) in post.media.slice(0, 9)" 
              :key="index"
              class="media-item"
              @click="viewMedia(post.media, index)"
            >
              <img 
                v-if="media.type === 'image'"
                :src="media.url"
                :alt="`图片${index + 1}`"
                class="media-image"
              />
              <video 
                v-else-if="media.type === 'video'"
                :src="media.url"
                class="media-video"
                controls
              />
              <div 
                v-if="index === 8 && post.media.length > 9" 
                class="more-media"
              >
                +{{ post.media.length - 9 }}
              </div>
            </div>
          </div>
        </div>

        <!-- 互动数据 -->
        <div class="post-stats">
          <button 
            class="stat-btn like-btn"
            :class="{ liked: post.isLiked }"
            @click="toggleLike(post)"
          >
            <span class="stat-icon">{{ post.isLiked ? '❤️' : '🤍' }}</span>
            <span class="stat-count">{{ formatCount(post.likeCount) }}</span>
          </button>
          
          <button class="stat-btn comment-btn" @click="toggleComments(post.id)">
            <span class="stat-icon">💬</span>
            <span class="stat-count">{{ formatCount(post.commentCount) }}</span>
          </button>
          
          <button class="stat-btn share-btn" @click="sharePost(post)">
            <span class="stat-icon">📤</span>
            <span class="stat-count">{{ formatCount(post.shareCount) }}</span>
          </button>
          
          <button class="stat-btn bookmark-btn" @click="toggleBookmark(post)">
            <span class="stat-icon">{{ post.isBookmarked ? '🔖' : '📑' }}</span>
          </button>
        </div>

        <!-- 评论区 -->
        <div v-if="showCommentsId === post.id" class="comments-section">
          <PostComments 
            :post-id="post.id"
            :comments="post.comments || []"
            @submit-comment="handleSubmitComment"
            @load-more="loadMoreComments"
          />
        </div>
      </div>
    </div>

    <!-- 加载更多 -->
    <div v-if="hasMore" class="load-more">
      <button 
        v-if="!isLoadingMore"
        class="load-more-btn"
        @click="loadMore"
      >
        加载更多
      </button>
      <div v-else class="loading-more">
        <div class="loading-spinner small"></div>
        <span>加载中...</span>
      </div>
    </div>

    <!-- 无限滚动触发器 -->
    <div ref="loadTrigger" class="load-trigger"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import PostComments from './PostComments.vue'

// 类型定义
interface Author {
  id: number
  nickname: string
  avatar: string
  level: number
}

interface Script {
  id: number
  title: string
  coverImage: string
  genre: string
}

interface Media {
  type: 'image' | 'video'
  url: string
  thumbnail?: string
}

interface Post {
  id: number
  title?: string
  content: string
  type: 'review' | 'share' | 'guide' | 'discussion'
  author: Author
  script?: Script
  media?: Media[]
  location?: string
  likeCount: number
  commentCount: number
  shareCount: number
  isLiked: boolean
  isBookmarked: boolean
  isHot: boolean
  isPinned: boolean
  createdAt: string
  comments?: any[]
}

// Props
interface Props {
  posts: Post[]
  isLoading?: boolean
  hasMore?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
  hasMore: true
})

// Emits
interface Emits {
  (e: 'create-post'): void
  (e: 'load-more'): void
  (e: 'refresh'): void
  (e: 'filter-change', filter: string): void
  (e: 'like-post', postId: number): void
  (e: 'bookmark-post', postId: number): void
}

const emit = defineEmits<Emits>()

// 路由
const router = useRouter()

// 响应式数据
const activeFilter = ref('hot')
const isRefreshing = ref(false)
const isLoadingMore = ref(false)
const expandedPosts = ref<number[]>([])
const showCommentsId = ref<number | null>(null)
const showMenuId = ref<number | null>(null)
const loadTrigger = ref<HTMLElement>()

// 筛选选项
const filters = [
  { key: 'hot', label: '热门', icon: '🔥' },
  { key: 'latest', label: '最新', icon: '⏰' },
  { key: 'following', label: '关注', icon: '👥' },
  { key: 'review', label: '测评', icon: '⭐' },
  { key: 'guide', label: '攻略', icon: '📖' }
]

// 方法
const setActiveFilter = (filterKey: string) => {
  activeFilter.value = filterKey
  emit('filter-change', filterKey)
}

const refreshFeed = async () => {
  isRefreshing.value = true
  emit('refresh')
  setTimeout(() => {
    isRefreshing.value = false
  }, 1000)
}

const loadMore = () => {
  if (!isLoadingMore.value && props.hasMore) {
    isLoadingMore.value = true
    emit('load-more')
    setTimeout(() => {
      isLoadingMore.value = false
    }, 1000)
  }
}

const getPostTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    review: '测评',
    share: '分享',
    guide: '攻略',
    discussion: '讨论'
  }
  return typeMap[type] || '动态'
}

const formatTime = (timeString: string): string => {
  const now = new Date()
  const postTime = new Date(timeString)
  const diff = now.getTime() - postTime.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days > 0) return `${days}天前`
  if (hours > 0) return `${hours}小时前`
  if (minutes > 0) return `${minutes}分钟前`
  return '刚刚'
}

const formatCount = (count: number): string => {
  if (count >= 10000) {
    return (count / 10000).toFixed(1) + 'w'
  }
  if (count >= 1000) {
    return (count / 1000).toFixed(1) + 'k'
  }
  return count.toString()
}

const expandPost = (postId: number) => {
  expandedPosts.value.push(postId)
}

const toggleComments = (postId: number) => {
  showCommentsId.value = showCommentsId.value === postId ? null : postId
}

const togglePostMenu = (postId: number) => {
  showMenuId.value = showMenuId.value === postId ? null : postId
}

const toggleLike = (post: Post) => {
  post.isLiked = !post.isLiked
  post.likeCount += post.isLiked ? 1 : -1
  emit('like-post', post.id)
}

const toggleBookmark = (post: Post) => {
  post.isBookmarked = !post.isBookmarked
  emit('bookmark-post', post.id)
}

const viewUserProfile = (userId: number) => {
  router.push(`/user/${userId}`)
}

const viewScript = (scriptId: number) => {
  router.push(`/scripts/${scriptId}`)
}

const viewMedia = (mediaList: Media[], index: number) => {
  // 打开媒体查看器
  console.log('查看媒体:', mediaList, index)
}

const sharePost = (post: Post) => {
  // 分享功能
  console.log('分享动态:', post.id)
}

const reportPost = (post: Post) => {
  // 举报功能
  console.log('举报动态:', post.id)
}

const editPost = (post: Post) => {
  // 编辑动态
  console.log('编辑动态:', post.id)
}

const deletePost = (post: Post) => {
  // 删除动态
  console.log('删除动态:', post.id)
}

const isMyPost = (post: Post): boolean => {
  // 判断是否是当前用户的动态
  return false // 这里应该根据实际登录用户判断
}

const handleSubmitComment = (commentData: any) => {
  console.log('提交评论:', commentData)
}

const loadMoreComments = (postId: number) => {
  console.log('加载更多评论:', postId)
}

// 无限滚动
const setupInfiniteScroll = () => {
  const observer = new IntersectionObserver(
    (entries) => {
      if (entries[0].isIntersecting && props.hasMore && !isLoadingMore.value) {
        loadMore()
      }
    },
    { threshold: 0.1 }
  )
  
  if (loadTrigger.value) {
    observer.observe(loadTrigger.value)
  }
  
  return observer
}

// 生命周期
let observer: IntersectionObserver | null = null

onMounted(() => {
  observer = setupInfiniteScroll()
  
  // 点击外部关闭菜单
  document.addEventListener('click', (e) => {
    if (!(e.target as Element).closest('.post-actions')) {
      showMenuId.value = null
    }
  })
})

onUnmounted(() => {
  if (observer) {
    observer.disconnect()
  }
})
</script>

<style lang="scss" scoped>
.feed-list {
  max-width: 800px;
  margin: 0 auto;
}

.feed-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 245, 212, 0.1);
  border-radius: 16px;
}

.filter-tabs {
  display: flex;
  gap: 4px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 4px;
}

.filter-tab {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: #B0B0B0;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.05);
    color: #fff;
  }
  
  &.active {
    background: rgba(0, 245, 212, 0.1);
    color: #00F5D4;
    border: 1px solid rgba(0, 245, 212, 0.3);
  }
}

.filter-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.refresh-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  color: #B0B0B0;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(0, 245, 212, 0.1);
    color: #00F5D4;
  }
  
  .refresh-icon.spinning {
    animation: spin 1s linear infinite;
  }
}

.create-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  border: none;
  border-radius: 8px;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 245, 212, 0.3);
  }
}

.loading-container, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  gap: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 245, 212, 0.1);
  border-top: 3px solid #00F5D4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  
  &.small {
    width: 20px;
    height: 20px;
    border-width: 2px;
  }
}

.empty-icon {
  font-size: 3rem;
  opacity: 0.5;
  margin-bottom: 16px;
}

.empty-title {
  font-size: 1.25rem;
  color: #fff;
  margin-bottom: 8px;
}

.empty-description {
  color: #B0B0B0;
  text-align: center;
}

.create-link {
  color: #00F5D4;
  background: none;
  border: none;
  text-decoration: underline;
  cursor: pointer;
  font-size: inherit;
  
  &:hover {
    color: #FF00E4;
  }
}

.posts-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.post-card {
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 245, 212, 0.1);
  border-radius: 16px;
  padding: 20px;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: rgba(0, 245, 212, 0.3);
  }
  
  &.post-hot {
    border-color: rgba(255, 68, 68, 0.4);
  }
  
  &.post-pinned {
    border-color: rgba(255, 193, 7, 0.4);
  }
}

.pinned-badge, .hot-badge {
  position: absolute;
  top: -8px;
  right: 16px;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
}

.pinned-badge {
  background: linear-gradient(135deg, #FFC107, #FF9800);
  color: #1A1A2E;
}

.hot-badge {
  background: linear-gradient(135deg, #FF4444, #FF6B6B);
  color: #fff;
  animation: glow 2s ease-in-out infinite alternate;
}

.post-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
}

.author-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.3s ease;
  
  &:hover {
    transform: scale(1.05);
  }
}

.author-info {
  flex: 1;
}

.author-name {
  font-size: 1rem;
  color: #fff;
  font-weight: 600;
  cursor: pointer;
  margin-bottom: 4px;
  
  &:hover {
    color: #00F5D4;
  }
}

.post-meta {
  display: flex;
  gap: 12px;
  align-items: center;
  font-size: 0.8rem;
  color: #888;
}

.post-type {
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 0.7rem;
  
  &.type-review {
    background: rgba(0, 245, 212, 0.1);
    color: #00F5D4;
  }
  
  &.type-share {
    background: rgba(255, 0, 228, 0.1);
    color: #FF00E4;
  }
  
  &.type-guide {
    background: rgba(255, 193, 7, 0.1);
    color: #FFC107;
  }
  
  &.type-discussion {
    background: rgba(156, 39, 176, 0.1);
    color: #9C27B0;
  }
}

.post-actions {
  position: relative;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  color: #888;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
  }
}

.post-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: rgba(26, 26, 46, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 8px;
  padding: 8px 0;
  min-width: 120px;
  z-index: 10;
  
  button {
    width: 100%;
    padding: 8px 16px;
    background: none;
    border: none;
    color: #B0B0B0;
    text-align: left;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(255, 255, 255, 0.05);
      color: #fff;
    }
  }
}

.related-script {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(0, 245, 212, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.1);
  border-radius: 8px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(0, 245, 212, 0.1);
    border-color: rgba(0, 245, 212, 0.3);
  }
}

.script-thumb {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  object-fit: cover;
}

.script-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.script-name {
  font-size: 0.9rem;
  color: #00F5D4;
  font-weight: 500;
}

.script-genre {
  font-size: 0.75rem;
  color: #888;
}

.post-content {
  margin-bottom: 16px;
}

.post-title {
  font-size: 1.1rem;
  color: #fff;
  font-weight: 600;
  margin-bottom: 8px;
  line-height: 1.4;
}

.post-text {
  color: #E0E0E0;
  line-height: 1.6;
  
  &:not(.expanded) {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

.expand-btn {
  color: #00F5D4;
  background: none;
  border: none;
  font-size: 0.85rem;
  cursor: pointer;
  margin-top: 8px;
  
  &:hover {
    color: #FF00E4;
  }
}

.post-media {
  margin-bottom: 16px;
}

.media-grid {
  display: grid;
  gap: 8px;
  border-radius: 8px;
  overflow: hidden;
  
  &.grid-1 {
    grid-template-columns: 1fr;
  }
  
  &.grid-2, &.grid-4 {
    grid-template-columns: 1fr 1fr;
  }
  
  &.grid-3, &.grid-5, &.grid-6 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  &.grid-7, &.grid-8, &.grid-9 {
    grid-template-columns: repeat(3, 1fr);
  }
}

.media-item {
  position: relative;
  aspect-ratio: 1;
  overflow: hidden;
  border-radius: 6px;
  cursor: pointer;
}

.media-image, .media-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
  
  &:hover {
    transform: scale(1.05);
  }
}

.more-media {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 1.2rem;
  font-weight: 600;
}

.post-stats {
  display: flex;
  gap: 20px;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background: none;
  border: none;
  color: #888;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    color: #00F5D4;
  }
  
  &.liked {
    color: #FF4444;
  }
}

.comments-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.load-more {
  display: flex;
  justify-content: center;
  padding: 24px;
}

.load-more-btn {
  padding: 12px 24px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 8px;
  color: #00F5D4;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(0, 245, 212, 0.1);
    border-color: #00F5D4;
  }
}

.loading-more {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #888;
  font-size: 0.9rem;
}

.load-trigger {
  height: 1px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes glow {
  0% { box-shadow: 0 0 10px rgba(255, 68, 68, 0.5); }
  100% { box-shadow: 0 0 20px rgba(255, 68, 68, 0.8); }
}

@media (max-width: 768px) {
  .feed-list {
    padding: 0 15px;
  }
  
  .feed-filters {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .filter-tabs {
    overflow-x: auto;
    padding: 4px;
  }
  
  .filter-actions {
    justify-content: center;
  }
  
  .post-card {
    padding: 16px;
  }
  
  .post-header {
    gap: 8px;
  }
  
  .author-avatar {
    width: 40px;
    height: 40px;
  }
  
  .post-meta {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .post-stats {
    gap: 16px;
  }
  
  .media-grid {
    &.grid-3, &.grid-5, &.grid-6,
    &.grid-7, &.grid-8, &.grid-9 {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}
</style>
