<template>
  <div class="post-comments">
    <div class="comments-header">
      <h4 class="comments-title">评论 ({{ comments.length }})</h4>
    </div>
    
    <!-- 评论输入框 -->
    <div class="comment-input-section">
      <div class="input-wrapper">
        <img 
          src="https://picsum.photos/32/32?random=99" 
          alt="用户头像"
          class="user-avatar"
        />
        <div class="input-container">
          <textarea 
            v-model="newComment"
            placeholder="写下你的评论..."
            class="comment-input"
            rows="3"
            maxlength="500"
          ></textarea>
          <div class="input-actions">
            <span class="char-count">{{ newComment.length }}/500</span>
            <button 
              class="submit-btn"
              :disabled="!newComment.trim()"
              @click="submitComment"
            >
              发布
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 评论列表 -->
    <div class="comments-list">
      <div 
        v-for="comment in comments" 
        :key="comment.id"
        class="comment-item"
      >
        <img 
          :src="comment.user.avatar" 
          :alt="comment.user.nickname"
          class="comment-avatar"
        />
        <div class="comment-content">
          <div class="comment-header">
            <span class="comment-author">{{ comment.user.nickname }}</span>
            <span class="comment-time">{{ formatTime(comment.createdAt) }}</span>
          </div>
          <div class="comment-text">{{ comment.content }}</div>
          <div class="comment-actions">
            <button 
              class="action-btn like-btn"
              :class="{ liked: comment.isLiked }"
              @click="toggleLike(comment)"
            >
              <span class="action-icon">{{ comment.isLiked ? '❤️' : '🤍' }}</span>
              <span class="action-count">{{ comment.likeCount || '' }}</span>
            </button>
            <button class="action-btn reply-btn" @click="replyToComment(comment)">
              回复
            </button>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-if="comments.length === 0" class="empty-comments">
        <div class="empty-icon">💬</div>
        <div class="empty-text">还没有评论，来发表第一个评论吧！</div>
      </div>
    </div>
    
    <!-- 加载更多 -->
    <div v-if="hasMore" class="load-more">
      <button class="load-more-btn" @click="$emit('load-more')">
        加载更多评论
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Comment {
  id: number
  user: {
    id: number
    nickname: string
    avatar: string
  }
  content: string
  likeCount: number
  isLiked: boolean
  createdAt: string
}

interface Props {
  postId: number
  comments: Comment[]
  hasMore?: boolean
}

interface Emits {
  (e: 'submit-comment', comment: any): void
  (e: 'load-more'): void
}

const props = withDefaults(defineProps<Props>(), {
  hasMore: false
})

const emit = defineEmits<Emits>()

const newComment = ref('')

const formatTime = (timeString: string): string => {
  const now = new Date()
  const time = new Date(timeString)
  const diff = now.getTime() - time.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days > 0) return `${days}天前`
  if (hours > 0) return `${hours}小时前`
  if (minutes > 0) return `${minutes}分钟前`
  return '刚刚'
}

const submitComment = () => {
  if (!newComment.value.trim()) return
  
  const comment = {
    id: Date.now(),
    user: {
      id: 1,
      nickname: '当前用户',
      avatar: 'https://picsum.photos/32/32?random=99'
    },
    content: newComment.value.trim(),
    likeCount: 0,
    isLiked: false,
    createdAt: new Date().toISOString()
  }
  
  emit('submit-comment', comment)
  newComment.value = ''
}

const toggleLike = (comment: Comment) => {
  comment.isLiked = !comment.isLiked
  comment.likeCount += comment.isLiked ? 1 : -1
}

const replyToComment = (comment: Comment) => {
  newComment.value = `@${comment.user.nickname} `
}
</script>

<style lang="scss" scoped>
.post-comments {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  padding: 16px;
}

.comments-header {
  margin-bottom: 16px;
}

.comments-title {
  font-size: 1rem;
  color: #fff;
  font-weight: 600;
}

.comment-input-section {
  margin-bottom: 20px;
}

.input-wrapper {
  display: flex;
  gap: 12px;
}

.user-avatar, .comment-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

.input-container {
  flex: 1;
}

.comment-input {
  width: 100%;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 8px;
  color: #fff;
  font-size: 0.85rem;
  resize: vertical;
  min-height: 80px;
  
  &::placeholder {
    color: #666;
  }
  
  &:focus {
    outline: none;
    border-color: #00F5D4;
    box-shadow: 0 0 0 2px rgba(0, 245, 212, 0.1);
  }
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.char-count {
  font-size: 0.75rem;
  color: #666;
}

.submit-btn {
  padding: 6px 16px;
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  border: none;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 245, 212, 0.3);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.comment-item {
  display: flex;
  gap: 12px;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.comment-author {
  font-size: 0.85rem;
  color: #00F5D4;
  font-weight: 500;
}

.comment-time {
  font-size: 0.75rem;
  color: #666;
}

.comment-text {
  color: #E0E0E0;
  font-size: 0.85rem;
  line-height: 1.5;
  margin-bottom: 8px;
}

.comment-actions {
  display: flex;
  gap: 16px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  background: none;
  border: none;
  color: #888;
  font-size: 0.75rem;
  cursor: pointer;
  transition: color 0.3s ease;
  
  &:hover {
    color: #00F5D4;
  }
  
  &.liked {
    color: #FF4444;
  }
}

.empty-comments {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.empty-icon {
  font-size: 2rem;
  margin-bottom: 8px;
}

.empty-text {
  font-size: 0.9rem;
}

.load-more {
  text-align: center;
  margin-top: 16px;
}

.load-more-btn {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 6px;
  color: #00F5D4;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(0, 245, 212, 0.1);
    border-color: #00F5D4;
  }
}
</style>
