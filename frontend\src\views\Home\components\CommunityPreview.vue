<template>
  <section class="community-preview">
    <div class="container">
      <!-- 区域标题 -->
      <div class="section-header">
        <h2 class="section-title">
          <span class="title-icon">💬</span>
          社区动态
        </h2>
        <p class="section-subtitle">玩家分享的精彩瞬间和游戏心得</p>
        <router-link to="/feed" class="view-all-link">
          进入动态广场 <span class="arrow">→</span>
        </router-link>
      </div>

      <!-- 动态列表 -->
      <div class="feed-grid">
        <div 
          v-for="post in featuredPosts" 
          :key="post.id"
          class="feed-card"
          @click="handlePostClick(post.id)"
        >
          <!-- 用户信息 -->
          <div class="post-header">
            <img 
              :src="post.author.avatar" 
              :alt="post.author.nickname"
              class="author-avatar"
            />
            <div class="author-info">
              <span class="author-name">{{ post.author.nickname }}</span>
              <span class="post-time">{{ formatTime(post.createdAt) }}</span>
            </div>
            <div class="post-type" :class="`type-${post.type}`">
              {{ getPostTypeText(post.type) }}
            </div>
          </div>

          <!-- 内容 -->
          <div class="post-content">
            <h3 class="post-title">{{ post.title }}</h3>
            <p class="post-excerpt">{{ post.content }}</p>
            
            <!-- 关联剧本 -->
            <div v-if="post.script" class="related-script">
              <img 
                :src="post.script.coverImage" 
                :alt="post.script.title"
                class="script-thumb"
              />
              <span class="script-name">{{ post.script.title }}</span>
            </div>
          </div>

          <!-- 图片预览 -->
          <div v-if="post.images && post.images.length > 0" class="post-images">
            <div 
              class="image-grid"
              :class="`grid-${Math.min(post.images.length, 3)}`"
            >
              <img 
                v-for="(image, index) in post.images.slice(0, 3)" 
                :key="index"
                :src="image"
                :alt="`图片${index + 1}`"
                class="post-image"
                @error="handleImageError"
              />
              <div 
                v-if="post.images.length > 3" 
                class="more-images"
              >
                +{{ post.images.length - 3 }}
              </div>
            </div>
          </div>

          <!-- 互动数据 -->
          <div class="post-stats">
            <div class="stat-item">
              <i class="icon-heart" :class="{ liked: post.isLiked }"></i>
              <span class="stat-count">{{ formatCount(post.likeCount) }}</span>
            </div>
            <div class="stat-item">
              <i class="icon-comment"></i>
              <span class="stat-count">{{ formatCount(post.commentCount) }}</span>
            </div>
            <div class="stat-item">
              <i class="icon-share"></i>
              <span class="stat-count">{{ formatCount(post.shareCount) }}</span>
            </div>
          </div>

          <!-- 热门标识 -->
          <div v-if="post.isHot" class="hot-badge">
            <span class="hot-text">🔥 热门</span>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

// 类型定义
interface Author {
  id: number
  nickname: string
  avatar: string
}

interface Script {
  id: number
  title: string
  coverImage: string
}

interface Post {
  id: number
  title: string
  content: string
  type: 'review' | 'share' | 'guide' | 'discussion'
  author: Author
  script?: Script
  images?: string[]
  likeCount: number
  commentCount: number
  shareCount: number
  isLiked: boolean
  isHot: boolean
  createdAt: string
}

// 路由
const router = useRouter()

// 响应式数据
const featuredPosts = ref<Post[]>([
  {
    id: 1,
    title: "《迷雾庄园》通关心得分享",
    content: "昨晚和朋友们一起玩了这个剧本，真的太精彩了！推理过程环环相扣，每个角色都有自己的秘密。特别是最后的真相揭露，完全出乎意料...",
    type: 'review',
    author: {
      id: 1,
      nickname: "推理达人",
      avatar: "https://picsum.photos/40/40?random=21"
    },
    script: {
      id: 1,
      title: "迷雾庄园",
      coverImage: "https://picsum.photos/60/60?random=1"
    },
    images: [
      "https://picsum.photos/200/150?random=31",
      "https://picsum.photos/200/150?random=32",
      "https://picsum.photos/200/150?random=33"
    ],
    likeCount: 156,
    commentCount: 23,
    shareCount: 12,
    isLiked: false,
    isHot: true,
    createdAt: "2024-07-29T14:30:00"
  },
  {
    id: 2,
    title: "新手入坑指南：如何选择适合的剧本",
    content: "作为一个剧本杀老玩家，经常有朋友问我新手应该怎么入坑。今天就来分享一下我的经验，希望能帮到刚接触剧本杀的小伙伴们...",
    type: 'guide',
    author: {
      id: 2,
      nickname: "剧本杀导师",
      avatar: "https://picsum.photos/40/40?random=22"
    },
    likeCount: 89,
    commentCount: 45,
    shareCount: 67,
    isLiked: true,
    isHot: false,
    createdAt: "2024-07-29T10:15:00"
  },
  {
    id: 3,
    title: "今天的拼车体验太棒了！",
    content: "通过平台找到了一群志同道合的朋友，大家都很友善，游戏氛围特别好。房主很用心地准备了道具和背景音乐，沉浸感满分！",
    type: 'share',
    author: {
      id: 3,
      nickname: "快乐玩家",
      avatar: "https://picsum.photos/40/40?random=23"
    },
    images: [
      "https://picsum.photos/200/150?random=34"
    ],
    likeCount: 234,
    commentCount: 18,
    shareCount: 8,
    isLiked: false,
    isHot: true,
    createdAt: "2024-07-29T16:45:00"
  },
  {
    id: 4,
    title: "关于恐怖本的一些讨论",
    content: "最近玩了几个恐怖本，发现大家对恐怖程度的接受度差异很大。想和大家讨论一下，你们觉得恐怖本应该如何分级比较合理？",
    type: 'discussion',
    author: {
      id: 4,
      nickname: "恐怖爱好者",
      avatar: "https://picsum.photos/40/40?random=24"
    },
    likeCount: 67,
    commentCount: 89,
    shareCount: 15,
    isLiked: true,
    isHot: false,
    createdAt: "2024-07-29T09:20:00"
  }
])

// 方法
const getPostTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    review: '测评',
    share: '分享',
    guide: '攻略',
    discussion: '讨论'
  }
  return typeMap[type] || '动态'
}

const formatTime = (timeString: string): string => {
  const now = new Date()
  const postTime = new Date(timeString)
  const diff = now.getTime() - postTime.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days > 0) return `${days}天前`
  if (hours > 0) return `${hours}小时前`
  if (minutes > 0) return `${minutes}分钟前`
  return '刚刚'
}

const formatCount = (count: number): string => {
  if (count >= 1000) {
    return (count / 1000).toFixed(1) + 'k'
  }
  return count.toString()
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = 'https://via.placeholder.com/200x150/1A1A2E/00F5D4?text=图片'
}

const handlePostClick = (postId: number) => {
  router.push(`/feed/${postId}`)
}
</script>

<style lang="scss" scoped>
.community-preview {
  padding: 80px 0;
  background: linear-gradient(180deg, #1A1A2E 0%, #0F0F1E 100%);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  
  .title-icon {
    font-size: 2rem;
  }
}

.section-subtitle {
  font-size: 1.1rem;
  color: #B0B0B0;
  margin-bottom: 24px;
}

.view-all-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: #00F5D4;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &:hover {
    color: #FF00E4;
    
    .arrow {
      transform: translateX(4px);
    }
  }
  
  .arrow {
    transition: transform 0.3s ease;
  }
}

.feed-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

.feed-card {
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 245, 212, 0.1);
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    transform: translateY(-4px);
    border-color: rgba(0, 245, 212, 0.4);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2);
  }
}

.post-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.author-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.author-name {
  font-size: 0.9rem;
  color: #fff;
  font-weight: 500;
}

.post-time {
  font-size: 0.75rem;
  color: #888;
}

.post-type {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  
  &.type-review {
    background: rgba(0, 245, 212, 0.1);
    color: #00F5D4;
    border: 1px solid rgba(0, 245, 212, 0.2);
  }
  
  &.type-share {
    background: rgba(255, 0, 228, 0.1);
    color: #FF00E4;
    border: 1px solid rgba(255, 0, 228, 0.2);
  }
  
  &.type-guide {
    background: rgba(255, 193, 7, 0.1);
    color: #FFC107;
    border: 1px solid rgba(255, 193, 7, 0.2);
  }
  
  &.type-discussion {
    background: rgba(156, 39, 176, 0.1);
    color: #9C27B0;
    border: 1px solid rgba(156, 39, 176, 0.2);
  }
}

.post-content {
  margin-bottom: 16px;
}

.post-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #fff;
  margin-bottom: 8px;
  line-height: 1.4;
}

.post-excerpt {
  font-size: 0.9rem;
  color: #B0B0B0;
  line-height: 1.5;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.related-script {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(0, 245, 212, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.1);
  border-radius: 8px;
}

.script-thumb {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  object-fit: cover;
}

.script-name {
  font-size: 0.8rem;
  color: #00F5D4;
  font-weight: 500;
}

.post-images {
  margin-bottom: 16px;
}

.image-grid {
  display: grid;
  gap: 8px;
  border-radius: 8px;
  overflow: hidden;
  
  &.grid-1 {
    grid-template-columns: 1fr;
  }
  
  &.grid-2 {
    grid-template-columns: 1fr 1fr;
  }
  
  &.grid-3 {
    grid-template-columns: 2fr 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    
    .post-image:first-child {
      grid-row: 1 / 3;
    }
  }
}

.post-image {
  width: 100%;
  height: 100px;
  object-fit: cover;
  transition: transform 0.3s ease;
  
  &:hover {
    transform: scale(1.05);
  }
}

.more-images {
  position: relative;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  font-weight: 600;
  height: 100px;
}

.post-stats {
  display: flex;
  gap: 20px;
  align-items: center;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.8rem;
  color: #888;
  cursor: pointer;
  transition: color 0.3s ease;
  
  &:hover {
    color: #00F5D4;
  }
  
  i {
    font-size: 0.9rem;
    transition: color 0.3s ease;
    
    &.icon-heart.liked {
      color: #FF4444;
    }
  }
}

.hot-badge {
  position: absolute;
  top: -8px;
  right: 16px;
  background: linear-gradient(135deg, #FF4444, #FF6B6B);
  color: #fff;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  0% { box-shadow: 0 0 10px rgba(255, 68, 68, 0.5); }
  100% { box-shadow: 0 0 20px rgba(255, 68, 68, 0.8); }
}

@media (max-width: 768px) {
  .community-preview {
    padding: 60px 0;
  }
  
  .container {
    padding: 0 15px;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .feed-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .feed-card {
    padding: 16px;
  }
  
  .post-stats {
    gap: 16px;
  }
}
</style>
