<template>
  <section class="featured-scripts">
    <div class="container">
      <!-- 区域标题 -->
      <div class="section-header">
        <h2 class="section-title">
          <span class="title-icon">🔥</span>
          热门剧本
        </h2>
        <p class="section-subtitle">精选高质量剧本，带你体验不同的故事世界</p>
        <router-link to="/scripts" class="view-all-link">
          查看全部 <span class="arrow">→</span>
        </router-link>
      </div>

      <!-- 剧本卡片列表 -->
      <div class="scripts-container">
        <div class="scripts-scroll" ref="scrollContainer">
          <div 
            v-for="script in featuredScripts" 
            :key="script.id"
            class="script-card"
            @click="handleScriptClick(script.id)"
          >
            <!-- 剧本封面 -->
            <div class="script-cover">
              <img 
                :src="script.coverImage" 
                :alt="script.title"
                class="cover-image"
                @error="handleImageError"
              />
              <div class="cover-overlay">
                <div class="play-button">
                  <span class="play-icon">▶</span>
                </div>
              </div>
              <!-- 难度标签 -->
              <div class="difficulty-badge" :class="`difficulty-${script.difficulty}`">
                {{ getDifficultyText(script.difficulty) }}
              </div>
              <!-- 评分 -->
              <div class="rating-badge">
                <span class="rating-star">★</span>
                <span class="rating-value">{{ script.rating }}</span>
              </div>
            </div>

            <!-- 剧本信息 -->
            <div class="script-info">
              <h3 class="script-title">{{ script.title }}</h3>
              <div class="script-meta">
                <span class="meta-item">
                  <i class="icon-users"></i>
                  {{ script.playerCount }}人
                </span>
                <span class="meta-item">
                  <i class="icon-clock"></i>
                  {{ formatDuration(script.duration) }}
                </span>
                <span class="meta-item">
                  <i class="icon-tag"></i>
                  {{ script.genre }}
                </span>
              </div>
              <p class="script-description">{{ script.description }}</p>
              
              <!-- 标签 -->
              <div class="script-tags">
                <span 
                  v-for="tag in script.tags.slice(0, 3)" 
                  :key="tag"
                  class="tag"
                >
                  {{ tag }}
                </span>
              </div>
              
              <!-- 价格和操作 -->
              <div class="script-footer">
                <div class="price">
                  <span class="price-symbol">¥</span>
                  <span class="price-value">{{ script.price }}</span>
                </div>
                <button class="join-button" @click.stop="handleQuickJoin(script.id)">
                  快速拼车
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 滚动控制按钮 -->
        <button 
          class="scroll-button scroll-left" 
          @click="scrollLeft"
          :disabled="!canScrollLeft"
        >
          <span class="arrow-icon">‹</span>
        </button>
        <button 
          class="scroll-button scroll-right" 
          @click="scrollRight"
          :disabled="!canScrollRight"
        >
          <span class="arrow-icon">›</span>
        </button>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'

// 类型定义
interface Script {
  id: number
  title: string
  coverImage: string
  genre: string
  difficulty: number
  playerCount: number
  duration: number
  rating: number
  description: string
  tags: string[]
  price: number
}

// 路由
const router = useRouter()

// 响应式数据
const scrollContainer = ref<HTMLElement>()
const scrollPosition = ref(0)
const featuredScripts = ref<Script[]>([
  {
    id: 1,
    title: "迷雾庄园",
    coverImage: "https://picsum.photos/300/400?random=1",
    genre: "推理",
    difficulty: 4,
    playerCount: 6,
    duration: 240,
    rating: 4.8,
    description: "一个充满谜团的古老庄园，隐藏着不为人知的秘密...",
    tags: ["推理", "悬疑", "古风"],
    price: 68
  },
  {
    id: 2,
    title: "末日求生",
    coverImage: "https://picsum.photos/300/400?random=2",
    genre: "恐怖",
    difficulty: 5,
    playerCount: 8,
    duration: 300,
    rating: 4.6,
    description: "末日降临，人性在绝境中的挣扎与选择...",
    tags: ["恐怖", "生存", "末日"],
    price: 88
  },
  {
    id: 3,
    title: "青春校园",
    coverImage: "https://picsum.photos/300/400?random=3",
    genre: "情感",
    difficulty: 2,
    playerCount: 4,
    duration: 180,
    rating: 4.9,
    description: "回到青春年代，体验纯真的校园爱情故事...",
    tags: ["情感", "校园", "青春"],
    price: 48
  },
  {
    id: 4,
    title: "古墓探险",
    coverImage: "https://picsum.photos/300/400?random=4",
    genre: "冒险",
    difficulty: 4,
    playerCount: 6,
    duration: 270,
    rating: 4.7,
    description: "深入神秘古墓，寻找失落的宝藏和真相...",
    tags: ["冒险", "探险", "古墓"],
    price: 78
  },
  {
    id: 5,
    title: "都市传说",
    coverImage: "https://picsum.photos/300/400?random=5",
    genre: "悬疑",
    difficulty: 3,
    playerCount: 5,
    duration: 200,
    rating: 4.5,
    description: "现代都市中流传的神秘传说，真相扑朔迷离...",
    tags: ["悬疑", "都市", "传说"],
    price: 58
  }
])

// 计算属性
const canScrollLeft = computed(() => scrollPosition.value > 0)
const canScrollRight = computed(() => {
  if (!scrollContainer.value) return false
  const maxScroll = scrollContainer.value.scrollWidth - scrollContainer.value.clientWidth
  return scrollPosition.value < maxScroll
})

// 方法
const getDifficultyText = (difficulty: number): string => {
  const difficultyMap: Record<number, string> = {
    1: '新手',
    2: '简单',
    3: '中等',
    4: '困难',
    5: '专家'
  }
  return difficultyMap[difficulty] || '未知'
}

const formatDuration = (minutes: number): string => {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  if (hours > 0) {
    return `${hours}h${mins > 0 ? mins + 'm' : ''}`
  }
  return `${mins}m`
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = 'https://via.placeholder.com/300x400/1A1A2E/00F5D4?text=剧本封面'
}

const handleScriptClick = (scriptId: number) => {
  router.push(`/scripts/${scriptId}`)
}

const handleQuickJoin = (scriptId: number) => {
  router.push(`/lobby?scriptId=${scriptId}`)
}

const scrollLeft = () => {
  if (!scrollContainer.value) return
  const scrollAmount = 320
  scrollContainer.value.scrollBy({ left: -scrollAmount, behavior: 'smooth' })
}

const scrollRight = () => {
  if (!scrollContainer.value) return
  const scrollAmount = 320
  scrollContainer.value.scrollBy({ left: scrollAmount, behavior: 'smooth' })
}

const updateScrollPosition = () => {
  if (scrollContainer.value) {
    scrollPosition.value = scrollContainer.value.scrollLeft
  }
}

// 生命周期
onMounted(() => {
  if (scrollContainer.value) {
    scrollContainer.value.addEventListener('scroll', updateScrollPosition)
  }
})
</script>

<style lang="scss" scoped>
.featured-scripts {
  padding: 80px 0;
  background: linear-gradient(180deg, #1A1A2E 0%, #16213E 100%);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  
  .title-icon {
    font-size: 2rem;
    animation: flicker 2s ease-in-out infinite alternate;
  }
}

.section-subtitle {
  font-size: 1.1rem;
  color: #B0B0B0;
  margin-bottom: 24px;
}

.view-all-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: #00F5D4;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &:hover {
    color: #FF00E4;
    
    .arrow {
      transform: translateX(4px);
    }
  }
  
  .arrow {
    transition: transform 0.3s ease;
  }
}

.scripts-container {
  position: relative;
}

.scripts-scroll {
  display: flex;
  gap: 24px;
  overflow-x: auto;
  scroll-behavior: smooth;
  padding: 20px 0;
  
  &::-webkit-scrollbar {
    display: none;
  }
  
  scrollbar-width: none;
}

.script-card {
  flex: 0 0 300px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 245, 212, 0.1);
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    transform: translateY(-8px);
    border-color: rgba(0, 245, 212, 0.4);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    
    .cover-overlay {
      opacity: 1;
    }
    
    .cover-image {
      transform: scale(1.05);
    }
  }
}

.script-cover {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.play-button {
  width: 60px;
  height: 60px;
  background: rgba(0, 245, 212, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .play-icon {
    color: #1A1A2E;
    font-size: 1.5rem;
    margin-left: 4px;
  }
}

.difficulty-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
  
  &.difficulty-1, &.difficulty-2 {
    background: rgba(76, 175, 80, 0.8);
    color: #fff;
  }
  
  &.difficulty-3 {
    background: rgba(255, 193, 7, 0.8);
    color: #1A1A2E;
  }
  
  &.difficulty-4, &.difficulty-5 {
    background: rgba(244, 67, 54, 0.8);
    color: #fff;
  }
}

.rating-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  
  .rating-star {
    color: #FFD700;
    font-size: 0.9rem;
  }
  
  .rating-value {
    color: #fff;
    font-size: 0.8rem;
    font-weight: 600;
  }
}

.script-info {
  padding: 20px;
}

.script-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #fff;
  margin-bottom: 12px;
  line-height: 1.3;
}

.script-meta {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.85rem;
  color: #B0B0B0;
  
  i {
    font-size: 0.8rem;
    color: #00F5D4;
  }
}

.script-description {
  font-size: 0.9rem;
  color: #999;
  line-height: 1.5;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.script-tags {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.tag {
  padding: 4px 8px;
  background: rgba(0, 245, 212, 0.1);
  color: #00F5D4;
  border-radius: 8px;
  font-size: 0.75rem;
  border: 1px solid rgba(0, 245, 212, 0.2);
}

.script-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.price {
  display: flex;
  align-items: baseline;
  gap: 2px;
  
  .price-symbol {
    font-size: 0.9rem;
    color: #FF00E4;
  }
  
  .price-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #FF00E4;
  }
}

.join-button {
  padding: 8px 16px;
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  border: none;
  border-radius: 8px;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 245, 212, 0.4);
  }
}

.scroll-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 48px;
  height: 48px;
  background: rgba(0, 245, 212, 0.9);
  border: none;
  border-radius: 50%;
  color: #1A1A2E;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 2;
  
  &:hover:not(:disabled) {
    background: rgba(0, 245, 212, 1);
    transform: translateY(-50%) scale(1.1);
  }
  
  &:disabled {
    opacity: 0.3;
    cursor: not-allowed;
  }
  
  &.scroll-left {
    left: -24px;
  }
  
  &.scroll-right {
    right: -24px;
  }
}

@keyframes flicker {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

@media (max-width: 768px) {
  .featured-scripts {
    padding: 60px 0;
  }
  
  .container {
    padding: 0 15px;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .script-card {
    flex: 0 0 280px;
  }
  
  .scroll-button {
    display: none;
  }
}
</style>
