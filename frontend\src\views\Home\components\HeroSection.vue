<template>
  <section class="hero-section">
    <!-- 背景粒子效果 -->
    <div class="hero-particles" ref="particlesContainer"></div>
    
    <!-- 主要内容 -->
    <div class="hero-content">
      <div class="hero-text">
        <h1 class="hero-title">
          <span class="title-line">探索无限可能的</span>
          <span class="title-line title-highlight">剧本世界</span>
        </h1>
        <p class="hero-subtitle">
          在迷雾中寻找真相，在角色中体验人生<br>
          与志同道合的伙伴一起，开启沉浸式剧本杀之旅
        </p>
        <div class="hero-stats">
          <div class="stat-item">
            <span class="stat-number">{{ formatNumber(stats.totalScripts) }}</span>
            <span class="stat-label">精品剧本</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ formatNumber(stats.activeUsers) }}</span>
            <span class="stat-label">活跃玩家</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ formatNumber(stats.completedGames) }}</span>
            <span class="stat-label">完成游戏</span>
          </div>
        </div>
      </div>
      
      <div class="hero-actions">
        <button class="cta-button primary" @click="handleStartPlaying">
          <span class="button-text">立即开始拼车</span>
          <div class="button-glow"></div>
        </button>
        <button class="cta-button secondary" @click="handleExploreScripts">
          <span class="button-text">浏览剧本库</span>
        </button>
      </div>
    </div>
    
    <!-- 装饰元素 -->
    <div class="hero-decorations">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-line line-1"></div>
      <div class="decoration-line line-2"></div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

// 路由
const router = useRouter()

// 响应式数据
const particlesContainer = ref<HTMLElement>()
const stats = ref({
  totalScripts: 1250,
  activeUsers: 8900,
  completedGames: 15600
})

// 粒子动画相关
let animationId: number
let particles: Array<{
  x: number
  y: number
  vx: number
  vy: number
  size: number
  opacity: number
}> = []

// 方法
const formatNumber = (num: number): string => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

const handleStartPlaying = () => {
  router.push('/lobby')
}

const handleExploreScripts = () => {
  router.push('/scripts')
}

// 粒子动画初始化
const initParticles = () => {
  if (!particlesContainer.value) return
  
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  if (!ctx) return
  
  particlesContainer.value.appendChild(canvas)
  
  const resizeCanvas = () => {
    canvas.width = particlesContainer.value!.offsetWidth
    canvas.height = particlesContainer.value!.offsetHeight
  }
  
  resizeCanvas()
  window.addEventListener('resize', resizeCanvas)
  
  // 创建粒子
  for (let i = 0; i < 50; i++) {
    particles.push({
      x: Math.random() * canvas.width,
      y: Math.random() * canvas.height,
      vx: (Math.random() - 0.5) * 0.5,
      vy: (Math.random() - 0.5) * 0.5,
      size: Math.random() * 2 + 1,
      opacity: Math.random() * 0.5 + 0.2
    })
  }
  
  // 动画循环
  const animate = () => {
    ctx.clearRect(0, 0, canvas.width, canvas.height)
    
    particles.forEach(particle => {
      // 更新位置
      particle.x += particle.vx
      particle.y += particle.vy
      
      // 边界检测
      if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1
      if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1
      
      // 绘制粒子
      ctx.beginPath()
      ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
      ctx.fillStyle = `rgba(0, 245, 212, ${particle.opacity})`
      ctx.fill()
    })
    
    animationId = requestAnimationFrame(animate)
  }
  
  animate()
}

// 生命周期
onMounted(() => {
  initParticles()
})

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
})
</script>

<style lang="scss" scoped>
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1A1A2E 0%, #0F0F1E 100%);
  overflow: hidden;
  padding: 0 20px;
}

.hero-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  
  canvas {
    width: 100%;
    height: 100%;
  }
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.hero-text {
  margin-bottom: 60px;
}

.hero-title {
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 30px;
  
  .title-line {
    display: block;
    color: #fff;
  }
  
  .title-highlight {
    background: linear-gradient(135deg, #00F5D4, #FF00E4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 80%;
      height: 3px;
      background: linear-gradient(90deg, #00F5D4, #FF00E4);
      border-radius: 2px;
      animation: glow 2s ease-in-out infinite alternate;
    }
  }
}

.hero-subtitle {
  font-size: 1.25rem;
  color: #B0B0B0;
  line-height: 1.6;
  margin-bottom: 40px;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 60px;
  margin-bottom: 40px;
  
  @media (max-width: 768px) {
    gap: 30px;
    flex-wrap: wrap;
  }
}

.stat-item {
  text-align: center;
  
  .stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: #00F5D4;
    line-height: 1;
    margin-bottom: 8px;
  }
  
  .stat-label {
    font-size: 0.9rem;
    color: #888;
    text-transform: uppercase;
    letter-spacing: 1px;
  }
}

.hero-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-button {
  position: relative;
  padding: 16px 32px;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  min-width: 180px;
  
  .button-text {
    position: relative;
    z-index: 2;
  }
  
  &.primary {
    background: linear-gradient(135deg, #00F5D4, #00C9A7);
    color: #1A1A2E;
    
    .button-glow {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, #00F5D4, #00C9A7);
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 30px rgba(0, 245, 212, 0.4);
      
      .button-glow {
        opacity: 0.2;
      }
    }
    
    &:active {
      transform: translateY(0);
    }
  }
  
  &.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: 1px solid rgba(0, 245, 212, 0.3);
    backdrop-filter: blur(10px);
    
    &:hover {
      background: rgba(255, 255, 255, 0.15);
      border-color: rgba(0, 245, 212, 0.6);
      transform: translateY(-2px);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
}

.hero-decorations {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
  
  &.circle-1 {
    width: 300px;
    height: 300px;
    top: 10%;
    right: 10%;
    animation-delay: 0s;
  }
  
  &.circle-2 {
    width: 200px;
    height: 200px;
    bottom: 20%;
    left: 15%;
    animation-delay: 3s;
  }
}

.decoration-line {
  position: absolute;
  background: linear-gradient(90deg, transparent, rgba(255, 0, 228, 0.3), transparent);
  height: 1px;
  animation: slide 8s linear infinite;
  
  &.line-1 {
    width: 200px;
    top: 30%;
    left: 0;
    animation-delay: 0s;
  }
  
  &.line-2 {
    width: 150px;
    bottom: 40%;
    right: 0;
    animation-delay: 4s;
  }
}

@keyframes glow {
  0% { box-shadow: 0 0 20px rgba(0, 245, 212, 0.5); }
  100% { box-shadow: 0 0 30px rgba(255, 0, 228, 0.5); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes slide {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100vw); }
}

@media (max-width: 768px) {
  .hero-section {
    min-height: 80vh;
    padding: 0 15px;
  }
  
  .hero-title {
    font-size: clamp(2rem, 6vw, 3.5rem);
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
  }
  
  .cta-button {
    min-width: 150px;
    padding: 14px 24px;
    font-size: 1rem;
  }
  
  .decoration-circle {
    &.circle-1 {
      width: 200px;
      height: 200px;
    }
    
    &.circle-2 {
      width: 150px;
      height: 150px;
    }
  }
}
</style>
