<template>
  <section class="live-lobby-feed">
    <div class="container">
      <!-- 区域标题 -->
      <div class="section-header">
        <h2 class="section-title">
          <span class="title-icon">🚗</span>
          正在组局
          <div class="live-indicator">
            <span class="live-dot"></span>
            <span class="live-text">LIVE</span>
          </div>
        </h2>
        <p class="section-subtitle">实时更新的拼车信息，快速找到心仪的车队</p>
        
        <!-- 筛选栏 -->
        <div class="filter-bar">
          <button 
            v-for="filter in filters" 
            :key="filter.key"
            class="filter-button"
            :class="{ active: activeFilter === filter.key }"
            @click="setActiveFilter(filter.key)"
          >
            {{ filter.label }}
          </button>
        </div>
      </div>

      <!-- 车队列表 -->
      <div class="lobby-grid">
        <div 
          v-for="lobby in filteredLobbies" 
          :key="lobby.id"
          class="lobby-card"
          :class="{ 'lobby-card--urgent': isUrgent(lobby) }"
          @click="handleLobbyClick(lobby.id)"
        >
          <!-- 剧本信息 -->
          <div class="lobby-script">
            <img 
              :src="lobby.script.coverImage" 
              :alt="lobby.script.title"
              class="script-cover"
              @error="handleImageError"
            />
            <div class="script-info">
              <h3 class="script-title">{{ lobby.script.title }}</h3>
              <span class="script-genre">{{ lobby.script.genre }}</span>
            </div>
          </div>

          <!-- 房主信息 -->
          <div class="lobby-host">
            <img 
              :src="lobby.host.avatar" 
              :alt="lobby.host.nickname"
              class="host-avatar"
            />
            <div class="host-info">
              <span class="host-name">{{ lobby.host.nickname }}</span>
              <span class="host-level">Lv.{{ lobby.host.level }}</span>
            </div>
          </div>

          <!-- 人数进度 -->
          <div class="lobby-progress">
            <div class="progress-info">
              <span class="progress-text">
                {{ lobby.currentPlayers }}/{{ lobby.maxPlayers }}人
              </span>
              <span class="progress-percentage">
                {{ Math.round((lobby.currentPlayers / lobby.maxPlayers) * 100) }}%
              </span>
            </div>
            <div class="progress-bar">
              <div 
                class="progress-fill"
                :style="{ width: `${(lobby.currentPlayers / lobby.maxPlayers) * 100}%` }"
              ></div>
            </div>
          </div>

          <!-- 时间信息 -->
          <div class="lobby-time">
            <div class="time-item">
              <i class="icon-clock"></i>
              <span>{{ formatTime(lobby.startTime) }}</span>
            </div>
            <div class="countdown" v-if="lobby.status === 'waiting'">
              <span class="countdown-text">{{ getCountdown(lobby.startTime) }}</span>
            </div>
          </div>

          <!-- 标签和状态 -->
          <div class="lobby-tags">
            <span class="tag location-tag">{{ lobby.location }}</span>
            <span class="tag price-tag">¥{{ lobby.price }}</span>
            <span 
              class="tag status-tag"
              :class="`status-${lobby.status}`"
            >
              {{ getStatusText(lobby.status) }}
            </span>
          </div>

          <!-- 操作按钮 -->
          <div class="lobby-actions">
            <button 
              class="action-button join-button"
              :class="{ disabled: lobby.status !== 'waiting' }"
              :disabled="lobby.status !== 'waiting'"
              @click.stop="handleJoinLobby(lobby.id)"
            >
              {{ getButtonText(lobby.status) }}
            </button>
          </div>

          <!-- 紧急标识 -->
          <div v-if="isUrgent(lobby)" class="urgent-badge">
            <span class="urgent-text">急缺人手</span>
          </div>
        </div>
      </div>

      <!-- 查看更多 -->
      <div class="section-footer">
        <router-link to="/lobby" class="view-more-button">
          进入拼车大厅
          <span class="arrow">→</span>
        </router-link>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

// 类型定义
interface Script {
  id: number
  title: string
  coverImage: string
  genre: string
}

interface Host {
  id: number
  nickname: string
  avatar: string
  level: number
}

interface Lobby {
  id: number
  script: Script
  host: Host
  currentPlayers: number
  maxPlayers: number
  status: 'waiting' | 'full' | 'in_progress'
  startTime: string
  location: string
  price: number
  createdAt: string
}

// 路由
const router = useRouter()

// 响应式数据
const activeFilter = ref('all')
const currentTime = ref(new Date())
let timeInterval: NodeJS.Timeout

const filters = [
  { key: 'all', label: '全部' },
  { key: 'waiting', label: '等待中' },
  { key: 'urgent', label: '急缺人手' },
  { key: 'newbie', label: '新手友好' }
]

const lobbies = ref<Lobby[]>([
  {
    id: 1,
    script: {
      id: 1,
      title: "迷雾庄园",
      coverImage: "https://picsum.photos/80/80?random=1",
      genre: "推理"
    },
    host: {
      id: 1,
      nickname: "推理大师",
      avatar: "https://picsum.photos/40/40?random=11",
      level: 15
    },
    currentPlayers: 5,
    maxPlayers: 6,
    status: 'waiting',
    startTime: '2024-07-30T19:00:00',
    location: '线上',
    price: 68,
    createdAt: '2024-07-29T14:30:00'
  },
  {
    id: 2,
    script: {
      id: 2,
      title: "末日求生",
      coverImage: "https://picsum.photos/80/80?random=2",
      genre: "恐怖"
    },
    host: {
      id: 2,
      nickname: "恐怖爱好者",
      avatar: "https://picsum.photos/40/40?random=12",
      level: 8
    },
    currentPlayers: 3,
    maxPlayers: 8,
    status: 'waiting',
    startTime: '2024-07-30T20:30:00',
    location: '线下',
    price: 88,
    createdAt: '2024-07-29T15:00:00'
  },
  {
    id: 3,
    script: {
      id: 3,
      title: "青春校园",
      coverImage: "https://picsum.photos/80/80?random=3",
      genre: "情感"
    },
    host: {
      id: 3,
      nickname: "校园回忆",
      avatar: "https://picsum.photos/40/40?random=13",
      level: 5
    },
    currentPlayers: 4,
    maxPlayers: 4,
    status: 'full',
    startTime: '2024-07-30T18:00:00',
    location: '线上',
    price: 48,
    createdAt: '2024-07-29T13:45:00'
  },
  {
    id: 4,
    script: {
      id: 4,
      title: "古墓探险",
      coverImage: "https://picsum.photos/80/80?random=4",
      genre: "冒险"
    },
    host: {
      id: 4,
      nickname: "探险家",
      avatar: "https://picsum.photos/40/40?random=14",
      level: 12
    },
    currentPlayers: 2,
    maxPlayers: 6,
    status: 'waiting',
    startTime: '2024-07-30T21:00:00',
    location: '线下',
    price: 78,
    createdAt: '2024-07-29T16:20:00'
  },
  {
    id: 5,
    script: {
      id: 5,
      title: "都市传说",
      coverImage: "https://picsum.photos/80/80?random=5",
      genre: "悬疑"
    },
    host: {
      id: 5,
      nickname: "悬疑控",
      avatar: "https://picsum.photos/40/40?random=15",
      level: 20
    },
    currentPlayers: 1,
    maxPlayers: 5,
    status: 'waiting',
    startTime: '2024-07-30T19:30:00',
    location: '线上',
    price: 58,
    createdAt: '2024-07-29T17:10:00'
  },
  {
    id: 6,
    script: {
      id: 6,
      title: "宫廷秘史",
      coverImage: "https://picsum.photos/80/80?random=6",
      genre: "古风"
    },
    host: {
      id: 6,
      nickname: "古风达人",
      avatar: "https://picsum.photos/40/40?random=16",
      level: 18
    },
    currentPlayers: 6,
    maxPlayers: 7,
    status: 'waiting',
    startTime: '2024-07-30T20:00:00',
    location: '线下',
    price: 98,
    createdAt: '2024-07-29T12:30:00'
  }
])

// 计算属性
const filteredLobbies = computed(() => {
  let filtered = lobbies.value

  switch (activeFilter.value) {
    case 'waiting':
      filtered = filtered.filter(lobby => lobby.status === 'waiting')
      break
    case 'urgent':
      filtered = filtered.filter(lobby => isUrgent(lobby))
      break
    case 'newbie':
      filtered = filtered.filter(lobby => lobby.host.level <= 10)
      break
  }

  return filtered.slice(0, 6) // 首页只显示6个
})

// 方法
const setActiveFilter = (filterKey: string) => {
  activeFilter.value = filterKey
}

const isUrgent = (lobby: Lobby): boolean => {
  const ratio = lobby.currentPlayers / lobby.maxPlayers
  const startTime = new Date(lobby.startTime)
  const now = new Date()
  const hoursUntilStart = (startTime.getTime() - now.getTime()) / (1000 * 60 * 60)
  
  return lobby.status === 'waiting' && ratio >= 0.7 && hoursUntilStart <= 2
}

const formatTime = (timeString: string): string => {
  const date = new Date(timeString)
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit',
    hour12: false 
  })
}

const getCountdown = (startTime: string): string => {
  const start = new Date(startTime)
  const now = currentTime.value
  const diff = start.getTime() - now.getTime()
  
  if (diff <= 0) return '已开始'
  
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  
  if (hours > 0) {
    return `${hours}h${minutes}m后开始`
  }
  return `${minutes}m后开始`
}

const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    waiting: '等待中',
    full: '已满员',
    in_progress: '游戏中'
  }
  return statusMap[status] || '未知'
}

const getButtonText = (status: string): string => {
  const buttonMap: Record<string, string> = {
    waiting: '立即加入',
    full: '已满员',
    in_progress: '游戏中'
  }
  return buttonMap[status] || '未知'
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = 'https://via.placeholder.com/80x80/1A1A2E/00F5D4?text=剧本'
}

const handleLobbyClick = (lobbyId: number) => {
  router.push(`/lobby/${lobbyId}`)
}

const handleJoinLobby = (lobbyId: number) => {
  // 这里应该调用加入车队的API
  console.log('加入车队:', lobbyId)
  // 模拟加入成功后的跳转
  router.push(`/lobby/${lobbyId}`)
}

// 生命周期
onMounted(() => {
  // 每秒更新时间，用于倒计时
  timeInterval = setInterval(() => {
    currentTime.value = new Date()
  }, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style lang="scss" scoped>
.live-lobby-feed {
  padding: 80px 0;
  background: linear-gradient(180deg, #16213E 0%, #1A1A2E 100%);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  
  .title-icon {
    font-size: 2rem;
  }
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-left: 16px;
}

.live-dot {
  width: 8px;
  height: 8px;
  background: #FF4444;
  border-radius: 50%;
  animation: pulse 1.5s ease-in-out infinite;
}

.live-text {
  font-size: 0.8rem;
  color: #FF4444;
  font-weight: 600;
  letter-spacing: 1px;
}

.section-subtitle {
  font-size: 1.1rem;
  color: #B0B0B0;
  margin-bottom: 32px;
}

.filter-bar {
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
}

.filter-button {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 20px;
  color: #B0B0B0;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: rgba(0, 245, 212, 0.4);
    color: #00F5D4;
  }
  
  &.active {
    background: rgba(0, 245, 212, 0.1);
    border-color: #00F5D4;
    color: #00F5D4;
  }
}

.lobby-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 60px;
}

.lobby-card {
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 245, 212, 0.1);
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    transform: translateY(-4px);
    border-color: rgba(0, 245, 212, 0.4);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2);
  }
  
  &--urgent {
    border-color: rgba(255, 68, 68, 0.4);
    
    &:hover {
      border-color: rgba(255, 68, 68, 0.6);
    }
  }
}

.lobby-script {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.script-cover {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
}

.script-info {
  flex: 1;
}

.script-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #fff;
  margin-bottom: 4px;
}

.script-genre {
  font-size: 0.85rem;
  color: #00F5D4;
  padding: 2px 8px;
  background: rgba(0, 245, 212, 0.1);
  border-radius: 12px;
}

.lobby-host {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.host-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.host-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.host-name {
  font-size: 0.9rem;
  color: #fff;
  font-weight: 500;
}

.host-level {
  font-size: 0.75rem;
  color: #888;
}

.lobby-progress {
  margin-bottom: 16px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 0.85rem;
}

.progress-text {
  color: #fff;
  font-weight: 500;
}

.progress-percentage {
  color: #00F5D4;
}

.progress-bar {
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00F5D4, #FF00E4);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.lobby-time {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.85rem;
  color: #B0B0B0;
  
  i {
    color: #00F5D4;
  }
}

.countdown {
  font-size: 0.8rem;
  color: #FF00E4;
  font-weight: 500;
}

.lobby-tags {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.tag {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  
  &.location-tag {
    background: rgba(0, 245, 212, 0.1);
    color: #00F5D4;
    border: 1px solid rgba(0, 245, 212, 0.2);
  }
  
  &.price-tag {
    background: rgba(255, 0, 228, 0.1);
    color: #FF00E4;
    border: 1px solid rgba(255, 0, 228, 0.2);
  }
  
  &.status-tag {
    &.status-waiting {
      background: rgba(76, 175, 80, 0.1);
      color: #4CAF50;
      border: 1px solid rgba(76, 175, 80, 0.2);
    }
    
    &.status-full {
      background: rgba(255, 193, 7, 0.1);
      color: #FFC107;
      border: 1px solid rgba(255, 193, 7, 0.2);
    }
    
    &.status-in_progress {
      background: rgba(244, 67, 54, 0.1);
      color: #F44336;
      border: 1px solid rgba(244, 67, 54, 0.2);
    }
  }
}

.lobby-actions {
  display: flex;
  justify-content: flex-end;
}

.action-button {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &.join-button {
    background: linear-gradient(135deg, #00F5D4, #00C9A7);
    color: #1A1A2E;
    
    &:hover:not(.disabled) {
      transform: scale(1.05);
      box-shadow: 0 4px 12px rgba(0, 245, 212, 0.4);
    }
    
    &.disabled {
      background: rgba(255, 255, 255, 0.1);
      color: #666;
      cursor: not-allowed;
    }
  }
}

.urgent-badge {
  position: absolute;
  top: -8px;
  right: 16px;
  background: #FF4444;
  color: #fff;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  animation: bounce 2s ease-in-out infinite;
}

.section-footer {
  text-align: center;
}

.view-more-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.3);
  border-radius: 12px;
  color: #00F5D4;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(0, 245, 212, 0.1);
    border-color: #00F5D4;
    transform: translateY(-2px);
    
    .arrow {
      transform: translateX(4px);
    }
  }
  
  .arrow {
    transition: transform 0.3s ease;
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.5; transform: scale(1.2); }
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-4px); }
}

@media (max-width: 768px) {
  .live-lobby-feed {
    padding: 60px 0;
  }
  
  .container {
    padding: 0 15px;
  }
  
  .section-title {
    font-size: 2rem;
    flex-direction: column;
    gap: 8px;
  }
  
  .lobby-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .lobby-card {
    padding: 16px;
  }
  
  .filter-bar {
    gap: 8px;
  }
  
  .filter-button {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}
</style>
