<template>
  <div class="home-page">
    <!-- 英雄区域 -->
    <HeroSection />
    
    <!-- 热门剧本推荐 -->
    <FeaturedScripts />
    
    <!-- 实时拼车信息流 -->
    <LiveLobbyFeed />
    
    <!-- 社区动态预览 -->
    <CommunityPreview />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import HeroSection from './components/HeroSection.vue'
import FeaturedScripts from './components/FeaturedScripts.vue'
import LiveLobbyFeed from './components/LiveLobbyFeed.vue'
import CommunityPreview from './components/CommunityPreview.vue'

// 页面元数据
defineOptions({
  name: 'HomePage'
})

// 生命周期
onMounted(() => {
  // 页面加载完成后的初始化操作
  document.title = '迷雾拼本 - 探索无限可能的剧本世界'
})
</script>

<style lang="scss" scoped>
.home-page {
  min-height: 100vh;
  background: #1A1A2E;
  
  // 确保各个section之间的连接自然
  > section {
    position: relative;
    z-index: 1;
  }
}
</style>
