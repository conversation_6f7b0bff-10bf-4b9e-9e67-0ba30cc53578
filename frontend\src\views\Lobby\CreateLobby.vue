<template>
  <div class="create-lobby">
    <div class="container">
      <div class="page-header">
        <button class="back-btn" @click="$router.go(-1)">
          <span class="back-icon">←</span>
          <span class="back-text">返回</span>
        </button>
        <h1 class="page-title">创建车队</h1>
        <p class="page-subtitle">组建你的专属车队，开启精彩的剧本杀之旅</p>
      </div>

      <div class="create-form">
        <form @submit.prevent="handleSubmit">
          <!-- 剧本选择 -->
          <div class="form-section">
            <h3 class="section-title">选择剧本</h3>
            <div class="script-selector">
              <div 
                v-for="script in scripts" 
                :key="script.id"
                class="script-option"
                :class="{ selected: formData.scriptId === script.id }"
                @click="selectScript(script)"
              >
                <img :src="script.coverImage" :alt="script.title" class="script-cover" />
                <div class="script-info">
                  <h4 class="script-title">{{ script.title }}</h4>
                  <div class="script-meta">
                    <span class="genre">{{ script.genre }}</span>
                    <span class="players">{{ script.playerCount }}人</span>
                    <span class="duration">{{ script.duration }}分钟</span>
                  </div>
                  <div class="script-price">¥{{ script.price }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 基本信息 -->
          <div class="form-section">
            <h3 class="section-title">基本信息</h3>
            <div class="form-grid">
              <div class="form-group">
                <label class="form-label">开始时间 *</label>
                <input 
                  v-model="formData.startTime"
                  type="datetime-local" 
                  class="form-input"
                  required
                />
              </div>
              
              <div class="form-group">
                <label class="form-label">游戏地点 *</label>
                <select v-model="formData.location" class="form-select" required>
                  <option value="">请选择地点</option>
                  <option value="线上">线上</option>
                  <option value="线下">线下</option>
                  <option value="混合">混合</option>
                </select>
              </div>
              
              <div class="form-group">
                <label class="form-label">最大人数 *</label>
                <select v-model="formData.maxPlayers" class="form-select" required>
                  <option value="">请选择人数</option>
                  <option v-for="num in [4,5,6,7,8,9,10]" :key="num" :value="num">
                    {{ num }}人
                  </option>
                </select>
              </div>
              
              <div class="form-group">
                <label class="form-label">费用 *</label>
                <div class="price-input">
                  <span class="currency">¥</span>
                  <input 
                    v-model.number="formData.price"
                    type="number" 
                    class="form-input"
                    placeholder="0"
                    min="0"
                    required
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 详细描述 -->
          <div class="form-section">
            <h3 class="section-title">车队描述</h3>
            <div class="form-group">
              <label class="form-label">描述信息</label>
              <textarea 
                v-model="formData.description"
                class="form-textarea"
                placeholder="介绍一下你的车队，吸引更多玩家加入..."
                rows="4"
                maxlength="500"
              ></textarea>
              <div class="char-count">{{ formData.description.length }}/500</div>
            </div>
            
            <div class="form-group">
              <label class="form-label">参与要求</label>
              <textarea 
                v-model="formData.requirements"
                class="form-textarea"
                placeholder="对参与者的要求，如经验水平、时间安排等..."
                rows="3"
                maxlength="300"
              ></textarea>
              <div class="char-count">{{ formData.requirements.length }}/300</div>
            </div>
          </div>

          <!-- 提交按钮 -->
          <div class="form-actions">
            <button type="button" class="cancel-btn" @click="$router.go(-1)">
              取消
            </button>
            <button 
              type="submit" 
              class="submit-btn"
              :disabled="!isFormValid || isSubmitting"
            >
              <span v-if="isSubmitting" class="loading-spinner"></span>
              <span>{{ isSubmitting ? '创建中...' : '创建车队' }}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const formData = ref({
  scriptId: null as number | null,
  startTime: '',
  location: '',
  maxPlayers: null as number | null,
  price: 0,
  description: '',
  requirements: ''
})

const scripts = ref([
  {
    id: 1,
    title: "迷雾庄园",
    coverImage: "https://picsum.photos/120/160?random=1",
    genre: "推理",
    playerCount: 6,
    duration: 240,
    price: 68
  },
  {
    id: 2,
    title: "血色玫瑰",
    coverImage: "https://picsum.photos/120/160?random=2",
    genre: "恐怖",
    playerCount: 7,
    duration: 300,
    price: 88
  },
  {
    id: 3,
    title: "时光倒流",
    coverImage: "https://picsum.photos/120/160?random=3",
    genre: "科幻",
    playerCount: 5,
    duration: 180,
    price: 58
  }
])

const isSubmitting = ref(false)

const isFormValid = computed(() => {
  return formData.value.scriptId && 
         formData.value.startTime && 
         formData.value.location && 
         formData.value.maxPlayers && 
         formData.value.price >= 0
})

const selectScript = (script: any) => {
  formData.value.scriptId = script.id
  formData.value.price = script.price
  formData.value.maxPlayers = script.playerCount
}

const handleSubmit = async () => {
  if (!isFormValid.value || isSubmitting.value) return
  
  try {
    isSubmitting.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    console.log('创建车队:', formData.value)
    
    // 跳转到车队详情页
    router.push('/lobby/1')
  } catch (error) {
    console.error('创建车队失败:', error)
  } finally {
    isSubmitting.value = false
  }
}

onMounted(() => {
  // 设置默认开始时间为明天晚上7点
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  tomorrow.setHours(19, 0, 0, 0)
  formData.value.startTime = tomorrow.toISOString().slice(0, 16)
})
</script>

<style lang="scss" scoped>
.create-lobby {
  min-height: 100vh;
  padding: 40px 0;
  background: linear-gradient(180deg, #1A1A2E 0%, #16213E 50%, #0F0F1E 100%);
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.back-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 8px;
  padding: 8px 16px;
  color: #B0B0B0;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 24px;
  
  &:hover {
    background: rgba(0, 245, 212, 0.1);
    border-color: #00F5D4;
    color: #00F5D4;
  }
}

.page-title {
  font-size: 2.5rem;
  color: #fff;
  font-weight: 700;
  margin-bottom: 12px;
}

.page-subtitle {
  color: #B0B0B0;
  font-size: 1.1rem;
}

.create-form {
  max-width: 800px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 245, 212, 0.1);
  border-radius: 16px;
  padding: 40px;
}

.form-section {
  margin-bottom: 40px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 1.3rem;
  color: #fff;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 8px;
  border-bottom: 2px solid rgba(0, 245, 212, 0.2);
}

.script-selector {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.script-option {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.03);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: rgba(0, 245, 212, 0.3);
    background: rgba(255, 255, 255, 0.05);
  }
  
  &.selected {
    border-color: #00F5D4;
    background: rgba(0, 245, 212, 0.1);
  }
}

.script-cover {
  width: 60px;
  height: 80px;
  border-radius: 6px;
  object-fit: cover;
  flex-shrink: 0;
}

.script-info {
  flex: 1;
}

.script-title {
  font-size: 1rem;
  color: #fff;
  font-weight: 600;
  margin-bottom: 8px;
}

.script-meta {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.genre, .players, .duration {
  padding: 2px 8px;
  background: rgba(0, 245, 212, 0.1);
  color: #00F5D4;
  border-radius: 8px;
  font-size: 0.75rem;
}

.script-price {
  font-size: 1.1rem;
  color: #FF00E4;
  font-weight: 600;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 0.9rem;
  color: #E0E0E0;
  font-weight: 500;
}

.form-input, .form-select, .form-textarea {
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 8px;
  color: #fff;
  font-size: 0.9rem;
  
  &::placeholder {
    color: #666;
  }
  
  &:focus {
    outline: none;
    border-color: #00F5D4;
    box-shadow: 0 0 0 2px rgba(0, 245, 212, 0.1);
  }
}

.price-input {
  position: relative;
  display: flex;
  align-items: center;
}

.currency {
  position: absolute;
  left: 12px;
  color: #B0B0B0;
  font-weight: 500;
  z-index: 1;
}

.price-input .form-input {
  padding-left: 32px;
}

.char-count {
  text-align: right;
  font-size: 0.75rem;
  color: #666;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 40px;
  padding-top: 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.cancel-btn, .submit-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: rgba(255, 255, 255, 0.05);
  color: #B0B0B0;
  border: 1px solid rgba(255, 255, 255, 0.1);
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
  }
}

.submit-btn {
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  display: flex;
  align-items: center;
  gap: 8px;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 245, 212, 0.4);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(26, 26, 46, 0.3);
  border-top: 2px solid #1A1A2E;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .create-lobby {
    padding: 20px 0;
  }
  
  .create-form {
    padding: 24px;
    margin: 0 15px;
  }
  
  .script-selector {
    grid-template-columns: 1fr;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
