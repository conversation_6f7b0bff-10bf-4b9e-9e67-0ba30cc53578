<template>
  <div class="lobby-detail">
    <div class="container">
      <!-- 返回按钮 -->
      <div class="back-navigation">
        <button class="back-btn" @click="$router.go(-1)">
          <span class="back-icon">←</span>
          <span class="back-text">返回车队列表</span>
        </button>
      </div>

      <!-- 车队信息 -->
      <div class="lobby-info" v-if="lobby">
        <div class="lobby-header">
          <div class="script-info">
            <img :src="lobby.script.coverImage" :alt="lobby.script.title" class="script-cover" />
            <div class="script-details">
              <h1 class="script-title">{{ lobby.script.title }}</h1>
              <div class="script-meta">
                <span class="genre-tag">{{ lobby.script.genre }}</span>
                <span class="player-count">{{ lobby.currentPlayers }}/{{ lobby.maxPlayers }}人</span>
                <span class="status-badge" :class="`status-${lobby.status}`">
                  {{ getStatusText(lobby.status) }}
                </span>
              </div>
            </div>
          </div>
          
          <div class="lobby-actions">
            <button 
              v-if="!isJoined && lobby.status === 'waiting'"
              class="join-btn"
              @click="joinLobby"
              :disabled="isJoining"
            >
              <span v-if="isJoining" class="loading-spinner"></span>
              <span>{{ isJoining ? '加入中...' : '加入车队' }}</span>
            </button>
            
            <button 
              v-if="isJoined"
              class="leave-btn"
              @click="leaveLobby"
            >
              退出车队
            </button>
          </div>
        </div>

        <!-- 车队详情 -->
        <div class="lobby-content">
          <div class="main-content">
            <!-- 基本信息 -->
            <div class="info-section">
              <h3 class="section-title">车队信息</h3>
              <div class="info-grid">
                <div class="info-item">
                  <span class="info-label">开始时间</span>
                  <span class="info-value">{{ formatDateTime(lobby.startTime) }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">游戏地点</span>
                  <span class="info-value">{{ lobby.location }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">费用</span>
                  <span class="info-value">¥{{ lobby.price }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">创建时间</span>
                  <span class="info-value">{{ formatDateTime(lobby.createdAt) }}</span>
                </div>
              </div>
            </div>

            <!-- 车队描述 -->
            <div class="description-section">
              <h3 class="section-title">车队描述</h3>
              <p class="description-text">{{ lobby.description || '暂无描述' }}</p>
            </div>

            <!-- 要求说明 -->
            <div v-if="lobby.requirements" class="requirements-section">
              <h3 class="section-title">参与要求</h3>
              <p class="requirements-text">{{ lobby.requirements }}</p>
            </div>
          </div>

          <!-- 侧边栏 -->
          <div class="sidebar">
            <!-- 车主信息 -->
            <div class="host-card">
              <h3 class="card-title">车主</h3>
              <div class="host-info">
                <img :src="lobby.host.avatar" :alt="lobby.host.nickname" class="host-avatar" />
                <div class="host-details">
                  <div class="host-name">{{ lobby.host.nickname }}</div>
                  <div class="host-level">Lv.{{ lobby.host.level }}</div>
                </div>
              </div>
            </div>

            <!-- 成员列表 -->
            <div class="members-card">
              <h3 class="card-title">成员列表 ({{ lobby.currentPlayers }}/{{ lobby.maxPlayers }})</h3>
              <div class="members-list">
                <div class="member-item">
                  <img :src="lobby.host.avatar" :alt="lobby.host.nickname" class="member-avatar" />
                  <div class="member-info">
                    <div class="member-name">{{ lobby.host.nickname }}</div>
                    <div class="member-role">车主</div>
                  </div>
                </div>
                <!-- 其他成员占位 -->
                <div v-for="i in (lobby.currentPlayers - 1)" :key="i" class="member-item">
                  <img src="https://picsum.photos/32/32?random=10" alt="成员" class="member-avatar" />
                  <div class="member-info">
                    <div class="member-name">神秘玩家{{ i }}</div>
                    <div class="member-role">成员</div>
                  </div>
                </div>
                <!-- 空位 -->
                <div v-for="i in (lobby.maxPlayers - lobby.currentPlayers)" :key="`empty-${i}`" class="member-item empty">
                  <div class="empty-avatar">?</div>
                  <div class="member-info">
                    <div class="member-name">等待加入...</div>
                    <div class="member-role">空位</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-else class="loading-container">
        <div class="loading-spinner large"></div>
        <span class="loading-text">加载车队信息...</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const lobby = ref<any>(null)
const isJoined = ref(false)
const isJoining = ref(false)

// 模拟数据
const mockLobby = {
  id: 1,
  script: {
    id: 1,
    title: "迷雾庄园",
    coverImage: "https://picsum.photos/300/400?random=1",
    genre: "推理"
  },
  host: {
    id: 1,
    nickname: "推理大师",
    avatar: "https://picsum.photos/48/48?random=1",
    level: 10
  },
  currentPlayers: 4,
  maxPlayers: 6,
  status: 'waiting',
  startTime: '2024-07-30T19:00:00',
  location: '线上',
  price: 68,
  description: '欢迎新手，氛围轻松，一起探索迷雾庄园的秘密！',
  requirements: '需要有基础的剧本杀经验，能够全程参与游戏',
  createdAt: '2024-07-29T14:30:00'
}

const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    waiting: '等待中',
    full: '已满员',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || '未知'
}

const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const joinLobby = async () => {
  isJoining.value = true
  // 模拟API调用
  await new Promise(resolve => setTimeout(resolve, 1000))
  isJoined.value = true
  isJoining.value = false
}

const leaveLobby = () => {
  isJoined.value = false
}

onMounted(() => {
  // 模拟加载数据
  setTimeout(() => {
    lobby.value = mockLobby
  }, 500)
})
</script>

<style lang="scss" scoped>
.lobby-detail {
  min-height: 100vh;
  padding: 40px 0;
  background: linear-gradient(180deg, #1A1A2E 0%, #16213E 50%, #0F0F1E 100%);
}

.back-navigation {
  margin-bottom: 24px;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 8px;
  padding: 8px 16px;
  color: #B0B0B0;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(0, 245, 212, 0.1);
    border-color: #00F5D4;
    color: #00F5D4;
  }
}

.lobby-info {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 245, 212, 0.1);
  border-radius: 16px;
  padding: 32px;
}

.lobby-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  gap: 24px;
}

.script-info {
  display: flex;
  gap: 20px;
  flex: 1;
}

.script-cover {
  width: 120px;
  height: 160px;
  border-radius: 12px;
  object-fit: cover;
}

.script-details {
  flex: 1;
}

.script-title {
  font-size: 2rem;
  color: #fff;
  font-weight: 700;
  margin-bottom: 16px;
}

.script-meta {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.genre-tag, .player-count {
  padding: 4px 12px;
  background: rgba(0, 245, 212, 0.1);
  color: #00F5D4;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  
  &.status-waiting {
    background: rgba(255, 193, 7, 0.1);
    color: #FFC107;
  }
  
  &.status-full {
    background: rgba(244, 67, 54, 0.1);
    color: #F44336;
  }
}

.lobby-actions {
  flex-shrink: 0;
}

.join-btn, .leave-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.join-btn {
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 245, 212, 0.4);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.leave-btn {
  background: rgba(244, 67, 54, 0.1);
  color: #F44336;
  border: 1px solid rgba(244, 67, 54, 0.3);
  
  &:hover {
    background: rgba(244, 67, 54, 0.2);
  }
}

.lobby-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 32px;
}

.info-section, .description-section, .requirements-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 1.2rem;
  color: #fff;
  font-weight: 600;
  margin-bottom: 16px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 0.8rem;
  color: #888;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.info-value {
  font-size: 0.9rem;
  color: #E0E0E0;
  font-weight: 500;
}

.description-text, .requirements-text {
  color: #B0B0B0;
  line-height: 1.6;
  font-size: 0.9rem;
}

.host-card, .members-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(0, 245, 212, 0.1);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
}

.card-title {
  font-size: 1rem;
  color: #fff;
  font-weight: 600;
  margin-bottom: 16px;
}

.host-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.host-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.host-name {
  font-size: 0.9rem;
  color: #00F5D4;
  font-weight: 500;
}

.host-level {
  font-size: 0.8rem;
  color: #888;
}

.members-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.member-item {
  display: flex;
  align-items: center;
  gap: 12px;
  
  &.empty {
    opacity: 0.5;
  }
}

.member-avatar, .empty-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.empty-avatar {
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 0.8rem;
}

.member-name {
  font-size: 0.85rem;
  color: #E0E0E0;
  font-weight: 500;
}

.member-role {
  font-size: 0.75rem;
  color: #888;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px;
  gap: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 245, 212, 0.1);
  border-top: 3px solid #00F5D4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  
  &.large {
    width: 60px;
    height: 60px;
    border-width: 4px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .lobby-detail {
    padding: 20px 0;
  }
  
  .lobby-info {
    padding: 20px;
  }
  
  .lobby-header {
    flex-direction: column;
    gap: 20px;
  }
  
  .script-info {
    flex-direction: column;
    text-align: center;
  }
  
  .lobby-content {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
}
</style>
