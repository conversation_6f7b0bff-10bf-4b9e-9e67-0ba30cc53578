  <template>
  <div class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <!-- 模态框头部 -->
      <div class="modal-header">
        <h2 class="modal-title">
          <span class="title-icon">🚗</span>
          创建车队
        </h2>
        <button class="close-button" @click="$emit('close')">
          ✕
        </button>
      </div>

      <!-- 模态框内容 -->
      <div class="modal-content">
        <form @submit.prevent="handleSubmit">
          <!-- 选择剧本 -->
          <div class="form-group">
            <label class="form-label">选择剧本 *</label>
            <div class="script-selector">
              <div 
                v-if="selectedScript"
                class="selected-script"
                @click="showScriptPicker = true"
              >
                <img 
                  :src="selectedScript.coverImage" 
                  :alt="selectedScript.title"
                  class="script-cover"
                />
                <div class="script-info">
                  <h3 class="script-title">{{ selectedScript.title }}</h3>
                  <div class="script-meta">
                    <span class="script-genre">{{ selectedScript.genre }}</span>
                    <span class="script-players">{{ selectedScript.playerCount }}人</span>
                    <span class="script-duration">{{ formatDuration(selectedScript.duration) }}</span>
                  </div>
                </div>
                <button type="button" class="change-script-btn">更换</button>
              </div>
              <button 
                v-else
                type="button"
                class="select-script-btn"
                @click="showScriptPicker = true"
              >
                <span class="btn-icon">📚</span>
                <span class="btn-text">选择剧本</span>
              </button>
            </div>
          </div>

          <!-- 基本设置 -->
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">最大人数 *</label>
              <select v-model="formData.maxPlayers" class="form-select" required>
                <option value="">请选择</option>
                <option v-for="count in playerCountOptions" :key="count" :value="count">
                  {{ count }}人
                </option>
              </select>
            </div>
            <div class="form-group">
              <label class="form-label">游戏地点 *</label>
              <select v-model="formData.location" class="form-select" required>
                <option value="">请选择</option>
                <option value="线上">线上</option>
                <option value="线下">线下</option>
                <option value="混合">混合</option>
              </select>
            </div>
          </div>

          <!-- 时间设置 -->
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">开始时间 *</label>
              <input 
                v-model="formData.startTime"
                type="datetime-local" 
                class="form-input"
                :min="minDateTime"
                required
              />
            </div>
            <div class="form-group">
              <label class="form-label">预计时长</label>
              <select v-model="formData.estimatedDuration" class="form-select">
                <option value="">自动计算</option>
                <option value="120">2小时</option>
                <option value="180">3小时</option>
                <option value="240">4小时</option>
                <option value="300">5小时</option>
                <option value="360">6小时</option>
              </select>
            </div>
          </div>

          <!-- 价格设置 -->
          <div class="form-group">
            <label class="form-label">参与费用</label>
            <div class="price-input-wrapper">
              <span class="price-symbol">¥</span>
              <input 
                v-model.number="formData.price"
                type="number" 
                class="price-input"
                placeholder="0"
                min="0"
                step="1"
              />
              <span class="price-unit">元/人</span>
            </div>
            <div class="price-tips">
              <span class="tip-item">💡 建议价格: ¥{{ suggestedPrice }}</span>
              <span class="tip-item">🎯 免费车队更容易招满人</span>
            </div>
          </div>

          <!-- 描述信息 -->
          <div class="form-group">
            <label class="form-label">车队描述</label>
            <textarea 
              v-model="formData.description"
              class="form-textarea"
              placeholder="介绍一下你的车队，比如游戏风格、对玩家的要求等..."
              rows="3"
              maxlength="200"
            ></textarea>
            <div class="char-count">{{ formData.description.length }}/200</div>
          </div>

          <!-- 要求设置 -->
          <div class="form-group">
            <label class="form-label">玩家要求</label>
            <div class="requirements-options">
              <label 
                v-for="requirement in requirementOptions" 
                :key="requirement.value"
                class="requirement-option"
              >
                <input 
                  v-model="selectedRequirements"
                  type="checkbox" 
                  :value="requirement.value"
                  class="requirement-checkbox"
                />
                <span class="requirement-text">{{ requirement.label }}</span>
              </label>
            </div>
          </div>

          <!-- 高级设置 -->
          <div class="form-group">
            <label class="form-label">高级设置</label>
            <div class="advanced-options">
              <label class="advanced-option">
                <input 
                  v-model="formData.allowSpectators"
                  type="checkbox" 
                  class="option-checkbox"
                />
                <span class="option-text">允许观战</span>
              </label>
              <label class="advanced-option">
                <input 
                  v-model="formData.autoStart"
                  type="checkbox" 
                  class="option-checkbox"
                />
                <span class="option-text">人满自动开始</span>
              </label>
              <label class="advanced-option">
                <input 
                  v-model="formData.isPrivate"
                  type="checkbox" 
                  class="option-checkbox"
                />
                <span class="option-text">私密车队（需要密码）</span>
              </label>
            </div>
            <div v-if="formData.isPrivate" class="private-settings">
              <input 
                v-model="formData.password"
                type="text" 
                class="form-input"
                placeholder="设置车队密码"
                maxlength="20"
              />
            </div>
          </div>
        </form>
      </div>

      <!-- 模态框底部 -->
      <div class="modal-footer">
        <button type="button" class="cancel-button" @click="$emit('close')">
          取消
        </button>
        <button 
          type="submit" 
          class="create-button"
          :disabled="!isFormValid || isCreating"
          @click="handleSubmit"
        >
          <span v-if="isCreating" class="loading-spinner"></span>
          <span>{{ isCreating ? '创建中...' : '创建车队' }}</span>
        </button>
      </div>
    </div>

    <!-- 剧本选择器 -->
    <ScriptPickerModal
      v-if="showScriptPicker"
      @close="showScriptPicker = false"
      @select="handleScriptSelect"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import ScriptPickerModal from './ScriptPickerModal.vue'

// 类型定义
interface Script {
  id: number
  title: string
  coverImage: string
  genre: string
  playerCount: number
  duration: number
  suggestedPrice: number
}

interface LobbyFormData {
  scriptId: number | null
  maxPlayers: number | null
  location: string
  startTime: string
  estimatedDuration: number | null
  price: number
  description: string
  allowSpectators: boolean
  autoStart: boolean
  isPrivate: boolean
  password: string
}

// Emits
interface Emits {
  (e: 'close'): void
  (e: 'created', lobby: any): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const isCreating = ref(false)
const showScriptPicker = ref(false)
const selectedScript = ref<Script | null>(null)
const selectedRequirements = ref<string[]>([])

const formData = ref<LobbyFormData>({
  scriptId: null,
  maxPlayers: null,
  location: '',
  startTime: '',
  estimatedDuration: null,
  price: 0,
  description: '',
  allowSpectators: false,
  autoStart: true,
  isPrivate: false,
  password: ''
})

// 静态数据
const playerCountOptions = [4, 5, 6, 7, 8, 9, 10]
const requirementOptions = [
  { value: 'experienced', label: '有剧本杀经验' },
  { value: 'newbie_friendly', label: '新手友好' },
  { value: 'serious', label: '认真游戏' },
  { value: 'casual', label: '轻松娱乐' },
  { value: 'voice_required', label: '需要语音' },
  { value: 'camera_required', label: '需要摄像头' }
]

// 计算属性
const minDateTime = computed(() => {
  const now = new Date()
  now.setMinutes(now.getMinutes() + 30) // 至少30分钟后
  return now.toISOString().slice(0, 16)
})

const suggestedPrice = computed(() => {
  return selectedScript.value?.suggestedPrice || 0
})

const isFormValid = computed(() => {
  return !!(
    selectedScript.value &&
    formData.value.maxPlayers &&
    formData.value.location &&
    formData.value.startTime &&
    (!formData.value.isPrivate || formData.value.password)
  )
})

// 方法
const formatDuration = (minutes: number): string => {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  if (hours > 0) {
    return `${hours}h${mins > 0 ? mins + 'm' : ''}`
  }
  return `${mins}m`
}

const handleOverlayClick = () => {
  emit('close')
}

const handleScriptSelect = (script: Script) => {
  selectedScript.value = script
  formData.value.scriptId = script.id
  
  // 自动设置建议的最大人数和价格
  if (!formData.value.maxPlayers) {
    formData.value.maxPlayers = script.playerCount
  }
  if (formData.value.price === 0) {
    formData.value.price = script.suggestedPrice
  }
  
  showScriptPicker.value = false
}

const handleSubmit = async () => {
  if (!isFormValid.value || isCreating.value) return
  
  try {
    isCreating.value = true
    
    const lobbyData = {
      ...formData.value,
      scriptId: selectedScript.value!.id,
      requirements: selectedRequirements.value.join(','),
      endTime: calculateEndTime()
    }
    
    console.log('创建车队:', lobbyData)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟创建成功
    const newLobby = {
      id: Date.now(),
      script: selectedScript.value,
      host: {
        id: 1,
        nickname: '当前用户',
        avatar: 'https://picsum.photos/40/40?random=99',
        level: 10
      },
      currentPlayers: 1,
      maxPlayers: formData.value.maxPlayers,
      status: 'waiting',
      startTime: formData.value.startTime,
      endTime: calculateEndTime(),
      location: formData.value.location,
      price: formData.value.price,
      description: formData.value.description,
      requirements: selectedRequirements.value.join(','),
      createdAt: new Date().toISOString()
    }
    
    emit('created', newLobby)
  } catch (error) {
    console.error('创建车队失败:', error)
  } finally {
    isCreating.value = false
  }
}

const calculateEndTime = (): string => {
  const startTime = new Date(formData.value.startTime)
  const duration = formData.value.estimatedDuration || selectedScript.value?.duration || 240
  const endTime = new Date(startTime.getTime() + duration * 60 * 1000)
  return endTime.toISOString()
}

// 生命周期
onMounted(() => {
  // 设置默认开始时间为1小时后
  const defaultTime = new Date()
  defaultTime.setHours(defaultTime.getHours() + 1)
  defaultTime.setMinutes(0)
  formData.value.startTime = defaultTime.toISOString().slice(0, 16)
})
</script>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-container {
  background: rgba(26, 26, 46, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 16px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #fff;
  display: flex;
  align-items: center;
  gap: 8px;
  
  .title-icon {
    font-size: 1.3rem;
  }
}

.close-button {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: #888;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.2);
    color: #fff;
  }
}

.modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-label {
  display: block;
  font-size: 0.9rem;
  color: #B0B0B0;
  font-weight: 500;
  margin-bottom: 8px;
}

.form-input, .form-select, .form-textarea {
  width: 100%;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 8px;
  color: #fff;
  font-size: 0.9rem;
  
  &::placeholder {
    color: #666;
  }
  
  &:focus {
    outline: none;
    border-color: #00F5D4;
    box-shadow: 0 0 0 2px rgba(0, 245, 212, 0.1);
  }
}

.script-selector {
  .selected-script {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(0, 245, 212, 0.2);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #00F5D4;
    }
  }
  
  .script-cover {
    width: 48px;
    height: 48px;
    border-radius: 6px;
    object-fit: cover;
  }
  
  .script-info {
    flex: 1;
  }
  
  .script-title {
    font-size: 1rem;
    color: #fff;
    margin-bottom: 4px;
  }
  
  .script-meta {
    display: flex;
    gap: 12px;
    font-size: 0.8rem;
    color: #888;
  }
  
  .change-script-btn {
    padding: 6px 12px;
    background: rgba(0, 245, 212, 0.1);
    border: 1px solid rgba(0, 245, 212, 0.3);
    border-radius: 6px;
    color: #00F5D4;
    font-size: 0.8rem;
    cursor: pointer;
    
    &:hover {
      background: rgba(0, 245, 212, 0.2);
    }
  }
  
  .select-script-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border: 2px dashed rgba(0, 245, 212, 0.3);
    border-radius: 8px;
    color: #00F5D4;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(0, 245, 212, 0.05);
      border-color: #00F5D4;
    }
  }
}

.price-input-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.price-symbol, .price-unit {
  color: #B0B0B0;
  font-size: 0.9rem;
}

.price-input {
  flex: 1;
  max-width: 120px;
}

.price-tips {
  display: flex;
  gap: 16px;
  margin-top: 8px;
  font-size: 0.8rem;
  color: #888;
}

.char-count {
  text-align: right;
  font-size: 0.75rem;
  color: #666;
  margin-top: 4px;
}

.requirements-options, .advanced-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 8px;
}

.requirement-option, .advanced-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}

.requirement-checkbox, .option-checkbox {
  accent-color: #00F5D4;
}

.requirement-text, .option-text {
  font-size: 0.85rem;
  color: #B0B0B0;
}

.private-settings {
  margin-top: 12px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.cancel-button, .create-button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-button {
  background: rgba(255, 255, 255, 0.05);
  color: #B0B0B0;
  border: 1px solid rgba(255, 255, 255, 0.1);
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
  }
}

.create-button {
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  display: flex;
  align-items: center;
  gap: 8px;
  
  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 245, 212, 0.3);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(26, 26, 46, 0.3);
  border-top: 2px solid #1A1A2E;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .modal-overlay {
    padding: 10px;
  }
  
  .modal-container {
    max-height: 95vh;
  }
  
  .modal-header, .modal-content, .modal-footer {
    padding: 16px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .requirements-options, .advanced-options {
    grid-template-columns: 1fr;
  }
  
  .price-tips {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
