<template>
  <div v-if="isVisible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h2 class="modal-title">房间详情</h2>
        <button class="close-btn" @click="$emit('close')">
          <span class="close-icon">×</span>
        </button>
      </div>
      
      <div class="modal-body">
        <div class="lobby-info">
          <div class="lobby-header">
            <h3 class="lobby-title">{{ lobby.title }}</h3>
            <div class="lobby-status" :class="lobby.status">
              {{ getStatusText(lobby.status) }}
            </div>
          </div>
          
          <div class="lobby-meta">
            <div class="meta-item">
              <span class="meta-label">房主</span>
              <span class="meta-value">{{ lobby.host }}</span>
            </div>
            
            <div class="meta-item">
              <span class="meta-label">剧本</span>
              <span class="meta-value">{{ lobby.script }}</span>
            </div>
            
            <div class="meta-item">
              <span class="meta-label">人数</span>
              <span class="meta-value">{{ lobby.currentPlayers }}/{{ lobby.maxPlayers }}</span>
            </div>
            
            <div class="meta-item">
              <span class="meta-label">开始时间</span>
              <span class="meta-value">{{ lobby.startTime }}</span>
            </div>
            
            <div class="meta-item">
              <span class="meta-label">地点</span>
              <span class="meta-value">{{ lobby.location }}</span>
            </div>
            
            <div class="meta-item">
              <span class="meta-label">费用</span>
              <span class="meta-value">{{ lobby.price }}</span>
            </div>
          </div>
          
          <div class="lobby-description" v-if="lobby.description">
            <h4 class="description-title">房间描述</h4>
            <p class="description-text">{{ lobby.description }}</p>
          </div>
          
          <div class="players-section">
            <h4 class="players-title">参与玩家</h4>
            <div class="players-list">
              <div 
                v-for="player in lobby.players" 
                :key="player.id"
                class="player-item"
              >
                <div class="player-avatar">
                  <img 
                    v-if="player.avatar" 
                    :src="player.avatar" 
                    :alt="player.name"
                    class="avatar-image"
                  />
                  <div v-else class="avatar-placeholder">
                    {{ player.name.charAt(0) }}
                  </div>
                </div>
                
                <div class="player-info">
                  <span class="player-name">{{ player.name }}</span>
                  <span class="player-role" v-if="player.role">{{ player.role }}</span>
                </div>
                
                <div class="player-badges">
                  <span v-if="player.isHost" class="host-badge">房主</span>
                  <span v-if="player.isReady" class="ready-badge">已准备</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="requirements-section" v-if="lobby.requirements?.length">
            <h4 class="requirements-title">参与要求</h4>
            <ul class="requirements-list">
              <li 
                v-for="requirement in lobby.requirements" 
                :key="requirement"
                class="requirement-item"
              >
                {{ requirement }}
              </li>
            </ul>
          </div>
        </div>
      </div>
      
      <div class="modal-footer">
        <button 
          class="cancel-btn" 
          @click="$emit('close')"
        >
          关闭
        </button>
        
        <button 
          v-if="canJoin"
          class="join-btn" 
          @click="handleJoin"
          :disabled="isJoining"
        >
          <span v-if="isJoining" class="loading-spinner"></span>
          <span>{{ isJoining ? '加入中...' : '加入房间' }}</span>
        </button>
        
        <button 
          v-else-if="isInLobby"
          class="leave-btn" 
          @click="handleLeave"
          :disabled="isLeaving"
        >
          <span v-if="isLeaving" class="loading-spinner"></span>
          <span>{{ isLeaving ? '退出中...' : '退出房间' }}</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Player {
  id: string
  name: string
  avatar?: string
  role?: string
  isHost: boolean
  isReady: boolean
}

interface Lobby {
  id: string
  title: string
  host: string
  script: string
  currentPlayers: number
  maxPlayers: number
  startTime: string
  location: string
  price: string
  status: 'waiting' | 'full' | 'started' | 'ended'
  description?: string
  players: Player[]
  requirements?: string[]
}

interface Props {
  isVisible: boolean
  lobby: Lobby
}

const props = defineProps<Props>()
const emit = defineEmits<{
  close: []
  join: [lobbyId: string]
  leave: [lobbyId: string]
}>()

const isJoining = ref(false)
const isLeaving = ref(false)

// 假设当前用户ID
const currentUserId = 'current-user-id'

const isInLobby = computed(() => 
  props.lobby.players.some(player => player.id === currentUserId)
)

const canJoin = computed(() => 
  !isInLobby.value && 
  props.lobby.status === 'waiting' && 
  props.lobby.currentPlayers < props.lobby.maxPlayers
)

const getStatusText = (status: string) => {
  const statusMap = {
    waiting: '等待中',
    full: '已满员',
    started: '已开始',
    ended: '已结束'
  }
  return statusMap[status] || status
}

const handleOverlayClick = () => {
  emit('close')
}

const handleJoin = async () => {
  if (isJoining.value) return
  
  try {
    isJoining.value = true
    emit('join', props.lobby.id)
    // 这里应该等待加入成功的响应
    await new Promise(resolve => setTimeout(resolve, 1000))
  } catch (error) {
    console.error('加入房间失败:', error)
  } finally {
    isJoining.value = false
  }
}

const handleLeave = async () => {
  if (isLeaving.value) return
  
  try {
    isLeaving.value = true
    emit('leave', props.lobby.id)
    // 这里应该等待退出成功的响应
    await new Promise(resolve => setTimeout(resolve, 1000))
  } catch (error) {
    console.error('退出房间失败:', error)
  } finally {
    isLeaving.value = false
  }
}
</script>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal-content {
  background: linear-gradient(180deg, #1A1A2E 0%, #16213E 100%);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 20px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-title {
  font-size: 1.5rem;
  color: #fff;
  font-weight: 700;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  color: #B0B0B0;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
  }
  
  .close-icon {
    font-size: 1.5rem;
    line-height: 1;
  }
}

.modal-body {
  padding: 32px;
  max-height: 60vh;
  overflow-y: auto;
}

.lobby-info {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.lobby-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.lobby-title {
  font-size: 1.3rem;
  color: #fff;
  font-weight: 700;
  margin: 0;
}

.lobby-status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  
  &.waiting {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
    border: 1px solid rgba(76, 175, 80, 0.3);
  }
  
  &.full {
    background: rgba(255, 193, 7, 0.2);
    color: #FFC107;
    border: 1px solid rgba(255, 193, 7, 0.3);
  }
  
  &.started, &.ended {
    background: rgba(244, 67, 54, 0.2);
    color: #F44336;
    border: 1px solid rgba(244, 67, 54, 0.3);
  }
}

.lobby-meta {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.meta-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.meta-label {
  font-size: 0.9rem;
  color: #B0B0B0;
  font-weight: 500;
}

.meta-value {
  font-size: 1rem;
  color: #fff;
  font-weight: 600;
}

.lobby-description {
  .description-title {
    font-size: 1.1rem;
    color: #00F5D4;
    font-weight: 600;
    margin-bottom: 12px;
  }
  
  .description-text {
    color: #E0E0E0;
    line-height: 1.6;
    margin: 0;
  }
}

.players-section, .requirements-section {
  .players-title, .requirements-title {
    font-size: 1.1rem;
    color: #00F5D4;
    font-weight: 600;
    margin-bottom: 16px;
  }
}

.players-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.player-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.player-avatar {
  width: 40px;
  height: 40px;
  
  .avatar-image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
  }
  
  .avatar-placeholder {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(135deg, #00F5D4, #FF00E4);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    color: #1A1A2E;
  }
}

.player-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
  
  .player-name {
    color: #fff;
    font-weight: 600;
  }
  
  .player-role {
    color: #B0B0B0;
    font-size: 0.85rem;
  }
}

.player-badges {
  display: flex;
  gap: 8px;
}

.host-badge, .ready-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.host-badge {
  background: rgba(255, 193, 7, 0.2);
  color: #FFC107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.ready-badge {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.requirements-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.requirement-item {
  color: #E0E0E0;
  padding-left: 16px;
  position: relative;
  
  &::before {
    content: '•';
    color: #00F5D4;
    position: absolute;
    left: 0;
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 24px 32px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.cancel-btn, .join-btn, .leave-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.cancel-btn {
  background: rgba(255, 255, 255, 0.1);
  color: #E0E0E0;
  border: 1px solid rgba(255, 255, 255, 0.2);
  
  &:hover {
    background: rgba(255, 255, 255, 0.15);
  }
}

.join-btn {
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 245, 212, 0.4);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
  }
}

.leave-btn {
  background: rgba(244, 67, 54, 0.2);
  color: #F44336;
  border: 1px solid rgba(244, 67, 54, 0.3);
  
  &:hover:not(:disabled) {
    background: rgba(244, 67, 54, 0.3);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 响应式设计
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    margin: 20px;
  }
  
  .modal-header, .modal-body, .modal-footer {
    padding: 20px;
  }
  
  .lobby-meta {
    grid-template-columns: 1fr;
  }
  
  .modal-footer {
    flex-direction: column;
  }
  
  .cancel-btn, .join-btn, .leave-btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
