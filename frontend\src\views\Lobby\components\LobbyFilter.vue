<template>
  <div class="lobby-filter">
    <div class="filter-container">
      <!-- 搜索框 -->
      <div class="search-section">
        <div class="search-input-wrapper">
          <i class="search-icon">🔍</i>
          <input 
            v-model="searchKeyword"
            type="text" 
            placeholder="搜索剧本名称或房主昵称..."
            class="search-input"
            @input="handleSearch"
          />
          <button 
            v-if="searchKeyword" 
            class="clear-button"
            @click="clearSearch"
          >
            ✕
          </button>
        </div>
      </div>

      <!-- 快速筛选标签 -->
      <div class="quick-filters">
        <button 
          v-for="quickFilter in quickFilters" 
          :key="quickFilter.key"
          class="quick-filter-btn"
          :class="{ active: activeQuickFilter === quickFilter.key }"
          @click="setQuickFilter(quickFilter.key)"
        >
          <span class="filter-icon">{{ quickFilter.icon }}</span>
          <span class="filter-text">{{ quickFilter.label }}</span>
        </button>
      </div>

      <!-- 详细筛选 -->
      <div class="detailed-filters">
        <!-- 剧本类型 -->
        <div class="filter-group">
          <label class="filter-label">剧本类型</label>
          <div class="filter-options">
            <button 
              v-for="genre in genres" 
              :key="genre"
              class="filter-option"
              :class="{ active: selectedGenres.includes(genre) }"
              @click="toggleGenre(genre)"
            >
              {{ genre }}
            </button>
          </div>
        </div>

        <!-- 人数要求 -->
        <div class="filter-group">
          <label class="filter-label">人数要求</label>
          <div class="filter-options">
            <button 
              v-for="playerCount in playerCounts" 
              :key="playerCount"
              class="filter-option"
              :class="{ active: selectedPlayerCounts.includes(playerCount) }"
              @click="togglePlayerCount(playerCount)"
            >
              {{ playerCount }}人
            </button>
          </div>
        </div>

        <!-- 游戏时间 -->
        <div class="filter-group">
          <label class="filter-label">开始时间</label>
          <div class="time-range">
            <select v-model="timeRange" class="time-select">
              <option value="">不限</option>
              <option value="today">今天</option>
              <option value="tomorrow">明天</option>
              <option value="week">本周</option>
              <option value="custom">自定义</option>
            </select>
            <div v-if="timeRange === 'custom'" class="custom-time">
              <input 
                v-model="customStartTime"
                type="datetime-local" 
                class="time-input"
              />
              <span class="time-separator">至</span>
              <input 
                v-model="customEndTime"
                type="datetime-local" 
                class="time-input"
              />
            </div>
          </div>
        </div>

        <!-- 价格范围 -->
        <div class="filter-group">
          <label class="filter-label">价格范围</label>
          <div class="price-range">
            <input 
              v-model.number="priceRange.min"
              type="number" 
              placeholder="最低价"
              class="price-input"
              min="0"
            />
            <span class="price-separator">-</span>
            <input 
              v-model.number="priceRange.max"
              type="number" 
              placeholder="最高价"
              class="price-input"
              min="0"
            />
            <span class="price-unit">元</span>
          </div>
        </div>

        <!-- 游戏地点 -->
        <div class="filter-group">
          <label class="filter-label">游戏地点</label>
          <div class="filter-options">
            <button 
              v-for="location in locations" 
              :key="location"
              class="filter-option"
              :class="{ active: selectedLocations.includes(location) }"
              @click="toggleLocation(location)"
            >
              {{ location }}
            </button>
          </div>
        </div>

        <!-- 排序方式 -->
        <div class="filter-group">
          <label class="filter-label">排序方式</label>
          <select v-model="sortBy" class="sort-select">
            <option value="createTime">创建时间</option>
            <option value="startTime">开始时间</option>
            <option value="playerCount">人数进度</option>
            <option value="price">价格</option>
          </select>
          <button 
            class="sort-order-btn"
            :class="{ desc: sortOrder === 'desc' }"
            @click="toggleSortOrder"
          >
            {{ sortOrder === 'asc' ? '↑' : '↓' }}
          </button>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="filter-actions">
        <button class="reset-button" @click="resetFilters">
          重置筛选
        </button>
        <button class="apply-button" @click="applyFilters">
          应用筛选 ({{ filteredCount }})
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

// 类型定义
interface FilterOptions {
  searchKeyword: string
  selectedGenres: string[]
  selectedPlayerCounts: number[]
  selectedLocations: string[]
  timeRange: string
  customStartTime: string
  customEndTime: string
  priceRange: { min: number | null; max: number | null }
  sortBy: string
  sortOrder: 'asc' | 'desc'
}

// Props
interface Props {
  filteredCount?: number
}

const props = withDefaults(defineProps<Props>(), {
  filteredCount: 0
})

// Emits
interface Emits {
  (e: 'filter-change', filters: FilterOptions): void
  (e: 'search', keyword: string): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const searchKeyword = ref('')
const activeQuickFilter = ref('all')

const selectedGenres = ref<string[]>([])
const selectedPlayerCounts = ref<number[]>([])
const selectedLocations = ref<string[]>([])
const timeRange = ref('')
const customStartTime = ref('')
const customEndTime = ref('')
const priceRange = ref<{ min: number | null; max: number | null }>({
  min: null,
  max: null
})
const sortBy = ref('createTime')
const sortOrder = ref<'asc' | 'desc'>('desc')

// 静态数据
const quickFilters = [
  { key: 'all', label: '全部', icon: '🎯' },
  { key: 'waiting', label: '等待中', icon: '⏳' },
  { key: 'urgent', label: '急缺人手', icon: '🚨' },
  { key: 'newbie', label: '新手友好', icon: '🌟' },
  { key: 'today', label: '今日开始', icon: '📅' }
]

const genres = ['推理', '恐怖', '情感', '冒险', '悬疑', '古风', '现代', '科幻']
const playerCounts = [4, 5, 6, 7, 8, 9]
const locations = ['线上', '线下', '混合']

// 计算属性
const currentFilters = computed((): FilterOptions => ({
  searchKeyword: searchKeyword.value,
  selectedGenres: selectedGenres.value,
  selectedPlayerCounts: selectedPlayerCounts.value,
  selectedLocations: selectedLocations.value,
  timeRange: timeRange.value,
  customStartTime: customStartTime.value,
  customEndTime: customEndTime.value,
  priceRange: priceRange.value,
  sortBy: sortBy.value,
  sortOrder: sortOrder.value
}))

// 方法
const handleSearch = () => {
  emit('search', searchKeyword.value)
}

const clearSearch = () => {
  searchKeyword.value = ''
  handleSearch()
}

const setQuickFilter = (filterKey: string) => {
  activeQuickFilter.value = filterKey
  
  // 根据快速筛选重置其他筛选条件
  switch (filterKey) {
    case 'all':
      resetFilters()
      break
    case 'waiting':
      // 只显示等待中的车队
      break
    case 'urgent':
      // 显示急缺人手的车队
      break
    case 'newbie':
      // 显示新手友好的车队
      break
    case 'today':
      timeRange.value = 'today'
      break
  }
  
  applyFilters()
}

const toggleGenre = (genre: string) => {
  const index = selectedGenres.value.indexOf(genre)
  if (index > -1) {
    selectedGenres.value.splice(index, 1)
  } else {
    selectedGenres.value.push(genre)
  }
}

const togglePlayerCount = (count: number) => {
  const index = selectedPlayerCounts.value.indexOf(count)
  if (index > -1) {
    selectedPlayerCounts.value.splice(index, 1)
  } else {
    selectedPlayerCounts.value.push(count)
  }
}

const toggleLocation = (location: string) => {
  const index = selectedLocations.value.indexOf(location)
  if (index > -1) {
    selectedLocations.value.splice(index, 1)
  } else {
    selectedLocations.value.push(location)
  }
}

const toggleSortOrder = () => {
  sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
}

const resetFilters = () => {
  searchKeyword.value = ''
  activeQuickFilter.value = 'all'
  selectedGenres.value = []
  selectedPlayerCounts.value = []
  selectedLocations.value = []
  timeRange.value = ''
  customStartTime.value = ''
  customEndTime.value = ''
  priceRange.value = { min: null, max: null }
  sortBy.value = 'createTime'
  sortOrder.value = 'desc'
}

const applyFilters = () => {
  emit('filter-change', currentFilters.value)
}

// 监听筛选条件变化
watch(currentFilters, () => {
  applyFilters()
}, { deep: true })
</script>

<style lang="scss" scoped>
.lobby-filter {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 245, 212, 0.1);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
}

.filter-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.search-section {
  .search-input-wrapper {
    position: relative;
    max-width: 400px;
    
    .search-icon {
      position: absolute;
      left: 16px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 1rem;
      color: #888;
    }
    
    .search-input {
      width: 100%;
      padding: 12px 16px 12px 48px;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(0, 245, 212, 0.2);
      border-radius: 12px;
      color: #fff;
      font-size: 0.9rem;
      
      &::placeholder {
        color: #888;
      }
      
      &:focus {
        outline: none;
        border-color: #00F5D4;
        box-shadow: 0 0 0 2px rgba(0, 245, 212, 0.1);
      }
    }
    
    .clear-button {
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
      background: none;
      border: none;
      color: #888;
      cursor: pointer;
      font-size: 1rem;
      
      &:hover {
        color: #fff;
      }
    }
  }
}

.quick-filters {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.quick-filter-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 20px;
  color: #B0B0B0;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: rgba(0, 245, 212, 0.4);
    color: #00F5D4;
  }
  
  &.active {
    background: rgba(0, 245, 212, 0.1);
    border-color: #00F5D4;
    color: #00F5D4;
  }
  
  .filter-icon {
    font-size: 0.9rem;
  }
}

.detailed-filters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-label {
  font-size: 0.85rem;
  color: #B0B0B0;
  font-weight: 500;
}

.filter-options {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-option {
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  color: #B0B0B0;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: rgba(0, 245, 212, 0.4);
    color: #00F5D4;
  }
  
  &.active {
    background: rgba(0, 245, 212, 0.1);
    border-color: #00F5D4;
    color: #00F5D4;
  }
}

.time-range, .price-range {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.time-select, .sort-select {
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 8px;
  color: #fff;
  font-size: 0.85rem;
  
  &:focus {
    outline: none;
    border-color: #00F5D4;
  }
  
  option {
    background: #1A1A2E;
    color: #fff;
  }
}

.custom-time {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

.time-input {
  padding: 6px 8px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 6px;
  color: #fff;
  font-size: 0.8rem;
  
  &:focus {
    outline: none;
    border-color: #00F5D4;
  }
}

.time-separator, .price-separator {
  color: #888;
  font-size: 0.85rem;
}

.price-input {
  width: 80px;
  padding: 6px 8px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 6px;
  color: #fff;
  font-size: 0.85rem;
  
  &:focus {
    outline: none;
    border-color: #00F5D4;
  }
}

.price-unit {
  color: #888;
  font-size: 0.85rem;
}

.sort-order-btn {
  padding: 6px 8px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 6px;
  color: #B0B0B0;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #00F5D4;
    color: #00F5D4;
  }
  
  &.desc {
    color: #00F5D4;
    border-color: #00F5D4;
  }
}

.filter-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.reset-button, .apply-button {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.reset-button {
  background: rgba(255, 255, 255, 0.05);
  color: #B0B0B0;
  border: 1px solid rgba(255, 255, 255, 0.1);
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
  }
}

.apply-button {
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 245, 212, 0.3);
  }
}

@media (max-width: 768px) {
  .lobby-filter {
    padding: 16px;
  }
  
  .detailed-filters {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .filter-actions {
    flex-direction: column;
  }
  
  .reset-button, .apply-button {
    width: 100%;
  }
}
</style>
