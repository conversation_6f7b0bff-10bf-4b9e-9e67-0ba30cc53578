<template>
  <div class="lobby-list">
    <!-- 列表头部 -->
    <div class="list-header">
      <div class="result-info">
        <span class="result-count">找到 {{ totalCount }} 个车队</span>
        <span class="result-status" v-if="isLoading">正在搜索...</span>
      </div>
      <div class="view-toggle">
        <button 
          class="toggle-btn"
          :class="{ active: viewMode === 'grid' }"
          @click="setViewMode('grid')"
          title="网格视图"
        >
          ⊞
        </button>
        <button 
          class="toggle-btn"
          :class="{ active: viewMode === 'list' }"
          @click="setViewMode('list')"
          title="列表视图"
        >
          ☰
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <span class="loading-text">正在加载车队信息...</span>
    </div>

    <!-- 空状态 -->
    <div v-else-if="lobbies.length === 0" class="empty-state">
      <div class="empty-icon">🚗</div>
      <h3 class="empty-title">暂无符合条件的车队</h3>
      <p class="empty-description">
        试试调整筛选条件，或者
        <button class="create-lobby-link" @click="$emit('create-lobby')">
          创建一个新车队
        </button>
      </p>
    </div>

    <!-- 车队列表 -->
    <div v-else class="lobbies-container" :class="`view-${viewMode}`">
      <LobbyCard
        v-for="lobby in lobbies"
        :key="lobby.id"
        :lobby="lobby"
        :view-mode="viewMode"
        @join-lobby="handleJoinLobby"
        @view-details="handleViewDetails"
        @favorite="handleFavorite"
      />
    </div>

    <!-- 分页 -->
    <div v-if="totalPages > 1" class="pagination">
      <button 
        class="page-btn prev-btn"
        :disabled="currentPage <= 1"
        @click="goToPage(currentPage - 1)"
      >
        ‹ 上一页
      </button>
      
      <div class="page-numbers">
        <button 
          v-for="page in visiblePages"
          :key="page"
          class="page-btn"
          :class="{ active: page === currentPage, ellipsis: page === '...' }"
          :disabled="page === '...'"
          @click="page !== '...' && goToPage(page as number)"
        >
          {{ page }}
        </button>
      </div>
      
      <button 
        class="page-btn next-btn"
        :disabled="currentPage >= totalPages"
        @click="goToPage(currentPage + 1)"
      >
        下一页 ›
      </button>
    </div>

    <!-- 快速操作浮动按钮 -->
    <div class="floating-actions">
      <button 
        class="floating-btn refresh-btn"
        @click="refreshList"
        title="刷新列表"
      >
        🔄
      </button>
      <button 
        class="floating-btn create-btn"
        @click="$emit('create-lobby')"
        title="创建车队"
      >
        ➕
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import LobbyCard from '../../../components/LobbyCard.vue'

// 类型定义
interface Lobby {
  id: number
  script: {
    id: number
    title: string
    coverImage: string
    genre: string
  }
  host: {
    id: number
    nickname: string
    avatar: string
    level: number
  }
  currentPlayers: number
  maxPlayers: number
  status: 'waiting' | 'full' | 'in_progress'
  startTime: string
  endTime: string
  location: string
  price: number
  description: string
  requirements: string
  createdAt: string
  isFavorite?: boolean
}

// Props
interface Props {
  lobbies: Lobby[]
  totalCount: number
  currentPage: number
  totalPages: number
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false
})

// Emits
interface Emits {
  (e: 'join-lobby', lobbyId: number): void
  (e: 'view-details', lobbyId: number): void
  (e: 'favorite', lobbyId: number): void
  (e: 'create-lobby'): void
  (e: 'page-change', page: number): void
  (e: 'refresh'): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const viewMode = ref<'grid' | 'list'>('grid')

// 计算属性
const visiblePages = computed(() => {
  const pages: (number | string)[] = []
  const total = props.totalPages
  const current = props.currentPage
  
  if (total <= 7) {
    // 如果总页数小于等于7，显示所有页码
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    // 复杂分页逻辑
    pages.push(1)
    
    if (current > 4) {
      pages.push('...')
    }
    
    const start = Math.max(2, current - 1)
    const end = Math.min(total - 1, current + 1)
    
    for (let i = start; i <= end; i++) {
      if (!pages.includes(i)) {
        pages.push(i)
      }
    }
    
    if (current < total - 3) {
      pages.push('...')
    }
    
    if (!pages.includes(total)) {
      pages.push(total)
    }
  }
  
  return pages
})

// 方法
const setViewMode = (mode: 'grid' | 'list') => {
  viewMode.value = mode
  // 保存用户偏好到本地存储
  localStorage.setItem('lobby-view-mode', mode)
}

const handleJoinLobby = (lobbyId: number) => {
  emit('join-lobby', lobbyId)
}

const handleViewDetails = (lobbyId: number) => {
  emit('view-details', lobbyId)
}

const handleFavorite = (lobbyId: number) => {
  emit('favorite', lobbyId)
}

const goToPage = (page: number) => {
  if (page >= 1 && page <= props.totalPages) {
    emit('page-change', page)
  }
}

const refreshList = () => {
  emit('refresh')
}

// 初始化视图模式
const initViewMode = () => {
  const savedMode = localStorage.getItem('lobby-view-mode') as 'grid' | 'list'
  if (savedMode) {
    viewMode.value = savedMode
  }
}

// 组件挂载时初始化
initViewMode()
</script>

<style lang="scss" scoped>
.lobby-list {
  position: relative;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.result-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.result-count {
  font-size: 1rem;
  color: #fff;
  font-weight: 500;
}

.result-status {
  font-size: 0.85rem;
  color: #00F5D4;
  animation: pulse 1.5s ease-in-out infinite;
}

.view-toggle {
  display: flex;
  gap: 4px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 4px;
}

.toggle-btn {
  padding: 8px 12px;
  background: transparent;
  border: none;
  border-radius: 6px;
  color: #888;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    color: #00F5D4;
    background: rgba(0, 245, 212, 0.1);
  }
  
  &.active {
    color: #00F5D4;
    background: rgba(0, 245, 212, 0.2);
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  gap: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 245, 212, 0.1);
  border-top: 3px solid #00F5D4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: #B0B0B0;
  font-size: 0.9rem;
}

.empty-state {
  text-align: center;
  padding: 80px 20px;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 24px;
  opacity: 0.5;
}

.empty-title {
  font-size: 1.5rem;
  color: #fff;
  margin-bottom: 12px;
}

.empty-description {
  color: #B0B0B0;
  font-size: 1rem;
  line-height: 1.5;
}

.create-lobby-link {
  color: #00F5D4;
  background: none;
  border: none;
  text-decoration: underline;
  cursor: pointer;
  font-size: inherit;
  
  &:hover {
    color: #FF00E4;
  }
}

.lobbies-container {
  &.view-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 24px;
  }
  
  &.view-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 40px;
  padding: 20px 0;
}

.page-btn {
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 8px;
  color: #B0B0B0;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 40px;
  
  &:hover:not(:disabled) {
    background: rgba(0, 245, 212, 0.1);
    border-color: #00F5D4;
    color: #00F5D4;
  }
  
  &.active {
    background: rgba(0, 245, 212, 0.2);
    border-color: #00F5D4;
    color: #00F5D4;
  }
  
  &:disabled {
    opacity: 0.3;
    cursor: not-allowed;
  }
  
  &.ellipsis {
    background: transparent;
    border: none;
    cursor: default;
    
    &:hover {
      background: transparent;
      color: #888;
    }
  }
}

.page-numbers {
  display: flex;
  gap: 4px;
}

.prev-btn, .next-btn {
  padding: 8px 16px;
  font-weight: 500;
}

.floating-actions {
  position: fixed;
  bottom: 24px;
  right: 24px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  z-index: 100;
}

.floating-btn {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  
  &:hover {
    transform: scale(1.1);
  }
  
  &.refresh-btn {
    background: rgba(255, 255, 255, 0.1);
    color: #B0B0B0;
    backdrop-filter: blur(10px);
    
    &:hover {
      background: rgba(0, 245, 212, 0.2);
      color: #00F5D4;
    }
  }
  
  &.create-btn {
    background: linear-gradient(135deg, #00F5D4, #00C9A7);
    color: #1A1A2E;
    
    &:hover {
      box-shadow: 0 6px 20px rgba(0, 245, 212, 0.4);
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@media (max-width: 768px) {
  .list-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .result-info {
    justify-content: center;
  }
  
  .view-toggle {
    align-self: center;
  }
  
  .lobbies-container.view-grid {
    grid-template-columns: 1fr;
  }
  
  .pagination {
    flex-wrap: wrap;
    gap: 4px;
  }
  
  .page-btn {
    padding: 6px 10px;
    font-size: 0.8rem;
    min-width: 36px;
  }
  
  .floating-actions {
    bottom: 16px;
    right: 16px;
  }
  
  .floating-btn {
    width: 48px;
    height: 48px;
    font-size: 1rem;
  }
}
</style>
