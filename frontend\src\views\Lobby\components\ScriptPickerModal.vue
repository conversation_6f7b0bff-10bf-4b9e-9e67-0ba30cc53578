<template>
  <div v-if="isVisible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h2 class="modal-title">选择剧本</h2>
        <button class="close-btn" @click="$emit('close')">
          <span class="close-icon">×</span>
        </button>
      </div>
      
      <div class="modal-body">
        <div class="search-section">
          <div class="search-bar">
            <input 
              v-model="searchQuery"
              type="text" 
              placeholder="搜索剧本名称..."
              class="search-input"
            />
            <span class="search-icon">🔍</span>
          </div>
          
          <div class="filter-tabs">
            <button 
              v-for="category in categories" 
              :key="category.value"
              class="filter-tab"
              :class="{ active: selectedCategory === category.value }"
              @click="selectedCategory = category.value"
            >
              {{ category.label }}
            </button>
          </div>
        </div>
        
        <div class="scripts-grid">
          <div 
            v-for="script in filteredScripts" 
            :key="script.id"
            class="script-card"
            :class="{ selected: selectedScript?.id === script.id }"
            @click="selectScript(script)"
          >
            <div class="script-image">
              <img 
                v-if="script.image" 
                :src="script.image" 
                :alt="script.title"
                class="script-img"
              />
              <div v-else class="script-placeholder">
                <span class="placeholder-icon">📚</span>
              </div>
              
              <div class="script-overlay">
                <span class="select-icon">{{ selectedScript?.id === script.id ? '✓' : '+' }}</span>
              </div>
            </div>
            
            <div class="script-info">
              <h3 class="script-title">{{ script.title }}</h3>
              <p class="script-description">{{ script.description }}</p>
              
              <div class="script-meta">
                <div class="meta-item">
                  <span class="meta-icon">👥</span>
                  <span class="meta-text">{{ script.playerCount }}人</span>
                </div>
                
                <div class="meta-item">
                  <span class="meta-icon">⏱️</span>
                  <span class="meta-text">{{ script.duration }}</span>
                </div>
                
                <div class="meta-item">
                  <span class="meta-icon">⭐</span>
                  <span class="meta-text">{{ script.difficulty }}/5</span>
                </div>
              </div>
              
              <div class="script-tags">
                <span 
                  v-for="tag in script.tags.slice(0, 3)" 
                  :key="tag"
                  class="script-tag"
                >
                  {{ tag }}
                </span>
                <span v-if="script.tags.length > 3" class="more-tags">
                  +{{ script.tags.length - 3 }}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <div v-if="filteredScripts.length === 0" class="empty-state">
          <div class="empty-icon">📚</div>
          <h3 class="empty-title">没有找到匹配的剧本</h3>
          <p class="empty-text">尝试调整搜索条件或选择其他分类</p>
        </div>
      </div>
      
      <div class="modal-footer">
        <button 
          class="cancel-btn" 
          @click="$emit('close')"
        >
          取消
        </button>
        
        <button 
          class="confirm-btn" 
          @click="handleConfirm"
          :disabled="!selectedScript"
        >
          确认选择
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Script {
  id: string
  title: string
  description: string
  image?: string
  playerCount: string
  duration: string
  difficulty: number
  tags: string[]
  category: string
}

interface Props {
  isVisible: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  close: []
  select: [script: Script]
}>()

const searchQuery = ref('')
const selectedCategory = ref('all')
const selectedScript = ref<Script | null>(null)

const categories = [
  { label: '全部', value: 'all' },
  { label: '推理', value: 'mystery' },
  { label: '恐怖', value: 'horror' },
  { label: '情感', value: 'emotion' },
  { label: '欢乐', value: 'comedy' },
  { label: '古风', value: 'ancient' },
  { label: '现代', value: 'modern' }
]

// 模拟剧本数据
const scripts = ref<Script[]>([
  {
    id: '1',
    title: '迷雾庄园',
    description: '一个充满悬疑的古老庄园，隐藏着不为人知的秘密...',
    image: '/images/scripts/manor.jpg',
    playerCount: '6-8',
    duration: '4-5小时',
    difficulty: 4,
    tags: ['推理', '悬疑', '古风'],
    category: 'mystery'
  },
  {
    id: '2',
    title: '校园青春',
    description: '回到那个青涩的年代，重温校园时光的美好与遗憾...',
    playerCount: '4-6',
    duration: '3-4小时',
    difficulty: 2,
    tags: ['情感', '青春', '现代'],
    category: 'emotion'
  },
  {
    id: '3',
    title: '末日求生',
    description: '世界末日来临，你们能否在绝境中找到生存的希望？',
    playerCount: '5-7',
    duration: '5-6小时',
    difficulty: 5,
    tags: ['恐怖', '生存', '现代'],
    category: 'horror'
  },
  {
    id: '4',
    title: '江湖恩仇',
    description: '刀光剑影的江湖世界，恩怨情仇交织的武侠传奇...',
    playerCount: '6-9',
    duration: '4-5小时',
    difficulty: 3,
    tags: ['武侠', '古风', '情感'],
    category: 'ancient'
  },
  {
    id: '5',
    title: '欢乐聚会',
    description: '轻松愉快的聚会剧本，适合新手和朋友聚会...',
    playerCount: '4-8',
    duration: '2-3小时',
    difficulty: 1,
    tags: ['欢乐', '轻松', '聚会'],
    category: 'comedy'
  },
  {
    id: '6',
    title: '都市传说',
    description: '现代都市中流传的神秘传说，真相究竟如何？',
    playerCount: '5-6',
    duration: '3-4小时',
    difficulty: 3,
    tags: ['推理', '现代', '悬疑'],
    category: 'mystery'
  }
])

const filteredScripts = computed(() => {
  let result = scripts.value
  
  // 按分类筛选
  if (selectedCategory.value !== 'all') {
    result = result.filter(script => script.category === selectedCategory.value)
  }
  
  // 按搜索关键词筛选
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(script => 
      script.title.toLowerCase().includes(query) ||
      script.description.toLowerCase().includes(query) ||
      script.tags.some(tag => tag.toLowerCase().includes(query))
    )
  }
  
  return result
})

const selectScript = (script: Script) => {
  selectedScript.value = selectedScript.value?.id === script.id ? null : script
}

const handleOverlayClick = () => {
  emit('close')
}

const handleConfirm = () => {
  if (selectedScript.value) {
    emit('select', selectedScript.value)
  }
}
</script>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal-content {
  background: linear-gradient(180deg, #1A1A2E 0%, #16213E 100%);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 20px;
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-title {
  font-size: 1.5rem;
  color: #fff;
  font-weight: 700;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  color: #B0B0B0;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
  }
  
  .close-icon {
    font-size: 1.5rem;
    line-height: 1;
  }
}

.modal-body {
  padding: 32px;
  max-height: 60vh;
  overflow-y: auto;
}

.search-section {
  margin-bottom: 24px;
}

.search-bar {
  position: relative;
  margin-bottom: 20px;
  
  .search-input {
    width: 100%;
    padding: 14px 50px 14px 16px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(0, 245, 212, 0.2);
    border-radius: 10px;
    color: #fff;
    font-size: 1rem;
    transition: all 0.3s ease;
    
    &::placeholder {
      color: #666;
    }
    
    &:focus {
      outline: none;
      border-color: #00F5D4;
      box-shadow: 0 0 0 3px rgba(0, 245, 212, 0.1);
    }
  }
  
  .search-icon {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #B0B0B0;
    font-size: 1.2rem;
  }
}

.filter-tabs {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-tab {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  color: #B0B0B0;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
  }
  
  &.active {
    background: linear-gradient(135deg, #00F5D4, #FF00E4);
    color: #1A1A2E;
    border-color: transparent;
    font-weight: 600;
  }
}

.scripts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.script-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(0, 245, 212, 0.3);
    transform: translateY(-2px);
  }
  
  &.selected {
    border-color: #00F5D4;
    box-shadow: 0 0 0 2px rgba(0, 245, 212, 0.2);
  }
}

.script-image {
  position: relative;
  height: 160px;
  overflow: hidden;
  
  .script-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .script-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #00F5D4, #FF00E4);
    display: flex;
    align-items: center;
    justify-content: center;
    
    .placeholder-icon {
      font-size: 3rem;
      color: #1A1A2E;
    }
  }
  
  .script-overlay {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 32px;
    height: 32px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 1.2rem;
    font-weight: 700;
    transition: all 0.3s ease;
  }
  
  .selected & .script-overlay {
    background: #00F5D4;
    color: #1A1A2E;
  }
}

.script-info {
  padding: 20px;
}

.script-title {
  font-size: 1.2rem;
  color: #fff;
  font-weight: 700;
  margin-bottom: 8px;
}

.script-description {
  color: #B0B0B0;
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.script-meta {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  
  .meta-icon {
    font-size: 0.9rem;
  }
  
  .meta-text {
    color: #E0E0E0;
    font-size: 0.85rem;
  }
}

.script-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.script-tag {
  padding: 4px 8px;
  background: rgba(0, 245, 212, 0.1);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 12px;
  color: #00F5D4;
  font-size: 0.75rem;
  font-weight: 500;
}

.more-tags {
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: #B0B0B0;
  font-size: 0.75rem;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  
  .empty-icon {
    font-size: 4rem;
    margin-bottom: 16px;
    opacity: 0.5;
  }
  
  .empty-title {
    font-size: 1.3rem;
    color: #fff;
    font-weight: 600;
    margin-bottom: 8px;
  }
  
  .empty-text {
    color: #B0B0B0;
    margin: 0;
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 24px 32px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.cancel-btn, .confirm-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: rgba(255, 255, 255, 0.1);
  color: #E0E0E0;
  border: 1px solid rgba(255, 255, 255, 0.2);
  
  &:hover {
    background: rgba(255, 255, 255, 0.15);
  }
}

.confirm-btn {
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 245, 212, 0.4);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    margin: 20px;
  }
  
  .modal-header, .modal-body, .modal-footer {
    padding: 20px;
  }
  
  .scripts-grid {
    grid-template-columns: 1fr;
  }
  
  .filter-tabs {
    gap: 6px;
  }
  
  .filter-tab {
    font-size: 0.8rem;
    padding: 6px 12px;
  }
  
  .modal-footer {
    flex-direction: column;
  }
  
  .cancel-btn, .confirm-btn {
    width: 100%;
  }
}
</style>
