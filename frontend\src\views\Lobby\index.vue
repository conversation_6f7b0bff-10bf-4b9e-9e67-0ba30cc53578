<template>
  <div class="lobby-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="container">
        <div class="header-content">
          <div class="header-text">
            <h1 class="page-title">
              <span class="title-icon">🚗</span>
              拼车大厅
              <div class="live-indicator">
                <span class="live-dot"></span>
                <span class="live-text">LIVE</span>
              </div>
            </h1>
            <p class="page-subtitle">实时更新的车队信息，快速找到心仪的伙伴</p>
          </div>
          <div class="header-actions">
            <button class="create-lobby-btn" @click="showCreateModal = true">
              <span class="btn-icon">➕</span>
              <span class="btn-text">创建车队</span>
            </button>
          </div>
        </div>
        
        <!-- 统计信息 -->
        <div class="stats-bar">
          <div class="stat-item">
            <span class="stat-number">{{ stats.totalLobbies }}</span>
            <span class="stat-label">活跃车队</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ stats.waitingLobbies }}</span>
            <span class="stat-label">等待中</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ stats.onlinePlayers }}</span>
            <span class="stat-label">在线玩家</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ stats.todayGames }}</span>
            <span class="stat-label">今日开局</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="page-content">
      <div class="container">
        <!-- 筛选器 -->
        <LobbyFilter 
          :filtered-count="filteredCount"
          @filter-change="handleFilterChange"
          @search="handleSearch"
        />
        
        <!-- 车队列表 -->
        <LobbyList
          :lobbies="lobbies"
          :total-count="totalCount"
          :current-page="currentPage"
          :total-pages="totalPages"
          :is-loading="isLoading"
          @join-lobby="handleJoinLobby"
          @view-details="handleViewDetails"
          @favorite="handleFavorite"
          @create-lobby="showCreateModal = true"
          @page-change="handlePageChange"
          @refresh="refreshLobbies"
        />
      </div>
    </div>

    <!-- 创建车队模态框 -->
    <CreateLobbyModal
      v-if="showCreateModal"
      @close="showCreateModal = false"
      @created="handleLobbyCreated"
    />

    <!-- 车队详情模态框 -->
    <LobbyDetailModal
      v-if="showDetailModal"
      :lobby-id="selectedLobbyId"
      @close="showDetailModal = false"
      @join="handleJoinLobby"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import LobbyFilter from './components/LobbyFilter.vue'
import LobbyList from './components/LobbyList.vue'
import CreateLobbyModal from './components/CreateLobbyModal.vue'
import LobbyDetailModal from './components/LobbyDetailModal.vue'

// 类型定义
interface Lobby {
  id: number
  script: {
    id: number
    title: string
    coverImage: string
    genre: string
  }
  host: {
    id: number
    nickname: string
    avatar: string
    level: number
  }
  currentPlayers: number
  maxPlayers: number
  status: 'waiting' | 'full' | 'in_progress'
  startTime: string
  endTime: string
  location: string
  price: number
  description: string
  requirements: string
  createdAt: string
  isFavorite?: boolean
}

interface FilterOptions {
  searchKeyword: string
  selectedGenres: string[]
  selectedPlayerCounts: number[]
  selectedLocations: string[]
  timeRange: string
  customStartTime: string
  customEndTime: string
  priceRange: { min: number | null; max: number | null }
  sortBy: string
  sortOrder: 'asc' | 'desc'
}

// 路由
const router = useRouter()

// 响应式数据
const isLoading = ref(false)
const showCreateModal = ref(false)
const showDetailModal = ref(false)
const selectedLobbyId = ref<number | null>(null)

const currentPage = ref(1)
const totalPages = ref(1)
const totalCount = ref(0)
const filteredCount = ref(0)

const currentFilters = ref<FilterOptions>({
  searchKeyword: '',
  selectedGenres: [],
  selectedPlayerCounts: [],
  selectedLocations: [],
  timeRange: '',
  customStartTime: '',
  customEndTime: '',
  priceRange: { min: null, max: null },
  sortBy: 'createTime',
  sortOrder: 'desc'
})

const stats = reactive({
  totalLobbies: 156,
  waitingLobbies: 89,
  onlinePlayers: 1247,
  todayGames: 67
})

const lobbies = ref<Lobby[]>([
  // 模拟数据
  {
    id: 1,
    script: {
      id: 1,
      title: "迷雾庄园",
      coverImage: "https://picsum.photos/80/80?random=1",
      genre: "推理"
    },
    host: {
      id: 1,
      nickname: "推理大师",
      avatar: "https://picsum.photos/40/40?random=11",
      level: 15
    },
    currentPlayers: 5,
    maxPlayers: 6,
    status: 'waiting',
    startTime: '2024-07-30T19:00:00',
    endTime: '2024-07-30T23:00:00',
    location: '线上',
    price: 68,
    description: '欢迎新手，氛围轻松',
    requirements: '有基础推理经验',
    createdAt: '2024-07-29T14:30:00',
    isFavorite: false
  }
  // 更多模拟数据...
])

// WebSocket连接
let ws: WebSocket | null = null

// 方法
const handleFilterChange = (filters: FilterOptions) => {
  currentFilters.value = filters
  currentPage.value = 1
  loadLobbies()
}

const handleSearch = (keyword: string) => {
  currentFilters.value.searchKeyword = keyword
  currentPage.value = 1
  loadLobbies()
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  loadLobbies()
}

const handleJoinLobby = async (lobbyId: number) => {
  try {
    isLoading.value = true
    // 这里调用加入车队的API
    console.log('加入车队:', lobbyId)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 加入成功后跳转到车队详情或游戏页面
    router.push(`/lobby/${lobbyId}`)
  } catch (error) {
    console.error('加入车队失败:', error)
  } finally {
    isLoading.value = false
  }
}

const handleViewDetails = (lobbyId: number) => {
  selectedLobbyId.value = lobbyId
  showDetailModal.value = true
}

const handleFavorite = (lobbyId: number) => {
  const lobby = lobbies.value.find(l => l.id === lobbyId)
  if (lobby) {
    lobby.isFavorite = !lobby.isFavorite
    // 这里调用收藏/取消收藏的API
    console.log('收藏状态变更:', lobbyId, lobby.isFavorite)
  }
}

const handleLobbyCreated = (newLobby: Lobby) => {
  lobbies.value.unshift(newLobby)
  showCreateModal.value = false
  // 刷新统计数据
  refreshStats()
}

const loadLobbies = async () => {
  try {
    isLoading.value = true
    
    // 这里调用获取车队列表的API
    console.log('加载车队列表:', {
      page: currentPage.value,
      filters: currentFilters.value
    })
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 模拟数据更新
    totalCount.value = 156
    filteredCount.value = 89
    totalPages.value = Math.ceil(filteredCount.value / 12)
    
  } catch (error) {
    console.error('加载车队列表失败:', error)
  } finally {
    isLoading.value = false
  }
}

const refreshLobbies = () => {
  loadLobbies()
  refreshStats()
}

const refreshStats = async () => {
  try {
    // 这里调用获取统计数据的API
    console.log('刷新统计数据')
    
    // 模拟数据更新
    stats.totalLobbies = Math.floor(Math.random() * 50) + 150
    stats.waitingLobbies = Math.floor(Math.random() * 30) + 80
    stats.onlinePlayers = Math.floor(Math.random() * 200) + 1200
    stats.todayGames = Math.floor(Math.random() * 20) + 60
  } catch (error) {
    console.error('刷新统计数据失败:', error)
  }
}

const initWebSocket = () => {
  try {
    ws = new WebSocket('ws://localhost:8081/ws')
    
    ws.onopen = () => {
      console.log('WebSocket连接已建立')
    }
    
    ws.onmessage = (event) => {
      const data = JSON.parse(event.data)
      handleWebSocketMessage(data)
    }
    
    ws.onclose = () => {
      console.log('WebSocket连接已关闭')
      // 重连逻辑
      setTimeout(initWebSocket, 5000)
    }
    
    ws.onerror = (error) => {
      console.error('WebSocket错误:', error)
    }
  } catch (error) {
    console.error('WebSocket初始化失败:', error)
  }
}

const handleWebSocketMessage = (data: any) => {
  switch (data.type) {
    case 'LOBBY_UPDATE':
      // 更新车队信息
      const lobbyIndex = lobbies.value.findIndex(l => l.id === data.data.lobbyId)
      if (lobbyIndex > -1) {
        Object.assign(lobbies.value[lobbyIndex], data.data)
      }
      break
    case 'NEW_LOBBY':
      // 新车队创建
      lobbies.value.unshift(data.data)
      refreshStats()
      break
    case 'LOBBY_REMOVED':
      // 车队被移除
      const removeIndex = lobbies.value.findIndex(l => l.id === data.data.lobbyId)
      if (removeIndex > -1) {
        lobbies.value.splice(removeIndex, 1)
      }
      refreshStats()
      break
  }
}

// 生命周期
onMounted(() => {
  document.title = '拼车大厅 - 迷雾拼本'
  loadLobbies()
  refreshStats()
  initWebSocket()
})

onUnmounted(() => {
  if (ws) {
    ws.close()
  }
})
</script>

<style lang="scss" scoped>
.lobby-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #1A1A2E 0%, #16213E 50%, #0F0F1E 100%);
}

.page-header {
  background: linear-gradient(135deg, rgba(0, 245, 212, 0.1), rgba(255, 0, 228, 0.1));
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 245, 212, 0.2);
  padding: 40px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
}

.header-text {
  flex: 1;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  
  .title-icon {
    font-size: 2rem;
  }
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-left: 16px;
}

.live-dot {
  width: 8px;
  height: 8px;
  background: #FF4444;
  border-radius: 50%;
  animation: pulse 1.5s ease-in-out infinite;
}

.live-text {
  font-size: 0.8rem;
  color: #FF4444;
  font-weight: 600;
  letter-spacing: 1px;
}

.page-subtitle {
  font-size: 1.1rem;
  color: #B0B0B0;
  line-height: 1.5;
}

.header-actions {
  flex-shrink: 0;
  margin-left: 24px;
}

.create-lobby-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 245, 212, 0.4);
  }
  
  .btn-icon {
    font-size: 1.1rem;
  }
}

.stats-bar {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 245, 212, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: rgba(0, 245, 212, 0.3);
    transform: translateY(-2px);
  }
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #00F5D4;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 0.9rem;
  color: #B0B0B0;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.page-content {
  padding: 40px 0 80px;
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.5; transform: scale(1.2); }
}

@media (max-width: 768px) {
  .page-header {
    padding: 24px 0;
  }
  
  .container {
    padding: 0 15px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }
  
  .page-title {
    font-size: 2rem;
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
  
  .live-indicator {
    justify-content: center;
    margin-left: 0;
  }
  
  .page-subtitle {
    text-align: center;
  }
  
  .header-actions {
    margin-left: 0;
    align-self: center;
  }
  
  .stats-bar {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  .stat-item {
    padding: 16px;
  }
  
  .stat-number {
    font-size: 1.5rem;
  }
  
  .page-content {
    padding: 24px 0 60px;
  }
}
</style>
