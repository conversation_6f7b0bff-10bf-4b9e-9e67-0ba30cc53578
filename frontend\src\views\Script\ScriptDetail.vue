<template>
  <div class="script-detail-page">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <span class="loading-text">正在加载剧本信息...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <div class="error-icon">😞</div>
      <h2 class="error-title">加载失败</h2>
      <p class="error-message">{{ error }}</p>
      <button class="retry-button" @click="loadScript">重试</button>
    </div>

    <!-- 剧本详情内容 -->
    <div v-else-if="script" class="script-content">
      <!-- 英雄区域 -->
      <ScriptHero 
        :script="script"
        @favorite="handleFavorite"
        @share="handleShare"
        @quick-join="handleQuickJoin"
      />

      <!-- 详细信息区域 -->
      <div class="detail-content">
        <div class="container">
          <!-- 导航标签 -->
          <div class="detail-nav">
            <button 
              v-for="tab in tabs" 
              :key="tab.key"
              class="nav-tab"
              :class="{ active: activeTab === tab.key }"
              @click="setActiveTab(tab.key)"
            >
              <span class="tab-icon">{{ tab.icon }}</span>
              <span class="tab-text">{{ tab.label }}</span>
            </button>
          </div>

          <!-- 标签内容 -->
          <div class="tab-content">
            <!-- 剧本介绍 -->
            <div v-if="activeTab === 'description'" class="tab-panel">
              <ScriptDescription :script="script" />
            </div>

            <!-- 角色信息 -->
            <div v-if="activeTab === 'characters'" class="tab-panel">
              <ScriptCharacters :characters="script.characters" />
            </div>

            <!-- 游戏规则 -->
            <div v-if="activeTab === 'rules'" class="tab-panel">
              <ScriptRules :rules="script.rules" />
            </div>

            <!-- 评价评论 -->
            <div v-if="activeTab === 'reviews'" class="tab-panel">
              <ScriptReviews 
                :reviews="script.recentReviews"
                :script-id="script.id"
                :average-rating="script.averageRating"
                :review-count="script.reviewCount"
                @load-more="loadMoreReviews"
              />
            </div>

            <!-- 可用车队 -->
            <div v-if="activeTab === 'lobbies'" class="tab-panel">
              <AvailableLobbies 
                :script-id="script.id"
                :lobbies="availableLobbies"
                @join-lobby="handleJoinLobby"
                @create-lobby="handleCreateLobby"
                @refresh="loadAvailableLobbies"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作浮动按钮 -->
    <div v-if="script" class="floating-actions">
      <button 
        class="floating-btn back-btn"
        @click="goBack"
        title="返回"
      >
        ←
      </button>
      <button 
        class="floating-btn favorite-btn"
        :class="{ active: script.isFavorite }"
        @click="handleFavorite"
        :title="script.isFavorite ? '取消收藏' : '收藏'"
      >
        {{ script.isFavorite ? '❤️' : '🤍' }}
      </button>
      <button 
        class="floating-btn join-btn"
        @click="handleQuickJoin"
        title="快速拼车"
      >
        🚗
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { scriptApi } from '@/api/script'
import type { ScriptDetail } from '@/types/script'
import ScriptHero from './components/ScriptHero.vue'
import ScriptDescription from './components/ScriptDescription.vue'
import ScriptCharacters from './components/ScriptCharacters.vue'
import ScriptRules from './components/ScriptRules.vue'
import ScriptReviews from './components/ScriptReviews.vue'
import AvailableLobbies from './components/AvailableLobbies.vue'

// 路由
const route = useRoute()
const router = useRouter()

// 响应式数据
const isLoading = ref(true)
const error = ref<string | null>(null)
const script = ref<ScriptDetail | null>(null)
const activeTab = ref('description')
const availableLobbies = ref([])

// 标签配置
const tabs = [
  { key: 'description', label: '剧本介绍', icon: '📖' },
  { key: 'characters', label: '角色信息', icon: '👥' },
  { key: 'rules', label: '游戏规则', icon: '📋' },
  { key: 'reviews', label: '评价评论', icon: '💬' },
  { key: 'lobbies', label: '可用车队', icon: '🚗' }
]

// 计算属性
const scriptId = computed(() => {
  return parseInt(route.params.id as string)
})

// 方法
const loadScript = async () => {
  try {
    isLoading.value = true
    error.value = null
    
    console.log('加载剧本详情:', scriptId.value)
    
    // 调用真实API
    const response = await scriptApi.getScriptDetail(scriptId.value)
    if (response.success && response.data) {
      script.value = response.data
      console.log('剧本详情加载成功:', script.value)
    } else {
      throw new Error(response.message || '获取剧本详情失败')
    }
  } catch (err: any) {
    console.error('加载剧本详情失败:', err)
    error.value = err.message || '加载失败，请稍后重试'
    ElMessage.error(error.value)
  } finally {
    isLoading.value = false
  }
}

const setActiveTab = (tab: string) => {
  activeTab.value = tab
}

const handleFavorite = async () => {
  if (!script.value) return

  try {
    const response = await scriptApi.toggleFavorite(script.value.id)
    if (response.code === 200) {
      script.value.isFavorite = response.data
      const message = script.value.isFavorite ? '收藏成功' : '取消收藏成功'
      ElMessage.success(message)
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (err: any) {
    ElMessage.error('操作失败：' + err.message)
  }
}

const handleShare = () => {
  if (!script.value) return
  
  // 使用Web Share API或复制链接
  if (navigator.share) {
    navigator.share({
      title: script.value.title,
      text: script.value.description,
      url: window.location.href
    })
  } else {
    // 复制链接到剪贴板
    navigator.clipboard.writeText(window.location.href)
    ElMessage.success('链接已复制到剪贴板')
  }
}

const handleQuickJoin = () => {
  if (!script.value) return
  
  // 跳转到创建房间页面
  router.push(`/lobby/create?scriptId=${script.value.id}`)
}

const handleJoinLobby = (lobbyId: string) => {
  // 跳转到房间详情页面
  router.push(`/lobby/${lobbyId}`)
}

const handleCreateLobby = () => {
  if (!script.value) return
  
  // 跳转到创建房间页面
  router.push(`/lobby/create?scriptId=${script.value.id}`)
}

const loadAvailableLobbies = async () => {
  try {
    // TODO: 调用获取房间列表API
    console.log('加载可用车队')
  } catch (err: any) {
    ElMessage.error('加载房间列表失败：' + err.message)
  }
}

const loadMoreReviews = async () => {
  try {
    // TODO: 加载更多评价
    console.log('加载更多评价')
  } catch (err: any) {
    ElMessage.error('加载评价失败：' + err.message)
  }
}

const goBack = () => {
  router.go(-1)
}

// 生命周期
onMounted(() => {
  loadScript()
  document.title = `剧本详情 - 迷雾拼本`
})

// 监听路由变化
watch(() => route.params.id, () => {
  if (route.params.id) {
    loadScript()
  }
})
</script>

<style lang="scss" scoped>
.script-detail-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #1A1A2E 0%, #16213E 50%, #0F0F1E 100%);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  gap: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 245, 212, 0.1);
  border-top: 4px solid #00F5D4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: #B0B0B0;
  font-size: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  padding: 40px 20px;
  text-align: center;
}

.error-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.error-title {
  font-size: 1.5rem;
  color: #fff;
  margin-bottom: 12px;
}

.error-message {
  color: #B0B0B0;
  margin-bottom: 24px;
  max-width: 400px;
}

.retry-button {
  padding: 12px 24px;
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 245, 212, 0.4);
  }
}

.detail-content {
  padding: 40px 0;
}

.detail-nav {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
  gap: 8px;
  flex-wrap: wrap;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  color: #B0B0B0;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
  }
  
  &.active {
    background: linear-gradient(135deg, #00F5D4, #FF00E4);
    color: #1A1A2E;
    border-color: transparent;
    
    .tab-text {
      font-weight: 600;
    }
  }
}

.tab-icon {
  font-size: 1.1rem;
}

.tab-text {
  font-size: 0.9rem;
}

.tab-content {
  margin-top: 32px;
}

.tab-panel {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.floating-actions {
  position: fixed;
  right: 24px;
  bottom: 24px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  z-index: 100;
}

.floating-btn {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
  }
}

.back-btn {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  backdrop-filter: blur(10px);
}

.favorite-btn {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  backdrop-filter: blur(10px);
  
  &.active {
    background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  }
}

.join-btn {
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
}

@media (max-width: 768px) {
  .detail-nav {
    padding: 0 20px;
  }
  
  .nav-tab {
    padding: 10px 16px;
    font-size: 0.85rem;
  }
  
  .floating-actions {
    right: 16px;
    bottom: 16px;
  }
  
  .floating-btn {
    width: 48px;
    height: 48px;
    font-size: 1rem;
  }
}
</style>