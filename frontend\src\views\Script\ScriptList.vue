<template>
  <div class="script-list">
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">剧本库</h1>
        <p class="page-subtitle">精选高质量剧本，带你体验不同的故事世界</p>
      </div>

      <!-- 筛选器 -->
      <div class="filters">
        <div class="filter-group">
          <label class="filter-label">类型</label>
          <select v-model="filters.category" class="filter-select">
            <option value="">全部类型</option>
            <option value="推理">推理</option>
            <option value="恐怖">恐怖</option>
            <option value="情感">情感</option>
            <option value="欢乐">欢乐</option>
          </select>
        </div>
        
        <div class="filter-group">
          <label class="filter-label">人数</label>
          <select v-model="filters.playerCount" class="filter-select">
            <option value="">不限人数</option>
            <option value="4">4人</option>
            <option value="5">5人</option>
            <option value="6">6人</option>
            <option value="7">7人</option>
          </select>
        </div>
        
        <div class="filter-group">
          <label class="filter-label">排序</label>
          <select v-model="filters.sort" class="filter-select">
            <option value="hot">热门推荐</option>
            <option value="rating">评分最高</option>
            <option value="newest">最新发布</option>
            <option value="price">价格最低</option>
          </select>
        </div>
      </div>

      <!-- 剧本列表 -->
      <div class="scripts-grid">
        <div 
          v-for="script in scripts" 
          :key="script.id"
          class="script-card"
          @click="viewScript(script.id)"
        >
          <div class="script-cover">
            <img :src="script.coverImage" :alt="script.title" />
            <div class="script-overlay">
              <button class="view-btn">查看详情</button>
            </div>
            <button
              class="favorite-btn"
              :class="{ active: script.isFavorite }"
              @click.stop="toggleFavorite(script)"
              :title="script.isFavorite ? '取消收藏' : '收藏'"
            >
              {{ script.isFavorite ? '❤️' : '🤍' }}
            </button>
          </div>
          
          <div class="script-info">
            <h3 class="script-title">{{ script.title }}</h3>
            <div class="script-meta">
              <span class="genre">{{ script.category }}</span>
              <span class="players">{{ script.playerCountMin }}{{ script.playerCountMax && script.playerCountMax !== script.playerCountMin ? '-' + script.playerCountMax : '' }}人</span>
              <span class="duration">{{ script.duration }}</span>
            </div>
            
            <div class="script-stats">
              <div class="rating">
                <span class="stars">⭐</span>
                <span class="rating-value">{{ script.averageRating || 0 }}</span>
                <span class="rating-count">({{ script.reviewCount || 0 }})</span>
              </div>
              <div class="price">¥{{ script.price || 0 }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载更多 -->
      <div class="load-more" v-if="hasMore">
        <button class="load-more-btn" @click="loadMore" :disabled="isLoading">
          {{ isLoading ? '加载中...' : '加载更多' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { scriptApi } from '@/api/script'
import type { Script, ScriptSearchParams } from '@/types/script'

const router = useRouter()

const filters = ref({
  category: '',
  playerCount: '',
  sort: 'hot'
})

const scripts = ref<Script[]>([])
const isLoading = ref(false)
const hasMore = ref(true)
const currentPage = ref(1)
const pageSize = 20

// 搜索参数
const searchParams = ref<ScriptSearchParams>({
  page: currentPage.value,
  size: pageSize,
  category: filters.value.category,
  playerCountMin: filters.value.playerCount ? parseInt(filters.value.playerCount) : undefined,
  playerCountMax: filters.value.playerCount ? parseInt(filters.value.playerCount) : undefined,
  sortBy: filters.value.sort
})

const viewScript = (id: number) => {
  router.push(`/scripts/${id}`)
}

const toggleFavorite = async (script: Script) => {
  try {
    const response = await scriptApi.toggleFavorite(script.id)
    if (response.code === 200) {
      script.isFavorite = response.data
      const message = script.isFavorite ? '收藏成功' : '取消收藏成功'
      ElMessage.success(message)
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (err: any) {
    ElMessage.error('操作失败：' + err.message)
  }
}

const loadScripts = async (reset = false) => {
  if (isLoading.value) return
  
  isLoading.value = true
  
  try {
    if (reset) {
      currentPage.value = 1
      scripts.value = []
      hasMore.value = true
    }
    
    // 更新搜索参数
    searchParams.value = {
      page: currentPage.value,
      size: pageSize,
      category: filters.value.category || undefined,
      playerCountMin: filters.value.playerCount ? parseInt(filters.value.playerCount) : undefined,
      playerCountMax: filters.value.playerCount ? parseInt(filters.value.playerCount) : undefined,
      sortBy: filters.value.sort
    }
    
    const response = await scriptApi.getScriptList(searchParams.value)
    
    if (response.code === 200 && response.data) {
      const newScripts = response.data.records || []
      
      if (reset) {
        scripts.value = newScripts
      } else {
        scripts.value.push(...newScripts)
      }
      
      // 检查是否还有更多数据
      hasMore.value = currentPage.value < (response.data.pages || 0)
      
      if (newScripts.length > 0) {
        currentPage.value++
      }
    }
  } catch (error) {
    console.error('加载剧本列表失败:', error)
    // 如果API失败，显示一些模拟数据作为fallback
    if (reset) {
      scripts.value = [
        {
          id: 1,
          title: "迷雾庄园",
          coverImage: "https://picsum.photos/300/400?random=1",
          category: "推理",
          playerCountMin: 6,
          playerCountMax: 6,
          duration: "240分钟",
          averageRating: 4.8,
          reviewCount: 156,
          price: 68
        },
        {
          id: 2,
          title: "血色玫瑰",
          coverImage: "https://picsum.photos/300/400?random=2",
          category: "恐怖",
          playerCountMin: 7,
          playerCountMax: 7,
          duration: "300分钟",
          averageRating: 4.6,
          reviewCount: 89,
          price: 88
        }
      ]
    }
  } finally {
    isLoading.value = false
  }
}

const loadMore = () => {
  loadScripts(false)
}

// 监听筛选条件变化
watch(filters, () => {
  loadScripts(true)
}, { deep: true })

onMounted(() => {
  document.title = '剧本库 - 迷雾拼本'
  loadScripts(true)
})
</script>

<style lang="scss" scoped>
.script-list {
  min-height: 100vh;
  padding: 40px 0;
  background: linear-gradient(180deg, #1A1A2E 0%, #16213E 50%, #0F0F1E 100%);
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 2.5rem;
  color: #fff;
  font-weight: 700;
  margin-bottom: 12px;
}

.page-subtitle {
  color: #B0B0B0;
  font-size: 1.1rem;
}

.filters {
  display: flex;
  gap: 20px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-label {
  font-size: 0.9rem;
  color: #E0E0E0;
  font-weight: 500;
}

.filter-select {
  padding: 10px 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 8px;
  color: #fff;
  font-size: 0.9rem;
  min-width: 120px;
  
  &:focus {
    outline: none;
    border-color: #00F5D4;
  }
}

.scripts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.script-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.1);
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    border-color: rgba(0, 245, 212, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }
}

.script-cover {
  position: relative;
  aspect-ratio: 3/4;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  &:hover img {
    transform: scale(1.05);
  }
}

.favorite-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 2;

  &:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
  }

  &.active {
    background: rgba(255, 59, 92, 0.9);
  }
}

.script-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  
  .script-card:hover & {
    opacity: 1;
  }
}

.view-btn {
  padding: 10px 20px;
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
}

.script-info {
  padding: 20px;
}

.script-title {
  font-size: 1.2rem;
  color: #fff;
  font-weight: 600;
  margin-bottom: 12px;
}

.script-meta {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.genre, .players, .duration {
  padding: 4px 8px;
  background: rgba(0, 245, 212, 0.1);
  color: #00F5D4;
  border-radius: 8px;
  font-size: 0.75rem;
}

.script-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rating {
  display: flex;
  align-items: center;
  gap: 4px;
}

.rating-value {
  color: #fff;
  font-weight: 600;
}

.rating-count {
  color: #888;
  font-size: 0.8rem;
}

.price {
  font-size: 1.1rem;
  color: #FF00E4;
  font-weight: 600;
}

.load-more {
  text-align: center;
}

.load-more-btn {
  padding: 12px 32px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 8px;
  color: #00F5D4;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover:not(:disabled) {
    background: rgba(0, 245, 212, 0.1);
    border-color: #00F5D4;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

@media (max-width: 768px) {
  .script-list {
    padding: 20px 0;
  }
  
  .filters {
    flex-direction: column;
    gap: 16px;
  }
  
  .scripts-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 16px;
  }
}
</style>
