<template>
  <div class="available-lobbies">
    <div class="section-header">
      <h3 class="section-title">可用房间</h3>
      <div class="section-divider"></div>
    </div>
    
    <div class="lobbies-filters">
      <div class="filter-options">
        <button 
          v-for="filter in filters" 
          :key="filter.value"
          class="filter-btn"
          :class="{ active: selectedFilter === filter.value }"
          @click="selectedFilter = filter.value"
        >
          {{ filter.label }}
        </button>
      </div>
      
      <button class="create-lobby-btn" @click="$emit('create-lobby')">
        <span class="btn-icon">+</span>
        <span class="btn-text">创建房间</span>
      </button>
    </div>
    
    <div class="lobbies-list">
      <div 
        v-for="lobby in filteredLobbies" 
        :key="lobby.id"
        class="lobby-card"
        @click="$emit('view-lobby', lobby)"
      >
        <div class="lobby-header">
          <h4 class="lobby-title">{{ lobby.title }}</h4>
          <div class="lobby-status" :class="lobby.status">
            {{ getStatusText(lobby.status) }}
          </div>
        </div>
        
        <div class="lobby-info">
          <div class="info-row">
            <div class="info-item">
              <span class="info-icon">👤</span>
              <span class="info-text">{{ lobby.host }}</span>
            </div>
            
            <div class="info-item">
              <span class="info-icon">👥</span>
              <span class="info-text">{{ lobby.currentPlayers }}/{{ lobby.maxPlayers }}</span>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-item">
              <span class="info-icon">⏰</span>
              <span class="info-text">{{ formatTime(lobby.startTime) }}</span>
            </div>
            
            <div class="info-item">
              <span class="info-icon">📍</span>
              <span class="info-text">{{ lobby.location }}</span>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-item">
              <span class="info-icon">💰</span>
              <span class="info-text">{{ lobby.price }}</span>
            </div>
            
            <div class="info-item" v-if="lobby.difficulty">
              <span class="info-icon">⭐</span>
              <span class="info-text">难度 {{ lobby.difficulty }}/5</span>
            </div>
          </div>
        </div>
        
        <div class="lobby-description" v-if="lobby.description">
          <p class="description-text">{{ lobby.description }}</p>
        </div>
        
        <div class="lobby-tags" v-if="lobby.tags?.length">
          <span 
            v-for="tag in lobby.tags.slice(0, 3)" 
            :key="tag"
            class="lobby-tag"
          >
            {{ tag }}
          </span>
          <span v-if="lobby.tags.length > 3" class="more-tags">
            +{{ lobby.tags.length - 3 }}
          </span>
        </div>
        
        <div class="lobby-footer">
          <div class="players-preview">
            <div class="players-avatars">
              <div 
                v-for="player in lobby.players.slice(0, 4)" 
                :key="player.id"
                class="player-avatar"
              >
                <img 
                  v-if="player.avatar" 
                  :src="player.avatar" 
                  :alt="player.name"
                  class="avatar-image"
                />
                <div v-else class="avatar-placeholder">
                  {{ player.name.charAt(0) }}
                </div>
              </div>
              
              <div v-if="lobby.players.length > 4" class="more-players">
                +{{ lobby.players.length - 4 }}
              </div>
            </div>
          </div>
          
          <button 
            class="join-btn"
            :class="{ disabled: !canJoin(lobby) }"
            :disabled="!canJoin(lobby)"
            @click.stop="handleJoin(lobby)"
          >
            {{ getJoinButtonText(lobby) }}
          </button>
        </div>
      </div>
    </div>
    
    <div v-if="filteredLobbies.length === 0" class="empty-lobbies">
      <div class="empty-icon">🏠</div>
      <h4 class="empty-title">暂无可用房间</h4>
      <p class="empty-text">
        {{ selectedFilter === 'all' ? '目前没有使用此剧本的房间' : '没有符合筛选条件的房间' }}
      </p>
      <button class="create-first-btn" @click="$emit('create-lobby')">
        创建第一个房间
      </button>
    </div>
    
    <div class="load-more" v-if="hasMoreLobbies">
      <button class="load-more-btn" @click="loadMoreLobbies" :disabled="isLoading">
        <span v-if="isLoading" class="loading-spinner"></span>
        <span>{{ isLoading ? '加载中...' : '加载更多' }}</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Player {
  id: string
  name: string
  avatar?: string
}

interface Lobby {
  id: string
  title: string
  host: string
  currentPlayers: number
  maxPlayers: number
  startTime: string
  location: string
  price: string
  status: 'waiting' | 'full' | 'started' | 'ended'
  description?: string
  tags?: string[]
  difficulty?: number
  players: Player[]
}

interface Props {
  scriptId: string
  lobbies: Lobby[]
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'view-lobby': [lobby: Lobby]
  'join-lobby': [lobbyId: string]
  'create-lobby': []
}>()

const selectedFilter = ref('all')
const isLoading = ref(false)
const hasMoreLobbies = ref(true)

const filters = [
  { label: '全部', value: 'all' },
  { label: '等待中', value: 'waiting' },
  { label: '即将开始', value: 'soon' },
  { label: '今日', value: 'today' },
  { label: '本周', value: 'week' }
]

const filteredLobbies = computed(() => {
  let lobbies = [...props.lobbies]
  
  switch (selectedFilter.value) {
    case 'waiting':
      lobbies = lobbies.filter(lobby => lobby.status === 'waiting')
      break
    case 'soon':
      // 筛选2小时内开始的房间
      const twoHoursLater = new Date(Date.now() + 2 * 60 * 60 * 1000)
      lobbies = lobbies.filter(lobby => {
        const startTime = new Date(lobby.startTime)
        return startTime <= twoHoursLater && lobby.status === 'waiting'
      })
      break
    case 'today':
      // 筛选今日的房间
      const today = new Date().toDateString()
      lobbies = lobbies.filter(lobby => {
        const startTime = new Date(lobby.startTime)
        return startTime.toDateString() === today
      })
      break
    case 'week':
      // 筛选本周的房间
      const weekLater = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
      lobbies = lobbies.filter(lobby => {
        const startTime = new Date(lobby.startTime)
        return startTime <= weekLater
      })
      break
  }
  
  return lobbies.sort((a, b) => {
    // 优先显示等待中的房间
    if (a.status === 'waiting' && b.status !== 'waiting') return -1
    if (b.status === 'waiting' && a.status !== 'waiting') return 1
    
    // 然后按开始时间排序
    return new Date(a.startTime).getTime() - new Date(b.startTime).getTime()
  })
})

const getStatusText = (status: string) => {
  const statusMap = {
    waiting: '等待中',
    full: '已满员',
    started: '已开始',
    ended: '已结束'
  }
  return statusMap[status] || status
}

const formatTime = (timeString: string) => {
  const date = new Date(timeString)
  const now = new Date()
  const diffMs = date.getTime() - now.getTime()
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffHours / 24)
  
  if (diffDays > 0) {
    return `${diffDays}天后`
  } else if (diffHours > 0) {
    return `${diffHours}小时后`
  } else if (diffMs > 0) {
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    return `${diffMinutes}分钟后`
  } else {
    return '已开始'
  }
}

const canJoin = (lobby: Lobby) => {
  return lobby.status === 'waiting' && lobby.currentPlayers < lobby.maxPlayers
}

const getJoinButtonText = (lobby: Lobby) => {
  if (lobby.status === 'full') return '已满员'
  if (lobby.status === 'started') return '已开始'
  if (lobby.status === 'ended') return '已结束'
  return '加入房间'
}

const handleJoin = (lobby: Lobby) => {
  if (canJoin(lobby)) {
    emit('join-lobby', lobby.id)
  }
}

const loadMoreLobbies = async () => {
  isLoading.value = true
  try {
    // 模拟加载更多房间
    await new Promise(resolve => setTimeout(resolve, 1000))
    hasMoreLobbies.value = false // 假设没有更多了
  } catch (error) {
    console.error('加载更多房间失败:', error)
  } finally {
    isLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
.available-lobbies {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.1);
  border-radius: 16px;
  padding: 32px;
  backdrop-filter: blur(10px);
}

.section-header {
  margin-bottom: 32px;
}

.section-title {
  font-size: 1.5rem;
  color: #fff;
  font-weight: 700;
  margin-bottom: 12px;
}

.section-divider {
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, #00F5D4, #FF00E4);
  border-radius: 2px;
}

.lobbies-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

.filter-options {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  color: #B0B0B0;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
  }
  
  &.active {
    background: linear-gradient(135deg, #00F5D4, #FF00E4);
    color: #1A1A2E;
    border-color: transparent;
    font-weight: 600;
  }
}

.create-lobby-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 245, 212, 0.4);
  }
}

.lobbies-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.lobby-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(0, 245, 212, 0.3);
    transform: translateY(-2px);
  }
}

.lobby-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.lobby-title {
  font-size: 1.2rem;
  color: #fff;
  font-weight: 700;
  margin: 0;
}

.lobby-status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  
  &.waiting {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
    border: 1px solid rgba(76, 175, 80, 0.3);
  }
  
  &.full {
    background: rgba(255, 193, 7, 0.2);
    color: #FFC107;
    border: 1px solid rgba(255, 193, 7, 0.3);
  }
  
  &.started, &.ended {
    background: rgba(244, 67, 54, 0.2);
    color: #F44336;
    border: 1px solid rgba(244, 67, 54, 0.3);
  }
}

.lobby-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.info-row {
  display: flex;
  gap: 24px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
  
  .info-icon {
    font-size: 0.9rem;
  }
  
  .info-text {
    color: #E0E0E0;
    font-size: 0.9rem;
  }
}

.lobby-description {
  margin-bottom: 16px;
  
  .description-text {
    color: #B0B0B0;
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

.lobby-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 16px;
}

.lobby-tag {
  padding: 4px 8px;
  background: rgba(0, 245, 212, 0.1);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 12px;
  color: #00F5D4;
  font-size: 0.75rem;
  font-weight: 500;
}

.more-tags {
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: #B0B0B0;
  font-size: 0.75rem;
}

.lobby-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.players-preview {
  .players-avatars {
    display: flex;
    gap: 8px;
    align-items: center;
  }
  
  .player-avatar {
    width: 32px;
    height: 32px;
    
    .avatar-image {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid rgba(0, 245, 212, 0.3);
    }
    
    .avatar-placeholder {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background: linear-gradient(135deg, #00F5D4, #FF00E4);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.8rem;
      font-weight: 700;
      color: #1A1A2E;
      border: 2px solid rgba(0, 245, 212, 0.3);
    }
  }
  
  .more-players {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    color: #B0B0B0;
    font-weight: 600;
  }
}

.join-btn {
  padding: 8px 16px;
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover:not(.disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 245, 212, 0.4);
  }
  
  &.disabled {
    background: rgba(255, 255, 255, 0.1);
    color: #666;
    cursor: not-allowed;
  }
}

.empty-lobbies {
  text-align: center;
  padding: 60px 20px;
  
  .empty-icon {
    font-size: 4rem;
    margin-bottom: 16px;
    opacity: 0.5;
  }
  
  .empty-title {
    font-size: 1.3rem;
    color: #fff;
    font-weight: 600;
    margin-bottom: 8px;
  }
  
  .empty-text {
    color: #B0B0B0;
    margin-bottom: 24px;
  }
  
  .create-first-btn {
    padding: 12px 24px;
    background: linear-gradient(135deg, #00F5D4, #00C9A7);
    color: #1A1A2E;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 245, 212, 0.4);
    }
  }
}

.load-more {
  text-align: center;
  margin-top: 32px;
}

.load-more-btn {
  padding: 12px 32px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  color: #E0E0E0;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 auto;
  
  &:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.15);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 响应式设计
@media (max-width: 768px) {
  .available-lobbies {
    padding: 24px;
  }
  
  .lobbies-filters {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .filter-options {
    justify-content: center;
  }
  
  .lobby-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .info-row {
    flex-direction: column;
    gap: 8px;
  }
  
  .lobby-footer {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .join-btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
