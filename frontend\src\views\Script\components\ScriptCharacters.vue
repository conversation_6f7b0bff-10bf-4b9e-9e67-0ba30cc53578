<template>
  <div class="script-characters">
    <div class="section-header">
      <h3 class="section-title">角色介绍</h3>
      <div class="section-divider"></div>
    </div>
    
    <div class="characters-grid">
      <div 
        v-for="character in characters" 
        :key="character.id"
        class="character-card"
      >
        <div class="character-avatar">
          <img 
            v-if="character.avatar" 
            :src="character.avatar" 
            :alt="character.name"
            class="avatar-image"
          />
          <div v-else class="avatar-placeholder">
            {{ character.name.charAt(0) }}
          </div>
          
          <div class="character-gender" :class="character.gender">
            {{ character.gender === '男' ? '♂' : character.gender === '女' ? '♀' : '⚧' }}
          </div>
        </div>
        
        <div class="character-info">
          <h4 class="character-name">{{ character.name }}</h4>
          <p class="character-title">{{ character.occupation }}</p>
          
          <div class="character-stats">
            <div class="stat-item">
              <span class="stat-label">年龄</span>
              <span class="stat-value">{{ character.age }}</span>
            </div>
            
            <div class="stat-item">
              <span class="stat-label">职业</span>
              <span class="stat-value">{{ character.occupation }}</span>
            </div>
            
            <div class="stat-item" v-if="character.difficulty">
              <span class="stat-label">难度</span>
              <div class="difficulty-indicator">
                <span 
                  v-for="i in 3" 
                  :key="i"
                  class="difficulty-dot"
                  :class="{ active: i <= character.difficulty }"
                ></span>
              </div>
            </div>
          </div>
          
          <div class="character-description">
            <p class="description-text">{{ character.description }}</p>
          </div>

          <div class="character-traits" v-if="character.traits">
            <h5 class="traits-title">性格特点</h5>
            <div class="personality-text">
              {{ character.traits }}
            </div>
          </div>

          <div class="character-secrets" v-if="character.secretsCount && character.secretsCount > 0">
            <h5 class="secrets-title">
              <span class="secrets-icon">🔒</span>
              秘密数量
            </h5>
            <div class="secrets-count">
              {{ character.secretsCount }} 个秘密
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="characters-summary">
      <div class="summary-stats">
        <div class="summary-item">
          <span class="summary-number">{{ characters.length }}</span>
          <span class="summary-label">总角色数</span>
        </div>
        
        <div class="summary-item">
          <span class="summary-number">{{ maleCount }}</span>
          <span class="summary-label">男性角色</span>
        </div>
        
        <div class="summary-item">
          <span class="summary-number">{{ femaleCount }}</span>
          <span class="summary-label">女性角色</span>
        </div>
        
        <div class="summary-item">
          <span class="summary-number">{{ coreCount }}</span>
          <span class="summary-label">核心角色</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { ScriptCharacter } from '@/types/script'

interface CharactersProps {
  characters: ScriptCharacter[]
}

const props = defineProps<CharactersProps>()

const maleCount = computed(() => 
  props.characters.filter(c => c.gender === '男').length
)

const femaleCount = computed(() => 
  props.characters.filter(c => c.gender === '女').length
)

const coreCount = computed(() => 
  props.characters.filter(c => c.isCore).length
)
</script>

<style lang="scss" scoped>
.script-characters {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.1);
  border-radius: 16px;
  padding: 32px;
  backdrop-filter: blur(10px);
}

.section-header {
  margin-bottom: 32px;
}

.section-title {
  font-size: 1.5rem;
  color: #fff;
  font-weight: 700;
  margin-bottom: 12px;
}

.section-divider {
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, #00F5D4, #FF00E4);
  border-radius: 2px;
}

.characters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.character-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(0, 245, 212, 0.3);
    transform: translateY(-2px);
  }
}

.character-avatar {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  
  .avatar-image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid rgba(0, 245, 212, 0.3);
  }
  
  .avatar-placeholder {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(135deg, #00F5D4, #FF00E4);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: 700;
    color: #1A1A2E;
  }
  
  .character-gender {
    position: absolute;
    bottom: -5px;
    right: -5px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: 600;
    border: 2px solid #1A1A2E;
    
    &.男 {
      background: #4A90E2;
      color: #fff;
    }
    
    &.女 {
      background: #E24A90;
      color: #fff;
    }
  }
}

.character-info {
  text-align: center;
}

.character-name {
  font-size: 1.3rem;
  color: #fff;
  font-weight: 700;
  margin-bottom: 4px;
}

.character-title {
  font-size: 1rem;
  color: #00F5D4;
  font-weight: 500;
  margin-bottom: 16px;
}

.character-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
  text-align: left;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .stat-label {
    font-size: 0.9rem;
    color: #B0B0B0;
  }
  
  .stat-value {
    font-size: 0.9rem;
    color: #E0E0E0;
    font-weight: 500;
  }
}

.difficulty-indicator {
  display: flex;
  gap: 4px;
  
  .difficulty-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    transition: background 0.3s ease;
    
    &.active {
      background: #FF6B6B;
    }
  }
}

.character-description {
  margin-bottom: 16px;
  text-align: left;
  
  .description-text {
    color: #E0E0E0;
    line-height: 1.6;
    font-size: 0.9rem;
    margin: 0;
  }
}

.character-traits {
  margin-bottom: 16px;
  text-align: left;
  
  .traits-title {
    font-size: 0.9rem;
    color: #00F5D4;
    font-weight: 600;
    margin-bottom: 8px;
  }
  
  .personality-text {
    color: #E0E0E0;
    line-height: 1.6;
    font-size: 0.9rem;
    margin: 0;
  }
}

.character-special {
  text-align: left;
  margin-bottom: 16px;
  
  .special-title {
    font-size: 0.9rem;
    color: #FFC107;
    font-weight: 600;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 6px;
    
    .special-icon {
      font-size: 0.8rem;
    }
  }
  
  .special-text {
    font-size: 0.8rem;
    color: #E0E0E0;
    line-height: 1.5;
  }
}

.character-core {
  text-align: center;
  
  .core-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background: rgba(255, 193, 7, 0.2);
    border: 1px solid rgba(255, 193, 7, 0.4);
    border-radius: 16px;
    color: #FFC107;
    font-size: 0.8rem;
    font-weight: 600;
    
    .core-icon {
      font-size: 0.9rem;
    }
  }
}

.characters-summary {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 24px;
}

.summary-stats {
  display: flex;
  justify-content: center;
  gap: 32px;
  flex-wrap: wrap;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  
  .summary-number {
    font-size: 2rem;
    font-weight: 700;
    color: #00F5D4;
  }
  
  .summary-label {
    font-size: 0.9rem;
    color: #B0B0B0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .script-characters {
    padding: 24px;
  }
  
  .characters-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .character-card {
    padding: 20px;
  }
  
  .summary-stats {
    gap: 24px;
  }
  
  .summary-item .summary-number {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .script-characters {
    padding: 20px;
  }
  
  .character-avatar {
    width: 60px;
    height: 60px;
    
    .avatar-placeholder {
      font-size: 1.5rem;
    }
    
    .character-gender {
      width: 24px;
      height: 24px;
      font-size: 1rem;
    }
  }
  
  .character-name {
    font-size: 1.1rem;
  }
  
  .summary-stats {
    gap: 16px;
  }
}
</style>
