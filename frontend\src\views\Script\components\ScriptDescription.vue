<template>
  <div class="script-description">
    <div class="section-header">
      <h3 class="section-title">剧本简介</h3>
      <div class="section-divider"></div>
    </div>
    
    <div class="description-content">
      <div class="story-overview">
        <h4 class="overview-title">故事背景</h4>
        <p class="overview-text">{{ script.description }}</p>
      </div>
      
      <div class="script-details">
        <div class="detail-grid">
          <div class="detail-item">
            <span class="detail-label">游戏时长</span>
            <span class="detail-value">{{ script.duration }}</span>
          </div>
          
          <div class="detail-item">
            <span class="detail-label">难度等级</span>
            <div class="difficulty-stars">
              <span 
                v-for="i in 5" 
                :key="i"
                class="star"
                :class="{ active: i <= script.difficulty }"
              >
                ⭐
              </span>
            </div>
          </div>
          
          <div class="detail-item">
            <span class="detail-label">推荐年龄</span>
            <span class="detail-value">{{ script.ageRange }}</span>
          </div>
          
          <div class="detail-item">
            <span class="detail-label">剧本类型</span>
            <div class="script-tags">
              <span 
                v-for="tag in script.tagList" 
                :key="tag"
                class="tag"
              >
                {{ tag }}
              </span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="story-highlights" v-if="script.highlights?.length">
        <h4 class="highlights-title">剧本亮点</h4>
        <ul class="highlights-list">
          <li 
            v-for="highlight in script.highlights" 
            :key="highlight"
            class="highlight-item"
          >
            <span class="highlight-icon">✨</span>
            <span class="highlight-text">{{ highlight }}</span>
          </li>
        </ul>
      </div>
      
      <div class="content-warning" v-if="script.warnings?.length">
        <h4 class="warning-title">
          <span class="warning-icon">⚠️</span>
          内容提醒
        </h4>
        <div class="warning-list">
          <span 
            v-for="warning in script.warnings" 
            :key="warning"
            class="warning-tag"
          >
            {{ warning }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ScriptDetail } from '@/types/script'

interface ScriptProps {
  script: ScriptDetail
}

defineProps<ScriptProps>()
</script>

<style lang="scss" scoped>
.script-description {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.1);
  border-radius: 16px;
  padding: 32px;
  backdrop-filter: blur(10px);
}

.section-header {
  margin-bottom: 32px;
}

.section-title {
  font-size: 1.5rem;
  color: #fff;
  font-weight: 700;
  margin-bottom: 12px;
}

.section-divider {
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, #00F5D4, #FF00E4);
  border-radius: 2px;
}

.description-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.story-overview {
  .overview-title {
    font-size: 1.2rem;
    color: #00F5D4;
    font-weight: 600;
    margin-bottom: 16px;
  }
  
  .overview-text {
    color: #E0E0E0;
    line-height: 1.8;
    font-size: 1rem;
    margin: 0;
  }
}

.script-details {
  .detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
  }
  
  .detail-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .detail-label {
    font-size: 0.9rem;
    color: #B0B0B0;
    font-weight: 500;
  }
  
  .detail-value {
    font-size: 1rem;
    color: #fff;
    font-weight: 600;
  }
}

.difficulty-stars {
  display: flex;
  gap: 4px;
  
  .star {
    font-size: 1.2rem;
    opacity: 0.3;
    transition: opacity 0.3s ease;
    
    &.active {
      opacity: 1;
    }
  }
}

.script-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  
  .tag {
    padding: 6px 12px;
    background: rgba(0, 245, 212, 0.1);
    border: 1px solid rgba(0, 245, 212, 0.3);
    border-radius: 20px;
    color: #00F5D4;
    font-size: 0.85rem;
    font-weight: 500;
  }
}

.story-highlights {
  .highlights-title {
    font-size: 1.2rem;
    color: #00F5D4;
    font-weight: 600;
    margin-bottom: 16px;
  }
  
  .highlights-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .highlight-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    
    .highlight-icon {
      font-size: 1.1rem;
      margin-top: 2px;
    }
    
    .highlight-text {
      color: #E0E0E0;
      line-height: 1.6;
      flex: 1;
    }
  }
}

.content-warning {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 12px;
  padding: 20px;
  
  .warning-title {
    font-size: 1.1rem;
    color: #FFC107;
    font-weight: 600;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    
    .warning-icon {
      font-size: 1.2rem;
    }
  }
  
  .warning-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .warning-tag {
    padding: 6px 12px;
    background: rgba(255, 193, 7, 0.2);
    border: 1px solid rgba(255, 193, 7, 0.4);
    border-radius: 16px;
    color: #FFC107;
    font-size: 0.85rem;
    font-weight: 500;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .script-description {
    padding: 24px;
  }
  
  .description-content {
    gap: 24px;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .script-tags, .warning-list {
    gap: 6px;
  }
  
  .tag, .warning-tag {
    font-size: 0.8rem;
    padding: 4px 10px;
  }
}

@media (max-width: 480px) {
  .script-description {
    padding: 20px;
  }
  
  .section-title {
    font-size: 1.3rem;
  }
  
  .overview-title, .highlights-title {
    font-size: 1.1rem;
  }
}
</style>
