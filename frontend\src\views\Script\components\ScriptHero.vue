<template>
  <section class="script-hero">
    <div class="hero-background">
      <img 
        :src="script.coverImage" 
        :alt="script.title"
        class="background-image"
      />
      <div class="background-overlay"></div>
    </div>
    
    <div class="hero-content">
      <div class="container">
        <div class="hero-layout">
          <!-- 剧本封面 -->
          <div class="script-cover-section">
            <div class="cover-wrapper">
              <img 
                :src="script.coverImage" 
                :alt="script.title"
                class="script-cover"
                @error="handleImageError"
              />
              <div class="cover-badges">
                <div class="difficulty-badge" :class="`difficulty-${script.difficulty}`">
                  {{ getDifficultyText(script.difficulty) }}
                </div>
                <div class="rating-badge">
                  <span class="rating-stars">
                    <span 
                      v-for="star in 5" 
                      :key="star"
                      class="star"
                      :class="{ filled: star <= Math.floor(script.averageRating) }"
                    >
                      ★
                    </span>
                  </span>
                  <span class="rating-value">{{ script.averageRating }}</span>
                  <span class="rating-count">({{ script.reviewCount }})</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 剧本信息 -->
          <div class="script-info-section">
            <div class="script-header">
              <h1 class="script-title">{{ script.title }}</h1>
              <div class="script-meta">
                <span class="meta-item">
                  <i class="icon-tag"></i>
                  {{ script.category }}
                </span>
                <span class="meta-item">
                  <i class="icon-users"></i>
                  {{ getPlayerCountText() }}
                </span>
                <span class="meta-item">
                  <i class="icon-clock"></i>
                  {{ script.duration }}
                </span>
                <span class="meta-item">
                  <i class="icon-calendar"></i>
                  {{ formatDate(script.createTime) }}
                </span>
              </div>
              
              <!-- 标签 -->
              <div class="script-tags">
                <span 
                  v-for="tag in script.tagList" 
                  :key="tag"
                  class="tag"
                >
                  {{ tag }}
                </span>
              </div>
            </div>

            <!-- 剧本描述 -->
            <div class="script-description">
              <p class="description-text">{{ script.description }}</p>
            </div>

            <!-- 价格和操作 -->
            <div class="script-actions">
              <div class="price-section">
                <span class="price-label">参考价格</span>
                <div class="price-value">
                  <span class="price-symbol">¥</span>
                  <span class="price-amount">{{ script.price }}</span>
                  <span class="price-unit">/人</span>
                </div>
              </div>
              
              <div class="action-buttons">
                <button 
                  class="action-btn favorite-btn"
                  :class="{ active: script.isFavorite }"
                  @click="handleFavorite"
                >
                  <span class="btn-icon">{{ script.isFavorite ? '❤️' : '🤍' }}</span>
                  <span class="btn-text">{{ script.isFavorite ? '已收藏' : '收藏' }}</span>
                </button>
                
                <button class="action-btn share-btn" @click="handleShare">
                  <span class="btn-icon">📤</span>
                  <span class="btn-text">分享</span>
                </button>
                
                <button class="action-btn primary-btn" @click="handleQuickJoin">
                  <span class="btn-icon">🚗</span>
                  <span class="btn-text">快速拼车</span>
                </button>
              </div>
            </div>

            <!-- 统计信息 -->
            <div class="script-stats">
              <div class="stat-item">
                <span class="stat-number">{{ formatNumber(script.playCount || 0) }}</span>
                <span class="stat-label">游戏次数</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">{{ script.reviewCount || 0 }}</span>
                <span class="stat-label">评价数</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">{{ getActiveLobbiesCount() }}</span>
                <span class="stat-label">活跃车队</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { ScriptDetail } from '@/types/script'

// Props
interface Props {
  script: ScriptDetail
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'favorite'): void
  (e: 'share'): void
  (e: 'quick-join'): void
}

const emit = defineEmits<Emits>()

// 方法
const getDifficultyText = (difficulty: number): string => {
  const difficultyMap: Record<number, string> = {
    1: '新手',
    2: '简单',
    3: '中等',
    4: '困难',
    5: '专家'
  }
  return difficultyMap[difficulty] || '未知'
}

const getPlayerCountText = (): string => {
  if (props.script.playerCountMin === props.script.playerCountMax) {
    return `${props.script.playerCountMin}人`
  }
  return `${props.script.playerCountMin}-${props.script.playerCountMax}人`
}

const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const formatNumber = (num: number): string => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

const getActiveLobbiesCount = (): number => {
  // 这里应该从props或store中获取活跃车队数量
  return Math.floor(Math.random() * 20) + 5
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = 'https://via.placeholder.com/400x600/1A1A2E/00F5D4?text=剧本封面'
}

const handleFavorite = () => {
  emit('favorite')
}

const handleShare = () => {
  emit('share')
}

const handleQuickJoin = () => {
  emit('quick-join')
}
</script>

<style lang="scss" scoped>
.script-hero {
  position: relative;
  min-height: 60vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.background-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: blur(20px) brightness(0.3);
  transform: scale(1.1);
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(26, 26, 46, 0.9) 0%,
    rgba(22, 33, 62, 0.8) 50%,
    rgba(15, 15, 30, 0.9) 100%
  );
}

.hero-content {
  position: relative;
  z-index: 2;
  width: 100%;
  padding: 60px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.hero-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 40px;
  align-items: start;
}

.script-cover-section {
  position: sticky;
  top: 20px;
}

.cover-wrapper {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.script-cover {
  width: 100%;
  height: 400px;
  object-fit: cover;
  display: block;
}

.cover-badges {
  position: absolute;
  top: 16px;
  left: 16px;
  right: 16px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.difficulty-badge {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
  
  &.difficulty-1, &.difficulty-2 {
    background: rgba(76, 175, 80, 0.8);
    color: #fff;
  }
  
  &.difficulty-3 {
    background: rgba(255, 193, 7, 0.8);
    color: #1A1A2E;
  }
  
  &.difficulty-4, &.difficulty-5 {
    background: rgba(244, 67, 54, 0.8);
    color: #fff;
  }
}

.rating-badge {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.rating-stars {
  display: flex;
  gap: 2px;
}

.star {
  color: #444;
  font-size: 0.9rem;
  
  &.filled {
    color: #FFD700;
  }
}

.rating-value {
  color: #fff;
  font-size: 0.9rem;
  font-weight: 600;
}

.rating-count {
  color: #888;
  font-size: 0.75rem;
}

.script-info-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.script-header {
  .script-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #fff;
    margin-bottom: 16px;
    line-height: 1.2;
  }
  
  .script-meta {
    display: flex;
    gap: 20px;
    margin-bottom: 16px;
    flex-wrap: wrap;
  }
  
  .meta-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem;
    color: #B0B0B0;
    
    i {
      color: #00F5D4;
      font-size: 0.8rem;
    }
  }
  
  .script-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }
  
  .tag {
    padding: 6px 12px;
    background: rgba(0, 245, 212, 0.1);
    color: #00F5D4;
    border-radius: 16px;
    font-size: 0.8rem;
    border: 1px solid rgba(0, 245, 212, 0.2);
  }
}

.script-description {
  .description-text {
    font-size: 1.1rem;
    color: #E0E0E0;
    line-height: 1.6;
    margin: 0;
  }
}

.script-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 245, 212, 0.1);
  border-radius: 16px;
}

.price-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.price-label {
  font-size: 0.8rem;
  color: #888;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.price-value {
  display: flex;
  align-items: baseline;
  gap: 2px;
}

.price-symbol {
  font-size: 1.2rem;
  color: #FF00E4;
}

.price-amount {
  font-size: 2rem;
  font-weight: 700;
  color: #FF00E4;
}

.price-unit {
  font-size: 0.9rem;
  color: #FF00E4;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  
  .btn-icon {
    font-size: 1rem;
  }
  
  &.favorite-btn {
    background: rgba(255, 255, 255, 0.05);
    color: #B0B0B0;
    border: 1px solid rgba(255, 255, 255, 0.1);
    
    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: #fff;
    }
    
    &.active {
      background: rgba(255, 68, 68, 0.1);
      color: #FF4444;
      border-color: rgba(255, 68, 68, 0.3);
    }
  }
  
  &.share-btn {
    background: rgba(255, 255, 255, 0.05);
    color: #B0B0B0;
    border: 1px solid rgba(255, 255, 255, 0.1);
    
    &:hover {
      background: rgba(0, 245, 212, 0.1);
      color: #00F5D4;
      border-color: rgba(0, 245, 212, 0.3);
    }
  }
  
  &.primary-btn {
    background: linear-gradient(135deg, #00F5D4, #00C9A7);
    color: #1A1A2E;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 245, 212, 0.4);
    }
  }
}

.script-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #00F5D4;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.8rem;
  color: #888;
  text-transform: uppercase;
  letter-spacing: 1px;
}

@media (max-width: 768px) {
  .hero-layout {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .script-cover-section {
    position: static;
    max-width: 250px;
    margin: 0 auto;
  }
  
  .cover-wrapper {
    .script-cover {
      height: 350px;
    }
  }
  
  .script-header {
    .script-title {
      font-size: 2rem;
    }
    
    .script-meta {
      gap: 12px;
    }
  }
  
  .script-actions {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .action-buttons {
    justify-content: center;
  }
  
  .script-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
}
</style>
