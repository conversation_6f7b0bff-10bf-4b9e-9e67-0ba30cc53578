<template>
  <div class="script-reviews">
    <div class="section-header">
      <h3 class="section-title">玩家评价</h3>
      <div class="section-divider"></div>
    </div>
    
    <div class="reviews-summary">
      <div class="rating-overview">
        <div class="overall-rating">
          <span class="rating-score">{{ averageRating.toFixed(1) }}</span>
          <div class="rating-stars">
            <span 
              v-for="i in 5" 
              :key="i"
              class="star"
              :class="{ active: i <= Math.round(averageRating) }"
            >
              ⭐
            </span>
          </div>
          <span class="rating-count">基于 {{ reviewCount }} 条评价</span>
        </div>
        
        <div class="rating-breakdown">
          <!-- 简化评分分解，暂时隐藏 -->
          <div class="rating-info">
            <span>平均评分：{{ averageRating.toFixed(1) }} / 5.0</span>
          </div>
        </div>
      </div>
      
      <button class="write-review-btn" @click="handleWriteReview">
        <span class="btn-icon">✍️</span>
        <span class="btn-text">写评价</span>
      </button>
    </div>
    
    <div class="reviews-list">
      <div 
        v-for="review in reviews" 
        :key="review.id"
        class="review-item"
      >
        <div class="review-header">
          <div class="reviewer-info">
            <img 
              v-if="review.userAvatar" 
              :src="review.userAvatar" 
              :alt="review.userNickname"
              class="reviewer-avatar"
            />
            <div v-else class="reviewer-avatar-placeholder">
              {{ review.userNickname.charAt(0) }}
            </div>
            
            <div class="reviewer-details">
              <h4 class="reviewer-name">
                {{ review.isAnonymous ? '匿名用户' : review.userNickname }}
              </h4>
              <div class="review-meta">
                <div class="review-rating">
                  <span 
                    v-for="i in 5" 
                    :key="i"
                    class="star"
                    :class="{ active: i <= review.rating }"
                  >
                    ⭐
                  </span>
                </div>
                <span class="review-date">{{ formatDate(review.createTime) }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="review-content">
          <h5 v-if="review.title" class="review-title">{{ review.title }}</h5>
          <p class="review-text">{{ review.content }}</p>
          
          <div v-if="review.images.length" class="review-images">
            <img 
              v-for="(image, index) in review.images" 
              :key="index"
              :src="image" 
              :alt="`评价图片 ${index + 1}`"
              class="review-image"
            />
          </div>
          
          <div v-if="review.tags.length" class="review-tags">
            <span 
              v-for="tag in review.tags" 
              :key="tag"
              class="review-tag"
            >
              {{ tag }}
            </span>
          </div>
        </div>
        
        <div class="review-actions">
          <button 
            class="action-btn like-btn"
            :class="{ active: review.isLiked }"
            @click="handleLike(review.id)"
          >
            <span class="btn-icon">👍</span>
            <span class="btn-text">{{ review.likedCount || 0 }}</span>
          </button>
          
          <button 
            class="action-btn helpful-btn"
            :class="{ active: review.isHelpful }"
            @click="handleHelpful(review.id)"
          >
            <span class="btn-icon">💪</span>
            <span class="btn-text">有用 {{ review.helpfulCount || 0 }}</span>
          </button>
        </div>
      </div>
      
      <div v-if="!reviews.length" class="empty-reviews">
        <div class="empty-icon">💭</div>
        <h4 class="empty-title">暂无评价</h4>
        <p class="empty-text">成为第一个评价者吧！</p>
      </div>
      
      <div v-if="reviews.length" class="load-more-section">
        <button class="load-more-btn" @click="handleLoadMore">
          <span class="btn-text">加载更多评价</span>
          <span class="btn-icon">↓</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ScriptReview } from '@/types/script'

interface ReviewsProps {
  reviews: ScriptReview[]
  scriptId: number
  averageRating: number
  reviewCount: number
}

interface Emits {
  (e: 'load-more'): void
}

const props = defineProps<ReviewsProps>()
const emit = defineEmits<Emits>()

const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const handleLike = (reviewId: number) => {
  // TODO: 实现点赞功能
  console.log('Like review:', reviewId)
}

const handleHelpful = (reviewId: number) => {
  // TODO: 实现有用功能
  console.log('Mark helpful:', reviewId)
}

const handleWriteReview = () => {
  // TODO: 打开写评价弹窗
  console.log('Write review for script:', props.scriptId)
}

const handleLoadMore = () => {
  emit('load-more')
}
</script>

<style lang="scss" scoped>
.script-reviews {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.1);
  border-radius: 16px;
  padding: 32px;
  backdrop-filter: blur(10px);
}

.section-header {
  margin-bottom: 32px;
}

.section-title {
  font-size: 1.5rem;
  color: #fff;
  font-weight: 700;
  margin-bottom: 12px;
}

.section-divider {
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, #00F5D4, #FF00E4);
  border-radius: 2px;
}

.reviews-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  gap: 20px;
  flex-wrap: wrap;
}

.rating-overview {
  display: flex;
  align-items: center;
  gap: 24px;
}

.overall-rating {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.rating-score {
  font-size: 2.5rem;
  font-weight: 700;
  color: #FF00E4;
}

.rating-stars {
  display: flex;
  gap: 2px;
}

.star {
  font-size: 1rem;
  opacity: 0.3;
  
  &.active {
    opacity: 1;
  }
}

.rating-count {
  font-size: 0.9rem;
  color: #B0B0B0;
}

.rating-breakdown {
  .rating-info {
    color: #E0E0E0;
    font-size: 1rem;
  }
}

.write-review-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 245, 212, 0.4);
  }
}

.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.review-item {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(0, 245, 212, 0.3);
  }
}

.review-header {
  margin-bottom: 16px;
}

.reviewer-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.reviewer-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(0, 245, 212, 0.3);
}

.reviewer-avatar-placeholder {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #00F5D4, #FF00E4);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1A1A2E;
}

.reviewer-details {
  flex: 1;
}

.reviewer-name {
  font-size: 1.1rem;
  color: #fff;
  font-weight: 600;
  margin-bottom: 4px;
}

.review-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.review-rating {
  display: flex;
  gap: 2px;
  
  .star {
    font-size: 0.8rem;
  }
}

.review-date {
  font-size: 0.85rem;
  color: #B0B0B0;
}

.review-content {
  margin-bottom: 16px;
}

.review-title {
  font-size: 1.1rem;
  color: #00F5D4;
  font-weight: 600;
  margin-bottom: 8px;
}

.review-text {
  color: #E0E0E0;
  line-height: 1.6;
  margin-bottom: 16px;
}

.review-images {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.review-image {
  width: 100px;
  height: 100px;
  border-radius: 8px;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.3s ease;
  
  &:hover {
    transform: scale(1.05);
  }
}

.review-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 16px;
}

.review-tag {
  padding: 4px 10px;
  background: rgba(0, 245, 212, 0.1);
  border: 1px solid rgba(0, 245, 212, 0.2);
  border-radius: 12px;
  color: #00F5D4;
  font-size: 0.8rem;
  font-weight: 500;
}

.review-actions {
  display: flex;
  gap: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 16px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  color: #B0B0B0;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
  }
  
  &.active {
    background: rgba(0, 245, 212, 0.1);
    border-color: rgba(0, 245, 212, 0.3);
    color: #00F5D4;
  }
}

.empty-reviews {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-title {
  font-size: 1.3rem;
  color: #fff;
  margin-bottom: 8px;
}

.empty-text {
  color: #B0B0B0;
  font-size: 1rem;
}

.load-more-section {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.load-more-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #E0E0E0;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(0, 245, 212, 0.1);
    border-color: rgba(0, 245, 212, 0.3);
    color: #00F5D4;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .script-reviews {
    padding: 24px;
  }
  
  .reviews-summary {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .rating-overview {
    justify-content: center;
    gap: 16px;
  }
  
  .review-item {
    padding: 20px;
  }
  
  .reviewer-info {
    gap: 10px;
  }
  
  .reviewer-avatar,
  .reviewer-avatar-placeholder {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
  
  .review-images {
    gap: 6px;
  }
  
  .review-image {
    width: 80px;
    height: 80px;
  }
}

@media (max-width: 480px) {
  .script-reviews {
    padding: 20px;
  }
  
  .section-title {
    font-size: 1.3rem;
  }
  
  .rating-score {
    font-size: 2rem;
  }
  
  .review-item {
    padding: 16px;
  }
  
  .review-actions {
    gap: 8px;
  }
  
  .action-btn {
    padding: 6px 10px;
    font-size: 0.8rem;
  }
}
</style>