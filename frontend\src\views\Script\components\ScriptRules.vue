<template>
  <div class="script-rules">
    <div class="section-header">
      <h3 class="section-title">游戏规则</h3>
      <div class="section-divider"></div>
    </div>
    
    <div class="rules-content">
      <div 
        v-for="rule in rules" 
        :key="rule.id"
        class="rule-item"
        :class="{ important: rule.isImportant }"
      >
        <div class="rule-header">
          <div class="rule-type">{{ rule.ruleType }}</div>
          <div class="rule-title">{{ rule.title }}</div>
          <div v-if="rule.isImportant" class="important-badge">
            <span class="important-icon">⚠️</span>
            重要
          </div>
        </div>
        <div class="rule-content">
          {{ rule.content }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ScriptRule } from '@/types/script'

interface RulesProps {
  rules: ScriptRule[]
}

defineProps<RulesProps>()
</script>

<style lang="scss" scoped>
.script-rules {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.1);
  border-radius: 16px;
  padding: 32px;
  backdrop-filter: blur(10px);
}

.section-header {
  margin-bottom: 32px;
}

.section-title {
  font-size: 1.5rem;
  color: #fff;
  font-weight: 700;
  margin-bottom: 12px;
}

.section-divider {
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, #00F5D4, #FF00E4);
  border-radius: 2px;
}

.rules-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.rule-item {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(0, 245, 212, 0.3);
  }
  
  &.important {
    border-color: rgba(255, 193, 7, 0.4);
    background: rgba(255, 193, 7, 0.1);
  }
}

.rule-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.rule-type {
  padding: 4px 12px;
  background: rgba(0, 245, 212, 0.2);
  color: #00F5D4;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.rule-title {
  font-size: 1.1rem;
  color: #fff;
  font-weight: 600;
  flex: 1;
}

.important-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: rgba(255, 193, 7, 0.2);
  color: #FFC107;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  
  .important-icon {
    font-size: 0.7rem;
  }
}

.rule-content {
  color: #E0E0E0;
  line-height: 1.6;
  font-size: 0.95rem;
}


@media (max-width: 768px) {
  .script-rules {
    padding: 24px;
  }
  
  .rules-content {
    gap: 12px;
  }
  
  .rule-item {
    padding: 16px;
  }
  
  .rule-header {
    gap: 8px;
  }
  
  .rule-title {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .script-rules {
    padding: 20px;
  }
  
  .section-title {
    font-size: 1.3rem;
  }
  
  .rule-item {
    padding: 12px;
  }
  
  .rule-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
