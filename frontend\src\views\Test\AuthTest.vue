<template>
  <div class="auth-test">
    <el-card class="test-card">
      <template #header>
        <h2>🔐 用户认证接口测试</h2>
      </template>

      <!-- 发送验证码测试 -->
      <el-card class="test-section" shadow="never">
        <template #header>
          <h3>1. 发送验证码</h3>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-input 
              v-model="phone" 
              placeholder="请输入手机号"
              style="margin-bottom: 10px;"
            />
            <el-button 
              type="primary" 
              @click="testSendCode"
              :loading="loading.sendCode"
            >
              发送验证码
            </el-button>
          </el-col>
          <el-col :span="12">
            <div class="result-box">
              <pre>{{ codeResult }}</pre>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 用户登录测试 -->
      <el-card class="test-section" shadow="never">
        <template #header>
          <h3>2. 用户登录</h3>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-input 
              v-model="loginForm.phone" 
              placeholder="手机号"
              style="margin-bottom: 10px;"
            />
            <el-input 
              v-model="loginForm.code" 
              placeholder="验证码"
              style="margin-bottom: 10px;"
            />
            <el-button 
              type="primary" 
              @click="testLogin"
              :loading="loading.login"
            >
              登录
            </el-button>
          </el-col>
          <el-col :span="12">
            <div class="result-box">
              <pre>{{ loginResult }}</pre>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 获取用户信息测试 -->
      <el-card class="test-section" shadow="never">
        <template #header>
          <h3>3. 获取用户信息</h3>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-button 
              type="primary" 
              @click="testGetUser"
              :loading="loading.getUser"
            >
              获取用户信息
            </el-button>
            <el-tag v-if="currentToken" type="success" style="margin-left: 10px;">
              Token已设置
            </el-tag>
          </el-col>
          <el-col :span="12">
            <div class="result-box">
              <pre>{{ userResult }}</pre>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 用户登出测试 -->
      <el-card class="test-section" shadow="never">
        <template #header>
          <h3>4. 用户登出</h3>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-button 
              type="danger" 
              @click="testLogout"
              :loading="loading.logout"
            >
              登出
            </el-button>
          </el-col>
          <el-col :span="12">
            <div class="result-box">
              <pre>{{ logoutResult }}</pre>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 401错误测试 -->
      <el-card class="test-section" shadow="never">
        <template #header>
          <h3>5. 401错误处理测试</h3>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-button
              type="warning"
              @click="test401Error"
              :loading="loading.test401"
              style="margin-right: 10px;"
            >
              模拟401错误
            </el-button>
            <el-button
              type="danger"
              @click="testDirectUnauthorized"
              :loading="loading.testDirect"
            >
              直接测试清理逻辑
            </el-button>
            <p style="margin-top: 10px; font-size: 12px; color: #666;">
              第一个按钮会发送无效token请求，第二个按钮直接测试401处理逻辑
            </p>
          </el-col>
          <el-col :span="12">
            <div class="result-box">
              <pre>{{ test401Result }}</pre>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 测试状态 -->
      <el-card class="test-section" shadow="never">
        <template #header>
          <h3>📊 测试状态</h3>
        </template>
        <el-row :gutter="20">
          <el-col :span="5">
            <el-statistic title="发送验证码" :value="testStatus.sendCode ? '✅' : '❌'" />
          </el-col>
          <el-col :span="5">
            <el-statistic title="用户登录" :value="testStatus.login ? '✅' : '❌'" />
          </el-col>
          <el-col :span="5">
            <el-statistic title="获取用户信息" :value="testStatus.getUser ? '✅' : '❌'" />
          </el-col>
          <el-col :span="4">
            <el-statistic title="用户登出" :value="testStatus.logout ? '✅' : '❌'" />
          </el-col>
          <el-col :span="5">
            <el-statistic title="401错误处理" :value="testStatus.test401 ? '✅' : '❌'" />
          </el-col>
        </el-row>
      </el-card>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'

// 配置axios基础URL
const api = axios.create({
  baseURL: 'http://localhost:8081',
  timeout: 10000
})

// 请求拦截器 - 添加token
api.interceptors.request.use(config => {
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// 响应拦截器
api.interceptors.response.use(
  response => response,
  error => {
    console.error('API Error:', error)
    return Promise.reject(error)
  }
)

// 响应式数据
const phone = ref('13800138000')
const loginForm = ref({
  phone: '13800138000',
  code: '123456'
})

const codeResult = ref('')
const loginResult = ref('')
const userResult = ref('')
const logoutResult = ref('')
const test401Result = ref('')

const loading = ref({
  sendCode: false,
  login: false,
  getUser: false,
  logout: false,
  test401: false,
  testDirect: false
})

const testStatus = ref({
  sendCode: false,
  login: false,
  getUser: false,
  logout: false,
  test401: false
})

const currentToken = computed(() => localStorage.getItem('token'))

// 测试发送验证码
const testSendCode = async () => {
  loading.value.sendCode = true
  try {
    const response = await api.post('/user/code', null, {
      params: { phone: phone.value }
    })
    codeResult.value = JSON.stringify(response.data, null, 2)
    testStatus.value.sendCode = response.data.code === 200

    if (response.data.code === 200) {
      ElMessage.success('验证码发送成功')
    } else {
      ElMessage.error(response.data.message || '发送失败')
    }
  } catch (error: any) {
    codeResult.value = `错误: ${error.message}`
    testStatus.value.sendCode = false
    ElMessage.error('请求失败')
  } finally {
    loading.value.sendCode = false
  }
}

// 测试登录
const testLogin = async () => {
  loading.value.login = true
  try {
    const response = await api.post('/api/user/login', loginForm.value)
    loginResult.value = JSON.stringify(response.data, null, 2)
    testStatus.value.login = response.data.code === 200

    if (response.data.code === 200) {
      localStorage.setItem('token', response.data.data)
      ElMessage.success('登录成功')
    } else {
      ElMessage.error(response.data.message || '登录失败')
    }
  } catch (error: any) {
    loginResult.value = `错误: ${error.message}`
    testStatus.value.login = false
    ElMessage.error('请求失败')
  } finally {
    loading.value.login = false
  }
}

// 测试获取用户信息
const testGetUser = async () => {
  loading.value.getUser = true
  try {
    const response = await api.get('/user/me')
    userResult.value = JSON.stringify(response.data, null, 2)
    testStatus.value.getUser = response.data.code === 200

    if (response.data.code === 200) {
      ElMessage.success('获取用户信息成功')
    } else {
      ElMessage.error(response.data.message || '获取失败')
    }
  } catch (error: any) {
    userResult.value = `错误: ${error.message}`
    testStatus.value.getUser = false
    ElMessage.error('请求失败')
  } finally {
    loading.value.getUser = false
  }
}

// 测试登出
const testLogout = async () => {
  loading.value.logout = true
  try {
    const response = await api.post('/user/logout')
    logoutResult.value = JSON.stringify(response.data, null, 2)
    testStatus.value.logout = response.data.code === 200

    if (response.data.code === 200) {
      localStorage.removeItem('token')
      ElMessage.success('登出成功')
    } else {
      ElMessage.error(response.data.message || '登出失败')
    }
  } catch (error: any) {
    logoutResult.value = `错误: ${error.message}`
    testStatus.value.logout = false
    ElMessage.error('请求失败')
  } finally {
    loading.value.logout = false
  }
}

// 测试401错误处理
const test401Error = async () => {
  loading.value.test401 = true
  test401Result.value = '开始测试401错误处理...\n'

  try {
    // 保存当前token
    const originalToken = localStorage.getItem('auth_token')
    test401Result.value += `原始token: ${originalToken ? '存在' : '不存在'}\n`

    // 设置一个无效的token
    localStorage.setItem('auth_token', 'invalid_token_for_testing')
    test401Result.value += '设置无效token: invalid_token_for_testing\n'

    // 使用http.ts中的axios实例发送请求（这会触发拦截器）
    const { http } = await import('@/api/http')

    try {
      test401Result.value += '发送需要认证的请求...\n'
      await http.get('/user/me')

      // 如果到达这里，说明没有触发401错误
      test401Result.value += '❌ 未触发401错误，测试失败\n'
      testStatus.value.test401 = false

    } catch (error: any) {
      test401Result.value += `✅ 成功触发错误: ${error.message}\n`

      // 等待一小段时间，让拦截器有时间处理错误
      await new Promise(resolve => setTimeout(resolve, 100))

      // 检查token是否被清除
      const tokenAfterError = localStorage.getItem('auth_token')
      test401Result.value += `错误处理后token状态: ${tokenAfterError ? '仍存在' : '已清除'}\n`

      // 检查用户状态是否被清除
      const { useUserStore } = await import('@/stores')
      const userStore = useUserStore()
      const userCleared = !userStore.isLoggedIn
      test401Result.value += `用户状态是否清除: ${userCleared ? '是' : '否'}\n`

      if (!tokenAfterError && userCleared) {
        test401Result.value += '✅ Token和用户状态已被正确清除\n'
        test401Result.value += '✅ 401错误处理测试成功\n'
        testStatus.value.test401 = true
        ElMessage.success('401错误处理测试成功')
      } else {
        if (tokenAfterError) {
          test401Result.value += '❌ Token未被清除\n'
        }
        if (!userCleared) {
          test401Result.value += '❌ 用户状态未被清除\n'
        }
        test401Result.value += '❌ 401错误处理测试失败\n'
        testStatus.value.test401 = false
        ElMessage.error('401错误处理测试失败')
      }
    }

    // 恢复原始token（如果存在）
    if (originalToken) {
      localStorage.setItem('auth_token', originalToken)
      test401Result.value += '已恢复原始token\n'
    } else {
      // 如果原来没有token，确保清除测试token
      localStorage.removeItem('auth_token')
      test401Result.value += '已清除测试token\n'
    }

  } catch (error: any) {
    test401Result.value += `测试过程中发生错误: ${error.message}\n`
    testStatus.value.test401 = false
    ElMessage.error('测试失败')
  } finally {
    loading.value.test401 = false
  }
}

// 直接测试401处理逻辑
const testDirectUnauthorized = async () => {
  loading.value.testDirect = true
  test401Result.value = '开始直接测试401处理逻辑...\n'

  try {
    // 保存当前状态
    const originalToken = localStorage.getItem('auth_token')
    test401Result.value += `原始token: ${originalToken ? '存在' : '不存在'}\n`

    // 设置测试token
    localStorage.setItem('auth_token', 'test_token_for_direct_test')
    test401Result.value += '设置测试token\n'

    // 导入stores
    const { useAppStore, useUserStore } = await import('@/stores')
    const appStore = useAppStore()
    const userStore = useUserStore()

    // 设置一个测试用户（模拟登录状态）
    userStore.setCurrentUser({
      id: 999,
      nickname: 'TestUser',
      avatar: '',
      email: '<EMAIL>',
      level: 1,
      experience: 0,
      createdAt: new Date().toISOString(),
      status: 'active'
    })
    test401Result.value += '设置测试用户状态\n'

    // 直接调用401处理函数
    test401Result.value += '直接调用handleUnauthorized函数...\n'

    // 模拟handleUnauthorized函数的逻辑
    appStore.showError('认证失败', '登录已过期，请重新登录')
    localStorage.removeItem('auth_token')
    userStore.clearUserData()

    test401Result.value += '执行清理逻辑完成\n'

    // 检查清理结果
    const tokenAfterClear = localStorage.getItem('auth_token')
    const userAfterClear = userStore.isLoggedIn

    test401Result.value += `Token状态: ${tokenAfterClear ? '仍存在' : '已清除'}\n`
    test401Result.value += `用户状态: ${userAfterClear ? '仍登录' : '已清除'}\n`

    if (!tokenAfterClear && !userAfterClear) {
      test401Result.value += '✅ 直接测试成功：Token和用户状态都已清除\n'
      testStatus.value.test401 = true
      ElMessage.success('401处理逻辑测试成功')
    } else {
      test401Result.value += '❌ 直接测试失败：清理不完整\n'
      testStatus.value.test401 = false
      ElMessage.error('401处理逻辑测试失败')
    }

    // 恢复原始状态
    if (originalToken) {
      localStorage.setItem('auth_token', originalToken)
      test401Result.value += '已恢复原始token\n'
    }

  } catch (error: any) {
    test401Result.value += `直接测试过程中发生错误: ${error.message}\n`
    testStatus.value.test401 = false
    ElMessage.error('直接测试失败')
  } finally {
    loading.value.testDirect = false
  }
}
</script>

<style scoped>
.auth-test {
  padding: 20px;
}

.test-card {
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 20px;
}

.test-section h3 {
  margin: 0;
  color: #333;
}

.result-box {
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  min-height: 100px;
  max-height: 200px;
  overflow-y: auto;
}

.result-box pre {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
