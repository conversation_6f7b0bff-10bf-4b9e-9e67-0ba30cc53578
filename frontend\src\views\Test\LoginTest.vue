<template>
  <div class="login-test">
    <h1>登录功能测试</h1>
    
    <div class="test-section">
      <h2>1. 发送验证码测试</h2>
      <div class="form-group">
        <input 
          v-model="phone" 
          type="tel" 
          placeholder="请输入手机号"
          class="input"
        />
        <button 
          @click="testSendCode" 
          :disabled="isLoading"
          class="btn"
        >
          {{ isLoading ? '发送中...' : '发送验证码' }}
        </button>
      </div>
      <div v-if="codeResult" class="result">
        <h3>发送验证码结果:</h3>
        <pre>{{ JSON.stringify(codeResult, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>2. 登录测试</h2>
      <div class="form-group">
        <input 
          v-model="loginForm.phone" 
          type="tel" 
          placeholder="手机号"
          class="input"
        />
        <input 
          v-model="loginForm.code" 
          type="text" 
          placeholder="验证码"
          class="input"
        />
        <button 
          @click="testLogin" 
          :disabled="isLoading"
          class="btn"
        >
          {{ isLoading ? '登录中...' : '登录' }}
        </button>
      </div>
      <div v-if="loginResult" class="result">
        <h3>登录结果:</h3>
        <pre>{{ JSON.stringify(loginResult, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>3. 获取用户信息测试</h2>
      <button 
        @click="testGetUserInfo" 
        :disabled="isLoading"
        class="btn"
      >
        {{ isLoading ? '获取中...' : '获取用户信息' }}
      </button>
      <div v-if="userInfoResult" class="result">
        <h3>用户信息结果:</h3>
        <pre>{{ JSON.stringify(userInfoResult, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>4. 当前登录状态</h2>
      <div class="status">
        <p><strong>是否已登录:</strong> {{ isLoggedIn ? '是' : '否' }}</p>
        <p><strong>当前用户:</strong> {{ currentUser ? currentUser.nickname : '未登录' }}</p>
        <p><strong>Token:</strong> {{ token || '无' }}</p>
      </div>
    </div>

    <div v-if="error" class="error">
      <h3>错误信息:</h3>
      <p>{{ error }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useAuth } from '@/composables/useAuth'
import { useUserStore } from '@/stores'
import axios from 'axios'

const { sendVerificationCode, login, isLoading, error } = useAuth()
const userStore = useUserStore()

const phone = ref('13800138000')
const loginForm = ref({
  phone: '13800138000',
  code: '123456'
})

const codeResult = ref(null)
const loginResult = ref(null)
const userInfoResult = ref(null)

const isLoggedIn = computed(() => userStore.isLoggedIn)
const currentUser = computed(() => userStore.currentUser)
const token = computed(() => localStorage.getItem('auth_token'))

const API_BASE_URL = 'http://localhost:8081'

// 测试发送验证码
const testSendCode = async () => {
  try {
    codeResult.value = null
    await sendVerificationCode(phone.value)
    codeResult.value = { success: true, message: '验证码发送成功' }
  } catch (err: any) {
    codeResult.value = { success: false, error: err.message }
  }
}

// 测试登录
const testLogin = async () => {
  try {
    loginResult.value = null
    const success = await login(loginForm.value)
    loginResult.value = { 
      success, 
      user: userStore.currentUser,
      token: localStorage.getItem('auth_token')
    }
  } catch (err: any) {
    loginResult.value = { success: false, error: err.message }
  }
}

// 测试获取用户信息
const testGetUserInfo = async () => {
  try {
    userInfoResult.value = null
    const token = localStorage.getItem('auth_token')
    
    if (!token) {
      userInfoResult.value = { success: false, error: '未登录，无token' }
      return
    }

    const response = await axios.get(`${API_BASE_URL}/user/me`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    
    userInfoResult.value = { 
      success: true, 
      data: response.data,
      status: response.status
    }
  } catch (err: any) {
    userInfoResult.value = { 
      success: false, 
      error: err.message,
      response: err.response?.data
    }
  }
}
</script>

<style lang="scss" scoped>
.login-test {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.form-group {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.input {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  min-width: 150px;
}

.btn {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;

  &:disabled {
    background: #ccc;
    cursor: not-allowed;
  }

  &:hover:not(:disabled) {
    background: #0056b3;
  }
}

.result {
  margin-top: 15px;
  padding: 15px;
  background: #e9ecef;
  border-radius: 4px;
  
  pre {
    margin: 0;
    white-space: pre-wrap;
    word-break: break-all;
  }
}

.status {
  p {
    margin: 5px 0;
  }
}

.error {
  margin-top: 20px;
  padding: 15px;
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
}

h1, h2, h3 {
  color: #333;
}

h1 {
  text-align: center;
  margin-bottom: 30px;
}

h2 {
  margin-top: 0;
  color: #007bff;
}
</style>
