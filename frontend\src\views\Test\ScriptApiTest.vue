<template>
  <div class="script-api-test">
    <div class="container">
      <h1>剧本API测试</h1>
      
      <div class="test-section">
        <h2>测试剧本列表API</h2>
        <button @click="testScriptList" :disabled="loading">
          {{ loading ? '测试中...' : '测试剧本列表' }}
        </button>
        
        <div v-if="result" class="result">
          <h3>结果:</h3>
          <pre>{{ JSON.stringify(result, null, 2) }}</pre>
        </div>
        
        <div v-if="error" class="error">
          <h3>错误:</h3>
          <pre>{{ error }}</pre>
        </div>
      </div>
      
      <div class="test-section">
        <h2>网络配置信息</h2>
        <p>基础URL: {{ baseURL }}</p>
        <p>完整URL: {{ fullURL }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { scriptApi } from '@/api/script'
import { http } from '@/api/http'

const loading = ref(false)
const result = ref(null)
const error = ref('')
const baseURL = ref('/api')
const fullURL = ref(baseURL.value + '/scripts/list')

const testScriptList = async () => {
  loading.value = true
  result.value = null
  error.value = ''
  
  try {
    console.log('开始测试API调用...')
    console.log('请求URL:', fullURL.value)
    
    const response = await scriptApi.getScriptList({
      page: 1,
      size: 5
    })
    
    console.log('API响应:', response)
    result.value = response
  } catch (err: any) {
    console.error('API调用失败:', err)
    error.value = err.message || err.toString()
    
    // 额外的错误信息
    if (err.response) {
      error.value += '\n响应状态: ' + err.response.status
      error.value += '\n响应数据: ' + JSON.stringify(err.response.data)
    } else if (err.request) {
      error.value += '\n请求发送但无响应'
    }
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.script-api-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin: 20px 0;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.result, .error {
  margin-top: 10px;
  padding: 10px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
}

.result {
  background: #f0f8ff;
  border: 1px solid #4a90e2;
}

.error {
  background: #fff0f0;
  border: 1px solid #e74c3c;
  color: #c0392b;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}

button {
  padding: 10px 20px;
  background: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  &:hover:not(:disabled) {
    background: #357abd;
  }
}
</style>