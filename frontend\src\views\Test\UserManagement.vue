<template>
  <div class="test-container">
    <div class="container">
      <h1>用户管理模块测试</h1>
      
      <!-- 用户信息测试 -->
      <div class="test-section">
        <h2>1. 获取用户信息</h2>
        <div class="test-actions">
          <el-button type="primary" @click="testGetUserInfo" :loading="loading.userInfo">
            获取用户信息
          </el-button>
        </div>
        <div v-if="userInfo" class="test-result">
          <h4>用户信息:</h4>
          <pre>{{ JSON.stringify(userInfo, null, 2) }}</pre>
        </div>
      </div>

      <!-- 更新用户信息测试 -->
      <div class="test-section">
        <h2>2. 更新用户信息</h2>
        <el-form :model="updateForm" inline>
          <el-form-item label="昵称">
            <el-input v-model="updateForm.nickName" placeholder="输入新昵称" />
          </el-form-item>
          <el-form-item label="城市">
            <el-input v-model="updateForm.city" placeholder="输入城市" />
          </el-form-item>
          <el-form-item label="签名">
            <el-input v-model="updateForm.signature" placeholder="输入个性签名" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="testUpdateProfile" :loading="loading.update">
              更新信息
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 头像上传测试 -->
      <div class="test-section">
        <h2>3. 头像上传</h2>
        <div class="test-actions">
          <el-upload
            :action="uploadUrl"
            :headers="uploadHeaders"
            :on-success="handleAvatarSuccess"
            :on-error="handleAvatarError"
            :before-upload="beforeAvatarUpload"
            :show-file-list="false"
            accept="image/*"
          >
            <el-button type="primary" :loading="loading.avatar">上传头像</el-button>
          </el-upload>
        </div>
        <div v-if="avatarResult" class="test-result">
          <h4>上传结果:</h4>
          <pre>{{ JSON.stringify(avatarResult, null, 2) }}</pre>
        </div>
      </div>

      <!-- 密码修改测试 -->
      <div class="test-section">
        <h2>4. 修改密码</h2>
        <el-form :model="passwordForm" inline>
          <el-form-item label="原密码">
            <el-input v-model="passwordForm.oldPassword" type="password" placeholder="输入原密码" />
          </el-form-item>
          <el-form-item label="新密码">
            <el-input v-model="passwordForm.newPassword" type="password" placeholder="输入新密码" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="testChangePassword" :loading="loading.password">
              修改密码
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 收藏管理测试 -->
      <div class="test-section">
        <h2>5. 收藏管理</h2>
        
        <!-- 添加收藏 -->
        <div class="sub-section">
          <h3>添加收藏</h3>
          <el-form :model="favoriteForm" inline>
            <el-form-item label="目标ID">
              <el-input-number v-model="favoriteForm.targetId" :min="1" />
            </el-form-item>
            <el-form-item label="类型">
              <el-select v-model="favoriteForm.targetType">
                <el-option label="剧本" value="script" />
                <el-option label="房间" value="room" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="testAddFavorite" :loading="loading.addFavorite">
                添加收藏
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 获取收藏列表 -->
        <div class="sub-section">
          <h3>获取收藏列表</h3>
          <div class="test-actions">
            <el-button type="primary" @click="testGetFavorites" :loading="loading.favorites">
              获取收藏列表
            </el-button>
          </div>
          <div v-if="favorites.length > 0" class="test-result">
            <h4>收藏列表:</h4>
            <div v-for="item in favorites" :key="item.id" class="favorite-item">
              <span>ID: {{ item.id }}, 目标: {{ item.targetType }}#{{ item.targetId }}</span>
              <el-button 
                size="small" 
                type="danger" 
                @click="testRemoveFavorite(item.id)"
                :loading="loading.removeFavorite"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 测试结果显示 -->
      <div v-if="testResults.length > 0" class="test-results">
        <h2>测试结果</h2>
        <div v-for="(result, index) in testResults" :key="index" class="result-item">
          <div class="result-header">
            <span class="result-time">{{ result.time }}</span>
            <span class="result-action">{{ result.action }}</span>
            <span :class="['result-status', result.success ? 'success' : 'error']">
              {{ result.success ? '成功' : '失败' }}
            </span>
          </div>
          <div class="result-content">
            <pre>{{ result.data }}</pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { userApi } from '@/api/user'

const loading = reactive({
  userInfo: false,
  update: false,
  avatar: false,
  password: false,
  addFavorite: false,
  favorites: false,
  removeFavorite: false
})

const userInfo = ref<any>(null)
const avatarResult = ref<any>(null)
const favorites = ref<any[]>([])
const testResults = ref<any[]>([])

const updateForm = reactive({
  nickName: '',
  city: '',
  signature: ''
})

const passwordForm = reactive({
  oldPassword: '',
  newPassword: ''
})

const favoriteForm = reactive({
  targetId: 1,
  targetType: 'script'
})

const uploadUrl = '/api/user/avatar'
const uploadHeaders = {
  'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
}

const addTestResult = (action: string, success: boolean, data: any) => {
  testResults.value.unshift({
    time: new Date().toLocaleTimeString(),
    action,
    success,
    data: typeof data === 'string' ? data : JSON.stringify(data, null, 2)
  })
}

const testGetUserInfo = async () => {
  try {
    loading.userInfo = true
    const response = await userApi.getCurrentUser()
    userInfo.value = response.data
    addTestResult('获取用户信息', response.code === 200, response)
    if (response.code === 200) {
      ElMessage.success('获取用户信息成功')
    }
  } catch (error) {
    addTestResult('获取用户信息', false, error)
    ElMessage.error('获取用户信息失败')
  } finally {
    loading.userInfo = false
  }
}

const testUpdateProfile = async () => {
  try {
    loading.update = true
    const response = await userApi.updateProfile(updateForm)
    addTestResult('更新用户信息', response.code === 200, response)
    if (response.code === 200) {
      ElMessage.success('更新成功')
      testGetUserInfo() // 重新获取用户信息
    }
  } catch (error) {
    addTestResult('更新用户信息', false, error)
    ElMessage.error('更新失败')
  } finally {
    loading.update = false
  }
}

const beforeAvatarUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  
  loading.avatar = true
  return true
}

const handleAvatarSuccess = (response: any) => {
  loading.avatar = false
  avatarResult.value = response
  addTestResult('上传头像', response.code === 200, response)
  if (response.code === 200) {
    ElMessage.success('头像上传成功')
  }
}

const handleAvatarError = () => {
  loading.avatar = false
  addTestResult('上传头像', false, '上传失败')
  ElMessage.error('上传失败')
}

const testChangePassword = async () => {
  try {
    loading.password = true
    const response = await userApi.changePassword(passwordForm)
    addTestResult('修改密码', response.code === 200, response)
    if (response.code === 200) {
      ElMessage.success('密码修改成功')
      passwordForm.oldPassword = ''
      passwordForm.newPassword = ''
    }
  } catch (error) {
    addTestResult('修改密码', false, error)
    ElMessage.error('密码修改失败')
  } finally {
    loading.password = false
  }
}

const testAddFavorite = async () => {
  try {
    loading.addFavorite = true
    const response = await userApi.addFavorite(favoriteForm)
    addTestResult('添加收藏', response.code === 200, response)
    if (response.code === 200) {
      ElMessage.success('添加收藏成功')
      testGetFavorites() // 重新获取收藏列表
    }
  } catch (error) {
    addTestResult('添加收藏', false, error)
    ElMessage.error('添加收藏失败')
  } finally {
    loading.addFavorite = false
  }
}

const testGetFavorites = async () => {
  try {
    loading.favorites = true
    const response = await userApi.getMyFavorites({
      page: 1,
      size: 10
    })
    favorites.value = response.data?.records || []
    addTestResult('获取收藏列表', response.code === 200, response)
    if (response.code === 200) {
      ElMessage.success('获取收藏列表成功')
    }
  } catch (error) {
    addTestResult('获取收藏列表', false, error)
    ElMessage.error('获取收藏列表失败')
  } finally {
    loading.favorites = false
  }
}

const testRemoveFavorite = async (id: number) => {
  try {
    loading.removeFavorite = true
    const response = await userApi.removeFavorite(id)
    addTestResult('取消收藏', response.code === 200, response)
    if (response.code === 200) {
      ElMessage.success('取消收藏成功')
      testGetFavorites() // 重新获取收藏列表
    }
  } catch (error) {
    addTestResult('取消收藏', false, error)
    ElMessage.error('取消收藏失败')
  } finally {
    loading.removeFavorite = false
  }
}
</script>

<style scoped lang="scss">
.test-container {
  min-height: 100vh;
  padding: 20px 0;
  background: linear-gradient(180deg, #1A1A2E 0%, #16213E 50%, #0F0F1E 100%);
  color: #fff;
}

.test-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;

  h2 {
    color: #00F5D4;
    margin-bottom: 20px;
  }
}

.sub-section {
  margin-bottom: 20px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;

  h3 {
    color: #fff;
    margin-bottom: 16px;
    font-size: 16px;
  }
}

.test-actions {
  margin-bottom: 16px;
}

.test-result {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;

  h4 {
    color: #00F5D4;
    margin-bottom: 12px;
  }

  pre {
    color: #B0B0B0;
    font-size: 12px;
    line-height: 1.4;
    white-space: pre-wrap;
    word-break: break-all;
  }
}

.favorite-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  &:last-child {
    border-bottom: none;
  }
}

.test-results {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 24px;

  h2 {
    color: #00F5D4;
    margin-bottom: 20px;
  }
}

.result-item {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  margin-bottom: 12px;
  overflow: hidden;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .result-time {
    color: #888;
    font-size: 12px;
  }

  .result-action {
    color: #fff;
    font-weight: 500;
  }

  .result-status {
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;

    &.success {
      background: #67c23a;
      color: #fff;
    }

    &.error {
      background: #f56c6c;
      color: #fff;
    }
  }
}

.result-content {
  padding: 12px 16px;

  pre {
    color: #B0B0B0;
    font-size: 12px;
    line-height: 1.4;
    white-space: pre-wrap;
    word-break: break-all;
    margin: 0;
  }
}
</style>
