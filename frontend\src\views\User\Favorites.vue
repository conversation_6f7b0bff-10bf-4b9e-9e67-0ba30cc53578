<template>
  <div class="favorites">
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">我的收藏</h1>
        <p class="page-subtitle">收藏的剧本和精彩内容</p>
      </div>

      <div class="favorites-tabs">
        <button 
          v-for="tab in tabs" 
          :key="tab.key"
          class="tab-btn"
          :class="{ active: activeTab === tab.key }"
          @click="activeTab = tab.key"
        >
          {{ tab.label }}
          <span class="tab-count">({{ getTabCount(tab.key) }})</span>
        </button>
      </div>

      <div class="favorites-content">
        <!-- 收藏的剧本 -->
        <div v-if="activeTab === 'scripts'" class="favorites-list">
          <div v-if="favoriteScripts.length === 0" class="empty-state">
            <div class="empty-icon">📚</div>
            <div class="empty-text">你还没有收藏任何剧本</div>
            <router-link to="/scripts" class="browse-scripts-btn">
              浏览剧本
            </router-link>
          </div>
          
          <div v-else class="scripts-grid">
            <div 
              v-for="script in favoriteScripts" 
              :key="script.id"
              class="script-card"
              @click="viewScript(script.id)"
            >
              <div class="script-cover">
                <img :src="script.coverImage" :alt="script.title" />
                <button class="unfavorite-btn" @click.stop="unfavoriteScript(script.id)">
                  ❤️
                </button>
              </div>
              
              <div class="script-info">
                <h3 class="script-title">{{ script.title }}</h3>
                <div class="script-meta">
                  <span class="genre">{{ script.genre }}</span>
                  <span class="players">{{ script.playerCount }}人</span>
                  <span class="duration">{{ script.duration }}分钟</span>
                </div>
                
                <div class="script-stats">
                  <div class="rating">
                    <span class="stars">⭐</span>
                    <span class="rating-value">{{ script.rating }}</span>
                  </div>
                  <div class="price">¥{{ script.price }}</div>
                </div>
                
                <div class="favorite-time">
                  收藏于 {{ formatDate(script.favoriteTime) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 收藏的动态 -->
        <div v-if="activeTab === 'posts'" class="favorites-list">
          <div v-if="favoritePosts.length === 0" class="empty-state">
            <div class="empty-icon">💬</div>
            <div class="empty-text">你还没有收藏任何动态</div>
            <router-link to="/feed" class="browse-posts-btn">
              浏览动态
            </router-link>
          </div>
          
          <div v-else class="posts-list">
            <div 
              v-for="post in favoritePosts" 
              :key="post.id"
              class="post-card"
            >
              <div class="post-header">
                <img :src="post.author.avatar" :alt="post.author.nickname" class="author-avatar" />
                <div class="post-author-info">
                  <div class="author-name">{{ post.author.nickname }}</div>
                  <div class="post-time">{{ formatDate(post.createdAt) }}</div>
                </div>
                <button class="unfavorite-btn" @click="unfavoritePost(post.id)">
                  🔖
                </button>
              </div>
              
              <div class="post-content">
                <h4 v-if="post.title" class="post-title">{{ post.title }}</h4>
                <p class="post-text">{{ post.content }}</p>
              </div>
              
              <div class="post-stats">
                <span class="stat-item">
                  <span class="stat-icon">👍</span>
                  <span class="stat-count">{{ post.likeCount }}</span>
                </span>
                <span class="stat-item">
                  <span class="stat-icon">💬</span>
                  <span class="stat-count">{{ post.commentCount }}</span>
                </span>
                <div class="favorite-time">
                  收藏于 {{ formatDate(post.favoriteTime) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 收藏的用户 -->
        <div v-if="activeTab === 'users'" class="favorites-list">
          <div v-if="favoriteUsers.length === 0" class="empty-state">
            <div class="empty-icon">👥</div>
            <div class="empty-text">你还没有关注任何用户</div>
          </div>
          
          <div v-else class="users-grid">
            <div 
              v-for="user in favoriteUsers" 
              :key="user.id"
              class="user-card"
            >
              <img :src="user.avatar" :alt="user.nickname" class="user-avatar" />
              <div class="user-info">
                <h4 class="user-name">{{ user.nickname }}</h4>
                <div class="user-level">Lv.{{ user.level }}</div>
                <p class="user-bio">{{ user.bio || '这个人很神秘，什么都没有留下...' }}</p>
                
                <div class="user-stats">
                  <span class="user-stat">
                    <span class="stat-label">游戏</span>
                    <span class="stat-value">{{ user.gameCount }}</span>
                  </span>
                  <span class="user-stat">
                    <span class="stat-label">粉丝</span>
                    <span class="stat-value">{{ user.followerCount }}</span>
                  </span>
                </div>
                
                <div class="user-actions">
                  <button class="view-profile-btn" @click="viewProfile(user.id)">
                    查看资料
                  </button>
                  <button class="unfollow-btn" @click="unfollowUser(user.id)">
                    取消关注
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const activeTab = ref('scripts')

const tabs = [
  { key: 'scripts', label: '剧本' },
  { key: 'posts', label: '动态' },
  { key: 'users', label: '用户' }
]

// 模拟数据
const favoriteScripts = ref([
  {
    id: 1,
    title: "迷雾庄园",
    coverImage: "https://picsum.photos/200/280?random=1",
    genre: "推理",
    playerCount: 6,
    duration: 240,
    rating: 4.8,
    price: 68,
    favoriteTime: "2024-07-25T10:30:00"
  }
])

const favoritePosts = ref([
  {
    id: 1,
    title: "《迷雾庄园》通关心得",
    content: "这个剧本的逻辑链非常严密，推理过程很有挑战性...",
    author: {
      nickname: "推理达人",
      avatar: "https://picsum.photos/40/40?random=21"
    },
    likeCount: 156,
    commentCount: 23,
    createdAt: "2024-07-24T15:20:00",
    favoriteTime: "2024-07-25T09:15:00"
  }
])

const favoriteUsers = ref([
  {
    id: 1,
    nickname: "神秘主持",
    avatar: "https://picsum.photos/80/80?random=31",
    level: 15,
    bio: "专业剧本杀主持，擅长营造氛围",
    gameCount: 234,
    followerCount: 567
  }
])

const getTabCount = (tabKey: string): number => {
  switch (tabKey) {
    case 'scripts': return favoriteScripts.value.length
    case 'posts': return favoritePosts.value.length
    case 'users': return favoriteUsers.value.length
    default: return 0
  }
}

const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit'
  })
}

const viewScript = (id: number) => {
  router.push(`/scripts/${id}`)
}

const viewProfile = (id: number) => {
  router.push(`/user/${id}`)
}

const unfavoriteScript = (id: number) => {
  console.log('取消收藏剧本:', id)
}

const unfavoritePost = (id: number) => {
  console.log('取消收藏动态:', id)
}

const unfollowUser = (id: number) => {
  console.log('取消关注用户:', id)
}
</script>

<style lang="scss" scoped>
.favorites {
  min-height: 100vh;
  padding: 40px 0;
  background: linear-gradient(180deg, #1A1A2E 0%, #16213E 50%, #0F0F1E 100%);
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 2.5rem;
  color: #fff;
  font-weight: 700;
  margin-bottom: 12px;
}

.page-subtitle {
  color: #B0B0B0;
  font-size: 1.1rem;
}

.favorites-tabs {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 40px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
  
  &.active {
    color: #00F5D4;
    border-bottom-color: #00F5D4;
  }
}

.tab-count {
  padding: 2px 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  font-size: 0.75rem;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 1.1rem;
  color: #888;
  margin-bottom: 24px;
}

.browse-scripts-btn, .browse-posts-btn {
  display: inline-block;
  padding: 12px 24px;
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 245, 212, 0.4);
  }
}

.scripts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 24px;
}

.script-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.1);
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    border-color: rgba(0, 245, 212, 0.3);
  }
}

.script-cover {
  position: relative;
  aspect-ratio: 3/4;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.unfavorite-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.1);
  }
}

.script-info {
  padding: 16px;
}

.script-title {
  font-size: 1.1rem;
  color: #fff;
  font-weight: 600;
  margin-bottom: 8px;
}

.script-meta {
  display: flex;
  gap: 6px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.genre, .players, .duration {
  padding: 2px 6px;
  background: rgba(0, 245, 212, 0.1);
  color: #00F5D4;
  border-radius: 6px;
  font-size: 0.7rem;
}

.script-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.rating {
  display: flex;
  align-items: center;
  gap: 4px;
}

.rating-value {
  color: #fff;
  font-weight: 600;
  font-size: 0.9rem;
}

.price {
  color: #FF00E4;
  font-weight: 600;
}

.favorite-time {
  font-size: 0.75rem;
  color: #666;
}

.posts-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.post-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.1);
  border-radius: 12px;
  padding: 20px;
}

.post-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.post-author-info {
  flex: 1;
}

.author-name {
  font-size: 0.9rem;
  color: #00F5D4;
  font-weight: 500;
}

.post-time {
  font-size: 0.75rem;
  color: #888;
}

.post-title {
  font-size: 1rem;
  color: #fff;
  font-weight: 600;
  margin-bottom: 8px;
}

.post-text {
  color: #B0B0B0;
  line-height: 1.5;
  margin-bottom: 12px;
}

.post-stats {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.8rem;
  color: #888;
}

.users-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
}

.user-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.1);
  border-radius: 16px;
  padding: 24px;
  text-align: center;
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 16px;
}

.user-name {
  font-size: 1.1rem;
  color: #fff;
  font-weight: 600;
  margin-bottom: 4px;
}

.user-level {
  font-size: 0.8rem;
  color: #00F5D4;
  margin-bottom: 12px;
}

.user-bio {
  font-size: 0.85rem;
  color: #B0B0B0;
  line-height: 1.4;
  margin-bottom: 16px;
}

.user-stats {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-bottom: 16px;
}

.user-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 0.75rem;
  color: #888;
}

.stat-value {
  font-size: 1rem;
  color: #00F5D4;
  font-weight: 600;
}

.user-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.view-profile-btn, .unfollow-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-profile-btn {
  background: rgba(0, 245, 212, 0.1);
  color: #00F5D4;
  border: 1px solid rgba(0, 245, 212, 0.3);
  
  &:hover {
    background: rgba(0, 245, 212, 0.2);
  }
}

.unfollow-btn {
  background: rgba(244, 67, 54, 0.1);
  color: #F44336;
  border: 1px solid rgba(244, 67, 54, 0.3);
  
  &:hover {
    background: rgba(244, 67, 54, 0.2);
  }
}

@media (max-width: 768px) {
  .favorites {
    padding: 20px 0;
  }
  
  .scripts-grid, .users-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
  }
  
  .favorites-tabs {
    justify-content: flex-start;
    overflow-x: auto;
  }
}
</style>
