<template>
  <div class="my-lobbies">
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">我的车队</h1>
        <p class="page-subtitle">管理你创建和参与的车队</p>
      </div>

      <div class="lobbies-tabs">
        <button 
          v-for="tab in tabs" 
          :key="tab.key"
          class="tab-btn"
          :class="{ active: activeTab === tab.key }"
          @click="activeTab = tab.key"
        >
          {{ tab.label }}
          <span class="tab-count">({{ getTabCount(tab.key) }})</span>
        </button>
      </div>

      <div class="lobbies-content">
        <!-- 我创建的车队 -->
        <div v-if="activeTab === 'created'" class="lobbies-list">
          <div v-if="createdLobbies.length === 0" class="empty-state">
            <div class="empty-icon">🚗</div>
            <div class="empty-text">你还没有创建过车队</div>
            <router-link to="/lobby/create" class="create-lobby-btn">
              创建第一个车队
            </router-link>
          </div>
          
          <div v-else class="lobby-cards">
            <div 
              v-for="lobby in createdLobbies" 
              :key="lobby.id"
              class="lobby-card"
            >
              <div class="lobby-header">
                <img :src="lobby.script.coverImage" :alt="lobby.script.title" class="script-cover" />
                <div class="lobby-info">
                  <h3 class="script-title">{{ lobby.script.title }}</h3>
                  <div class="lobby-meta">
                    <span class="status-badge" :class="`status-${lobby.status}`">
                      {{ getStatusText(lobby.status) }}
                    </span>
                    <span class="player-count">{{ lobby.currentPlayers }}/{{ lobby.maxPlayers }}人</span>
                  </div>
                  <div class="lobby-time">{{ formatDateTime(lobby.startTime) }}</div>
                </div>
              </div>
              
              <div class="lobby-actions">
                <button class="action-btn view-btn" @click="viewLobby(lobby.id)">
                  查看详情
                </button>
                <button 
                  v-if="lobby.status === 'waiting'"
                  class="action-btn edit-btn"
                  @click="editLobby(lobby.id)"
                >
                  编辑
                </button>
                <button 
                  v-if="lobby.status === 'waiting'"
                  class="action-btn cancel-btn"
                  @click="cancelLobby(lobby.id)"
                >
                  取消
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 我参与的车队 -->
        <div v-if="activeTab === 'joined'" class="lobbies-list">
          <div v-if="joinedLobbies.length === 0" class="empty-state">
            <div class="empty-icon">👥</div>
            <div class="empty-text">你还没有参与任何车队</div>
            <router-link to="/lobby" class="browse-lobbies-btn">
              浏览车队
            </router-link>
          </div>
          
          <div v-else class="lobby-cards">
            <div 
              v-for="lobby in joinedLobbies" 
              :key="lobby.id"
              class="lobby-card"
            >
              <div class="lobby-header">
                <img :src="lobby.script.coverImage" :alt="lobby.script.title" class="script-cover" />
                <div class="lobby-info">
                  <h3 class="script-title">{{ lobby.script.title }}</h3>
                  <div class="lobby-meta">
                    <span class="status-badge" :class="`status-${lobby.status}`">
                      {{ getStatusText(lobby.status) }}
                    </span>
                    <span class="player-count">{{ lobby.currentPlayers }}/{{ lobby.maxPlayers }}人</span>
                  </div>
                  <div class="lobby-time">{{ formatDateTime(lobby.startTime) }}</div>
                  <div class="host-info">
                    车主：{{ lobby.host.nickname }}
                  </div>
                </div>
              </div>
              
              <div class="lobby-actions">
                <button class="action-btn view-btn" @click="viewLobby(lobby.id)">
                  查看详情
                </button>
                <button 
                  v-if="lobby.status === 'waiting'"
                  class="action-btn leave-btn"
                  @click="leaveLobby(lobby.id)"
                >
                  退出车队
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 历史记录 -->
        <div v-if="activeTab === 'history'" class="lobbies-list">
          <div v-if="historyLobbies.length === 0" class="empty-state">
            <div class="empty-icon">📚</div>
            <div class="empty-text">暂无历史记录</div>
          </div>
          
          <div v-else class="lobby-cards">
            <div 
              v-for="lobby in historyLobbies" 
              :key="lobby.id"
              class="lobby-card history-card"
            >
              <div class="lobby-header">
                <img :src="lobby.script.coverImage" :alt="lobby.script.title" class="script-cover" />
                <div class="lobby-info">
                  <h3 class="script-title">{{ lobby.script.title }}</h3>
                  <div class="lobby-meta">
                    <span class="status-badge" :class="`status-${lobby.status}`">
                      {{ getStatusText(lobby.status) }}
                    </span>
                    <span class="completion-time">{{ formatDateTime(lobby.endTime) }}</span>
                  </div>
                </div>
              </div>
              
              <div class="lobby-actions">
                <button class="action-btn review-btn">
                  写评价
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const activeTab = ref('created')

const tabs = [
  { key: 'created', label: '我创建的' },
  { key: 'joined', label: '我参与的' },
  { key: 'history', label: '历史记录' }
]

// 模拟数据
const createdLobbies = ref([
  {
    id: 1,
    script: {
      title: "迷雾庄园",
      coverImage: "https://picsum.photos/80/100?random=1"
    },
    status: 'waiting',
    currentPlayers: 4,
    maxPlayers: 6,
    startTime: '2024-07-30T19:00:00',
    host: { nickname: '推理大师' }
  }
])

const joinedLobbies = ref([
  {
    id: 2,
    script: {
      title: "血色玫瑰",
      coverImage: "https://picsum.photos/80/100?random=2"
    },
    status: 'waiting',
    currentPlayers: 5,
    maxPlayers: 7,
    startTime: '2024-07-31T20:00:00',
    host: { nickname: '神秘主持' }
  }
])

const historyLobbies = ref([
  {
    id: 3,
    script: {
      title: "时光倒流",
      coverImage: "https://picsum.photos/80/100?random=3"
    },
    status: 'completed',
    endTime: '2024-07-25T22:30:00'
  }
])

const getTabCount = (tabKey: string): number => {
  switch (tabKey) {
    case 'created': return createdLobbies.value.length
    case 'joined': return joinedLobbies.value.length
    case 'history': return historyLobbies.value.length
    default: return 0
  }
}

const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    waiting: '等待中',
    full: '已满员',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || '未知'
}

const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const viewLobby = (id: number) => {
  router.push(`/lobby/${id}`)
}

const editLobby = (id: number) => {
  console.log('编辑车队:', id)
}

const cancelLobby = (id: number) => {
  console.log('取消车队:', id)
}

const leaveLobby = (id: number) => {
  console.log('退出车队:', id)
}
</script>

<style lang="scss" scoped>
.my-lobbies {
  min-height: 100vh;
  padding: 40px 0;
  background: linear-gradient(180deg, #1A1A2E 0%, #16213E 50%, #0F0F1E 100%);
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 2.5rem;
  color: #fff;
  font-weight: 700;
  margin-bottom: 12px;
}

.page-subtitle {
  color: #B0B0B0;
  font-size: 1.1rem;
}

.lobbies-tabs {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 40px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
  
  &.active {
    color: #00F5D4;
    border-bottom-color: #00F5D4;
  }
  
  &:hover:not(.active) {
    color: #B0B0B0;
  }
}

.tab-count {
  padding: 2px 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  font-size: 0.75rem;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 1.1rem;
  color: #888;
  margin-bottom: 24px;
}

.create-lobby-btn, .browse-lobbies-btn {
  display: inline-block;
  padding: 12px 24px;
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 245, 212, 0.4);
  }
}

.lobby-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 24px;
}

.lobby-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 245, 212, 0.1);
  border-radius: 16px;
  padding: 20px;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: rgba(0, 245, 212, 0.3);
    transform: translateY(-2px);
  }
  
  &.history-card {
    opacity: 0.8;
  }
}

.lobby-header {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.script-cover {
  width: 60px;
  height: 80px;
  border-radius: 8px;
  object-fit: cover;
  flex-shrink: 0;
}

.lobby-info {
  flex: 1;
}

.script-title {
  font-size: 1.1rem;
  color: #fff;
  font-weight: 600;
  margin-bottom: 8px;
}

.lobby-meta {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.status-badge {
  padding: 2px 8px;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 500;
  
  &.status-waiting {
    background: rgba(255, 193, 7, 0.1);
    color: #FFC107;
  }
  
  &.status-full {
    background: rgba(244, 67, 54, 0.1);
    color: #F44336;
  }
  
  &.status-in_progress {
    background: rgba(33, 150, 243, 0.1);
    color: #2196F3;
  }
  
  &.status-completed {
    background: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
  }
  
  &.status-cancelled {
    background: rgba(158, 158, 158, 0.1);
    color: #9E9E9E;
  }
}

.player-count, .completion-time {
  font-size: 0.8rem;
  color: #B0B0B0;
}

.lobby-time, .host-info {
  font-size: 0.85rem;
  color: #888;
  margin-bottom: 4px;
}

.lobby-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-btn {
  background: rgba(0, 245, 212, 0.1);
  color: #00F5D4;
  border: 1px solid rgba(0, 245, 212, 0.3);
  
  &:hover {
    background: rgba(0, 245, 212, 0.2);
  }
}

.edit-btn {
  background: rgba(255, 193, 7, 0.1);
  color: #FFC107;
  border: 1px solid rgba(255, 193, 7, 0.3);
  
  &:hover {
    background: rgba(255, 193, 7, 0.2);
  }
}

.cancel-btn, .leave-btn {
  background: rgba(244, 67, 54, 0.1);
  color: #F44336;
  border: 1px solid rgba(244, 67, 54, 0.3);
  
  &:hover {
    background: rgba(244, 67, 54, 0.2);
  }
}

.review-btn {
  background: rgba(156, 39, 176, 0.1);
  color: #9C27B0;
  border: 1px solid rgba(156, 39, 176, 0.3);
  
  &:hover {
    background: rgba(156, 39, 176, 0.2);
  }
}

@media (max-width: 768px) {
  .my-lobbies {
    padding: 20px 0;
  }
  
  .lobby-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .lobbies-tabs {
    flex-wrap: wrap;
    justify-content: flex-start;
  }
  
  .tab-btn {
    padding: 8px 16px;
    font-size: 0.9rem;
  }
}
</style>
