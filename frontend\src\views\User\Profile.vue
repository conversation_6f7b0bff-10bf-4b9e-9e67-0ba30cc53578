<template>
  <div class="user-profile">
    <div class="container">
      <div class="profile-header">
        <div class="profile-info">
          <div class="avatar-container">
            <img
              :src="userInfo?.icon || 'https://picsum.photos/120/120?random=99'"
              alt="用户头像"
              class="profile-avatar"
            />
            <button class="avatar-edit-btn" @click="showAvatarUpload = true">
              <i class="el-icon-camera"></i>
            </button>
          </div>
          <div class="profile-details">
            <h1 class="profile-name">{{ userInfo?.nickName || '未设置昵称' }}</h1>
            <div class="profile-meta">
              <span class="level">Lv.{{ userInfo?.level || 1 }}</span>
              <span class="join-date">{{ formatJoinDate(userInfo?.createTime) }}</span>
            </div>
            <div class="profile-email" v-if="userInfo?.email">
              <i class="email-icon">📧</i>
              <span>{{ userInfo.email }}</span>
            </div>
            <p class="profile-bio">{{ userInfo?.signature || '这个人很懒，什么都没有留下...' }}</p>
            <div class="profile-actions">
              <button class="edit-btn" @click="showProfileEdit = true">编辑资料</button>
              <button class="edit-btn" @click="showPasswordChange = true">修改密码</button>
            </div>
          </div>
        </div>

        <div class="profile-stats">
          <div class="stat-item">
            <div class="stat-value">{{ userInfo?.experience || 0 }}</div>
            <div class="stat-label">经验值</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">0</div>
            <div class="stat-label">关注</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">0</div>
            <div class="stat-label">粉丝</div>
          </div>
        </div>
      </div>
      
      <div class="profile-content">
        <div class="content-tabs">
          <button
            v-for="tab in tabs"
            :key="tab.key"
            class="tab-btn"
            :class="{ active: activeTab === tab.key }"
            @click="activeTab = tab.key"
          >
            {{ tab.label }}
          </button>
        </div>

        <div class="tab-content">
          <div v-if="activeTab === 'favorites'" class="favorites-content">
            <FavoriteList />
          </div>

          <div v-if="activeTab === 'games'" class="games-content">
            <h3>游戏记录</h3>
            <p>暂无记录</p>
          </div>

          <div v-if="activeTab === 'achievements'" class="achievements-content">
            <h3>成就徽章</h3>
            <p>暂无成就</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 头像上传对话框 -->
    <AvatarUpload
      v-model:visible="showAvatarUpload"
      @success="handleAvatarSuccess"
    />

    <!-- 个人资料编辑对话框 -->
    <ProfileEdit
      v-model:visible="showProfileEdit"
      :user-info="userInfo"
      @success="handleProfileSuccess"
    />

    <!-- 密码修改对话框 -->
    <PasswordChange
      v-model:visible="showPasswordChange"
      @success="handlePasswordSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { userApi } from '@/api/user'
import { useAuth } from '@/composables/useAuth'
import AvatarUpload from './components/AvatarUpload.vue'
import ProfileEdit from './components/ProfileEdit.vue'
import PasswordChange from './components/PasswordChange.vue'
import FavoriteList from './components/FavoriteList.vue'

const router = useRouter()
const { isLoggedIn, checkAuth } = useAuth()

const activeTab = ref('favorites')
const userInfo = ref<any>(null)
const showAvatarUpload = ref(false)
const showProfileEdit = ref(false)
const showPasswordChange = ref(false)

const tabs = [
  { key: 'favorites', label: '我的收藏' },
  { key: 'games', label: '游戏记录' },
  { key: 'achievements', label: '成就' }
]

const fetchUserInfo = async () => {
  try {
    // 检查登录状态
    const isAuthenticated = await checkAuth()
    if (!isAuthenticated) {
      ElMessage.warning('请先登录')
      router.push('/auth/login')
      return
    }

    const response = await userApi.getCurrentUser()
    if (response.code === 200) {
      userInfo.value = response.data
    } else {
      throw new Error(response.message || '获取用户信息失败')
    }
  } catch (error: any) {
    console.error('获取用户信息失败:', error)
    if (error.response?.status === 401) {
      ElMessage.warning('登录已过期，请重新登录')
      router.push('/auth/login')
    } else {
      ElMessage.error('获取用户信息失败')
    }
  }
}

const formatJoinDate = (dateStr: string) => {
  if (!dateStr) return '未知'
  const date = new Date(dateStr)
  return `${date.getFullYear()}年加入`
}

const handleAvatarSuccess = (url: string) => {
  if (userInfo.value) {
    userInfo.value.icon = url
  }
  ElMessage.success('头像更新成功')
}

const handleProfileSuccess = () => {
  fetchUserInfo()
  ElMessage.success('资料更新成功')
}

const handlePasswordSuccess = () => {
  ElMessage.success('密码修改成功')
}

onMounted(() => {
  fetchUserInfo()
})
</script>

<style lang="scss" scoped>
.user-profile {
  min-height: 100vh;
  padding: 40px 0;
  background: linear-gradient(180deg, #1A1A2E 0%, #16213E 50%, #0F0F1E 100%);
}

.profile-header {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 40px;
  margin-bottom: 40px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 32px;
}

.profile-info {
  display: flex;
  gap: 24px;
}

.avatar-container {
  position: relative;
  display: inline-block;
}

.profile-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-edit-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #00F5D4;
  border: none;
  color: #1A1A2E;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:hover {
    background: #00C9A7;
    transform: scale(1.1);
  }
}

.profile-name {
  font-size: 2rem;
  color: #fff;
  font-weight: 700;
  margin-bottom: 8px;
}

.profile-meta {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.level {
  padding: 4px 12px;
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.join-date {
  color: #888;
  font-size: 0.9rem;
}

.profile-email {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 8px 0;
  color: #B0B0B0;
  font-size: 0.9rem;
}

.email-icon {
  font-size: 1rem;
}

.profile-bio {
  color: #B0B0B0;
  line-height: 1.5;
  max-width: 400px;
  margin-bottom: 16px;
}

.profile-actions {
  display: flex;
  gap: 12px;
}

.edit-btn {
  padding: 8px 16px;
  background: rgba(0, 245, 212, 0.1);
  border: 1px solid #00F5D4;
  color: #00F5D4;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;

  &:hover {
    background: #00F5D4;
    color: #1A1A2E;
  }
}

.profile-stats {
  display: flex;
  gap: 32px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 2rem;
  color: #00F5D4;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  color: #888;
  font-size: 0.9rem;
}

.content-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tab-btn {
  padding: 12px 24px;
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
  
  &.active {
    color: #00F5D4;
    border-bottom-color: #00F5D4;
  }
}

.tab-content {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 24px;
}

.tab-content h3 {
  color: #fff;
  margin-bottom: 16px;
}

.tab-content p {
  color: #B0B0B0;
}

@media (max-width: 768px) {
  .profile-header {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .profile-info {
    flex-direction: column;
    text-align: center;
  }
  
  .profile-stats {
    justify-content: center;
  }
}
</style>
