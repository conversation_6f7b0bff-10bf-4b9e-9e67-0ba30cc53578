<template>
  <div class="user-settings">
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">
          <span class="title-icon">⚙️</span>
          账户设置
        </h1>
        <p class="page-subtitle">管理你的个人信息和偏好设置</p>

        <!-- 快速操作 -->
        <div class="quick-actions">
          <el-button
            :icon="isDark ? 'Sunny' : 'Moon'"
            @click="toggleTheme"
            :loading="themeLoading"
            circle
            :title="isDark ? '切换到浅色主题' : '切换到深色主题'"
          />
          <el-button
            icon="Refresh"
            @click="refreshAllData"
            :loading="loading"
            circle
            title="刷新数据"
          />
          <el-button
            icon="Download"
            @click="exportSettings"
            circle
            title="导出设置"
          />
        </div>
      </div>

      <div class="settings-content">
        <div class="settings-sidebar">
          <nav class="settings-nav">
            <button
              v-for="section in sections"
              :key="section.key"
              class="nav-item"
              :class="{ active: activeSection === section.key }"
              @click="activeSection = section.key"
            >
              <span class="nav-icon">{{ section.icon }}</span>
              <span class="nav-text">{{ section.label }}</span>
              <span v-if="section.badge" class="nav-badge">{{ section.badge }}</span>
            </button>
          </nav>
        </div>

        <div class="settings-main">
          <!-- 基本信息 -->
          <div v-if="activeSection === 'profile'" class="settings-section">
            <div class="section-header">
              <h2 class="section-title">👤 基本信息</h2>
              <p class="section-description">管理你的个人资料和基本信息</p>
            </div>

            <el-card class="settings-card">
              <div class="form-group">
                <label class="form-label">头像</label>
                <div class="avatar-upload">
                  <el-avatar :size="80" :src="userInfo.icon" />
                  <el-upload
                    class="avatar-uploader"
                    action="/api/upload/avatar"
                    :show-file-list="false"
                    :before-upload="beforeAvatarUpload"
                    :on-success="handleAvatarSuccess"
                  >
                    <el-button type="primary" size="small">更换头像</el-button>
                  </el-upload>
                </div>
              </div>

              <div class="form-group">
                <label class="form-label">昵称</label>
                <el-input
                  v-model="userInfo.nickName"
                  placeholder="请输入昵称"
                  maxlength="20"
                  show-word-limit
                />
              </div>

              <div class="form-group">
                <label class="form-label">个人简介</label>
                <el-input
                  v-model="userInfo.intro"
                  type="textarea"
                  :rows="3"
                  placeholder="介绍一下自己吧..."
                  maxlength="200"
                  show-word-limit
                />
              </div>

              <div class="form-group">
                <label class="form-label">性别</label>
                <el-select v-model="userInfo.gender" placeholder="请选择性别">
                  <el-option label="不设置" value="" />
                  <el-option label="男" value="1" />
                  <el-option label="女" value="0" />
                </el-select>
              </div>

              <div class="form-actions">
                <el-button type="primary" @click="saveProfile" :loading="loading">
                  保存更改
                </el-button>
              </div>
            </el-card>
          </div>

          <!-- 偏好设置 -->
          <div v-if="activeSection === 'preferences'" class="settings-section">
            <div class="section-header">
              <h2 class="section-title">🎨 偏好设置</h2>
              <p class="section-description">个性化你的使用体验</p>
            </div>

            <el-card class="settings-card">
              <div class="preference-group">
                <h3 class="group-title">主题设置</h3>
                <div class="theme-selector">
                  <div class="theme-option" :class="{ active: isDark }" @click="setTheme('dark')">
                    <div class="theme-preview dark-preview">
                      <div class="preview-header"></div>
                      <div class="preview-content"></div>
                    </div>
                    <div class="theme-info">
                      <span class="theme-name">🌙 深色主题</span>
                      <span class="theme-desc">适合夜间使用</span>
                    </div>
                  </div>

                  <div class="theme-option" :class="{ active: isLight }" @click="setTheme('light')">
                    <div class="theme-preview light-preview">
                      <div class="preview-header"></div>
                      <div class="preview-content"></div>
                    </div>
                    <div class="theme-info">
                      <span class="theme-name">☀️ 浅色主题</span>
                      <span class="theme-desc">适合白天使用</span>
                    </div>
                  </div>
                </div>
              </div>

              <el-divider />

              <div class="preference-group">
                <h3 class="group-title">语言和地区</h3>
                <div class="form-row">
                  <div class="form-group">
                    <label class="form-label">语言</label>
                    <el-select v-model="settings.language" @change="updateLanguage">
                      <el-option label="简体中文" value="zh-CN" />
                      <el-option label="English" value="en-US" />
                      <el-option label="繁體中文" value="zh-TW" />
                      <el-option label="日本語" value="ja-JP" />
                    </el-select>
                  </div>

                  <div class="form-group">
                    <label class="form-label">时区</label>
                    <el-select v-model="settings.timezone" @change="updateTimezone">
                      <el-option label="北京时间 (UTC+8)" value="Asia/Shanghai" />
                      <el-option label="东京时间 (UTC+9)" value="Asia/Tokyo" />
                      <el-option label="纽约时间 (UTC-5)" value="America/New_York" />
                      <el-option label="伦敦时间 (UTC+0)" value="Europe/London" />
                    </el-select>
                  </div>
                </div>
              </div>

              <el-divider />

              <div class="preference-group">
                <h3 class="group-title">隐私设置</h3>
                <div class="form-group">
                  <label class="form-label">个人资料可见性</label>
                  <el-radio-group v-model="settings.privacyLevel" @change="updatePrivacyLevel">
                    <el-radio :label="1">🌍 公开 - 所有人可见</el-radio>
                    <el-radio :label="2">👥 好友 - 仅好友可见</el-radio>
                    <el-radio :label="3">🔒 私密 - 仅自己可见</el-radio>
                  </el-radio-group>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 通知设置 -->
          <div v-if="activeSection === 'notifications'" class="settings-section">
            <div class="section-header">
              <h2 class="section-title">🔔 通知设置</h2>
              <p class="section-description">{{ notificationSummary }}</p>
            </div>

            <el-card class="settings-card">
              <div class="notification-header">
                <div class="notification-stats">
                  <el-progress
                    type="circle"
                    :percentage="(enabledCount / 4) * 100"
                    :width="60"
                    :stroke-width="6"
                  >
                    <span class="progress-text">{{ enabledCount }}/4</span>
                  </el-progress>
                </div>

                <div class="notification-actions">
                  <el-button @click="enableAllNotifications" size="small">全部开启</el-button>
                  <el-button @click="disableAllNotifications" size="small">全部关闭</el-button>
                </div>
              </div>

              <el-divider />

              <div class="notification-list">
                <div
                  v-for="(config, type) in notificationTypes"
                  :key="type"
                  class="notification-item"
                >
                  <div class="notification-info">
                    <div class="notification-icon">{{ config.icon }}</div>
                    <div class="notification-content">
                      <h4 class="notification-name">{{ config.name }}</h4>
                      <p class="notification-desc">{{ config.description }}</p>
                      <div class="notification-examples">
                        <span v-for="example in config.examples" :key="example" class="example-tag">
                          {{ example }}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div class="notification-controls">
                    <el-switch
                      :model-value="getNotificationStatus(type)"
                      @change="toggleSingleNotification(type)"
                      size="large"
                    />
                    <el-button
                      @click="sendTestNotification(type)"
                      size="small"
                      text
                      :disabled="!getNotificationStatus(type)"
                    >
                      测试
                    </el-button>
                  </div>
                </div>
              </div>

              <el-divider />

              <div class="notification-presets">
                <h4 class="presets-title">快速设置</h4>
                <div class="presets-list">
                  <el-button
                    v-for="(preset, key) in presets"
                    :key="key"
                    @click="applyRecommendedSettings(key)"
                    size="small"
                  >
                    {{ preset.name }}
                  </el-button>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 登录历史 -->
          <div v-if="activeSection === 'security'" class="settings-section">
            <div class="section-header">
              <h2 class="section-title">🔒 账户安全</h2>
              <p class="section-description">管理你的账户安全和登录历史</p>
            </div>

            <!-- 安全概览 -->
            <el-card class="settings-card">
              <div class="security-overview">
                <div class="security-stats">
                  <div class="stat-item">
                    <div class="stat-value">{{ statistics.totalLogins }}</div>
                    <div class="stat-label">总登录次数</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value">{{ successRate }}%</div>
                    <div class="stat-label">成功率</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value">{{ avgSessionDurationText }}</div>
                    <div class="stat-label">平均在线时长</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value" :class="{ online: statistics.isOnline }">
                      {{ statistics.isOnline ? '在线' : '离线' }}
                    </div>
                    <div class="stat-label">当前状态</div>
                  </div>
                </div>
              </div>
            </el-card>

            <!-- 最近登录记录 -->
            <el-card class="settings-card">
              <div class="card-header">
                <h3 class="card-title">最近登录记录</h3>
                <el-button @click="fetchRecentLogins" size="small" text>刷新</el-button>
              </div>

              <div class="login-history-list">
                <div
                  v-for="login in recentLogins"
                  :key="login.id"
                  class="login-item"
                >
                  <div class="login-icon">
                    {{ getDeviceIcon(login.deviceType) }}
                  </div>
                  <div class="login-info">
                    <div class="login-device">{{ login.browser }} · {{ login.deviceTypeDesc }}</div>
                    <div class="login-location">{{ login.loginLocation }} · {{ login.loginIp }}</div>
                    <div class="login-time">{{ formatLoginTime(login.loginTime) }}</div>
                  </div>
                  <div class="login-status">
                    <span class="status-icon">{{ getStatusIcon(login.status) }}</span>
                    <span v-if="login.isOnline" class="online-badge">在线</span>
                  </div>
                </div>

                <div v-if="recentLogins.length === 0" class="empty-state">
                  <el-empty description="暂无登录记录" />
                </div>
              </div>

              <div class="card-footer">
                <el-button @click="activeSection = 'history'" text>查看完整历史</el-button>
              </div>
            </el-card>

            <!-- 在线会话管理 -->
            <el-card v-if="hasOnlineSessions" class="settings-card">
              <div class="card-header">
                <h3 class="card-title">在线会话 ({{ onlineSessions.length }})</h3>
                <el-button @click="logoutAll" type="danger" size="small">登出所有设备</el-button>
              </div>

              <div class="session-list">
                <div
                  v-for="session in onlineSessions"
                  :key="session.id"
                  class="session-item"
                >
                  <div class="session-info">
                    <div class="session-device">
                      {{ getDeviceIcon(session.deviceType) }} {{ session.browser }}
                    </div>
                    <div class="session-location">{{ session.loginLocation }}</div>
                    <div class="session-time">登录于 {{ formatLoginTime(session.loginTime) }}</div>
                  </div>
                  <div class="session-status">
                    <span class="online-indicator">🟢 在线</span>
                  </div>
                </div>
              </div>
            </el-card>

            <!-- 可疑登录提醒 -->
            <el-card v-if="hasSuspiciousLogins" class="settings-card warning-card">
              <div class="card-header">
                <h3 class="card-title">⚠️ 可疑登录检测</h3>
                <el-button @click="fetchSuspiciousLogins" size="small" text>刷新</el-button>
              </div>

              <el-alert
                title="检测到可疑登录活动"
                type="warning"
                :description="`发现 ${suspiciousLogins.length} 条可疑登录记录，请及时检查`"
                show-icon
                :closable="false"
              />

              <div class="suspicious-list">
                <div
                  v-for="login in suspiciousLogins.slice(0, 3)"
                  :key="login.id"
                  class="suspicious-item"
                >
                  <div class="suspicious-info">
                    <div class="suspicious-device">{{ login.browser }} · {{ login.deviceTypeDesc }}</div>
                    <div class="suspicious-location">{{ login.loginLocation }} · {{ login.loginIp }}</div>
                    <div class="suspicious-time">{{ formatLoginTime(login.loginTime) }}</div>
                  </div>
                  <div class="suspicious-status">
                    <el-tag type="warning" size="small">可疑</el-tag>
                  </div>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 完整登录历史 -->
          <div v-if="activeSection === 'history'" class="settings-section">
            <div class="section-header">
              <h2 class="section-title">📊 登录历史</h2>
              <p class="section-description">查看详细的登录记录和安全分析</p>
            </div>

            <el-card class="settings-card">
              <div class="history-controls">
                <div class="controls-left">
                  <el-button @click="refreshAll" :loading="loading" icon="Refresh">刷新</el-button>
                  <el-button @click="exportHistory" icon="Download">导出</el-button>
                </div>
                <div class="controls-right">
                  <el-button @click="clearHistory" type="danger" plain>清除历史</el-button>
                </div>
              </div>

              <div class="table-container">
                <el-table
                  :data="historyData.records"
                  :loading="historyLoading"
                  stripe
                  style="width: 100%"
                  class="history-table"
                >
                  <el-table-column prop="loginTime" label="登录时间" min-width="160" show-overflow-tooltip>
                    <template #default="{ row }">
                      <div class="time-cell">
                        <div class="time-primary">{{ formatDate(row.loginTime) }}</div>
                        <div class="time-secondary">{{ formatTime(row.loginTime) }}</div>
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column prop="deviceInfo" label="设备信息" min-width="180" show-overflow-tooltip>
                    <template #default="{ row }">
                      <div class="device-cell">
                        <div class="device-primary">
                          {{ getDeviceIcon(row.deviceType) }} {{ row.deviceTypeDesc || 'Unknown' }}
                        </div>
                        <div class="device-secondary">{{ row.browser || 'Unknown Browser' }}</div>
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column prop="location" label="位置信息" min-width="160" show-overflow-tooltip>
                    <template #default="{ row }">
                      <div class="location-cell">
                        <div class="location-primary">{{ row.loginLocation || '未知地点' }}</div>
                        <div class="location-secondary">{{ row.loginIp || '未知IP' }}</div>
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column prop="status" label="状态" width="80" align="center">
                    <template #default="{ row }">
                      <el-tag :type="row.status === 1 ? 'success' : 'danger'" size="small">
                        {{ getStatusText(row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>

                  <el-table-column prop="sessionInfo" label="会话信息" min-width="120" show-overflow-tooltip>
                    <template #default="{ row }">
                      <div class="session-cell">
                        <div class="session-duration">{{ formatDuration(row.sessionDuration) }}</div>
                        <div class="session-status">
                          <span v-if="row.isOnline" class="online-badge">🟢 在线</span>
                          <span v-else class="offline-badge">⚫ 离线</span>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <div class="pagination-wrapper">
                <el-pagination
                  v-model:current-page="historyData.current"
                  v-model:page-size="historyData.size"
                  :total="historyData.total"
                  :page-sizes="[10, 20, 50, 100]"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useSettings } from '@/composables/useSettings'
import { useTheme } from '@/composables/useTheme'
import { useNotifications } from '@/composables/useNotifications'
import { useLoginHistory } from '@/composables/useLoginHistory'
import { userApi } from '@/api/user'

// 使用 Composables
const {
  loading,
  settings,
  updateSettings,
  updateSingleField,
  resetSettings,
  exportSettings,
  importSettings
} = useSettings()

const {
  currentTheme,
  isDark,
  isLight,
  toggleTheme: themeToggle,
  setTheme
} = useTheme()

const {
  notificationTypes,
  enabledCount,
  notificationSummary,
  getNotificationStatus,
  toggleSingleNotification,
  enableAllNotifications,
  disableAllNotifications,
  sendTestNotification,
  applyRecommendedSettings,
  getRecommendedSettings
} = useNotifications()

const {
  historyData,
  recentLogins,
  onlineSessions,
  statistics,
  suspiciousLogins,
  hasOnlineSessions,
  hasSuspiciousLogins,
  successRate,
  avgSessionDurationText,
  fetchHistory,
  fetchRecentLogins,
  fetchOnlineSessions,
  fetchStatistics,
  fetchSuspiciousLogins,
  clearHistory,
  logoutAll,
  refreshAll,
  exportHistory,
  generateSecurityReport,
  formatLoginTime,
  getDeviceIcon,
  getStatusIcon,
  historyLoading
} = useLoginHistory()

// 本地状态
const activeSection = ref('profile')
const themeLoading = ref(false)
const userInfo = reactive({
  nickName: '',
  icon: '',
  intro: '',
  gender: ''
})

// 导航菜单
const sections = computed(() => [
  { key: 'profile', label: '基本信息', icon: '👤' },
  { key: 'preferences', label: '偏好设置', icon: '🎨' },
  { key: 'notifications', label: '通知设置', icon: '🔔', badge: enabledCount.value },
  { key: 'security', label: '账户安全', icon: '🔒' },
  { key: 'history', label: '登录历史', icon: '📊' }
])

// 通知预设
const presets = computed(() => ({
  work: getRecommendedSettings('work'),
  gaming: getRecommendedSettings('gaming'),
  minimal: getRecommendedSettings('minimal'),
  full: getRecommendedSettings('full')
}))

// 方法
const refreshAllData = async () => {
  await Promise.all([
    fetchUserInfo(),
    fetchRecentLogins(),
    fetchOnlineSessions(),
    fetchStatistics()
  ])
}

const fetchUserInfo = async () => {
  try {
    const response = await userApi.getCurrentUser()
    if (response.code === 200) {
      Object.assign(userInfo, response.data)
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

const saveProfile = async () => {
  try {
    const response = await userApi.updateProfile(userInfo)
    if (response.code === 200) {
      ElMessage.success('个人信息保存成功')
    } else {
      ElMessage.error(response.message || '保存失败')
    }
  } catch (error) {
    console.error('保存个人信息失败:', error)
    ElMessage.error('保存失败')
  }
}

const toggleTheme = async () => {
  themeLoading.value = true
  try {
    await themeToggle()
  } finally {
    themeLoading.value = false
  }
}

const updateLanguage = (language) => {
  updateSingleField('language', language)
}

const updateTimezone = (timezone) => {
  updateSingleField('timezone', timezone)
}

const updatePrivacyLevel = (level) => {
  updateSingleField('privacyLevel', level)
}

const beforeAvatarUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('头像图片只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('头像图片大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}

const handleAvatarSuccess = (response) => {
  if (response.code === 200) {
    userInfo.icon = response.data.url
    ElMessage.success('头像上传成功')
  } else {
    ElMessage.error(response.message || '头像上传失败')
  }
}

const handleSizeChange = (size) => {
  fetchHistory(1, size)
}

const handleCurrentChange = (page) => {
  fetchHistory(page, historyData.size)
}

// 新增的格式化方法
const formatDate = (time) => {
  if (!time) return '未知'
  const date = new Date(time)
  return date.toLocaleDateString('zh-CN')
}

const formatTime = (time) => {
  if (!time) return ''
  const date = new Date(time)
  return date.toLocaleTimeString('zh-CN', { hour12: false })
}

const formatDuration = (duration) => {
  if (!duration) return '未知'
  if (typeof duration === 'string') return duration
  
  const hours = Math.floor(duration / 3600)
  const minutes = Math.floor((duration % 3600) / 60)
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟`
  } else {
    return `${duration}秒`
  }
}

const getStatusText = (status) => {
  return status === 1 ? '成功' : '失败'
}

// 生命周期
onMounted(() => {
  refreshAllData()
})
</script>

<style lang="scss" scoped>
.user-settings {
  min-height: 100vh;
  padding: 20px;
  background: var(--theme-background, #1a1a1a);
  color: var(--theme-text, #ffffff);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
  position: relative;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 10px;
  background: linear-gradient(135deg, #00F5D4, #00D4AA);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;

  .title-icon {
    font-size: 2rem;
  }
}

.page-subtitle {
  font-size: 1.1rem;
  color: var(--theme-text-secondary, #b3b3b3);
  margin-bottom: 20px;
}

.quick-actions {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  gap: 10px;
}

.settings-content {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 30px;
  align-items: start;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

.settings-sidebar {
  position: sticky;
  top: 20px;
}

.settings-nav {
  background: var(--theme-surface, #2d2d2d);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 1px solid var(--theme-border, #333333);
}

.nav-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 15px 20px;
  margin-bottom: 8px;
  background: transparent;
  border: none;
  border-radius: 12px;
  color: var(--theme-text-secondary, #b3b3b3);
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    background: rgba(0, 245, 212, 0.1);
    color: #00F5D4;
    transform: translateX(5px);
  }

  &.active {
    background: linear-gradient(135deg, rgba(0, 245, 212, 0.2), rgba(0, 212, 170, 0.1));
    color: #00F5D4;
    box-shadow: 0 4px 16px rgba(0, 245, 212, 0.3);

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 60%;
      background: linear-gradient(135deg, #00F5D4, #00D4AA);
      border-radius: 2px;
    }
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.nav-icon {
  font-size: 1.2rem;
  margin-right: 12px;
  min-width: 20px;
}

.nav-text {
  flex: 1;
  text-align: left;
}

.nav-badge {
  background: #00F5D4;
  color: #1a1a1a;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 10px;
  min-width: 20px;
  text-align: center;
}

.settings-main {
  min-height: 600px;
}

.settings-section {
  animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.section-header {
  margin-bottom: 30px;
}

.section-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 10px;
  color: var(--theme-text, #ffffff);
  display: flex;
  align-items: center;
  gap: 10px;
}

.section-description {
  font-size: 1rem;
  color: var(--theme-text-secondary, #b3b3b3);
  margin: 0;
}

.settings-card {
  background: var(--theme-surface, #2d2d2d);
  border: 1px solid var(--theme-border, #333333);
  border-radius: 16px;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

  :deep(.el-card__body) {
    padding: 30px;
  }
}

.warning-card {
  border-color: #E6A23C;
  background: rgba(230, 162, 60, 0.05);
}

// 表单样式
.form-group {
  margin-bottom: 25px;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  display: block;
  font-size: 1rem;
  font-weight: 600;
  color: var(--theme-text, #ffffff);
  margin-bottom: 10px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.form-actions {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid var(--theme-border, #333333);
}

// 头像上传
.avatar-upload {
  display: flex;
  align-items: center;
  gap: 20px;
}

.avatar-uploader {
  :deep(.el-upload) {
    border: none;
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }
  }
}

// 主题选择器
.theme-selector {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 15px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.theme-option {
  padding: 20px;
  border: 2px solid var(--theme-border, #333333);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--theme-background, #1a1a1a);

  &:hover {
    border-color: #00F5D4;
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 245, 212, 0.2);
  }

  &.active {
    border-color: #00F5D4;
    background: rgba(0, 245, 212, 0.1);
    box-shadow: 0 8px 24px rgba(0, 245, 212, 0.3);
  }
}

.theme-preview {
  width: 100%;
  height: 60px;
  border-radius: 8px;
  margin-bottom: 15px;
  position: relative;
  overflow: hidden;

  .preview-header {
    height: 20px;
    width: 100%;
  }

  .preview-content {
    height: 40px;
    width: 100%;
  }

  &.dark-preview {
    .preview-header {
      background: #2d2d2d;
    }
    .preview-content {
      background: #1a1a1a;
    }
  }

  &.light-preview {
    .preview-header {
      background: #f5f5f5;
    }
    .preview-content {
      background: #ffffff;
    }
  }
}

.theme-info {
  text-align: center;

  .theme-name {
    display: block;
    font-weight: 600;
    color: var(--theme-text, #ffffff);
    margin-bottom: 5px;
  }

  .theme-desc {
    font-size: 0.9rem;
    color: var(--theme-text-secondary, #b3b3b3);
  }
}

// 偏好设置组
.preference-group {
  margin-bottom: 30px;

  &:last-child {
    margin-bottom: 0;
  }
}

.group-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--theme-text, #ffffff);
  margin-bottom: 15px;
}

// 通知设置
.notification-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 15px;
  }
}

.notification-stats {
  display: flex;
  align-items: center;
  gap: 20px;

  .progress-text {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--theme-text, #ffffff);
  }
}

.notification-actions {
  display: flex;
  gap: 10px;
}

.notification-list {
  margin: 20px 0;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 20px;
  border: 1px solid var(--theme-border, #333333);
  border-radius: 12px;
  margin-bottom: 15px;
  background: var(--theme-background, #1a1a1a);
  transition: all 0.3s ease;

  &:hover {
    border-color: #00F5D4;
    box-shadow: 0 4px 16px rgba(0, 245, 212, 0.1);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.notification-info {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  flex: 1;
}

.notification-icon {
  font-size: 1.5rem;
  margin-top: 2px;
}

.notification-content {
  flex: 1;
}

.notification-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--theme-text, #ffffff);
  margin: 0 0 8px 0;
}

.notification-desc {
  font-size: 0.95rem;
  color: var(--theme-text-secondary, #b3b3b3);
  margin: 0 0 10px 0;
  line-height: 1.5;
}

.notification-examples {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.example-tag {
  font-size: 0.8rem;
  background: rgba(0, 245, 212, 0.1);
  color: #00F5D4;
  padding: 2px 8px;
  border-radius: 6px;
  border: 1px solid rgba(0, 245, 212, 0.3);
}

.notification-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  margin-left: 20px;
}

.notification-presets {
  margin-top: 20px;
}

.presets-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--theme-text, #ffffff);
  margin-bottom: 15px;
}

.presets-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

// 安全概览
.security-overview {
  margin-bottom: 20px;
}

.security-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: var(--theme-background, #1a1a1a);
  border-radius: 12px;
  border: 1px solid var(--theme-border, #333333);
}

.stat-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: #00F5D4;
  margin-bottom: 5px;

  &.online {
    color: #67C23A;
  }
}

.stat-label {
  font-size: 0.9rem;
  color: var(--theme-text-secondary, #b3b3b3);
}

// 卡片通用样式
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.card-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--theme-text, #ffffff);
  margin: 0;
}

.card-footer {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid var(--theme-border, #333333);
  text-align: center;
}

// 登录历史
.login-history-list {
  max-height: 400px;
  overflow-y: auto;
}

.login-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 1px solid var(--theme-border, #333333);
  border-radius: 8px;
  margin-bottom: 10px;
  background: var(--theme-background, #1a1a1a);
  transition: all 0.3s ease;

  &:hover {
    border-color: #00F5D4;
    transform: translateX(5px);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.login-icon {
  font-size: 1.5rem;
  margin-right: 15px;
}

.login-info {
  flex: 1;
}

.login-device {
  font-weight: 600;
  color: var(--theme-text, #ffffff);
  margin-bottom: 4px;
}

.login-location {
  font-size: 0.9rem;
  color: var(--theme-text-secondary, #b3b3b3);
  margin-bottom: 4px;
}

.login-time {
  font-size: 0.85rem;
  color: var(--theme-text-secondary, #b3b3b3);
}

.login-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.status-icon {
  font-size: 1.2rem;
}

.online-badge {
  font-size: 0.8rem;
  color: #67C23A;
  font-weight: 600;
}

// 会话管理
.session-list {
  margin: 20px 0;
}

.session-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  border: 1px solid var(--theme-border, #333333);
  border-radius: 8px;
  margin-bottom: 10px;
  background: var(--theme-background, #1a1a1a);

  &:last-child {
    margin-bottom: 0;
  }
}

.session-info {
  flex: 1;
}

.session-device {
  font-weight: 600;
  color: var(--theme-text, #ffffff);
  margin-bottom: 4px;
}

.session-location {
  font-size: 0.9rem;
  color: var(--theme-text-secondary, #b3b3b3);
  margin-bottom: 4px;
}

.session-time {
  font-size: 0.85rem;
  color: var(--theme-text-secondary, #b3b3b3);
}

.session-status {
  text-align: right;
}

.online-indicator {
  font-size: 0.9rem;
  color: #67C23A;
  font-weight: 600;
}

// 可疑登录
.suspicious-list {
  margin: 20px 0;
}

.suspicious-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  border: 1px solid #E6A23C;
  border-radius: 8px;
  margin-bottom: 10px;
  background: rgba(230, 162, 60, 0.05);

  &:last-child {
    margin-bottom: 0;
  }
}

.suspicious-info {
  flex: 1;
}

.suspicious-device {
  font-weight: 600;
  color: var(--theme-text, #ffffff);
  margin-bottom: 4px;
}

.suspicious-location {
  font-size: 0.9rem;
  color: var(--theme-text-secondary, #b3b3b3);
  margin-bottom: 4px;
}

.suspicious-time {
  font-size: 0.85rem;
  color: var(--theme-text-secondary, #b3b3b3);
}

.suspicious-status {
  text-align: right;
}

// 历史控制
.history-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 15px;
  }
}

.controls-left {
  display: flex;
  gap: 10px;
}

.controls-right {
  display: flex;
  gap: 10px;
}

// 优化后的表格样式
.table-container {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid var(--theme-border, #333333);
  
  // 自定义滚动条
  &::-webkit-scrollbar {
    height: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: var(--theme-background, #1a1a1a);
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--theme-border, #333333);
    border-radius: 4px;
    
    &:hover {
      background: #00F5D4;
    }
  }
}

.history-table {
  min-width: 700px; // 确保表格最小宽度
  
  :deep(.el-table__header) {
    background-color: var(--theme-background, #1a1a1a);
  }

  :deep(.el-table__row) {
    background-color: var(--theme-surface, #2d2d2d);

    &:hover {
      background-color: rgba(0, 245, 212, 0.1);
    }
  }

  :deep(.el-table__cell) {
    border-color: var(--theme-border, #333333);
    padding: 12px 8px;
  }

  // 单元格内容样式
  .time-cell {
    .time-primary {
      font-size: 0.9rem;
      font-weight: 500;
      color: var(--theme-text, #ffffff);
      margin-bottom: 2px;
    }
    
    .time-secondary {
      font-size: 0.8rem;
      color: var(--theme-text-secondary, #b3b3b3);
    }
  }

  .device-cell {
    .device-primary {
      font-size: 0.9rem;
      font-weight: 500;
      color: var(--theme-text, #ffffff);
      margin-bottom: 2px;
      display: flex;
      align-items: center;
      gap: 4px;
    }
    
    .device-secondary {
      font-size: 0.8rem;
      color: var(--theme-text-secondary, #b3b3b3);
    }
  }

  .location-cell {
    .location-primary {
      font-size: 0.9rem;
      font-weight: 500;
      color: var(--theme-text, #ffffff);
      margin-bottom: 2px;
    }
    
    .location-secondary {
      font-size: 0.8rem;
      color: var(--theme-text-secondary, #b3b3b3);
      word-break: break-all;
    }
  }

  .session-cell {
    text-align: center;
    
    .session-duration {
      font-size: 0.85rem;
      color: var(--theme-text, #ffffff);
      margin-bottom: 4px;
    }
    
    .session-status {
      font-size: 0.8rem;
    }
  }

  .online-badge {
    color: #67C23A;
    font-size: 0.8rem;
    font-weight: 500;
  }

  .offline-badge {
    color: var(--theme-text-secondary, #b3b3b3);
    font-size: 0.8rem;
  }
}

// 响应式优化
@media (max-width: 1200px) {
  .table-container {
    border-radius: 6px;
  }
  
  .history-table {
    min-width: 600px;
    
    :deep(.el-table__cell) {
      padding: 8px 6px;
    }
    
    .time-cell,
    .device-cell,
    .location-cell {
      .time-primary,
      .device-primary,
      .location-primary {
        font-size: 0.85rem;
      }
      
      .time-secondary,
      .device-secondary,
      .location-secondary {
        font-size: 0.75rem;
      }
    }
  }
}

@media (max-width: 768px) {
  .table-container {
    margin: 0 -20px; // 在手机上扩展到全宽
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
  
  .history-table {
    min-width: 500px;
    
    :deep(.el-table__cell) {
      padding: 6px 4px;
    }
    
    .session-cell {
      .session-duration {
        font-size: 0.8rem;
      }
      
      .session-status {
        font-size: 0.75rem;
      }
    }
    
    .time-cell,
    .device-cell,
    .location-cell {
      .time-primary,
      .device-primary,
      .location-primary {
        font-size: 0.8rem;
      }
      
      .time-secondary,
      .device-secondary,
      .location-secondary {
        font-size: 0.7rem;
      }
    }
  }
}

@media (max-width: 480px) {
  .history-table {
    min-width: 450px;
    
    :deep(.el-table__cell) {
      padding: 4px 2px;
    }
  }
}

// 分页
.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  
  :deep(.el-pagination) {
    @media (max-width: 768px) {
      .el-pagination__sizes {
        display: none;
      }
      
      .el-pagination__jump {
        display: none;
      }
      
      .el-pagination__total {
        order: -1;
        margin-right: auto;
        margin-left: 0;
      }
    }
    
    @media (max-width: 480px) {
      flex-wrap: wrap;
      gap: 10px;
      
      .el-pagination__total {
        width: 100%;
        text-align: center;
        margin-bottom: 10px;
      }
    }
  }
}

// 空状态
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--theme-text-secondary, #b3b3b3);
}

// 响应式设计
@media (max-width: 768px) {
  .user-settings {
    padding: 10px;
  }

  .page-header {
    margin-bottom: 20px;
  }

  .page-title {
    font-size: 2rem;
  }

  .quick-actions {
    position: static;
    justify-content: center;
    margin-top: 15px;
  }

  .settings-card {
    :deep(.el-card__body) {
      padding: 20px;
    }
  }

  .notification-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .notification-controls {
    flex-direction: row;
    align-items: center;
    margin-left: 0;
    width: 100%;
    justify-content: space-between;
  }

  .login-item,
  .session-item,
  .suspicious-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .login-status,
  .session-status,
  .suspicious-status {
    align-self: flex-end;
  }
  
  // 移动端表格优化
  .settings-section {
    .section-header {
      margin-bottom: 20px;
      
      .section-title {
        font-size: 1.5rem;
      }
    }
  }
  
  .table-container {
    // 在移动设备上添加提示
    position: relative;
    
    &::after {
      content: '左右滑动查看更多';
      position: absolute;
      bottom: -25px;
      left: 50%;
      transform: translateX(-50%);
      font-size: 0.75rem;
      color: var(--theme-text-secondary, #b3b3b3);
      text-align: center;
      white-space: nowrap;
      opacity: 0.7;
      
      @media (min-width: 769px) {
        display: none;
      }
    }
  }
}

// 深色主题特定样式
:deep(.dark) {
  .el-table {
    background-color: var(--theme-surface, #2d2d2d);
    color: var(--theme-text, #ffffff);

    .el-table__header {
      background-color: var(--theme-background, #1a1a1a);
    }

    .el-table__row {
      background-color: var(--theme-surface, #2d2d2d);

      &:hover {
        background-color: rgba(0, 245, 212, 0.1);
      }
    }
  }

  .el-pagination {
    .el-pager li {
      background-color: var(--theme-surface, #2d2d2d);
      color: var(--theme-text, #ffffff);

      &.active {
        background-color: #00F5D4;
        color: #1a1a1a;
      }
    }

    .btn-prev,
    .btn-next {
      background-color: var(--theme-surface, #2d2d2d);
      color: var(--theme-text, #ffffff);
    }
  }
}
</style>