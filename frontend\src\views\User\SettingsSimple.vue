<template>
  <div class="user-settings">
    <div class="container">
      <!-- 页面头部 -->
      <div class="page-header">
        <h1 class="page-title">
          <span class="title-icon">⚙️</span>
          用户设置
        </h1>
        <p class="page-subtitle">管理你的个人偏好和账户设置</p>
      </div>

      <!-- 设置内容 -->
      <div class="settings-content">
        <!-- 侧边栏导航 -->
        <div class="settings-sidebar">
          <nav class="settings-nav">
            <button 
              v-for="section in sections" 
              :key="section.key"
              class="nav-item"
              :class="{ active: activeSection === section.key }"
              @click="activeSection = section.key"
            >
              <span class="nav-icon">{{ section.icon }}</span>
              <span class="nav-text">{{ section.label }}</span>
              <span v-if="section.badge" class="nav-badge">{{ section.badge }}</span>
            </button>
          </nav>
        </div>

        <!-- 主要内容区域 -->
        <div class="settings-main">
          <!-- 基本信息 -->
          <div v-if="activeSection === 'profile'" class="settings-section">
            <div class="section-header">
              <h2 class="section-title">👤 基本信息</h2>
              <p class="section-description">管理你的个人资料和头像</p>
            </div>
            
            <el-card class="settings-card">
              <div class="form-group">
                <label class="form-label">昵称</label>
                <el-input v-model="userInfo.nickName" placeholder="请输入昵称" />
              </div>
              
              <div class="form-group">
                <label class="form-label">个人简介</label>
                <el-input 
                  v-model="userInfo.intro" 
                  type="textarea" 
                  :rows="3"
                  placeholder="介绍一下自己吧..."
                />
              </div>
              
              <div class="form-actions">
                <el-button type="primary" @click="saveProfile">保存设置</el-button>
                <el-button @click="resetProfile">重置</el-button>
              </div>
            </el-card>
          </div>

          <!-- 偏好设置 -->
          <div v-if="activeSection === 'preferences'" class="settings-section">
            <div class="section-header">
              <h2 class="section-title">🎨 偏好设置</h2>
              <p class="section-description">个性化你的使用体验</p>
            </div>
            
            <el-card class="settings-card">
              <div class="preference-group">
                <h3 class="group-title">主题设置</h3>
                <div class="theme-selector">
                  <div 
                    class="theme-option"
                    :class="{ active: currentTheme === 'dark' }"
                    @click="setTheme('dark')"
                  >
                    <div class="theme-preview dark-preview">
                      <div class="preview-header"></div>
                      <div class="preview-content"></div>
                    </div>
                    <div class="theme-info">
                      <span class="theme-name">🌙 深色主题</span>
                      <span class="theme-desc">适合夜间使用</span>
                    </div>
                  </div>
                  
                  <div 
                    class="theme-option"
                    :class="{ active: currentTheme === 'light' }"
                    @click="setTheme('light')"
                  >
                    <div class="theme-preview light-preview">
                      <div class="preview-header"></div>
                      <div class="preview-content"></div>
                    </div>
                    <div class="theme-info">
                      <span class="theme-name">☀️ 浅色主题</span>
                      <span class="theme-desc">适合白天使用</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 通知设置 -->
          <div v-if="activeSection === 'notifications'" class="settings-section">
            <div class="section-header">
              <h2 class="section-title">🔔 通知设置</h2>
              <p class="section-description">控制你接收的通知类型</p>
            </div>
            
            <el-card class="settings-card">
              <div class="notification-list">
                <div 
                  v-for="(config, type) in notificationTypes" 
                  :key="type"
                  class="notification-item"
                >
                  <div class="notification-info">
                    <div class="notification-icon">{{ config.icon }}</div>
                    <div class="notification-content">
                      <h4 class="notification-name">{{ config.name }}</h4>
                      <p class="notification-desc">{{ config.description }}</p>
                    </div>
                  </div>
                  <div class="notification-controls">
                    <el-switch 
                      v-model="config.enabled"
                      size="large"
                    />
                  </div>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 账户安全 -->
          <div v-if="activeSection === 'security'" class="settings-section">
            <div class="section-header">
              <h2 class="section-title">🔒 账户安全</h2>
              <p class="section-description">保护你的账户安全</p>
            </div>
            
            <el-card class="settings-card">
              <div class="security-items">
                <div class="security-item">
                  <div class="security-info">
                    <h3 class="security-title">修改密码</h3>
                    <p class="security-desc">定期更换密码以保护账户安全</p>
                  </div>
                  <el-button class="security-action">修改</el-button>
                </div>
                
                <div class="security-item">
                  <div class="security-info">
                    <h3 class="security-title">登录历史</h3>
                    <p class="security-desc">查看最近的登录记录</p>
                  </div>
                  <el-button class="security-action">查看</el-button>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

// 当前激活的设置页面
const activeSection = ref('profile')

// 当前主题
const currentTheme = ref('dark')

// 导航菜单
const sections = [
  { key: 'profile', label: '基本信息', icon: '👤' },
  { key: 'preferences', label: '偏好设置', icon: '🎨' },
  { key: 'notifications', label: '通知设置', icon: '🔔', badge: 3 },
  { key: 'security', label: '账户安全', icon: '🔒' }
]

// 用户信息
const userInfo = reactive({
  nickName: '推理大师',
  intro: '热爱推理剧本，擅长逻辑分析'
})

// 通知类型
const notificationTypes = reactive({
  email: {
    icon: '📧',
    name: '邮件通知',
    description: '重要信息和更新通知',
    enabled: true
  },
  system: {
    icon: '🔔',
    name: '系统通知',
    description: '系统消息和状态更新',
    enabled: true
  },
  activity: {
    icon: '🎮',
    name: '活动通知',
    description: '游戏活动和事件提醒',
    enabled: true
  },
  social: {
    icon: '👥',
    name: '社交通知',
    description: '社交互动和关注提醒',
    enabled: false
  }
})

// 方法
const saveProfile = () => {
  ElMessage.success('个人信息保存成功')
}

const resetProfile = () => {
  userInfo.nickName = '推理大师'
  userInfo.intro = '热爱推理剧本，擅长逻辑分析'
  ElMessage.info('已重置为默认设置')
}

const setTheme = (theme) => {
  currentTheme.value = theme
  ElMessage.success(`已切换到${theme === 'dark' ? '深色' : '浅色'}主题`)
}
</script>

<style lang="scss" scoped>
.user-settings {
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(180deg, #1A1A2E 0%, #16213E 50%, #0F0F1E 100%);
  color: #ffffff;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 10px;
  background: linear-gradient(135deg, #00F5D4, #00D4AA);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;

  .title-icon {
    font-size: 2rem;
  }
}

.page-subtitle {
  font-size: 1.1rem;
  color: #b3b3b3;
  margin-bottom: 20px;
}

.settings-content {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 30px;
  align-items: start;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

.settings-sidebar {
  position: sticky;
  top: 20px;
}

.settings-nav {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.nav-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 15px 20px;
  margin-bottom: 8px;
  background: transparent;
  border: none;
  border-radius: 12px;
  color: #b3b3b3;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    background: rgba(0, 245, 212, 0.1);
    color: #00F5D4;
    transform: translateX(5px);
  }

  &.active {
    background: linear-gradient(135deg, rgba(0, 245, 212, 0.2), rgba(0, 212, 170, 0.1));
    color: #00F5D4;
    box-shadow: 0 4px 16px rgba(0, 245, 212, 0.3);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.nav-icon {
  font-size: 1.2rem;
  margin-right: 12px;
  min-width: 20px;
}

.nav-text {
  flex: 1;
  text-align: left;
}

.nav-badge {
  background: #00F5D4;
  color: #1a1a1a;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 10px;
  min-width: 20px;
  text-align: center;
}

.settings-main {
  min-height: 600px;
}

.settings-section {
  animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.section-header {
  margin-bottom: 30px;
}

.section-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 10px;
  color: #ffffff;
  display: flex;
  align-items: center;
  gap: 10px;
}

.section-description {
  font-size: 1rem;
  color: #b3b3b3;
  margin: 0;
}

.settings-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

  :deep(.el-card__body) {
    padding: 30px;
  }
}

// 其他样式省略，保持简洁
</style>
