<template>
  <el-dialog
    v-model="dialogVisible"
    title="更换头像"
    width="400px"
    @close="handleClose"
  >
    <div class="avatar-upload">
      <div class="upload-area">
        <el-upload
          ref="uploadRef"
          :action="uploadUrl"
          :headers="uploadHeaders"
          :on-success="handleSuccess"
          :on-error="handleError"
          :before-upload="beforeUpload"
          :show-file-list="false"
          accept="image/*"
          drag
        >
          <div class="upload-content">
            <el-icon class="upload-icon"><Plus /></el-icon>
            <div class="upload-text">点击或拖拽上传头像</div>
            <div class="upload-hint">支持 JPG、PNG、GIF 格式，文件大小不超过 2MB</div>
          </div>
        </el-upload>
      </div>
      
      <div v-if="previewUrl" class="preview-area">
        <div class="preview-title">预览</div>
        <img :src="previewUrl" alt="头像预览" class="preview-image" />
      </div>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="uploading" @click="handleConfirm">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { userApi } from '@/api/user'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success', url: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const uploadRef = ref()
const uploading = ref(false)
const previewUrl = ref('')
const currentFile = ref<File | null>(null)

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const uploadUrl = '/api/upload/avatar'
const uploadHeaders = {
  'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
}

const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }

  // 生成预览
  const reader = new FileReader()
  reader.onload = (e) => {
    previewUrl.value = e.target?.result as string
  }
  reader.readAsDataURL(file)
  
  currentFile.value = file
  return false // 阻止自动上传
}

const handleSuccess = (response: any) => {
  uploading.value = false
  if (response.code === 200) {
    ElMessage.success('头像上传成功')
    emit('success', response.data.url)
    handleClose()
  } else {
    ElMessage.error(response.message || '上传失败')
  }
}

const handleError = () => {
  uploading.value = false
  ElMessage.error('上传失败，请重试')
}

const handleConfirm = async () => {
  if (!currentFile.value) {
    ElMessage.warning('请先选择头像文件')
    return
  }

  try {
    uploading.value = true
    const response = await userApi.uploadAvatar(currentFile.value)
    handleSuccess(response)
  } catch (error) {
    handleError()
  }
}

const handleClose = () => {
  previewUrl.value = ''
  currentFile.value = null
  uploading.value = false
  emit('update:visible', false)
}

watch(() => props.visible, (newVal) => {
  if (!newVal) {
    handleClose()
  }
})
</script>

<style scoped lang="scss">
.avatar-upload {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.upload-area {
  :deep(.el-upload) {
    width: 100%;
  }
  
  :deep(.el-upload-dragger) {
    width: 100%;
    height: 150px;
    border: 2px dashed #dcdfe6;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:hover {
      border-color: #409eff;
    }
  }
}

.upload-content {
  text-align: center;
}

.upload-icon {
  font-size: 32px;
  color: #c0c4cc;
  margin-bottom: 10px;
}

.upload-text {
  color: #606266;
  font-size: 14px;
  margin-bottom: 5px;
}

.upload-hint {
  color: #909399;
  font-size: 12px;
}

.preview-area {
  text-align: center;
}

.preview-title {
  color: #606266;
  font-size: 14px;
  margin-bottom: 10px;
}

.preview-image {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e4e7ed;
}
</style>
