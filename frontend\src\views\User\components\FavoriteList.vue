<template>
  <div class="favorite-list">
    <div class="filter-bar">
      <el-select
        v-model="filterType"
        placeholder="选择收藏类型"
        style="width: 200px"
        @change="handleFilterChange"
      >
        <el-option label="全部" value="" />
        <el-option label="剧本" value="script" />
        <el-option label="房间" value="room" />
      </el-select>
    </div>

    <div v-if="loading" class="loading">
      <el-skeleton :rows="5" animated />
    </div>

    <div v-else-if="favorites.length === 0" class="empty">
      <el-empty description="暂无收藏" />
    </div>

    <div v-else class="favorite-items">
      <div
        v-for="item in favorites"
        :key="item.id"
        class="favorite-item"
      >
        <div class="item-info">
          <div class="item-type">
            <el-tag :type="item.targetType === 'script' ? 'primary' : 'success'">
              {{ item.targetType === 'script' ? '剧本' : '房间' }}
            </el-tag>
          </div>
          <div class="item-content">
            <h4 class="item-title">{{ getItemTitle(item) }}</h4>
            <p class="item-desc">{{ getItemDescription(item) }}</p>
            <div class="item-meta">
              <span class="create-time">收藏于 {{ formatDate(item.createTime) }}</span>
            </div>
          </div>
        </div>
        <div class="item-actions">
          <el-button
            type="danger"
            size="small"
            :loading="removingIds.includes(item.id)"
            @click="handleRemove(item)"
          >
            取消收藏
          </el-button>
        </div>
      </div>
    </div>

    <div v-if="total > 0" class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { userApi } from '@/api/user'

const loading = ref(false)
const favorites = ref<any[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const filterType = ref('')
const removingIds = ref<number[]>([])

const fetchFavorites = async () => {
  try {
    loading.value = true
    const response = await userApi.getMyFavorites({
      page: currentPage.value,
      size: pageSize.value,
      type: filterType.value || undefined
    })
    
    if (response.code === 200) {
      favorites.value = response.data.records || []
      total.value = response.data.total || 0
    }
  } catch (error) {
    console.error('获取收藏列表失败:', error)
    ElMessage.error('获取收藏列表失败')
  } finally {
    loading.value = false
  }
}

const getItemTitle = (item: any) => {
  if (item.targetInfo) {
    return item.targetInfo.title || item.targetInfo.name || '未知标题'
  }
  return `${item.targetType === 'script' ? '剧本' : '房间'} #${item.targetId}`
}

const getItemDescription = (item: any) => {
  if (item.targetInfo) {
    return item.targetInfo.description || item.targetInfo.intro || '暂无描述'
  }
  return '暂无描述'
}

const formatDate = (dateStr: string) => {
  if (!dateStr) return '未知时间'
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN')
}

const handleFilterChange = () => {
  currentPage.value = 1
  fetchFavorites()
}

const handleSizeChange = () => {
  currentPage.value = 1
  fetchFavorites()
}

const handleCurrentChange = () => {
  fetchFavorites()
}

const handleRemove = async (item: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消收藏这个${item.targetType === 'script' ? '剧本' : '房间'}吗？`,
      '确认取消收藏',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    removingIds.value.push(item.id)
    const response = await userApi.removeFavorite(item.id)
    
    if (response.code === 200) {
      ElMessage.success('取消收藏成功')
      fetchFavorites()
    } else {
      ElMessage.error(response.message || '取消收藏失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消收藏失败:', error)
      ElMessage.error('取消收藏失败')
    }
  } finally {
    removingIds.value = removingIds.value.filter(id => id !== item.id)
  }
}

onMounted(() => {
  fetchFavorites()
})
</script>

<style scoped lang="scss">
.favorite-list {
  .filter-bar {
    margin-bottom: 20px;
  }

  .loading {
    padding: 20px;
  }

  .empty {
    padding: 50px 20px;
  }

  .favorite-items {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .favorite-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.08);
    }
  }

  .item-info {
    flex: 1;
    display: flex;
    gap: 16px;
  }

  .item-type {
    flex-shrink: 0;
  }

  .item-content {
    flex: 1;
  }

  .item-title {
    color: #fff;
    font-size: 16px;
    margin: 0 0 8px 0;
  }

  .item-desc {
    color: #B0B0B0;
    font-size: 14px;
    margin: 0 0 12px 0;
    line-height: 1.4;
  }

  .item-meta {
    .create-time {
      color: #888;
      font-size: 12px;
    }
  }

  .item-actions {
    flex-shrink: 0;
  }

  .pagination {
    margin-top: 30px;
    display: flex;
    justify-content: center;
  }
}
</style>
