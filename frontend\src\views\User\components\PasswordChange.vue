<template>
  <div v-if="dialogVisible" class="modal-overlay" @click="handleClose">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h2 class="modal-title">修改密码</h2>
        <button class="close-btn" @click="handleClose">
          <i class="icon-close">×</i>
        </button>
      </div>

      <form class="password-form" @submit.prevent="handleSubmit">
        <div class="form-group">
          <label class="form-label">原密码</label>
          <div class="password-input-wrapper">
            <input
              v-model="form.oldPassword"
              :type="showOldPassword ? 'text' : 'password'"
              class="form-input"
              placeholder="请输入原密码"
              required
            />
            <button
              type="button"
              class="password-toggle"
              @click="showOldPassword = !showOldPassword"
            >
              <i :class="showOldPassword ? 'icon-eye-off' : 'icon-eye'">
                {{ showOldPassword ? '🙈' : '👁️' }}
              </i>
            </button>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">新密码</label>
          <div class="password-input-wrapper">
            <input
              v-model="form.newPassword"
              :type="showNewPassword ? 'text' : 'password'"
              class="form-input"
              placeholder="请输入新密码"
              required
            />
            <button
              type="button"
              class="password-toggle"
              @click="showNewPassword = !showNewPassword"
            >
              <i :class="showNewPassword ? 'icon-eye-off' : 'icon-eye'">
                {{ showNewPassword ? '🙈' : '👁️' }}
              </i>
            </button>
          </div>
          <div class="password-hint">密码长度必须在6-20位之间</div>
        </div>

        <div class="form-group">
          <label class="form-label">确认密码</label>
          <div class="password-input-wrapper">
            <input
              v-model="form.confirmPassword"
              :type="showConfirmPassword ? 'text' : 'password'"
              class="form-input"
              placeholder="请再次输入新密码"
              required
            />
            <button
              type="button"
              class="password-toggle"
              @click="showConfirmPassword = !showConfirmPassword"
            >
              <i :class="showConfirmPassword ? 'icon-eye-off' : 'icon-eye'">
                {{ showConfirmPassword ? '🙈' : '👁️' }}
              </i>
            </button>
          </div>
        </div>

        <div class="form-actions">
          <button type="button" class="btn-cancel" @click="handleClose">
            取消
          </button>
          <button type="submit" class="btn-submit" :disabled="loading">
            <span v-if="loading" class="loading-spinner"></span>
            <span>{{ loading ? '修改中...' : '确认修改' }}</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { userApi } from '@/api/user'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const loading = ref(false)
const showOldPassword = ref(false)
const showNewPassword = ref(false)
const showConfirmPassword = ref(false)

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const form = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const validateForm = () => {
  if (!form.oldPassword.trim()) {
    ElMessage.error('请输入原密码')
    return false
  }
  if (!form.newPassword.trim()) {
    ElMessage.error('请输入新密码')
    return false
  }
  if (form.newPassword.length < 6 || form.newPassword.length > 20) {
    ElMessage.error('密码长度必须在6-20位之间')
    return false
  }
  if (!form.confirmPassword.trim()) {
    ElMessage.error('请再次输入新密码')
    return false
  }
  if (form.newPassword !== form.confirmPassword) {
    ElMessage.error('两次输入的密码不一致')
    return false
  }
  return true
}

const handleSubmit = async () => {
  if (!validateForm()) return

  try {
    loading.value = true

    const response = await userApi.changePassword({
      oldPassword: form.oldPassword,
      newPassword: form.newPassword
    })

    if (response.code === 200) {
      emit('success')
      handleClose()
    } else {
      ElMessage.error(response.message || '密码修改失败')
    }
  } catch (error) {
    console.error('密码修改失败:', error)
    ElMessage.error('密码修改失败')
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  // 重置表单
  Object.assign(form, {
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  // 重置密码显示状态
  showOldPassword.value = false
  showNewPassword.value = false
  showConfirmPassword.value = false
  loading.value = false
  emit('update:visible', false)
}
</script>

<style scoped lang="scss">
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-container {
  background: linear-gradient(145deg, #1A1A2E 0%, #16213E 100%);
  border-radius: 16px;
  width: 90%;
  max-width: 450px;
  max-height: 90vh;
  overflow-y: auto;
  border: 1px solid rgba(0, 245, 212, 0.2);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0;
  margin-bottom: 24px;
}

.modal-title {
  color: #fff;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  color: #888;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    color: #fff;
    background: rgba(255, 255, 255, 0.1);
  }
}

.password-form {
  padding: 0 24px 24px;
}

.form-group {
  margin-bottom: 20px;
  position: relative;
}

.form-label {
  display: block;
  color: #B0B0B0;
  font-size: 0.9rem;
  margin-bottom: 8px;
  font-weight: 500;
}

.password-input-wrapper {
  position: relative;
}

.form-input {
  width: 100%;
  padding: 12px 50px 12px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #fff;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  box-sizing: border-box;

  &::placeholder {
    color: #666;
  }

  &:focus {
    outline: none;
    border-color: #00F5D4;
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 0 2px rgba(0, 245, 212, 0.2);
  }
}

.password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    color: #00F5D4;
    background: rgba(0, 245, 212, 0.1);
  }
}

.password-hint {
  font-size: 0.75rem;
  color: #666;
  margin-top: 4px;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-cancel,
.btn-submit {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-cancel {
  background: rgba(255, 255, 255, 0.1);
  color: #B0B0B0;

  &:hover {
    background: rgba(255, 255, 255, 0.15);
    color: #fff;
  }
}

.btn-submit {
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  font-weight: 600;

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #00C9A7, #00A085);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 245, 212, 0.3);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@media (max-width: 768px) {
  .modal-container {
    width: 95%;
    margin: 20px;
  }

  .form-actions {
    flex-direction: column;
  }

  .btn-cancel,
  .btn-submit {
    width: 100%;
    justify-content: center;
  }
}
</style>
