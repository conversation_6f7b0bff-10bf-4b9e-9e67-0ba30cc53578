<template>
  <div v-if="dialogVisible" class="modal-overlay" @click="handleClose">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h2 class="modal-title">编辑个人资料</h2>
        <button class="close-btn" @click="handleClose">
          <i class="icon-close">×</i>
        </button>
      </div>

      <form class="profile-form" @submit.prevent="handleSubmit">
        <div class="form-group">
          <label class="form-label">昵称</label>
          <input
            v-model="form.nickName"
            type="text"
            class="form-input"
            placeholder="请输入昵称"
            maxlength="32"
            required
          />
          <div class="char-count">{{ form.nickName.length }}/32</div>
        </div>



        <div class="form-group">
          <label class="form-label">性别</label>
          <div class="radio-group">
            <label class="radio-item">
              <input v-model="form.gender" type="radio" :value="0" />
              <span class="radio-text">保密</span>
            </label>
            <label class="radio-item">
              <input v-model="form.gender" type="radio" :value="1" />
              <span class="radio-text">男</span>
            </label>
            <label class="radio-item">
              <input v-model="form.gender" type="radio" :value="2" />
              <span class="radio-text">女</span>
            </label>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">生日</label>
          <input
            v-model="form.birthday"
            type="date"
            class="form-input"
          />
        </div>

        <div class="form-group">
          <label class="form-label">城市</label>
          <input
            v-model="form.city"
            type="text"
            class="form-input"
            placeholder="请输入所在城市"
            maxlength="50"
          />
        </div>

        <div class="form-group">
          <label class="form-label">个性签名</label>
          <textarea
            v-model="form.signature"
            class="form-textarea"
            rows="3"
            placeholder="写点什么介绍一下自己吧"
            maxlength="200"
          ></textarea>
          <div class="char-count">{{ form.signature.length }}/200</div>
        </div>

        <div class="form-actions">
          <button type="button" class="btn-cancel" @click="handleClose">
            取消
          </button>
          <button type="submit" class="btn-submit" :disabled="loading">
            <span v-if="loading" class="loading-spinner"></span>
            <span>{{ loading ? '保存中...' : '保存修改' }}</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { userApi } from '@/api/user'

interface Props {
  visible: boolean
  userInfo?: any
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const loading = ref(false)

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const form = reactive({
  nickName: '',
  gender: 0,
  birthday: '',
  city: '',
  signature: ''
})

const initForm = () => {
  if (props.userInfo) {
    form.nickName = props.userInfo.nickName || ''
    form.gender = props.userInfo.gender !== undefined ? props.userInfo.gender : 0
    form.birthday = props.userInfo.birthday || ''
    form.city = props.userInfo.city || ''
    form.signature = props.userInfo.signature || props.userInfo.introduce || ''
  }
}

const validateForm = () => {
  if (!form.nickName.trim()) {
    ElMessage.error('请输入昵称')
    return false
  }
  if (form.nickName.length > 32) {
    ElMessage.error('昵称长度不能超过32个字符')
    return false
  }

  if (form.city && form.city.length > 50) {
    ElMessage.error('城市名称长度不能超过50个字符')
    return false
  }
  if (form.signature && form.signature.length > 200) {
    ElMessage.error('个性签名长度不能超过200个字符')
    return false
  }
  return true
}

const handleSubmit = async () => {
  if (!validateForm()) return

  try {
    loading.value = true

    // 只提交可修改的字段，排除邮箱
    const updateData = {
      nickName: form.nickName,
      gender: form.gender,
      birthday: form.birthday,
      city: form.city,
      signature: form.signature
    }

    const response = await userApi.updateProfile(updateData)
    if (response.code === 200) {
      emit('success')
      handleClose()
    } else {
      ElMessage.error(response.message || '保存失败')
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  // 重置表单
  Object.assign(form, {
    nickName: '',
    gender: 0,
    birthday: '',
    city: '',
    signature: ''
  })
  loading.value = false
  emit('update:visible', false)
}

watch(() => props.visible, (newVal) => {
  if (newVal) {
    initForm()
  }
})

watch(() => props.userInfo, () => {
  if (props.visible) {
    initForm()
  }
})
</script>

<style scoped lang="scss">
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-container {
  background: linear-gradient(145deg, #1A1A2E 0%, #16213E 100%);
  border-radius: 16px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  border: 1px solid rgba(0, 245, 212, 0.2);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0;
  margin-bottom: 24px;
}

.modal-title {
  color: #fff;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  color: #888;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    color: #fff;
    background: rgba(255, 255, 255, 0.1);
  }
}

.profile-form {
  padding: 0 24px 24px;
}

.form-group {
  margin-bottom: 20px;
  position: relative;
}

.form-label {
  display: block;
  color: #B0B0B0;
  font-size: 0.9rem;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #fff;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  box-sizing: border-box;

  &::placeholder {
    color: #666;
  }

  &:focus {
    outline: none;
    border-color: #00F5D4;
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 0 2px rgba(0, 245, 212, 0.2);
  }


}

.form-textarea {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.char-count {
  position: absolute;
  right: 8px;
  bottom: -18px;
  font-size: 0.75rem;
  color: #666;
}

.radio-group {
  display: flex;
  gap: 20px;
}

.radio-item {
  display: flex;
  align-items: center;
  cursor: pointer;

  input[type="radio"] {
    margin-right: 8px;
    accent-color: #00F5D4;
  }
}

.radio-text {
  color: #B0B0B0;
  font-size: 0.9rem;
  user-select: none;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-cancel,
.btn-submit {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-cancel {
  background: rgba(255, 255, 255, 0.1);
  color: #B0B0B0;

  &:hover {
    background: rgba(255, 255, 255, 0.15);
    color: #fff;
  }
}

.btn-submit {
  background: linear-gradient(135deg, #00F5D4, #00C9A7);
  color: #1A1A2E;
  font-weight: 600;

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #00C9A7, #00A085);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 245, 212, 0.3);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@media (max-width: 768px) {
  .modal-container {
    width: 95%;
    margin: 20px;
  }

  .form-actions {
    flex-direction: column;
  }

  .btn-cancel,
  .btn-submit {
    width: 100%;
    justify-content: center;
  }
}
</style>
