# 安全清理Git历史记录中的敏感信息
# 先修改文件，再清理历史记录

Write-Host "=== 安全清理敏感信息 ===" -ForegroundColor Green

# 1. 首先修改当前文件中的敏感信息
Write-Host "1. 修改当前文件中的敏感信息..." -ForegroundColor Yellow

# 检查并修改配置文件说明.md
if (Test-Path "配置文件说明.md") {
    Write-Host "   - 修改 配置文件说明.md" -ForegroundColor Cyan
    $content = Get-Content "配置文件说明.md" -Raw -Encoding UTF8
    $content = $content -replace "LTAI5tMtDM6tVFiKAbuB2wZN", "your-access-key-id"
    $content = $content -replace "******************************", "your-access-key-secret"
    Set-Content "配置文件说明.md" -Value $content -Encoding UTF8
}

# 检查并修改阿里云OSS文档
if (Test-Path "阿里云OSS博客上传功能集成文档.md") {
    Write-Host "   - 修改 阿里云OSS博客上传功能集成文档.md" -ForegroundColor Cyan
    $content = Get-Content "阿里云OSS博客上传功能集成文档.md" -Raw -Encoding UTF8
    $content = $content -replace "LTAI5tMtDM6tVFiKAbuB2wZN", "your-access-key-id"
    $content = $content -replace "******************************", "your-access-key-secret"
    Set-Content "阿里云OSS博客上传功能集成文档.md" -Value $content -Encoding UTF8
}

# 检查application.yaml
if (Test-Path "backend/src/main/resources/application.yaml") {
    Write-Host "   - 检查 application.yaml" -ForegroundColor Cyan
    $content = Get-Content "backend/src/main/resources/application.yaml" -Raw -Encoding UTF8
    if ($content -match "LTAI5tMtDM6tVFiKAbuB2wZN" -or $content -match "******************************") {
        $content = $content -replace "LTAI5tMtDM6tVFiKAbuB2wZN", "your-access-key-id"
        $content = $content -replace "******************************", "your-access-key-secret"
        Set-Content "backend/src/main/resources/application.yaml" -Value $content -Encoding UTF8
        Write-Host "     已修改 application.yaml" -ForegroundColor Green
    } else {
        Write-Host "     application.yaml 中未发现敏感信息" -ForegroundColor Green
    }
}

# 2. 提交修改
Write-Host "2. 提交修改..." -ForegroundColor Yellow
git add .
git commit -m "Security: Remove sensitive Alibaba Cloud credentials from documentation"

Write-Host "=== 修改完成 ===" -ForegroundColor Green
Write-Host "现在可以尝试推送：git push origin dev" -ForegroundColor White
Write-Host "如果仍然被阻止，需要清理Git历史记录" -ForegroundColor Yellow
