# 清理Git历史记录中的敏感信息
# 使用git filter-branch移除敏感信息

Write-Host "=== 清理Git历史记录中的敏感信息 ===" -ForegroundColor Red

# 备份当前分支
Write-Host "1. 创建备份分支..." -ForegroundColor Yellow
git branch backup-before-cleanup

# 清理敏感信息
Write-Host "2. 开始清理敏感信息..." -ForegroundColor Yellow

# 替换阿里云AccessKey ID
Write-Host "   - 清理 AccessKey ID..." -ForegroundColor Cyan
git filter-branch --force --index-filter "
git ls-files -z | xargs -0 sed -i 's/LTAI5tMtDM6tVFiKAbuB2wZN/your-access-key-id/g'
" --prune-empty --tag-name-filter cat -- --all

# 替换阿里云AccessKey Secret
Write-Host "   - 清理 AccessKey Secret..." -ForegroundColor Cyan
git filter-branch --force --index-filter "
git ls-files -z | xargs -0 sed -i 's/******************************/your-access-key-secret/g'
" --prune-empty --tag-name-filter cat -- --all

# 清理引用
Write-Host "3. 清理引用..." -ForegroundColor Yellow
git for-each-ref --format='delete %(refname)' refs/original | git update-ref --stdin
git reflog expire --expire=now --all
git gc --prune=now --aggressive

Write-Host "=== 清理完成 ===" -ForegroundColor Green
Write-Host "请检查文件内容，确认敏感信息已被替换" -ForegroundColor White
Write-Host "如果确认无误，可以强制推送：git push --force-with-lease origin dev" -ForegroundColor White
