# Fix BOM characters in Java files
Write-Host "Starting BOM fix..." -ForegroundColor Green

# Get all Java files
$javaFiles = Get-ChildItem -Path "src\main\java" -Filter "*.java" -Recurse

$fixedCount = 0

foreach ($file in $javaFiles) {
    # Read file content as bytes
    $bytes = [System.IO.File]::ReadAllBytes($file.FullName)

    # Check for BOM characters (UTF-8 BOM: EF BB BF)
    if ($bytes.Length -ge 3 -and $bytes[0] -eq 0xEF -and $bytes[1] -eq 0xBB -and $bytes[2] -eq 0xBF) {
        Write-Host "Fix file: $($file.FullName)" -ForegroundColor Yellow

        # Remove BOM characters
        $newBytes = $bytes[3..($bytes.Length-1)]

        # Write back to file
        [System.IO.File]::WriteAllBytes($file.FullName, $newBytes)
        $fixedCount++
    }
}

Write-Host "BOM fix completed! Fixed $fixedCount files" -ForegroundColor Green
