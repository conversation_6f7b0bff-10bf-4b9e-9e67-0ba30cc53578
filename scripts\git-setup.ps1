# Git仓库初始化和提交脚本
# 用于将项目提交到GitHub

param(
    [Parameter(Mandatory=$true)]
    [string]$RepoUrl,
    
    [Parameter(Mandatory=$false)]
    [string]$CommitMessage = "Initial commit: Script Murder Platform setup"
)

Write-Host "=== Git Repository Setup ===" -ForegroundColor Green

# 检查是否已经是Git仓库
if (Test-Path ".git") {
    Write-Host "Git repository already exists." -ForegroundColor Yellow
} else {
    Write-Host "Initializing Git repository..." -ForegroundColor Cyan
    git init
}

# 添加所有文件
Write-Host "Adding files to Git..." -ForegroundColor Cyan
git add .

# 检查状态
Write-Host "Git status:" -ForegroundColor Cyan
git status

# 提交
Write-Host "Committing changes..." -ForegroundColor Cyan
git commit -m "$CommitMessage"

# 添加远程仓库
Write-Host "Adding remote repository..." -ForegroundColor Cyan
git remote add origin $RepoUrl

# 推送到远程仓库
Write-Host "Pushing to remote repository..." -ForegroundColor Cyan
git branch -M main
git push -u origin main

Write-Host "=== Setup Complete ===" -ForegroundColor Green
Write-Host "Repository URL: $RepoUrl" -ForegroundColor White
Write-Host "Branch: main" -ForegroundColor White
