# 包名重构脚本
# 将 com.scriptmurder 重构为 com.scriptmurder

Write-Host "开始包名重构..." -ForegroundColor Green

# 1. 重命名目录结构
$oldPath = "backend\src\main\java\com\hmdp"
$newPath = "backend\src\main\java\com\scriptmurder"

if (Test-Path $oldPath) {
    Write-Host "重命名目录: $oldPath -> $newPath" -ForegroundColor Yellow
    
    # 创建新的目录结构
    New-Item -ItemType Directory -Path "backend\src\main\java\com\scriptmurder" -Force
    
    # 移动所有文件和子目录
    Get-ChildItem -Path $oldPath -Recurse | ForEach-Object {
        $relativePath = $_.FullName.Substring((Resolve-Path $oldPath).Path.Length + 1)
        $newFilePath = Join-Path $newPath $relativePath
        
        if ($_.PSIsContainer) {
            New-Item -ItemType Directory -Path $newFilePath -Force
        } else {
            $newFileDir = Split-Path $newFilePath -Parent
            if (!(Test-Path $newFileDir)) {
                New-Item -ItemType Directory -Path $newFileDir -Force
            }
            Move-Item -Path $_.FullName -Destination $newFilePath -Force
        }
    }
    
    # 删除旧目录
    Remove-Item -Path $oldPath -Recurse -Force
}

# 2. 替换文件内容中的包名
Write-Host "替换文件内容中的包名..." -ForegroundColor Yellow

$javaFiles = Get-ChildItem -Path "backend\src" -Filter "*.java" -Recurse
$configFiles = Get-ChildItem -Path "backend\src\main\resources" -Filter "*.yaml" -Recurse
$configFiles += Get-ChildItem -Path "backend\src\main\resources" -Filter "*.yml" -Recurse

$allFiles = $javaFiles + $configFiles

foreach ($file in $allFiles) {
    $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
    
    if ($content -match "com\.hmdp") {
        Write-Host "更新文件: $($file.FullName)" -ForegroundColor Cyan
        
        # 替换包声明
        $content = $content -replace "package com\.hmdp", "package com.scriptmurder"
        
        # 替换import语句
        $content = $content -replace "import com\.hmdp", "import com.scriptmurder"
        
        # 替换配置文件中的包名引用
        $content = $content -replace "com\.hmdp", "com.scriptmurder"
        
        # 保存文件
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
    }
}

Write-Host "包名重构完成！" -ForegroundColor Green
Write-Host "请检查以下内容：" -ForegroundColor Yellow
Write-Host "1. 所有Java文件的package声明" -ForegroundColor White
Write-Host "2. 所有import语句" -ForegroundColor White
Write-Host "3. 配置文件中的包名引用" -ForegroundColor White
Write-Host "4. IDE中是否有编译错误" -ForegroundColor White
