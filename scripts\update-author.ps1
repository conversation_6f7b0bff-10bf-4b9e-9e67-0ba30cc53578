# 更新作者信息脚本
# 将所有Java文件中的@author注释改为"an"

Write-Host "Starting to update author info..." -ForegroundColor Green

# 获取所有Java文件
$javaFiles = Get-ChildItem -Path "backend\src\main\java" -Filter "*.java" -Recurse

$updatedCount = 0

foreach ($file in $javaFiles) {
    $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
    
    if ($content -match '@author') {
        Write-Host "Updating file: $($file.FullName)" -ForegroundColor Cyan
        
        # 替换@author注释
        $content = $content -replace '@author\s+.*', '<AUTHOR>
        
        # 保存文件
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8 -NoNewline
        $updatedCount++
    }
}

Write-Host "Author info updated! Updated $updatedCount files" -ForegroundColor Green
