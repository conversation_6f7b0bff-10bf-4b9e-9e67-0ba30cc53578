# 用户设置模块数据库升级脚本
# 版本：v1.1.0
# 描述：自动执行用户设置表的创建和初始化

param(
    [string]$Host = "localhost",
    [string]$Port = "3306",
    [string]$Database = "hmdp",
    [string]$Username = "root",
    [string]$Password = "",
    [switch]$Test = $false,
    [switch]$Backup = $true,
    [switch]$Force = $false
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✅ $Message" "Green"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠️  $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "❌ $Message" "Red"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ️  $Message" "Cyan"
}

# 检查MySQL客户端
function Test-MySQLClient {
    try {
        $null = Get-Command mysql -ErrorAction Stop
        return $true
    }
    catch {
        return $false
    }
}

# 执行SQL文件
function Invoke-SQLFile {
    param(
        [string]$FilePath,
        [string]$Description
    )
    
    Write-Info "执行 $Description..."
    
    if (-not (Test-Path $FilePath)) {
        throw "SQL文件不存在: $FilePath"
    }
    
    $mysqlArgs = @(
        "-h", $Host,
        "-P", $Port,
        "-u", $Username,
        "-D", $Database
    )
    
    if ($Password) {
        $mysqlArgs += "-p$Password"
    }
    
    try {
        Get-Content $FilePath | mysql @mysqlArgs
        Write-Success "$Description 执行成功"
    }
    catch {
        Write-Error "$Description 执行失败: $($_.Exception.Message)"
        throw
    }
}

# 备份数据库
function Backup-Database {
    if (-not $Backup) {
        Write-Info "跳过数据库备份"
        return
    }
    
    Write-Info "开始备份数据库..."
    
    $backupDir = "backup"
    if (-not (Test-Path $backupDir)) {
        New-Item -ItemType Directory -Path $backupDir | Out-Null
    }
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupFile = "$backupDir/hmdp_backup_$timestamp.sql"
    
    $mysqldumpArgs = @(
        "-h", $Host,
        "-P", $Port,
        "-u", $Username,
        "--single-transaction",
        "--routines",
        "--triggers",
        $Database
    )
    
    if ($Password) {
        $mysqldumpArgs += "-p$Password"
    }
    
    try {
        mysqldump @mysqldumpArgs | Out-File -FilePath $backupFile -Encoding UTF8
        Write-Success "数据库备份完成: $backupFile"
    }
    catch {
        Write-Warning "数据库备份失败，但继续执行升级: $($_.Exception.Message)"
    }
}

# 检查表是否存在
function Test-TableExists {
    param([string]$TableName)
    
    $query = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '$Database' AND table_name = '$TableName'"
    
    $mysqlArgs = @(
        "-h", $Host,
        "-P", $Port,
        "-u", $Username,
        "-D", $Database,
        "-e", $query,
        "-s", "-N"
    )
    
    if ($Password) {
        $mysqlArgs += "-p$Password"
    }
    
    try {
        $result = mysql @mysqlArgs
        return [int]$result -gt 0
    }
    catch {
        return $false
    }
}

# 主执行函数
function Main {
    Write-ColorOutput "🚀 用户设置模块数据库升级工具" "Magenta"
    Write-ColorOutput "=================================" "Magenta"
    
    # 检查MySQL客户端
    if (-not (Test-MySQLClient)) {
        Write-Error "未找到MySQL客户端，请确保MySQL已安装并添加到PATH"
        exit 1
    }
    
    # 显示配置信息
    Write-Info "数据库配置:"
    Write-Host "  主机: $Host"
    Write-Host "  端口: $Port"
    Write-Host "  数据库: $Database"
    Write-Host "  用户: $Username"
    Write-Host "  备份: $Backup"
    Write-Host "  测试模式: $Test"
    
    # 确认执行
    if (-not $Force -and -not $Test) {
        $confirm = Read-Host "是否继续执行升级? (y/N)"
        if ($confirm -ne "y" -and $confirm -ne "Y") {
            Write-Info "升级已取消"
            exit 0
        }
    }
    
    try {
        # 检查现有表
        Write-Info "检查现有表结构..."
        $userSettingsExists = Test-TableExists "tb_user_settings"
        $loginHistoryExists = Test-TableExists "tb_login_history"
        
        if ($userSettingsExists -and $loginHistoryExists -and -not $Force) {
            Write-Warning "用户设置表已存在，使用 -Force 参数强制重新创建"
            exit 0
        }
        
        # 备份数据库
        if (-not $Test) {
            Backup-Database
        }
        
        # 执行升级脚本
        if ($Test) {
            Write-Info "测试模式：验证SQL文件..."
            if (Test-Path "sql/upgrade_user_settings.sql") {
                Write-Success "升级脚本文件存在"
            } else {
                Write-Error "升级脚本文件不存在"
            }
            
            if (Test-Path "sql/test_user_settings.sql") {
                Write-Success "测试脚本文件存在"
            } else {
                Write-Error "测试脚本文件不存在"
            }
        } else {
            # 执行升级
            Invoke-SQLFile "sql/upgrade_user_settings.sql" "用户设置模块升级"
            
            # 执行测试验证
            Write-Info "执行功能验证..."
            Invoke-SQLFile "sql/test_user_settings.sql" "功能验证测试"
        }
        
        Write-Success "用户设置模块升级完成！"
        Write-Info "下一步："
        Write-Host "  1. 检查数据库表结构"
        Write-Host "  2. 实现后端实体类和服务"
        Write-Host "  3. 完善前端设置页面"
        
    }
    catch {
        Write-Error "升级过程中发生错误: $($_.Exception.Message)"
        Write-Info "如果需要恢复，请使用备份文件"
        exit 1
    }
}

# 显示帮助信息
function Show-Help {
    Write-Host @"
用户设置模块数据库升级工具

用法:
    .\upgrade-user-settings.ps1 [参数]

参数:
    -Host <主机>        数据库主机 (默认: localhost)
    -Port <端口>        数据库端口 (默认: 3306)
    -Database <数据库>  数据库名称 (默认: hmdp)
    -Username <用户名>  数据库用户名 (默认: root)
    -Password <密码>    数据库密码
    -Test              测试模式，只验证文件不执行
    -Backup            是否备份数据库 (默认: true)
    -Force             强制执行，即使表已存在
    -Help              显示此帮助信息

示例:
    .\upgrade-user-settings.ps1 -Test
    .\upgrade-user-settings.ps1 -Password "your_password"
    .\upgrade-user-settings.ps1 -Host "*************" -Password "password" -Force
"@
}

# 检查是否需要显示帮助
if ($args -contains "-Help" -or $args -contains "--help" -or $args -contains "-h") {
    Show-Help
    exit 0
}

# 执行主函数
Main
