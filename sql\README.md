# 数据库脚本使用指南

## 📁 文件说明

### 核心数据库文件
- **`hmdp.sql`** - 主数据库结构文件
- **`missing_tables.sql`** - 补充缺失的核心业务表
- **`table_optimizations.sql`** - 数据库性能优化脚本

### 用户设置模块 🆕
- **`user_settings_tables.sql`** - 用户设置模块完整表结构
- **`upgrade_user_settings.sql`** - 数据库升级脚本（推荐使用）
- **`test_user_settings.sql`** - 功能验证和测试脚本

### 分析和文档
- **`table_analysis_report.md`** - 数据库表结构分析报告
- **`README.md`** - 本使用指南

## 🚀 快速开始

### 1. 全新安装

如果是全新项目，按以下顺序执行：

```bash
# 1. 创建基础数据库结构
mysql -u root -p hmdp < hmdp.sql

# 2. 添加核心业务表
mysql -u root -p hmdp < missing_tables.sql

# 3. 添加用户设置模块
mysql -u root -p hmdp < upgrade_user_settings.sql

# 4. 应用性能优化
mysql -u root -p hmdp < table_optimizations.sql
```

### 2. 现有项目升级

如果项目已存在，只需要添加用户设置模块：

```bash
# 使用PowerShell脚本（推荐）
.\scripts\upgrade-user-settings.ps1 -Password "your_password"

# 或手动执行
mysql -u root -p hmdp < upgrade_user_settings.sql
```

### 3. 功能验证

```bash
# 执行测试脚本验证功能
mysql -u root -p hmdp < test_user_settings.sql
```

## 🛠️ PowerShell 升级工具

### 基本用法

```powershell
# 测试模式（推荐先执行）
.\scripts\upgrade-user-settings.ps1 -Test

# 正常升级
.\scripts\upgrade-user-settings.ps1 -Password "your_password"

# 强制重新创建表
.\scripts\upgrade-user-settings.ps1 -Password "your_password" -Force

# 自定义数据库连接
.\scripts\upgrade-user-settings.ps1 -Host "*************" -Database "hmdp" -Username "admin" -Password "password"
```

### 参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `-Host` | 数据库主机 | localhost |
| `-Port` | 数据库端口 | 3306 |
| `-Database` | 数据库名称 | hmdp |
| `-Username` | 数据库用户名 | root |
| `-Password` | 数据库密码 | 空 |
| `-Test` | 测试模式，只验证不执行 | false |
| `-Backup` | 是否自动备份 | true |
| `-Force` | 强制执行 | false |

## 📊 用户设置模块详情

### 新增表结构

#### tb_user_settings - 用户设置表
```sql
-- 主要字段
user_id              -- 用户ID（外键）
theme                -- 主题偏好（dark/light）
language             -- 语言设置（zh-CN/en-US等）
timezone             -- 时区设置
email_notifications  -- 邮件通知开关
system_notifications -- 系统通知开关
activity_notifications -- 活动通知开关
social_notifications -- 社交通知开关
privacy_level        -- 隐私级别（1-公开 2-好友 3-私密）
auto_save           -- 自动保存开关
```

#### tb_login_history - 登录历史表
```sql
-- 主要字段
user_id          -- 用户ID（外键）
login_ip         -- 登录IP
login_location   -- 登录地点
device_type      -- 设备类型（Mobile/Tablet/Desktop）
browser          -- 浏览器信息
user_agent       -- 完整User-Agent
login_time       -- 登录时间
logout_time      -- 登出时间
session_duration -- 会话时长
status           -- 登录状态（1-成功 0-失败）
failure_reason   -- 失败原因
```

### 功能特性

#### 🎨 个性化设置
- **主题切换**：深色/浅色主题
- **语言本地化**：多语言支持
- **时区设置**：个性化时间显示

#### 🔔 通知管理
- **邮件通知**：重要信息邮件推送
- **系统通知**：系统消息提醒
- **活动通知**：游戏活动更新
- **社交通知**：关注、点赞等社交互动

#### 🛡️ 隐私控制
- **公开模式**：所有人可见
- **好友模式**：仅好友可见
- **私密模式**：仅自己可见

#### 📈 安全审计
- **登录追踪**：IP和地理位置记录
- **设备管理**：设备类型识别
- **会话监控**：登录时长统计
- **异常检测**：失败登录分析

## 🔧 开发集成

### 后端实体类

需要创建对应的Java实体类：
- `UserSettings.java`
- `LoginHistory.java`

### API接口

需要实现的主要接口：
- `GET /api/user/settings` - 获取用户设置
- `PUT /api/user/settings` - 更新用户设置
- `GET /api/user/login-history` - 获取登录历史
- `DELETE /api/user/login-history` - 清除登录历史

### 前端状态管理

建议创建的Composables：
- `useSettings.ts` - 设置状态管理
- `useTheme.ts` - 主题管理
- `useNotifications.ts` - 通知设置管理
- `usePrivacy.ts` - 隐私设置管理

## 📝 注意事项

### 数据备份
- 升级前会自动创建备份（可通过`-Backup false`禁用）
- 备份文件保存在`backup/`目录
- 建议在生产环境手动备份

### 兼容性
- 支持MySQL 5.7+
- 建议使用MySQL 8.0+
- 确保有足够的数据库权限

### 性能考虑
- 已优化常用查询的索引
- 登录历史表可能增长较快，建议定期清理
- 可根据实际使用情况调整索引

### 安全建议
- 登录历史包含敏感信息，注意访问权限控制
- IP地址信息遵循隐私保护法规
- 定期清理过期的登录历史记录

## 🐛 故障排除

### 常见问题

1. **权限不足**
   ```
   ERROR 1142: CREATE command denied
   ```
   解决：确保数据库用户有CREATE、ALTER权限

2. **表已存在**
   ```
   ERROR 1050: Table 'tb_user_settings' already exists
   ```
   解决：使用`-Force`参数强制重新创建

3. **外键约束失败**
   ```
   ERROR 1452: Cannot add or update a child row
   ```
   解决：确保tb_user表存在且有数据

### 回滚操作

如果升级失败需要回滚：

```sql
-- 删除新创建的表
DROP TABLE IF EXISTS tb_login_history;
DROP TABLE IF EXISTS tb_user_settings;

-- 删除触发器
DROP TRIGGER IF EXISTS tr_user_settings_insert;
```

## 📞 技术支持

如有问题，请查看：
1. `table_analysis_report.md` - 详细的表结构分析
2. `test_user_settings.sql` - 测试脚本和示例查询
3. 项目文档中的相关模块说明

---

**最后更新**: 2025-01-31  
**版本**: v1.1.0  
**维护者**: 开发团队
