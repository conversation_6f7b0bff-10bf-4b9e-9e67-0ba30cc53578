-- 剧本角色测试数据插入脚本
-- 为前10个剧本创建角色数据

-- 清空现有角色数据（可选）
-- TRUNCATE TABLE tb_script_character;

-- 角色名称数组
SET @male_names = '张伟,李强,王磊,刘洋,陈杰,杨帆,赵明,孙涛,周鹏,吴斌,徐亮,朱军,马超,胡勇,郭峰,何东,高飞,林浩,罗斌,宋涛';
SET @female_names = '王芳,李娜,张敏,刘静,陈丽,杨雪,赵颖,孙梅,周洁,吴红,徐燕,朱莉,马丽,胡敏,郭娟,何琳,高雅,林婷,罗萍,宋霞';

-- 职业数组
SET @occupations = '医生,律师,教师,警察,记者,商人,艺术家,工程师,厨师,司机,秘书,学生,退休人员,自由职业者,公务员,银行家,设计师,演员,作家,科学家';

-- 性格特点数组
SET @traits = '冷静理智,热情开朗,内向害羞,外向活泼,严肃认真,幽默风趣,温柔体贴,坚强独立,敏感细腻,大大咧咧,谨慎小心,冲动鲁莽,乐观向上,悲观消极,聪明机智,单纯天真,成熟稳重,年轻冲动,善良正直,狡猾奸诈';

-- 角色描述模板
SET @descriptions = '一个神秘的角色，身上隐藏着不为人知的秘密...|看似普通的外表下，却有着复杂的内心世界...|在这个故事中扮演着关键的角色，掌握着重要的线索...|表面上与其他人没有什么不同，但实际上...|一个充满矛盾的角色，既有光明的一面，也有黑暗的一面...|在关键时刻总能做出令人意外的选择...|拥有特殊的能力或知识，在故事中起到重要作用...|一个值得信任的角色，但也可能隐藏着秘密...|在团队中扮演着重要的角色，但也有自己的小算盘...|看起来无害，但可能是最危险的角色...';

-- 插入角色数据的存储过程
DELIMITER $$

DROP PROCEDURE IF EXISTS InsertScriptCharacterData$$

CREATE PROCEDURE InsertScriptCharacterData()
BEGIN
    DECLARE i INT DEFAULT 1;
    DECLARE script_id INT DEFAULT 1;
    DECLARE char_count INT;
    DECLARE j INT;
    
    DECLARE v_name VARCHAR(64);
    DECLARE v_title VARCHAR(128);
    DECLARE v_gender ENUM('male','female','other');
    DECLARE v_age VARCHAR(32);
    DECLARE v_occupation VARCHAR(64);
    DECLARE v_description TEXT;
    DECLARE v_traits VARCHAR(512);
    DECLARE v_difficulty INT;
    DECLARE v_secrets_count INT;
    DECLARE v_sort_order INT;
    
    -- 临时变量
    DECLARE name_idx INT;
    DECLARE occupation_idx INT;
    DECLARE trait_idx INT;
    DECLARE desc_idx INT;
    
    -- 为前10个剧本创建角色
    WHILE script_id <= 10 DO
        -- 每个剧本4-7个角色
        SET char_count = 4 + FLOOR(RAND() * 4);
        SET j = 1;
        
        WHILE j <= char_count DO
            -- 随机性别
            IF RAND() < 0.5 THEN
                SET v_gender = 'male';
                SET name_idx = FLOOR(1 + RAND() * 20);
                SET v_name = SUBSTRING_INDEX(SUBSTRING_INDEX(@male_names, ',', name_idx), ',', -1);
            ELSE
                SET v_gender = 'female';
                SET name_idx = FLOOR(1 + RAND() * 20);
                SET v_name = SUBSTRING_INDEX(SUBSTRING_INDEX(@female_names, ',', name_idx), ',', -1);
            END IF;
            
            -- 生成角色称号
            SET v_title = CASE 
                WHEN j = 1 THEN '主角'
                WHEN j = 2 THEN '关键人物'
                WHEN RAND() < 0.3 THEN '神秘角色'
                WHEN RAND() < 0.6 THEN '重要角色'
                ELSE '普通角色'
            END;
            
            -- 生成年龄
            SET v_age = CASE 
                WHEN RAND() < 0.2 THEN '18-25岁'
                WHEN RAND() < 0.5 THEN '26-35岁'
                WHEN RAND() < 0.8 THEN '36-45岁'
                ELSE '46-60岁'
            END;
            
            -- 生成职业
            SET occupation_idx = FLOOR(1 + RAND() * 20);
            SET v_occupation = SUBSTRING_INDEX(SUBSTRING_INDEX(@occupations, ',', occupation_idx), ',', -1);
            
            -- 生成描述
            SET desc_idx = FLOOR(1 + RAND() * 10);
            SET v_description = SUBSTRING_INDEX(SUBSTRING_INDEX(@descriptions, '|', desc_idx), '|', -1);
            
            -- 生成性格特点（选择2-3个）
            SET trait_idx = FLOOR(1 + RAND() * 20);
            SET v_traits = SUBSTRING_INDEX(SUBSTRING_INDEX(@traits, ',', trait_idx), ',', -1);
            SET trait_idx = FLOOR(1 + RAND() * 20);
            SET v_traits = CONCAT(v_traits, ',', SUBSTRING_INDEX(SUBSTRING_INDEX(@traits, ',', trait_idx), ',', -1));
            IF RAND() < 0.5 THEN
                SET trait_idx = FLOOR(1 + RAND() * 20);
                SET v_traits = CONCAT(v_traits, ',', SUBSTRING_INDEX(SUBSTRING_INDEX(@traits, ',', trait_idx), ',', -1));
            END IF;
            
            -- 生成难度 (1-3)
            SET v_difficulty = 1 + FLOOR(RAND() * 3);
            
            -- 生成秘密数量 (0-5)
            SET v_secrets_count = FLOOR(RAND() * 6);
            
            -- 排序顺序
            SET v_sort_order = j;
            
            -- 插入角色数据
            INSERT INTO tb_script_character (
                script_id, name, title, gender, age, occupation,
                description, avatar, traits, difficulty, secrets_count, sort_order,
                create_time, update_time
            ) VALUES (
                script_id,
                v_name,
                v_title,
                v_gender,
                v_age,
                v_occupation,
                v_description,
                CONCAT('/images/characters/avatar_', script_id, '_', j, '.jpg'),
                v_traits,
                v_difficulty,
                v_secrets_count,
                v_sort_order,
                NOW(),
                NOW()
            );
            
            SET j = j + 1;
        END WHILE;
        
        SET script_id = script_id + 1;
    END WHILE;
    
    COMMIT;
    
END$$

DELIMITER ;

-- 执行存储过程插入数据
CALL InsertScriptCharacterData();

-- 删除存储过程
DROP PROCEDURE InsertScriptCharacterData;

-- 验证插入结果
SELECT 
    script_id,
    COUNT(*) as character_count,
    COUNT(CASE WHEN gender = 'male' THEN 1 END) as male_count,
    COUNT(CASE WHEN gender = 'female' THEN 1 END) as female_count,
    AVG(difficulty) as avg_difficulty,
    AVG(secrets_count) as avg_secrets
FROM tb_script_character 
GROUP BY script_id 
ORDER BY script_id;

-- 查看角色详情示例
SELECT 
    sc.script_id,
    s.title as script_title,
    sc.name,
    sc.title,
    sc.gender,
    sc.age,
    sc.occupation,
    sc.difficulty,
    sc.secrets_count
FROM tb_script_character sc
LEFT JOIN tb_script s ON sc.script_id = s.id
WHERE sc.script_id <= 3
ORDER BY sc.script_id, sc.sort_order;
