-- 剧本管理状态模块 - 1000条模拟数据插入脚本
-- 基于 tb_script 表结构生成测试数据

-- 清空现有数据（可选）
-- TRUNCATE TABLE tb_script;

-- 剧本分类数组
SET @categories = '推理,恐怖,情感,欢乐,古风,现代,科幻,悬疑,历史,奇幻';

-- 剧本标题前缀数组
SET @title_prefixes = '雪山神庙,古宅疑云,都市传说,校园怪谈,豪门恩怨,江湖风云,星际迷航,时空穿越,魔法学院,末日求生,密室逃脱,心理游戏,商战风云,宫廷秘史,武侠传奇,科技谜团,爱情公寓,青春校园,职场风云,家族传承';

-- 剧本标题后缀数组  
SET @title_suffixes = '之谜,传说,风云,秘密,真相,阴谋,复仇,救赎,重生,觉醒,崛起,沉沦,迷局,破晓,黄昏,永恒,幻想,现实,命运,选择';

-- 年龄范围数组
SET @age_ranges = '12+,16+,18+,全年龄';

-- 游戏时长数组
SET @durations = '2-3小时,3-4小时,4-5小时,5-6小时,6小时以上';

-- 标签数组
SET @tags = '推理,悬疑,密室,心理,经典,烧脑,反转,团队,策略,角色扮演,社交,竞技,合作,沉浸,互动';

-- 亮点数组
SET @highlights = '经典推理,密室悬疑,心理博弈,反转结局,团队合作,角色扮演,沉浸体验,烧脑解谜,社交互动,策略思考';

-- 警告数组
SET @warnings = '包含暴力内容,心理压力较大,涉及恐怖元素,包含成人话题,需要团队配合,时间较长,难度较高';

-- 剧本描述模板
SET @descriptions = '在一个风雪交加的夜晚，几名旅客因为意外被困在神秘的地方。当第一个秘密被揭开时，每个人都成了嫌疑犯...|古老的宅邸中隐藏着不为人知的秘密，当夜幕降临时，诡异的事件接连发生...|繁华都市的背后隐藏着黑暗的真相，一场看似简单的聚会却引发了连锁反应...|校园里流传着古老的传说，当学生们决定探寻真相时，却发现事情远比想象的复杂...|豪门世家的内部斗争愈演愈烈，金钱、权力、爱情交织在一起，谁才是最后的赢家？|江湖风云变幻莫测，英雄豪杰齐聚一堂，一场关于正义与邪恶的较量即将展开...|在遥远的星际空间中，探险队发现了神秘的信号，这将改变人类的命运...|时空的裂缝突然出现，不同时代的人们相遇在同一个空间，历史将被改写...|魔法学院的新学期开始了，年轻的法师们将面临前所未有的挑战和考验...|末日降临，幸存者们必须团结一致，在废土上寻找生存的希望...';

-- 插入1000条剧本数据
DELIMITER $$

DROP PROCEDURE IF EXISTS InsertScriptMockData$$

CREATE PROCEDURE InsertScriptMockData()
BEGIN
    DECLARE i INT DEFAULT 1;
    DECLARE v_title VARCHAR(255);
    DECLARE v_description TEXT;
    DECLARE v_category VARCHAR(64);
    DECLARE v_player_min INT;
    DECLARE v_player_max INT;
    DECLARE v_duration VARCHAR(32);
    DECLARE v_difficulty INT;
    DECLARE v_age_range VARCHAR(32);
    DECLARE v_price DECIMAL(10,2);
    DECLARE v_status TINYINT;
    DECLARE v_tags VARCHAR(512);
    DECLARE v_highlights TEXT;
    DECLARE v_warnings VARCHAR(512);
    DECLARE v_rating DECIMAL(3,2);
    DECLARE v_review_count INT;
    DECLARE v_play_count INT;
    
    -- 临时变量
    DECLARE prefix_idx INT;
    DECLARE suffix_idx INT;
    DECLARE category_idx INT;
    DECLARE duration_idx INT;
    DECLARE age_idx INT;
    DECLARE desc_idx INT;
    
    WHILE i <= 1000 DO
        -- 生成随机标题
        SET prefix_idx = FLOOR(1 + RAND() * 20);
        SET suffix_idx = FLOOR(1 + RAND() * 20);
        SET v_title = CONCAT(
            SUBSTRING_INDEX(SUBSTRING_INDEX(@title_prefixes, ',', prefix_idx), ',', -1),
            SUBSTRING_INDEX(SUBSTRING_INDEX(@title_suffixes, ',', suffix_idx), ',', -1)
        );
        
        -- 生成随机分类
        SET category_idx = FLOOR(1 + RAND() * 10);
        SET v_category = SUBSTRING_INDEX(SUBSTRING_INDEX(@categories, ',', category_idx), ',', -1);
        
        -- 生成随机描述
        SET desc_idx = FLOOR(1 + RAND() * 10);
        SET v_description = SUBSTRING_INDEX(SUBSTRING_INDEX(@descriptions, '|', desc_idx), '|', -1);
        
        -- 生成玩家人数 (4-8人)
        SET v_player_min = 4 + FLOOR(RAND() * 3); -- 4-6
        SET v_player_max = v_player_min + FLOOR(RAND() * 3); -- 最多比最小多2人
        
        -- 生成游戏时长
        SET duration_idx = FLOOR(1 + RAND() * 5);
        SET v_duration = SUBSTRING_INDEX(SUBSTRING_INDEX(@durations, ',', duration_idx), ',', -1);
        
        -- 生成难度 (1-5)
        SET v_difficulty = 1 + FLOOR(RAND() * 5);
        
        -- 生成年龄范围
        SET age_idx = FLOOR(1 + RAND() * 4);
        SET v_age_range = SUBSTRING_INDEX(SUBSTRING_INDEX(@age_ranges, ',', age_idx), ',', -1);
        
        -- 生成价格 (50-300元)
        SET v_price = 50 + FLOOR(RAND() * 251);
        
        -- 生成状态 (1-上架 80%, 2-下架 15%, 3-审核中 5%)
        SET v_status = CASE 
            WHEN RAND() < 0.8 THEN 1
            WHEN RAND() < 0.95 THEN 2
            ELSE 3
        END;
        
        -- 生成标签 (随机选择2-4个)
        SET v_tags = CONCAT(
            SUBSTRING_INDEX(SUBSTRING_INDEX(@tags, ',', 1 + FLOOR(RAND() * 15)), ',', -1), ',',
            SUBSTRING_INDEX(SUBSTRING_INDEX(@tags, ',', 1 + FLOOR(RAND() * 15)), ',', -1), ',',
            SUBSTRING_INDEX(SUBSTRING_INDEX(@tags, ',', 1 + FLOOR(RAND() * 15)), ',', -1)
        );
        
        -- 生成亮点 (JSON格式)
        SET v_highlights = CONCAT('[\"',
            SUBSTRING_INDEX(SUBSTRING_INDEX(@highlights, ',', 1 + FLOOR(RAND() * 10)), ',', -1), '\",\"',
            SUBSTRING_INDEX(SUBSTRING_INDEX(@highlights, ',', 1 + FLOOR(RAND() * 10)), ',', -1), '\",\"',
            SUBSTRING_INDEX(SUBSTRING_INDEX(@highlights, ',', 1 + FLOOR(RAND() * 10)), ',', -1), '\"]'
        );
        
        -- 生成警告
        SET v_warnings = SUBSTRING_INDEX(SUBSTRING_INDEX(@warnings, ',', 1 + FLOOR(RAND() * 7)), ',', -1);
        
        -- 生成评分 (3.0-5.0)
        SET v_rating = 3.0 + RAND() * 2.0;
        
        -- 生成评价数量 (0-500)
        SET v_review_count = FLOOR(RAND() * 501);
        
        -- 生成游玩次数 (评价数量的2-10倍)
        SET v_play_count = v_review_count * (2 + FLOOR(RAND() * 9));
        
        -- 插入数据
        INSERT INTO tb_script (
            title, description, cover_image, images, category,
            player_count_min, player_count_max, duration, difficulty, age_range,
            tags, highlights, warnings, price, status,
            average_rating, review_count, play_count,
            create_time, update_time
        ) VALUES (
            v_title,
            v_description,
            CONCAT('/images/scripts/cover_', i, '.jpg'),
            CONCAT('/images/scripts/img_', i, '_1.jpg,/images/scripts/img_', i, '_2.jpg'),
            v_category,
            v_player_min,
            v_player_max,
            v_duration,
            v_difficulty,
            v_age_range,
            v_tags,
            v_highlights,
            v_warnings,
            v_price,
            v_status,
            v_rating,
            v_review_count,
            v_play_count,
            DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 365) DAY),
            NOW()
        );
        
        SET i = i + 1;
        
        -- 每100条提交一次，避免长事务
        IF i % 100 = 0 THEN
            COMMIT;
        END IF;
        
    END WHILE;
    
    COMMIT;
    
END$$

DELIMITER ;

-- 执行存储过程插入数据
CALL InsertScriptMockData();

-- 删除存储过程
DROP PROCEDURE InsertScriptMockData;

-- 验证插入结果
SELECT 
    COUNT(*) as total_scripts,
    COUNT(CASE WHEN status = 1 THEN 1 END) as published,
    COUNT(CASE WHEN status = 2 THEN 1 END) as unpublished,
    COUNT(CASE WHEN status = 3 THEN 1 END) as reviewing,
    AVG(price) as avg_price,
    AVG(average_rating) as avg_rating
FROM tb_script;

-- 按分类统计
SELECT 
    category,
    COUNT(*) as count,
    AVG(price) as avg_price,
    AVG(average_rating) as avg_rating
FROM tb_script 
GROUP BY category 
ORDER BY count DESC;

-- 按难度统计
SELECT 
    difficulty,
    COUNT(*) as count,
    AVG(price) as avg_price
FROM tb_script 
GROUP BY difficulty 
ORDER BY difficulty;
