-- 补充缺少的核心业务表
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 游戏房间表
-- ----------------------------
DROP TABLE IF EXISTS `tb_lobby`;
CREATE TABLE `tb_lobby` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '房间标题',
  `script_id` bigint UNSIGNED NOT NULL COMMENT '剧本ID',
  `host_user_id` bigint UNSIGNED NOT NULL COMMENT '房主用户ID',
  `max_players` int UNSIGNED NOT NULL COMMENT '最大玩家数',
  `current_players` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '当前玩家数',
  `start_time` timestamp NOT NULL COMMENT '开始时间',
  `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '游戏地点',
  `price` decimal(10,2) UNSIGNED NOT NULL COMMENT '费用',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '房间描述',
  `requirements` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '参与要求，JSON格式',
  `tags` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签，以","隔开',
  `status` enum('waiting','full','started','ended','cancelled') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'waiting' COMMENT '房间状态',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_script_id` (`script_id` ASC) USING BTREE,
  INDEX `idx_host_user_id` (`host_user_id` ASC) USING BTREE,
  INDEX `idx_status` (`status` ASC) USING BTREE,
  INDEX `idx_start_time` (`start_time` ASC) USING BTREE,
  CONSTRAINT `tb_lobby_ibfk_1` FOREIGN KEY (`script_id`) REFERENCES `tb_script` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tb_lobby_ibfk_2` FOREIGN KEY (`host_user_id`) REFERENCES `tb_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '游戏房间表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- 房间玩家表
-- ----------------------------
DROP TABLE IF EXISTS `tb_lobby_player`;
CREATE TABLE `tb_lobby_player` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `lobby_id` bigint UNSIGNED NOT NULL COMMENT '房间ID',
  `user_id` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `character_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '选择的角色ID',
  `is_ready` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否准备',
  `join_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
  `status` enum('joined','left','kicked') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'joined' COMMENT '状态',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_lobby_user` (`lobby_id`, `user_id`) USING BTREE,
  INDEX `idx_user_id` (`user_id` ASC) USING BTREE,
  INDEX `idx_character_id` (`character_id` ASC) USING BTREE,
  CONSTRAINT `tb_lobby_player_ibfk_1` FOREIGN KEY (`lobby_id`) REFERENCES `tb_lobby` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `tb_lobby_player_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `tb_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tb_lobby_player_ibfk_3` FOREIGN KEY (`character_id`) REFERENCES `tb_script_character` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '房间玩家表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- 评价有用性表
-- ----------------------------
DROP TABLE IF EXISTS `tb_review_helpful`;
CREATE TABLE `tb_review_helpful` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `review_id` bigint UNSIGNED NOT NULL COMMENT '评价ID',
  `user_id` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_review_user` (`review_id`, `user_id`) USING BTREE,
  INDEX `idx_user_id` (`user_id` ASC) USING BTREE,
  CONSTRAINT `tb_review_helpful_ibfk_1` FOREIGN KEY (`review_id`) REFERENCES `tb_script_review` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `tb_review_helpful_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `tb_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '评价有用性表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- 关注关系表
-- ----------------------------
DROP TABLE IF EXISTS `tb_follow`;
CREATE TABLE `tb_follow` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint UNSIGNED NOT NULL COMMENT '用户id',
  `follow_user_id` bigint UNSIGNED NOT NULL COMMENT '关注的用户id',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_user_follow` (`user_id`, `follow_user_id`) USING BTREE,
  INDEX `idx_follow_user_id` (`follow_user_id` ASC) USING BTREE,
  CONSTRAINT `tb_follow_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `tb_user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `tb_follow_ibfk_2` FOREIGN KEY (`follow_user_id`) REFERENCES `tb_user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '关注关系表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for tb_user_settings
-- ----------------------------
DROP TABLE IF EXISTS `tb_user_settings`;
CREATE TABLE `tb_user_settings` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '用户ID',
  `theme` varchar(20) DEFAULT 'dark' COMMENT '主题偏好',
  `language` varchar(10) DEFAULT 'zh-CN' COMMENT '语言设置',
  `timezone` varchar(50) DEFAULT 'Asia/Shanghai' COMMENT '时区设置',
  `email_notifications` tinyint(1) DEFAULT 1 COMMENT '邮件通知开关',
  `system_notifications` tinyint(1) DEFAULT 1 COMMENT '系统通知开关',
  `activity_notifications` tinyint(1) DEFAULT 1 COMMENT '活动通知开关',
  `social_notifications` tinyint(1) DEFAULT 1 COMMENT '社交通知开关',
  `privacy_level` tinyint(1) DEFAULT 1 COMMENT '隐私级别 1-公开 2-好友 3-私密',
  `auto_save` tinyint(1) DEFAULT 1 COMMENT '自动保存开关',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `tb_user_settings_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `tb_user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户设置表';

-- ----------------------------
-- Table structure for tb_login_history
-- ----------------------------
DROP TABLE IF EXISTS `tb_login_history`;
CREATE TABLE `tb_login_history` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '用户ID',
  `login_ip` varchar(45) NOT NULL COMMENT '登录IP',
  `login_location` varchar(100) DEFAULT NULL COMMENT '登录地点',
  `device_type` varchar(50) DEFAULT NULL COMMENT '设备类型',
  `browser` varchar(100) DEFAULT NULL COMMENT '浏览器信息',
  `login_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
  `logout_time` timestamp NULL DEFAULT NULL COMMENT '登出时间',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1-成功 0-失败',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_login_time` (`login_time`),
  CONSTRAINT `tb_login_history_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `tb_user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='登录历史表';

SET FOREIGN_KEY_CHECKS = 1;
