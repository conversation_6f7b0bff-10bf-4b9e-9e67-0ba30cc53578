-- 现有表结构优化建议
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 1. 优化 tb_blog 表以适应剧本杀社区
-- ----------------------------
ALTER TABLE `tb_blog` 
CHANGE COLUMN `shop_id` `script_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '关联剧本ID',
ADD COLUMN `lobby_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '关联房间ID' AFTER `script_id`,
ADD COLUMN `type` enum('experience','review','recruitment','discussion') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'experience' COMMENT '动态类型' AFTER `user_id`,
ADD INDEX `idx_script_id` (`script_id` ASC) USING BTREE,
ADD INDEX `idx_lobby_id` (`lobby_id` ASC) USING BTREE,
ADD INDEX `idx_type` (`type` ASC) USING BTREE;

-- 添加外键约束（如果需要）
-- ALTER TABLE `tb_blog` 
-- ADD CONSTRAINT `tb_blog_ibfk_script` FOREIGN KEY (`script_id`) REFERENCES `tb_script` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
-- ADD CONSTRAINT `tb_blog_ibfk_lobby` FOREIGN KEY (`lobby_id`) REFERENCES `tb_lobby` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT;

-- ----------------------------
-- 2. 优化 tb_blog_comments 表
-- ----------------------------
ALTER TABLE `tb_blog_comments`
ADD INDEX `idx_blog_id` (`blog_id` ASC) USING BTREE,
ADD INDEX `idx_parent_id` (`parent_id` ASC) USING BTREE,
ADD INDEX `idx_create_time` (`create_time` ASC) USING BTREE;

-- ----------------------------
-- 3. 为 tb_script 表添加更多索引优化查询
-- ----------------------------
ALTER TABLE `tb_script`
ADD INDEX `idx_average_rating` (`average_rating` ASC) USING BTREE,
ADD INDEX `idx_play_count` (`play_count` ASC) USING BTREE,
ADD INDEX `idx_create_time` (`create_time` ASC) USING BTREE,
ADD INDEX `idx_price` (`price` ASC) USING BTREE;

-- ----------------------------
-- 4. 为 tb_script_review 表添加索引优化
-- ----------------------------
ALTER TABLE `tb_script_review`
ADD INDEX `idx_create_time` (`create_time` ASC) USING BTREE,
ADD INDEX `idx_helpful_count` (`helpful_count` ASC) USING BTREE,
ADD INDEX `idx_status` (`status` ASC) USING BTREE;

-- ----------------------------
-- 5. 为 tb_user 表添加更多索引
-- ----------------------------
ALTER TABLE `tb_user`
ADD INDEX `idx_create_time` (`create_time` ASC) USING BTREE,
ADD INDEX `idx_last_login_time` (`last_login_time` ASC) USING BTREE;

-- ----------------------------
-- 6. 为 tb_user_info 表添加索引
-- ----------------------------
ALTER TABLE `tb_user_info`
ADD INDEX `idx_city` (`city` ASC) USING BTREE,
ADD INDEX `idx_level` (`level` ASC) USING BTREE,
ADD INDEX `idx_play_count` (`play_count` ASC) USING BTREE,
ADD INDEX `idx_host_count` (`host_count` ASC) USING BTREE;

-- ----------------------------
-- 7. 创建剧本分类表（如果需要标准化分类）
-- ----------------------------
DROP TABLE IF EXISTS `tb_script_category`;
CREATE TABLE `tb_script_category` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分类描述',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类图标',
  `sort_order` int UNSIGNED NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态：1-启用，2-禁用',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_name` (`name`) USING BTREE,
  INDEX `idx_status` (`status` ASC) USING BTREE,
  INDEX `idx_sort_order` (`sort_order` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '剧本分类表' ROW_FORMAT = COMPACT;

-- 插入默认分类数据
INSERT INTO `tb_script_category` (`name`, `description`, `icon`, `sort_order`) VALUES
('推理', '逻辑推理类剧本', '/icons/mystery.png', 1),
('恐怖', '恐怖悬疑类剧本', '/icons/horror.png', 2),
('情感', '情感体验类剧本', '/icons/emotion.png', 3),
('欢乐', '轻松欢乐类剧本', '/icons/fun.png', 4),
('古风', '古代背景类剧本', '/icons/ancient.png', 5),
('现代', '现代背景类剧本', '/icons/modern.png', 6),
('科幻', '科幻题材类剧本', '/icons/scifi.png', 7),
('武侠', '武侠题材类剧本', '/icons/wuxia.png', 8);

-- ----------------------------
-- 8. 创建系统配置表
-- ----------------------------
DROP TABLE IF EXISTS `tb_system_config`;
CREATE TABLE `tb_system_config` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `config_key` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置键',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置值',
  `config_type` enum('string','number','boolean','json') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'string' COMMENT '配置类型',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '配置描述',
  `is_public` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否公开：0-私有，1-公开',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_config_key` (`config_key`) USING BTREE,
  INDEX `idx_is_public` (`is_public` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统配置表' ROW_FORMAT = COMPACT;

-- 插入默认配置
INSERT INTO `tb_system_config` (`config_key`, `config_value`, `config_type`, `description`, `is_public`) VALUES
('site.name', '剧本杀应用', 'string', '网站名称', 1),
('site.description', '专业的剧本杀社交平台', 'string', '网站描述', 1),
('user.max_lobby_count', '5', 'number', '用户最大创建房间数', 0),
('lobby.max_duration_hours', '8', 'number', '房间最大持续时间（小时）', 0),
('review.min_play_time_minutes', '60', 'number', '评价最小游戏时长（分钟）', 0),
('upload.max_file_size_mb', '10', 'number', '文件上传最大大小（MB）', 0),
('sms.daily_limit', '10', 'number', '短信验证码每日限制', 0);

-- ----------------------------
-- 9. 创建操作日志表
-- ----------------------------
DROP TABLE IF EXISTS `tb_operation_log`;
CREATE TABLE `tb_operation_log` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '用户ID',
  `operation_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作类型',
  `operation_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作描述',
  `request_method` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求方法',
  `request_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求URL',
  `request_params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '请求参数',
  `response_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '响应结果',
  `ip_address` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户代理',
  `execution_time` int UNSIGNED NULL DEFAULT NULL COMMENT '执行时间（毫秒）',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态：1-成功，2-失败',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '错误信息',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id` (`user_id` ASC) USING BTREE,
  INDEX `idx_operation_type` (`operation_type` ASC) USING BTREE,
  INDEX `idx_create_time` (`create_time` ASC) USING BTREE,
  INDEX `idx_status` (`status` ASC) USING BTREE,
  CONSTRAINT `tb_operation_log_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `tb_user` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '操作日志表' ROW_FORMAT = COMPACT;

SET FOREIGN_KEY_CHECKS = 1;
