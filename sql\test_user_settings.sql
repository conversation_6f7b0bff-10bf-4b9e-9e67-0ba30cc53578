-- 用户设置模块测试脚本
-- 用于验证表结构和功能是否正常

-- ----------------------------
-- 测试1：验证表结构
-- ----------------------------
DESCRIBE tb_user_settings;
DESCRIBE tb_login_history;

-- ----------------------------
-- 测试2：验证约束和索引
-- ----------------------------
SHOW INDEX FROM tb_user_settings;
SHOW INDEX FROM tb_login_history;

-- ----------------------------
-- 测试3：插入测试数据
-- ----------------------------
-- 假设用户ID为1的用户存在，测试设置更新
UPDATE tb_user_settings 
SET 
  theme = 'light',
  language = 'en-US',
  email_notifications = 0,
  privacy_level = 2
WHERE user_id = 1;

-- 插入登录历史测试数据
INSERT INTO tb_login_history (
  user_id,
  login_ip,
  login_location,
  device_type,
  browser,
  user_agent,
  status
) VALUES (
  1,
  '*************',
  '北京市',
  'Desktop',
  'Chrome 120.0',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  1
);

-- ----------------------------
-- 测试4：查询功能测试
-- ----------------------------
-- 查询用户设置
SELECT 
  us.*,
  u.nick_name
FROM tb_user_settings us
LEFT JOIN tb_user u ON us.user_id = u.id
WHERE us.user_id = 1;

-- 查询登录历史
SELECT 
  lh.*,
  u.nick_name
FROM tb_login_history lh
LEFT JOIN tb_user u ON lh.user_id = u.id
WHERE lh.user_id = 1
ORDER BY lh.login_time DESC
LIMIT 5;

-- ----------------------------
-- 测试5：统计查询
-- ----------------------------
-- 用户设置统计
SELECT 
  theme,
  COUNT(*) as user_count,
  ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM tb_user_settings), 2) as percentage
FROM tb_user_settings
GROUP BY theme;

SELECT 
  privacy_level,
  CASE privacy_level
    WHEN 1 THEN '公开'
    WHEN 2 THEN '好友'
    WHEN 3 THEN '私密'
  END as privacy_name,
  COUNT(*) as user_count
FROM tb_user_settings
GROUP BY privacy_level;

-- 通知设置统计
SELECT 
  'email_notifications' as notification_type,
  SUM(email_notifications) as enabled_count,
  COUNT(*) - SUM(email_notifications) as disabled_count
FROM tb_user_settings
UNION ALL
SELECT 
  'system_notifications',
  SUM(system_notifications),
  COUNT(*) - SUM(system_notifications)
FROM tb_user_settings
UNION ALL
SELECT 
  'activity_notifications',
  SUM(activity_notifications),
  COUNT(*) - SUM(activity_notifications)
FROM tb_user_settings
UNION ALL
SELECT 
  'social_notifications',
  SUM(social_notifications),
  COUNT(*) - SUM(social_notifications)
FROM tb_user_settings;

-- ----------------------------
-- 测试6：登录历史分析
-- ----------------------------
-- 最近7天登录统计
SELECT 
  DATE(login_time) as login_date,
  COUNT(*) as login_count,
  COUNT(DISTINCT user_id) as unique_users
FROM tb_login_history
WHERE login_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
  AND status = 1
GROUP BY DATE(login_time)
ORDER BY login_date DESC;

-- 设备类型统计
SELECT 
  device_type,
  COUNT(*) as login_count,
  COUNT(DISTINCT user_id) as unique_users
FROM tb_login_history
WHERE status = 1
GROUP BY device_type
ORDER BY login_count DESC;

-- 浏览器统计
SELECT 
  SUBSTRING_INDEX(browser, ' ', 1) as browser_name,
  COUNT(*) as login_count
FROM tb_login_history
WHERE status = 1 AND browser IS NOT NULL
GROUP BY SUBSTRING_INDEX(browser, ' ', 1)
ORDER BY login_count DESC
LIMIT 10;

-- ----------------------------
-- 测试7：性能测试查询
-- ----------------------------
-- 测试索引效果
EXPLAIN SELECT * FROM tb_user_settings WHERE user_id = 1;
EXPLAIN SELECT * FROM tb_login_history WHERE user_id = 1 ORDER BY login_time DESC;
EXPLAIN SELECT * FROM tb_login_history WHERE login_time >= '2025-01-01';

-- ----------------------------
-- 测试8：数据完整性测试
-- ----------------------------
-- 检查是否所有用户都有设置记录
SELECT 
  u.id,
  u.nick_name,
  CASE WHEN us.user_id IS NULL THEN '缺少设置' ELSE '设置正常' END as settings_status
FROM tb_user u
LEFT JOIN tb_user_settings us ON u.id = us.user_id
WHERE us.user_id IS NULL;

-- 检查设置数据的有效性
SELECT 
  user_id,
  CASE 
    WHEN theme NOT IN ('dark', 'light') THEN '主题设置无效'
    WHEN privacy_level NOT IN (1, 2, 3) THEN '隐私级别无效'
    WHEN language NOT LIKE '%-%' THEN '语言格式无效'
    ELSE '数据正常'
  END as validation_result
FROM tb_user_settings
WHERE theme NOT IN ('dark', 'light')
   OR privacy_level NOT IN (1, 2, 3)
   OR language NOT LIKE '%-%';

-- ----------------------------
-- 测试9：清理测试数据
-- ----------------------------
-- 删除测试插入的登录历史记录
-- DELETE FROM tb_login_history WHERE login_ip = '*************';

-- 恢复用户设置为默认值
-- UPDATE tb_user_settings 
-- SET 
--   theme = 'dark',
--   language = 'zh-CN',
--   email_notifications = 1,
--   privacy_level = 1
-- WHERE user_id = 1;

-- ----------------------------
-- 测试完成报告
-- ----------------------------
SELECT 
  '用户设置模块测试完成' as message,
  NOW() as test_time,
  (SELECT COUNT(*) FROM tb_user_settings) as total_user_settings,
  (SELECT COUNT(*) FROM tb_login_history) as total_login_records;
