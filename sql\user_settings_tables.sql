-- 用户设置模块相关表结构
-- 包含用户设置表和登录历史表的完整定义

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for tb_user_settings
-- ----------------------------
DROP TABLE IF EXISTS `tb_user_settings`;
CREATE TABLE `tb_user_settings` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '用户ID',
  `theme` varchar(20) DEFAULT 'dark' COMMENT '主题偏好 dark/light',
  `language` varchar(10) DEFAULT 'zh-CN' COMMENT '语言设置',
  `timezone` varchar(50) DEFAULT 'Asia/Shanghai' COMMENT '时区设置',
  `email_notifications` tinyint(1) DEFAULT 1 COMMENT '邮件通知开关',
  `system_notifications` tinyint(1) DEFAULT 1 COMMENT '系统通知开关',
  `activity_notifications` tinyint(1) DEFAULT 1 COMMENT '活动通知开关',
  `social_notifications` tinyint(1) DEFAULT 1 COMMENT '社交通知开关',
  `privacy_level` tinyint(1) DEFAULT 1 COMMENT '隐私级别 1-公开 2-好友 3-私密',
  `auto_save` tinyint(1) DEFAULT 1 COMMENT '自动保存开关',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_theme` (`theme`),
  KEY `idx_privacy_level` (`privacy_level`),
  CONSTRAINT `tb_user_settings_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `tb_user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户设置表';

-- ----------------------------
-- Table structure for tb_login_history
-- ----------------------------
DROP TABLE IF EXISTS `tb_login_history`;
CREATE TABLE `tb_login_history` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '用户ID',
  `login_ip` varchar(45) NOT NULL COMMENT '登录IP',
  `login_location` varchar(100) DEFAULT NULL COMMENT '登录地点',
  `device_type` varchar(50) DEFAULT NULL COMMENT '设备类型 Mobile/Tablet/Desktop',
  `browser` varchar(100) DEFAULT NULL COMMENT '浏览器信息',
  `user_agent` text DEFAULT NULL COMMENT '完整的User-Agent信息',
  `login_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
  `logout_time` timestamp NULL DEFAULT NULL COMMENT '登出时间',
  `session_duration` int DEFAULT NULL COMMENT '会话时长(秒)',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1-成功 0-失败',
  `failure_reason` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_login_time` (`login_time`),
  KEY `idx_status` (`status`),
  KEY `idx_login_ip` (`login_ip`),
  CONSTRAINT `tb_login_history_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `tb_user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='登录历史表';

-- ----------------------------
-- 初始化默认设置数据
-- ----------------------------
-- 为现有用户创建默认设置
INSERT INTO `tb_user_settings` (`user_id`, `theme`, `language`, `timezone`, `email_notifications`, `system_notifications`, `activity_notifications`, `social_notifications`, `privacy_level`, `auto_save`)
SELECT 
  `id` as `user_id`,
  'dark' as `theme`,
  'zh-CN' as `language`,
  'Asia/Shanghai' as `timezone`,
  1 as `email_notifications`,
  1 as `system_notifications`,
  1 as `activity_notifications`,
  0 as `social_notifications`,
  1 as `privacy_level`,
  1 as `auto_save`
FROM `tb_user` 
WHERE `id` NOT IN (SELECT `user_id` FROM `tb_user_settings`);

-- ----------------------------
-- 创建触发器：用户注册时自动创建默认设置
-- ----------------------------
DELIMITER $$
CREATE TRIGGER `tr_user_settings_insert`
AFTER INSERT ON `tb_user`
FOR EACH ROW
BEGIN
  INSERT INTO `tb_user_settings` (
    `user_id`, 
    `theme`, 
    `language`, 
    `timezone`, 
    `email_notifications`, 
    `system_notifications`, 
    `activity_notifications`, 
    `social_notifications`, 
    `privacy_level`, 
    `auto_save`
  ) VALUES (
    NEW.id,
    'dark',
    'zh-CN',
    'Asia/Shanghai',
    1,
    1,
    1,
    0,
    1,
    1
  );
END$$
DELIMITER ;

SET FOREIGN_KEY_CHECKS = 1;

-- ----------------------------
-- 示例查询语句
-- ----------------------------
-- 查询用户设置
-- SELECT * FROM tb_user_settings WHERE user_id = 1;

-- 查询用户登录历史（最近10条）
-- SELECT * FROM tb_login_history WHERE user_id = 1 ORDER BY login_time DESC LIMIT 10;

-- 查询用户在线时长统计
-- SELECT 
--   user_id,
--   COUNT(*) as login_count,
--   AVG(session_duration) as avg_session_duration,
--   SUM(session_duration) as total_online_time
-- FROM tb_login_history 
-- WHERE user_id = 1 AND status = 1 AND session_duration IS NOT NULL
-- GROUP BY user_id;
